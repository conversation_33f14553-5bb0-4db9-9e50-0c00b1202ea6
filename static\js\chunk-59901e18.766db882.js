(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-59901e18"],{"172d":function(t,e,n){"use strict";n("bcc4")},b3fd:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"mycontent"},[n("el-button",{attrs:{icon:"el-icon-d-arrow-left",type:"info",plain:"",size:"small"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")]),n("h2",[t._v("创建API")]),n("common",{ref:"apiAdd"}),n("el-button",{staticStyle:{margin:"10px 0"},attrs:{type:"primary",plain:""},on:{click:t.save}},[t._v("保存")])],1)},r=[],s=(n("b0c0"),n("e9c4"),n("eb18")),c={data:function(){return{}},components:{common:s["a"]},methods:{save:function(){var t=this.$refs.apiAdd.detail,e=this.$store.getters.getSql,n={name:t.name,path:t.path,note:t.note,groupId:t.groupId,previlege:t.previlege,cachePlugin:t.cachePlugin,cachePluginParams:t.cachePluginParams,transformPlugin:t.transformPlugin,transformPluginParams:t.transformPluginParams,datasourceId:this.$refs.apiAdd.$refs.sqlCode.datasourceId,sqlList:e,params:JSON.stringify(t.params)};console.log(n),""!=n.sql&&null!=n.datasourceId&&null!=n.name&&null!=n.path&&null!=n.groupId||this.$message.error("必填项未填")}},created:function(){}},o=c,i=(n("172d"),n("2877")),l=Object(i["a"])(o,a,r,!1,null,"2c37c7ad",null);e["default"]=l.exports},bcc4:function(t,e,n){}}]);