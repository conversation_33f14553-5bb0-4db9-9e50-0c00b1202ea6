(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8e0d8a8e"],{"26ee":function(t,e,n){"use strict";var r=n("5ea3"),i="object"==typeof self&&self&&self.Object===Object&&self,s=r["a"]||i||Function("return this")();e["a"]=s},"2f74":function(t,e,n){"use strict";function r(){return!1}e["a"]=r},5728:function(t,e,n){"use strict";n.d(e,"d",(function(){return HC})),n.d(e,"p",(function(){return JC})),n.d(e,"n",(function(){return tA})),n.d(e,"e",(function(){return KC})),n.d(e,"w",(function(){return mx})),n.d(e,"m",(function(){return rO})),n.d(e,"f",(function(){return Ow})),n.d(e,"i",(function(){return E})),n.d(e,"c",(function(){return Oh})),n.d(e,"b",(function(){return s})),n.d(e,"r",(function(){return i})),n.d(e,"q",(function(){return a})),n.d(e,"k",(function(){return r})),n.d(e,"h",(function(){return Sm})),n.d(e,"j",(function(){return c})),n.d(e,"v",(function(){return ty})),n.d(e,"o",(function(){return db})),n.d(e,"g",(function(){return h})),n.d(e,"a",(function(){return xb["Angle"]})),n.d(e,"s",(function(){return xb["Point"]})),n.d(e,"t",(function(){return xb["Rectangle"]})),n.d(e,"l",(function(){return xb["GeometryUtil"]})),n.d(e,"u",(function(){return O}));var r={};n.r(r),n.d(r,"debounce",(function(){return bt})),n.d(r,"throttle",(function(){return xt})),n.d(r,"apply",(function(){return Pt})),n.d(r,"call",(function(){return Ct})),n.d(r,"isAsyncLike",(function(){return At})),n.d(r,"isAsync",(function(){return Ot})),n.d(r,"toAsyncBoolean",(function(){return Et})),n.d(r,"toDeferredBoolean",(function(){return St}));var i={};n.r(i),n.d(i,"has",(function(){return sr})),n.d(i,"pick",(function(){return ni})),n.d(i,"merge",(function(){return go})),n.d(i,"isEqual",(function(){return tl})),n.d(i,"isEmpty",(function(){return ol})),n.d(i,"isObject",(function(){return T})),n.d(i,"isPlainObject",(function(){return es})),n.d(i,"clone",(function(){return Jc})),n.d(i,"cloneDeep",(function(){return Kc})),n.d(i,"defaults",(function(){return nh})),n.d(i,"defaultsDeep",(function(){return lh})),n.d(i,"applyMixins",(function(){return ch})),n.d(i,"inherit",(function(){return uh})),n.d(i,"createClass",(function(){return fh})),n.d(i,"ensure",(function(){return ph})),n.d(i,"getValue",(function(){return mh})),n.d(i,"getNumber",(function(){return yh})),n.d(i,"getBoolean",(function(){return bh})),n.d(i,"isMaliciousProp",(function(){return vh})),n.d(i,"getByPath",(function(){return wh})),n.d(i,"setByPath",(function(){return xh})),n.d(i,"unsetByPath",(function(){return Ph})),n.d(i,"flatten",(function(){return Ch}));var s={};n.r(s),n.d(s,"uniq",(function(){return Wh})),n.d(s,"union",(function(){return Xh})),n.d(s,"sortedIndex",(function(){return au})),n.d(s,"sortedIndexBy",(function(){return Bu})),n.d(s,"sortBy",(function(){return Qu})),n.d(s,"groupBy",(function(){return cd})),n.d(s,"difference",(function(){return fd})),n.d(s,"max",(function(){return wd}));var o={};n.r(o),n.d(o,"uniqueId",(function(){return Cd})),n.d(o,"lowerFirst",(function(){return Gf})),n.d(o,"upperFirst",(function(){return jf})),n.d(o,"camelCase",(function(){return Df})),n.d(o,"kebabCase",(function(){return _f})),n.d(o,"pascalCase",(function(){return Hf})),n.d(o,"constantCase",(function(){return Uf})),n.d(o,"dotCase",(function(){return Wf})),n.d(o,"pathCase",(function(){return Jf})),n.d(o,"sentenceCase",(function(){return Xf})),n.d(o,"titleCase",(function(){return Yf})),n.d(o,"hashcode",(function(){return Zf})),n.d(o,"uuid",(function(){return Kf})),n.d(o,"getSpellingSuggestion",(function(){return Qf}));var a={};n.r(a),n.d(a,"isNumber",(function(){return rp})),n.d(a,"clamp",(function(){return ap})),n.d(a,"mod",(function(){return lp})),n.d(a,"random",(function(){return cp})),n.d(a,"isPercentage",(function(){return hp})),n.d(a,"normalizePercentage",(function(){return up})),n.d(a,"parseCssNumeric",(function(){return dp})),n.d(a,"normalizeSides",(function(){return gp}));var l={};n.r(l),n.d(l,"annotate",(function(){return Pm})),n.d(l,"findAnnotationsAtIndex",(function(){return Cm})),n.d(l,"findAnnotationsBetweenIndexes",(function(){return Am})),n.d(l,"shiftAnnotations",(function(){return Om})),n.d(l,"sanitize",(function(){return Em}));var c={};n.r(c),n.d(c,"CASE_SENSITIVE_ATTR",(function(){return dm})),n.d(c,"getAttribute",(function(){return gm})),n.d(c,"removeAttribute",(function(){return fm})),n.d(c,"setAttribute",(function(){return pm})),n.d(c,"setAttributes",(function(){return mm})),n.d(c,"attr",(function(){return ym})),n.d(c,"qualifyAttr",(function(){return bm})),n.d(c,"kebablizeAttrs",(function(){return vm})),n.d(c,"styleToObject",(function(){return wm})),n.d(c,"mergeAttrs",(function(){return xm})),n.d(c,"uniqueId",(function(){return Fp})),n.d(c,"ensureId",(function(){return $p})),n.d(c,"isSVGGraphicsElement",(function(){return Gp})),n.d(c,"ns",(function(){return qp})),n.d(c,"svgVersion",(function(){return _p})),n.d(c,"createElement",(function(){return Hp})),n.d(c,"createElementNS",(function(){return Up})),n.d(c,"createSvgElement",(function(){return Wp})),n.d(c,"createSvgDocument",(function(){return Jp})),n.d(c,"parseXML",(function(){return Xp})),n.d(c,"tagName",(function(){return Yp})),n.d(c,"index",(function(){return Zp})),n.d(c,"find",(function(){return Kp})),n.d(c,"findOne",(function(){return Qp})),n.d(c,"findParentByClass",(function(){return tm})),n.d(c,"contains",(function(){return em})),n.d(c,"remove",(function(){return nm})),n.d(c,"empty",(function(){return rm})),n.d(c,"append",(function(){return im})),n.d(c,"prepend",(function(){return sm})),n.d(c,"before",(function(){return om})),n.d(c,"after",(function(){return am})),n.d(c,"appendTo",(function(){return lm})),n.d(c,"isElement",(function(){return cm})),n.d(c,"isHTMLElement",(function(){return hm})),n.d(c,"children",(function(){return um})),n.d(c,"getClass",(function(){return Rp})),n.d(c,"hasClass",(function(){return Bp})),n.d(c,"addClass",(function(){return Dp})),n.d(c,"removeClass",(function(){return Ip})),n.d(c,"toggleClass",(function(){return Vp})),n.d(c,"setPrefixedStyle",(function(){return Vm})),n.d(c,"getComputedStyle",(function(){return zm})),n.d(c,"hasScrollbars",(function(){return Fm})),n.d(c,"getVendorPrefixedName",(function(){return Im})),n.d(c,"clearSelection",(function(){return $m})),n.d(c,"isCSSVariable",(function(){return qm})),n.d(c,"computeStyle",(function(){return _m})),n.d(c,"computeStyleInt",(function(){return Hm})),n.d(c,"css",(function(){return Wm})),n.d(c,"getData",(function(){return Xm})),n.d(c,"setData",(function(){return Ym})),n.d(c,"data",(function(){return Zm})),n.d(c,"prop",(function(){return Qm})),n.d(c,"text",(function(){return oy})),n.d(c,"measureText",(function(){return ay})),n.d(c,"splitTextByLength",(function(){return ly})),n.d(c,"breakText",(function(){return cy})),n.d(c,"KAPPA",(function(){return hy})),n.d(c,"sample",(function(){return dy})),n.d(c,"lineToPathData",(function(){return gy})),n.d(c,"polygonToPathData",(function(){return fy})),n.d(c,"polylineToPathData",(function(){return py})),n.d(c,"getPointsFromSvgElement",(function(){return yy})),n.d(c,"circleToPathData",(function(){return by})),n.d(c,"ellipseToPathData",(function(){return vy})),n.d(c,"rectangleToPathData",(function(){return wy})),n.d(c,"rectToPathData",(function(){return xy})),n.d(c,"toPath",(function(){return Py})),n.d(c,"toPathData",(function(){return Cy})),n.d(c,"createSlicePathData",(function(){return Ay})),n.d(c,"createSVGPoint",(function(){return My})),n.d(c,"createSVGMatrix",(function(){return Ty})),n.d(c,"createSVGTransform",(function(){return jy})),n.d(c,"transformStringToMatrix",(function(){return ky})),n.d(c,"matrixToTransformString",(function(){return Ny})),n.d(c,"parseTransformString",(function(){return Ly})),n.d(c,"decomposeMatrix",(function(){return By})),n.d(c,"matrixToScale",(function(){return Dy})),n.d(c,"matrixToRotation",(function(){return Iy})),n.d(c,"matrixToTranslation",(function(){return Vy})),n.d(c,"transform",(function(){return zy})),n.d(c,"translate",(function(){return Fy})),n.d(c,"rotate",(function(){return $y})),n.d(c,"scale",(function(){return Gy})),n.d(c,"getTransformToElement",(function(){return qy})),n.d(c,"getTransformToParentElement",(function(){return _y})),n.d(c,"toLocalPoint",(function(){return Hy})),n.d(c,"EventHook",(function(){return Uy})),n.d(c,"Event",(function(){return Ky})),n.d(c,"EventObject",(function(){return Yy})),n.d(c,"MouseWheelHandle",(function(){return eb})),n.d(c,"offset",(function(){return nb})),n.d(c,"width",(function(){return rb})),n.d(c,"height",(function(){return ib})),n.d(c,"position",(function(){return sb}));var h={};n.r(h),n.d(h,"ensure",(function(){return vb})),n.d(h,"clean",(function(){return wb}));var u={};n.r(u),n.d(u,"dot",(function(){return Cb})),n.d(u,"fixedDot",(function(){return Ab})),n.d(u,"mesh",(function(){return Ob})),n.d(u,"doubleMesh",(function(){return Eb}));var d={};n.r(d),n.d(d,"flipX",(function(){return Mb})),n.d(d,"flipY",(function(){return Tb})),n.d(d,"flipXY",(function(){return jb})),n.d(d,"watermark",(function(){return kb}));var g={};n.r(g),n.d(g,"outline",(function(){return Db})),n.d(g,"highlight",(function(){return Ib})),n.d(g,"blur",(function(){return Vb})),n.d(g,"dropShadow",(function(){return zb})),n.d(g,"grayScale",(function(){return Fb})),n.d(g,"sepia",(function(){return $b})),n.d(g,"saturate",(function(){return Gb})),n.d(g,"hueRotate",(function(){return qb})),n.d(g,"invert",(function(){return _b})),n.d(g,"brightness",(function(){return Hb})),n.d(g,"contrast",(function(){return Ub}));var f={};n.r(f),n.d(f,"block",(function(){return Hv})),n.d(f,"classic",(function(){return Uv})),n.d(f,"diamond",(function(){return Xv})),n.d(f,"path",(function(){return Zv})),n.d(f,"cross",(function(){return Qv})),n.d(f,"async",(function(){return ew})),n.d(f,"circle",(function(){return rw})),n.d(f,"circlePlus",(function(){return iw})),n.d(f,"ellipse",(function(){return ow}));var p={};n.r(p),n.d(p,"ref",(function(){return Jb})),n.d(p,"refX",(function(){return Xb})),n.d(p,"refY",(function(){return Yb})),n.d(p,"refDx",(function(){return Zb})),n.d(p,"refDy",(function(){return Kb})),n.d(p,"refWidth",(function(){return Qb})),n.d(p,"refHeight",(function(){return tv})),n.d(p,"refRx",(function(){return ev})),n.d(p,"refRy",(function(){return nv})),n.d(p,"refRInscribed",(function(){return rv})),n.d(p,"refRCircumscribed",(function(){return iv})),n.d(p,"refCx",(function(){return sv})),n.d(p,"refCy",(function(){return ov})),n.d(p,"refDResetOffset",(function(){return av})),n.d(p,"refDKeepOffset",(function(){return lv})),n.d(p,"refPointsResetOffset",(function(){return cv})),n.d(p,"refPointsKeepOffset",(function(){return hv})),n.d(p,"refR",(function(){return uv})),n.d(p,"refD",(function(){return dv})),n.d(p,"refPoints",(function(){return gv})),n.d(p,"refX2",(function(){return fv})),n.d(p,"refY2",(function(){return pv})),n.d(p,"refWidth2",(function(){return mv})),n.d(p,"refHeight2",(function(){return yv})),n.d(p,"fill",(function(){return Cv})),n.d(p,"stroke",(function(){return Av})),n.d(p,"text",(function(){return Ov})),n.d(p,"textWrap",(function(){return Ev})),n.d(p,"lineHeight",(function(){return Mv})),n.d(p,"textVerticalAnchor",(function(){return Tv})),n.d(p,"textPath",(function(){return jv})),n.d(p,"annotations",(function(){return kv})),n.d(p,"eol",(function(){return Nv})),n.d(p,"displayEmpty",(function(){return Lv})),n.d(p,"title",(function(){return Rv})),n.d(p,"xAlign",(function(){return Bv})),n.d(p,"yAlign",(function(){return Dv})),n.d(p,"resetOffset",(function(){return Iv})),n.d(p,"style",(function(){return zv})),n.d(p,"html",(function(){return Fv})),n.d(p,"filter",(function(){return $v})),n.d(p,"port",(function(){return Gv})),n.d(p,"sourceMarker",(function(){return hw})),n.d(p,"targetMarker",(function(){return uw})),n.d(p,"vertexMarker",(function(){return dw})),n.d(p,"connection",(function(){return mw})),n.d(p,"atConnectionLengthKeepGradient",(function(){return yw})),n.d(p,"atConnectionLengthIgnoreGradient",(function(){return bw})),n.d(p,"atConnectionRatioKeepGradient",(function(){return vw})),n.d(p,"atConnectionRatioIgnoreGradient",(function(){return ww})),n.d(p,"atConnectionLength",(function(){return xw})),n.d(p,"atConnectionRatio",(function(){return Pw}));var m={};n.r(m),n.d(m,"className",(function(){return Sw})),n.d(m,"opacity",(function(){return Tw})),n.d(m,"stroke",(function(){return Nw}));var y={};n.r(y),n.d(y,"absolute",(function(){return Iw})),n.d(y,"ellipse",(function(){return Vw})),n.d(y,"ellipseSpread",(function(){return zw})),n.d(y,"line",(function(){return Gw})),n.d(y,"left",(function(){return qw})),n.d(y,"right",(function(){return _w})),n.d(y,"top",(function(){return Hw})),n.d(y,"bottom",(function(){return Uw}));var b={};n.r(b),n.d(b,"manual",(function(){return Zw})),n.d(b,"left",(function(){return Kw})),n.d(b,"right",(function(){return Qw})),n.d(b,"top",(function(){return tx})),n.d(b,"bottom",(function(){return ex})),n.d(b,"outside",(function(){return nx})),n.d(b,"outsideOriented",(function(){return rx})),n.d(b,"inside",(function(){return ix})),n.d(b,"insideOriented",(function(){return sx})),n.d(b,"radial",(function(){return cx})),n.d(b,"radialOriented",(function(){return hx}));var v={};n.r(v),n.d(v,"noop",(function(){return Ax})),n.d(v,"pinRelative",(function(){return Tx})),n.d(v,"pinAbsolute",(function(){return jx}));var w={};n.r(w),n.d(w,"center",(function(){return Yx})),n.d(w,"top",(function(){return Zx})),n.d(w,"bottom",(function(){return Kx})),n.d(w,"left",(function(){return Qx})),n.d(w,"right",(function(){return tP})),n.d(w,"topLeft",(function(){return eP})),n.d(w,"topRight",(function(){return nP})),n.d(w,"bottomLeft",(function(){return rP})),n.d(w,"bottomRight",(function(){return iP})),n.d(w,"orth",(function(){return cP})),n.d(w,"nodeCenter",(function(){return hP})),n.d(w,"midSide",(function(){return dP}));var x={};n.r(x),n.d(x,"ratio",(function(){return fP})),n.d(x,"length",(function(){return pP})),n.d(x,"orth",(function(){return vP})),n.d(x,"closest",(function(){return yP}));var P={};n.r(P),n.d(P,"bbox",(function(){return AP})),n.d(P,"rect",(function(){return OP})),n.d(P,"boundary",(function(){return EP})),n.d(P,"anchor",(function(){return MP}));var C={};n.r(C),n.d(C,"normal",(function(){return jP})),n.d(C,"oneSide",(function(){return kP})),n.d(C,"orth",(function(){return VP})),n.d(C,"metro",(function(){return mC})),n.d(C,"manhattan",(function(){return fC})),n.d(C,"er",(function(){return yC})),n.d(C,"loop",(function(){return vC}));var A={};n.r(A),n.d(A,"normal",(function(){return xC})),n.d(A,"loop",(function(){return PC})),n.d(A,"rounded",(function(){return CC})),n.d(A,"smooth",(function(){return AC})),n.d(A,"jumpover",(function(){return IC}));var O={};n.r(O),n.d(O,"Rect",(function(){return aA})),n.d(O,"Edge",(function(){return lA})),n.d(O,"Ellipse",(function(){return cA})),n.d(O,"Polygon",(function(){return dA})),n.d(O,"Polyline",(function(){return gA})),n.d(O,"Path",(function(){return pA})),n.d(O,"TextBlock",(function(){return yA})),n.d(O,"Image",(function(){return bA})),n.d(O,"Circle",(function(){return vA})),n.d(O,"HTML",(function(){return oO}));n("6362");class E{get disposed(){return!0===this._disposed}dispose(){this._disposed=!0}}(function(t){function e(){return(t,e,n)=>{const r=n.value,i=t.__proto__;n.value=function(...t){this.disposed||(r.call(this,...t),i.dispose.call(this))}}}t.dispose=e})(E||(E={}));class S{constructor(){this.isDisposed=!1,this.items=new Set}get disposed(){return this.isDisposed}dispose(){this.isDisposed||(this.isDisposed=!0,this.items.forEach(t=>{t.dispose()}),this.items.clear())}contains(t){return this.items.has(t)}add(t){this.items.add(t)}remove(t){this.items.delete(t)}clear(){this.items.clear()}}function M(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}(function(t){function e(e){const n=new t;return e.forEach(t=>{n.add(t)}),n}t.from=e})(S||(S={}));var T=M,j=n("26ee"),k=function(){return j["a"].Date.now()},N=k,L=/\s/;function R(t){var e=t.length;while(e--&&L.test(t.charAt(e)));return e}var B=R,D=/^\s+/;function I(t){return t?t.slice(0,B(t)+1).replace(D,""):t}var V=I,z=j["a"].Symbol,F=z,$=Object.prototype,G=$.hasOwnProperty,q=$.toString,_=F?F.toStringTag:void 0;function H(t){var e=G.call(t,_),n=t[_];try{t[_]=void 0;var r=!0}catch(s){}var i=q.call(t);return r&&(e?t[_]=n:delete t[_]),i}var U=H,W=Object.prototype,J=W.toString;function X(t){return J.call(t)}var Y=X,Z="[object Null]",K="[object Undefined]",Q=F?F.toStringTag:void 0;function tt(t){return null==t?void 0===t?K:Z:Q&&Q in Object(t)?U(t):Y(t)}var et=tt;function nt(t){return null!=t&&"object"==typeof t}var rt=nt,it="[object Symbol]";function st(t){return"symbol"==typeof t||rt(t)&&et(t)==it}var ot=st,at=NaN,lt=/^[-+]0x[0-9a-f]+$/i,ct=/^0b[01]+$/i,ht=/^0o[0-7]+$/i,ut=parseInt;function dt(t){if("number"==typeof t)return t;if(ot(t))return at;if(T(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=T(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=V(t);var n=ct.test(t);return n||ht.test(t)?ut(t.slice(2),n?2:8):lt.test(t)?at:+t}var gt=dt,ft="Expected a function",pt=Math.max,mt=Math.min;function yt(t,e,n){var r,i,s,o,a,l,c=0,h=!1,u=!1,d=!0;if("function"!=typeof t)throw new TypeError(ft);function g(e){var n=r,s=i;return r=i=void 0,c=e,o=t.apply(s,n),o}function f(t){return c=t,a=setTimeout(y,e),h?g(t):o}function p(t){var n=t-l,r=t-c,i=e-n;return u?mt(i,s-r):i}function m(t){var n=t-l,r=t-c;return void 0===l||n>=e||n<0||u&&r>=s}function y(){var t=N();if(m(t))return b(t);a=setTimeout(y,p(t))}function b(t){return a=void 0,d&&r?g(t):(r=i=void 0,o)}function v(){void 0!==a&&clearTimeout(a),c=0,r=l=i=a=void 0}function w(){return void 0===a?o:b(N())}function x(){var t=N(),n=m(t);if(r=arguments,i=this,l=t,n){if(void 0===a)return f(l);if(u)return clearTimeout(a),a=setTimeout(y,e),g(l)}return void 0===a&&(a=setTimeout(y,e)),o}return e=gt(e)||0,T(n)&&(h=!!n.leading,u="maxWait"in n,s=u?pt(gt(n.maxWait)||0,e):s,d="trailing"in n?!!n.trailing:d),x.cancel=v,x.flush=w,x}var bt=yt,vt="Expected a function";function wt(t,e,n){var r=!0,i=!0;if("function"!=typeof t)throw new TypeError(vt);return T(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),bt(t,e,{leading:r,maxWait:e,trailing:i})}var xt=wt;function Pt(t,e,n){if(n)switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2]);case 4:return t.call(e,n[0],n[1],n[2],n[3]);case 5:return t.call(e,n[0],n[1],n[2],n[3],n[4]);case 6:return t.call(e,n[0],n[1],n[2],n[3],n[4],n[5]);default:return t.apply(e,n)}return t.call(e)}function Ct(t,e,...n){return Pt(t,e,n)}function At(t){return"object"===typeof t&&t.then&&"function"===typeof t.then}function Ot(t){return null!=t&&(t instanceof Promise||At(t))}function Et(...t){const e=[];t.forEach(t=>{Array.isArray(t)?e.push(...t):e.push(t)});const n=e.some(t=>Ot(t));if(n){const t=e.map(t=>Ot(t)?t:Promise.resolve(!1!==t));return Promise.all(t).then(t=>t.reduce((t,e)=>!1!==e&&t,!0))}return e.every(t=>!1!==t)}function St(...t){const e=Et(t);return"boolean"===typeof e?Promise.resolve(e):e}function Mt(t,e){const n=[];for(let i=0;i<t.length;i+=2){const s=t[i],o=t[i+1],a=Array.isArray(e)?e:[e],l=r.apply(s,o,a);n.push(l)}return r.toAsyncBoolean(n)}class Tt{constructor(){this.listeners={}}on(t,e,n){if(null==e)return this;this.listeners[t]||(this.listeners[t]=[]);const r=this.listeners[t];return r.push(e,n),this}once(t,e,n){const r=(...i)=>(this.off(t,r),Mt([e,n],i));return this.on(t,r,this)}off(t,e,n){if(!(t||e||n))return this.listeners={},this;const r=this.listeners,i=t?[t]:Object.keys(r);return i.forEach(t=>{const i=r[t];if(i)if(e||n)for(let r=i.length-2;r>=0;r-=2)e&&i[r]!==e||n&&i[r+1]!==n||i.splice(r,2);else delete r[t]}),this}trigger(t,...e){let n=!0;if("*"!==t){const r=this.listeners[t];null!=r&&(n=Mt([...r],e))}const i=this.listeners["*"];return null!=i?r.toAsyncBoolean([n,Mt([...i],[t,...e])]):n}emit(t,...e){return this.trigger(t,...e)}}var jt=Object.prototype,kt=jt.hasOwnProperty;function Nt(t,e){return null!=t&&kt.call(t,e)}var Lt=Nt,Rt=Array.isArray,Bt=Rt,Dt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,It=/^\w*$/;function Vt(t,e){if(Bt(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!ot(t))||(It.test(t)||!Dt.test(t)||null!=e&&t in Object(e))}var zt=Vt,Ft="[object AsyncFunction]",$t="[object Function]",Gt="[object GeneratorFunction]",qt="[object Proxy]";function _t(t){if(!T(t))return!1;var e=et(t);return e==$t||e==Gt||e==Ft||e==qt}var Ht=_t,Ut=j["a"]["__core-js_shared__"],Wt=Ut,Jt=function(){var t=/[^.]+$/.exec(Wt&&Wt.keys&&Wt.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function Xt(t){return!!Jt&&Jt in t}var Yt=Xt,Zt=Function.prototype,Kt=Zt.toString;function Qt(t){if(null!=t){try{return Kt.call(t)}catch(e){}try{return t+""}catch(e){}}return""}var te=Qt,ee=/[\\^$.*+?()[\]{}|]/g,ne=/^\[object .+?Constructor\]$/,re=Function.prototype,ie=Object.prototype,se=re.toString,oe=ie.hasOwnProperty,ae=RegExp("^"+se.call(oe).replace(ee,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function le(t){if(!T(t)||Yt(t))return!1;var e=Ht(t)?ae:ne;return e.test(te(t))}var ce=le;function he(t,e){return null==t?void 0:t[e]}var ue=he;function de(t,e){var n=ue(t,e);return ce(n)?n:void 0}var ge=de,fe=ge(Object,"create"),pe=fe;function me(){this.__data__=pe?pe(null):{},this.size=0}var ye=me;function be(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var ve=be,we="__lodash_hash_undefined__",xe=Object.prototype,Pe=xe.hasOwnProperty;function Ce(t){var e=this.__data__;if(pe){var n=e[t];return n===we?void 0:n}return Pe.call(e,t)?e[t]:void 0}var Ae=Ce,Oe=Object.prototype,Ee=Oe.hasOwnProperty;function Se(t){var e=this.__data__;return pe?void 0!==e[t]:Ee.call(e,t)}var Me=Se,Te="__lodash_hash_undefined__";function je(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=pe&&void 0===e?Te:e,this}var ke=je;function Ne(t){var e=-1,n=null==t?0:t.length;this.clear();while(++e<n){var r=t[e];this.set(r[0],r[1])}}Ne.prototype.clear=ye,Ne.prototype["delete"]=ve,Ne.prototype.get=Ae,Ne.prototype.has=Me,Ne.prototype.set=ke;var Le=Ne;function Re(){this.__data__=[],this.size=0}var Be=Re;function De(t,e){return t===e||t!==t&&e!==e}var Ie=De;function Ve(t,e){var n=t.length;while(n--)if(Ie(t[n][0],e))return n;return-1}var ze=Ve,Fe=Array.prototype,$e=Fe.splice;function Ge(t){var e=this.__data__,n=ze(e,t);if(n<0)return!1;var r=e.length-1;return n==r?e.pop():$e.call(e,n,1),--this.size,!0}var qe=Ge;function _e(t){var e=this.__data__,n=ze(e,t);return n<0?void 0:e[n][1]}var He=_e;function Ue(t){return ze(this.__data__,t)>-1}var We=Ue;function Je(t,e){var n=this.__data__,r=ze(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this}var Xe=Je;function Ye(t){var e=-1,n=null==t?0:t.length;this.clear();while(++e<n){var r=t[e];this.set(r[0],r[1])}}Ye.prototype.clear=Be,Ye.prototype["delete"]=qe,Ye.prototype.get=He,Ye.prototype.has=We,Ye.prototype.set=Xe;var Ze=Ye,Ke=ge(j["a"],"Map"),Qe=Ke;function tn(){this.size=0,this.__data__={hash:new Le,map:new(Qe||Ze),string:new Le}}var en=tn;function nn(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}var rn=nn;function sn(t,e){var n=t.__data__;return rn(e)?n["string"==typeof e?"string":"hash"]:n.map}var on=sn;function an(t){var e=on(this,t)["delete"](t);return this.size-=e?1:0,e}var ln=an;function cn(t){return on(this,t).get(t)}var hn=cn;function un(t){return on(this,t).has(t)}var dn=un;function gn(t,e){var n=on(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this}var fn=gn;function pn(t){var e=-1,n=null==t?0:t.length;this.clear();while(++e<n){var r=t[e];this.set(r[0],r[1])}}pn.prototype.clear=en,pn.prototype["delete"]=ln,pn.prototype.get=hn,pn.prototype.has=dn,pn.prototype.set=fn;var mn=pn,yn="Expected a function";function bn(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError(yn);var n=function(){var r=arguments,i=e?e.apply(this,r):r[0],s=n.cache;if(s.has(i))return s.get(i);var o=t.apply(this,r);return n.cache=s.set(i,o)||s,o};return n.cache=new(bn.Cache||mn),n}bn.Cache=mn;var vn=bn,wn=500;function xn(t){var e=vn(t,(function(t){return n.size===wn&&n.clear(),t})),n=e.cache;return e}var Pn=xn,Cn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,An=/\\(\\)?/g,On=Pn((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(Cn,(function(t,n,r,i){e.push(r?i.replace(An,"$1"):n||t)})),e})),En=On;function Sn(t,e){var n=-1,r=null==t?0:t.length,i=Array(r);while(++n<r)i[n]=e(t[n],n,t);return i}var Mn=Sn,Tn=1/0,jn=F?F.prototype:void 0,kn=jn?jn.toString:void 0;function Nn(t){if("string"==typeof t)return t;if(Bt(t))return Mn(t,Nn)+"";if(ot(t))return kn?kn.call(t):"";var e=t+"";return"0"==e&&1/t==-Tn?"-0":e}var Ln=Nn;function Rn(t){return null==t?"":Ln(t)}var Bn=Rn;function Dn(t,e){return Bt(t)?t:zt(t,e)?[t]:En(Bn(t))}var In=Dn,Vn="[object Arguments]";function zn(t){return rt(t)&&et(t)==Vn}var Fn=zn,$n=Object.prototype,Gn=$n.hasOwnProperty,qn=$n.propertyIsEnumerable,_n=Fn(function(){return arguments}())?Fn:function(t){return rt(t)&&Gn.call(t,"callee")&&!qn.call(t,"callee")},Hn=_n,Un=9007199254740991,Wn=/^(?:0|[1-9]\d*)$/;function Jn(t,e){var n=typeof t;return e=null==e?Un:e,!!e&&("number"==n||"symbol"!=n&&Wn.test(t))&&t>-1&&t%1==0&&t<e}var Xn=Jn,Yn=9007199254740991;function Zn(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=Yn}var Kn=Zn,Qn=1/0;function tr(t){if("string"==typeof t||ot(t))return t;var e=t+"";return"0"==e&&1/t==-Qn?"-0":e}var er=tr;function nr(t,e,n){e=In(e,t);var r=-1,i=e.length,s=!1;while(++r<i){var o=er(e[r]);if(!(s=null!=t&&n(t,o)))break;t=t[o]}return s||++r!=i?s:(i=null==t?0:t.length,!!i&&Kn(i)&&Xn(o,i)&&(Bt(t)||Hn(t)))}var rr=nr;function ir(t,e){return null!=t&&rr(t,e,Lt)}var sr=ir;function or(t,e){e=In(e,t);var n=0,r=e.length;while(null!=t&&n<r)t=t[er(e[n++])];return n&&n==r?t:void 0}var ar=or,lr=function(){try{var t=ge(Object,"defineProperty");return t({},"",{}),t}catch(e){}}(),cr=lr;function hr(t,e,n){"__proto__"==e&&cr?cr(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}var ur=hr,dr=Object.prototype,gr=dr.hasOwnProperty;function fr(t,e,n){var r=t[e];gr.call(t,e)&&Ie(r,n)&&(void 0!==n||e in t)||ur(t,e,n)}var pr=fr;function mr(t,e,n,r){if(!T(t))return t;e=In(e,t);var i=-1,s=e.length,o=s-1,a=t;while(null!=a&&++i<s){var l=er(e[i]),c=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return t;if(i!=o){var h=a[l];c=r?r(h,l,a):void 0,void 0===c&&(c=T(h)?h:Xn(e[i+1])?[]:{})}pr(a,l,c),a=a[l]}return t}var yr=mr;function br(t,e,n){var r=-1,i=e.length,s={};while(++r<i){var o=e[r],a=ar(t,o);n(a,o)&&yr(s,In(o,t),a)}return s}var vr=br;function wr(t,e){return null!=t&&e in Object(t)}var xr=wr;function Pr(t,e){return null!=t&&rr(t,e,xr)}var Cr=Pr;function Ar(t,e){return vr(t,e,(function(e,n){return Cr(t,n)}))}var Or=Ar;function Er(t,e){var n=-1,r=e.length,i=t.length;while(++n<r)t[i+n]=e[n];return t}var Sr=Er,Mr=F?F.isConcatSpreadable:void 0;function Tr(t){return Bt(t)||Hn(t)||!!(Mr&&t&&t[Mr])}var jr=Tr;function kr(t,e,n,r,i){var s=-1,o=t.length;n||(n=jr),i||(i=[]);while(++s<o){var a=t[s];e>0&&n(a)?e>1?kr(a,e-1,n,r,i):Sr(i,a):r||(i[i.length]=a)}return i}var Nr=kr;function Lr(t){var e=null==t?0:t.length;return e?Nr(t,1):[]}var Rr=Lr;function Br(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}var Dr=Br,Ir=Math.max;function Vr(t,e,n){return e=Ir(void 0===e?t.length-1:e,0),function(){var r=arguments,i=-1,s=Ir(r.length-e,0),o=Array(s);while(++i<s)o[i]=r[e+i];i=-1;var a=Array(e+1);while(++i<e)a[i]=r[i];return a[e]=n(o),Dr(t,this,a)}}var zr=Vr;function Fr(t){return function(){return t}}var $r=Fr;function Gr(t){return t}var qr=Gr,_r=cr?function(t,e){return cr(t,"toString",{configurable:!0,enumerable:!1,value:$r(e),writable:!0})}:qr,Hr=_r,Ur=800,Wr=16,Jr=Date.now;function Xr(t){var e=0,n=0;return function(){var r=Jr(),i=Wr-(r-n);if(n=r,i>0){if(++e>=Ur)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}var Yr=Xr,Zr=Yr(Hr),Kr=Zr;function Qr(t){return Kr(zr(t,void 0,Rr),t+"")}var ti=Qr,ei=ti((function(t,e){return null==t?{}:Or(t,e)})),ni=ei;function ri(){this.__data__=new Ze,this.size=0}var ii=ri;function si(t){var e=this.__data__,n=e["delete"](t);return this.size=e.size,n}var oi=si;function ai(t){return this.__data__.get(t)}var li=ai;function ci(t){return this.__data__.has(t)}var hi=ci,ui=200;function di(t,e){var n=this.__data__;if(n instanceof Ze){var r=n.__data__;if(!Qe||r.length<ui-1)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new mn(r)}return n.set(t,e),this.size=n.size,this}var gi=di;function fi(t){var e=this.__data__=new Ze(t);this.size=e.size}fi.prototype.clear=ii,fi.prototype["delete"]=oi,fi.prototype.get=li,fi.prototype.has=hi,fi.prototype.set=gi;var pi=fi;function mi(t,e,n){(void 0!==n&&!Ie(t[e],n)||void 0===n&&!(e in t))&&ur(t,e,n)}var yi=mi;function bi(t){return function(e,n,r){var i=-1,s=Object(e),o=r(e),a=o.length;while(a--){var l=o[t?a:++i];if(!1===n(s[l],l,s))break}return e}}var vi=bi,wi=vi(),xi=wi,Pi=n("dff1"),Ci=j["a"].Uint8Array,Ai=Ci;function Oi(t){var e=new t.constructor(t.byteLength);return new Ai(e).set(new Ai(t)),e}var Ei=Oi;function Si(t,e){var n=e?Ei(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}var Mi=Si;function Ti(t,e){var n=-1,r=t.length;e||(e=Array(r));while(++n<r)e[n]=t[n];return e}var ji=Ti,ki=Object.create,Ni=function(){function t(){}return function(e){if(!T(e))return{};if(ki)return ki(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}(),Li=Ni;function Ri(t,e){return function(n){return t(e(n))}}var Bi=Ri,Di=Bi(Object.getPrototypeOf,Object),Ii=Di,Vi=Object.prototype;function zi(t){var e=t&&t.constructor,n="function"==typeof e&&e.prototype||Vi;return t===n}var Fi=zi;function $i(t){return"function"!=typeof t.constructor||Fi(t)?{}:Li(Ii(t))}var Gi=$i;function qi(t){return null!=t&&Kn(t.length)&&!Ht(t)}var _i=qi;function Hi(t){return rt(t)&&_i(t)}var Ui=Hi,Wi=n("58e0"),Ji="[object Object]",Xi=Function.prototype,Yi=Object.prototype,Zi=Xi.toString,Ki=Yi.hasOwnProperty,Qi=Zi.call(Object);function ts(t){if(!rt(t)||et(t)!=Ji)return!1;var e=Ii(t);if(null===e)return!0;var n=Ki.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&Zi.call(n)==Qi}var es=ts,ns="[object Arguments]",rs="[object Array]",is="[object Boolean]",ss="[object Date]",os="[object Error]",as="[object Function]",ls="[object Map]",cs="[object Number]",hs="[object Object]",us="[object RegExp]",ds="[object Set]",gs="[object String]",fs="[object WeakMap]",ps="[object ArrayBuffer]",ms="[object DataView]",ys="[object Float32Array]",bs="[object Float64Array]",vs="[object Int8Array]",ws="[object Int16Array]",xs="[object Int32Array]",Ps="[object Uint8Array]",Cs="[object Uint8ClampedArray]",As="[object Uint16Array]",Os="[object Uint32Array]",Es={};function Ss(t){return rt(t)&&Kn(t.length)&&!!Es[et(t)]}Es[ys]=Es[bs]=Es[vs]=Es[ws]=Es[xs]=Es[Ps]=Es[Cs]=Es[As]=Es[Os]=!0,Es[ns]=Es[rs]=Es[ps]=Es[is]=Es[ms]=Es[ss]=Es[os]=Es[as]=Es[ls]=Es[cs]=Es[hs]=Es[us]=Es[ds]=Es[gs]=Es[fs]=!1;var Ms=Ss;function Ts(t){return function(e){return t(e)}}var js=Ts,ks=n("c6eb"),Ns=ks["a"]&&ks["a"].isTypedArray,Ls=Ns?js(Ns):Ms,Rs=Ls;function Bs(t,e){if(("constructor"!==e||"function"!==typeof t[e])&&"__proto__"!=e)return t[e]}var Ds=Bs;function Is(t,e,n,r){var i=!n;n||(n={});var s=-1,o=e.length;while(++s<o){var a=e[s],l=r?r(n[a],t[a],a,n,t):void 0;void 0===l&&(l=t[a]),i?ur(n,a,l):pr(n,a,l)}return n}var Vs=Is;function zs(t,e){var n=-1,r=Array(t);while(++n<t)r[n]=e(n);return r}var Fs=zs,$s=Object.prototype,Gs=$s.hasOwnProperty;function qs(t,e){var n=Bt(t),r=!n&&Hn(t),i=!n&&!r&&Object(Wi["a"])(t),s=!n&&!r&&!i&&Rs(t),o=n||r||i||s,a=o?Fs(t.length,String):[],l=a.length;for(var c in t)!e&&!Gs.call(t,c)||o&&("length"==c||i&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Xn(c,l))||a.push(c);return a}var _s=qs;function Hs(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}var Us=Hs,Ws=Object.prototype,Js=Ws.hasOwnProperty;function Xs(t){if(!T(t))return Us(t);var e=Fi(t),n=[];for(var r in t)("constructor"!=r||!e&&Js.call(t,r))&&n.push(r);return n}var Ys=Xs;function Zs(t){return _i(t)?_s(t,!0):Ys(t)}var Ks=Zs;function Qs(t){return Vs(t,Ks(t))}var to=Qs;function eo(t,e,n,r,i,s,o){var a=Ds(t,n),l=Ds(e,n),c=o.get(l);if(c)yi(t,n,c);else{var h=s?s(a,l,n+"",t,e,o):void 0,u=void 0===h;if(u){var d=Bt(l),g=!d&&Object(Wi["a"])(l),f=!d&&!g&&Rs(l);h=l,d||g||f?Bt(a)?h=a:Ui(a)?h=ji(a):g?(u=!1,h=Object(Pi["a"])(l,!0)):f?(u=!1,h=Mi(l,!0)):h=[]:es(l)||Hn(l)?(h=a,Hn(a)?h=to(a):T(a)&&!Ht(a)||(h=Gi(l))):u=!1}u&&(o.set(l,h),i(h,l,r,s,o),o["delete"](l)),yi(t,n,h)}}var no=eo;function ro(t,e,n,r,i){t!==e&&xi(e,(function(s,o){if(i||(i=new pi),T(s))no(t,e,o,n,ro,r,i);else{var a=r?r(Ds(t,o),s,o+"",t,e,i):void 0;void 0===a&&(a=s),yi(t,o,a)}}),Ks)}var io=ro;function so(t,e){return Kr(zr(t,e,qr),t+"")}var oo=so;function ao(t,e,n){if(!T(n))return!1;var r=typeof e;return!!("number"==r?_i(n)&&Xn(e,n.length):"string"==r&&e in n)&&Ie(n[e],t)}var lo=ao;function co(t){return oo((function(e,n){var r=-1,i=n.length,s=i>1?n[i-1]:void 0,o=i>2?n[2]:void 0;s=t.length>3&&"function"==typeof s?(i--,s):void 0,o&&lo(n[0],n[1],o)&&(s=i<3?void 0:s,i=1),e=Object(e);while(++r<i){var a=n[r];a&&t(e,a,r,s)}return e}))}var ho=co,uo=ho((function(t,e,n){io(t,e,n)})),go=uo,fo="__lodash_hash_undefined__";function po(t){return this.__data__.set(t,fo),this}var mo=po;function yo(t){return this.__data__.has(t)}var bo=yo;function vo(t){var e=-1,n=null==t?0:t.length;this.__data__=new mn;while(++e<n)this.add(t[e])}vo.prototype.add=vo.prototype.push=mo,vo.prototype.has=bo;var wo=vo;function xo(t,e){var n=-1,r=null==t?0:t.length;while(++n<r)if(e(t[n],n,t))return!0;return!1}var Po=xo;function Co(t,e){return t.has(e)}var Ao=Co,Oo=1,Eo=2;function So(t,e,n,r,i,s){var o=n&Oo,a=t.length,l=e.length;if(a!=l&&!(o&&l>a))return!1;var c=s.get(t),h=s.get(e);if(c&&h)return c==e&&h==t;var u=-1,d=!0,g=n&Eo?new wo:void 0;s.set(t,e),s.set(e,t);while(++u<a){var f=t[u],p=e[u];if(r)var m=o?r(p,f,u,e,t,s):r(f,p,u,t,e,s);if(void 0!==m){if(m)continue;d=!1;break}if(g){if(!Po(e,(function(t,e){if(!Ao(g,e)&&(f===t||i(f,t,n,r,s)))return g.push(e)}))){d=!1;break}}else if(f!==p&&!i(f,p,n,r,s)){d=!1;break}}return s["delete"](t),s["delete"](e),d}var Mo=So;function To(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}var jo=To;function ko(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var No=ko,Lo=1,Ro=2,Bo="[object Boolean]",Do="[object Date]",Io="[object Error]",Vo="[object Map]",zo="[object Number]",Fo="[object RegExp]",$o="[object Set]",Go="[object String]",qo="[object Symbol]",_o="[object ArrayBuffer]",Ho="[object DataView]",Uo=F?F.prototype:void 0,Wo=Uo?Uo.valueOf:void 0;function Jo(t,e,n,r,i,s,o){switch(n){case Ho:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case _o:return!(t.byteLength!=e.byteLength||!s(new Ai(t),new Ai(e)));case Bo:case Do:case zo:return Ie(+t,+e);case Io:return t.name==e.name&&t.message==e.message;case Fo:case Go:return t==e+"";case Vo:var a=jo;case $o:var l=r&Lo;if(a||(a=No),t.size!=e.size&&!l)return!1;var c=o.get(t);if(c)return c==e;r|=Ro,o.set(t,e);var h=Mo(a(t),a(e),r,i,s,o);return o["delete"](t),h;case qo:if(Wo)return Wo.call(t)==Wo.call(e)}return!1}var Xo=Jo;function Yo(t,e,n){var r=e(t);return Bt(t)?r:Sr(r,n(t))}var Zo=Yo;function Ko(t,e){var n=-1,r=null==t?0:t.length,i=0,s=[];while(++n<r){var o=t[n];e(o,n,t)&&(s[i++]=o)}return s}var Qo=Ko;function ta(){return[]}var ea=ta,na=Object.prototype,ra=na.propertyIsEnumerable,ia=Object.getOwnPropertySymbols,sa=ia?function(t){return null==t?[]:(t=Object(t),Qo(ia(t),(function(e){return ra.call(t,e)})))}:ea,oa=sa,aa=Bi(Object.keys,Object),la=aa,ca=Object.prototype,ha=ca.hasOwnProperty;function ua(t){if(!Fi(t))return la(t);var e=[];for(var n in Object(t))ha.call(t,n)&&"constructor"!=n&&e.push(n);return e}var da=ua;function ga(t){return _i(t)?_s(t):da(t)}var fa=ga;function pa(t){return Zo(t,fa,oa)}var ma=pa,ya=1,ba=Object.prototype,va=ba.hasOwnProperty;function wa(t,e,n,r,i,s){var o=n&ya,a=ma(t),l=a.length,c=ma(e),h=c.length;if(l!=h&&!o)return!1;var u=l;while(u--){var d=a[u];if(!(o?d in e:va.call(e,d)))return!1}var g=s.get(t),f=s.get(e);if(g&&f)return g==e&&f==t;var p=!0;s.set(t,e),s.set(e,t);var m=o;while(++u<l){d=a[u];var y=t[d],b=e[d];if(r)var v=o?r(b,y,d,e,t,s):r(y,b,d,t,e,s);if(!(void 0===v?y===b||i(y,b,n,r,s):v)){p=!1;break}m||(m="constructor"==d)}if(p&&!m){var w=t.constructor,x=e.constructor;w==x||!("constructor"in t)||!("constructor"in e)||"function"==typeof w&&w instanceof w&&"function"==typeof x&&x instanceof x||(p=!1)}return s["delete"](t),s["delete"](e),p}var xa=wa,Pa=ge(j["a"],"DataView"),Ca=Pa,Aa=ge(j["a"],"Promise"),Oa=Aa,Ea=ge(j["a"],"Set"),Sa=Ea,Ma=ge(j["a"],"WeakMap"),Ta=Ma,ja="[object Map]",ka="[object Object]",Na="[object Promise]",La="[object Set]",Ra="[object WeakMap]",Ba="[object DataView]",Da=te(Ca),Ia=te(Qe),Va=te(Oa),za=te(Sa),Fa=te(Ta),$a=et;(Ca&&$a(new Ca(new ArrayBuffer(1)))!=Ba||Qe&&$a(new Qe)!=ja||Oa&&$a(Oa.resolve())!=Na||Sa&&$a(new Sa)!=La||Ta&&$a(new Ta)!=Ra)&&($a=function(t){var e=et(t),n=e==ka?t.constructor:void 0,r=n?te(n):"";if(r)switch(r){case Da:return Ba;case Ia:return ja;case Va:return Na;case za:return La;case Fa:return Ra}return e});var Ga=$a,qa=1,_a="[object Arguments]",Ha="[object Array]",Ua="[object Object]",Wa=Object.prototype,Ja=Wa.hasOwnProperty;function Xa(t,e,n,r,i,s){var o=Bt(t),a=Bt(e),l=o?Ha:Ga(t),c=a?Ha:Ga(e);l=l==_a?Ua:l,c=c==_a?Ua:c;var h=l==Ua,u=c==Ua,d=l==c;if(d&&Object(Wi["a"])(t)){if(!Object(Wi["a"])(e))return!1;o=!0,h=!1}if(d&&!h)return s||(s=new pi),o||Rs(t)?Mo(t,e,n,r,i,s):Xo(t,e,l,n,r,i,s);if(!(n&qa)){var g=h&&Ja.call(t,"__wrapped__"),f=u&&Ja.call(e,"__wrapped__");if(g||f){var p=g?t.value():t,m=f?e.value():e;return s||(s=new pi),i(p,m,n,r,s)}}return!!d&&(s||(s=new pi),xa(t,e,n,r,i,s))}var Ya=Xa;function Za(t,e,n,r,i){return t===e||(null==t||null==e||!rt(t)&&!rt(e)?t!==t&&e!==e:Ya(t,e,n,r,Za,i))}var Ka=Za;function Qa(t,e){return Ka(t,e)}var tl=Qa,el="[object Map]",nl="[object Set]",rl=Object.prototype,il=rl.hasOwnProperty;function sl(t){if(null==t)return!0;if(_i(t)&&(Bt(t)||"string"==typeof t||"function"==typeof t.splice||Object(Wi["a"])(t)||Rs(t)||Hn(t)))return!t.length;var e=Ga(t);if(e==el||e==nl)return!t.size;if(Fi(t))return!da(t).length;for(var n in t)if(il.call(t,n))return!1;return!0}var ol=sl;function al(t,e){var n=-1,r=null==t?0:t.length;while(++n<r)if(!1===e(t[n],n,t))break;return t}var ll=al;function cl(t,e){return t&&Vs(e,fa(e),t)}var hl=cl;function ul(t,e){return t&&Vs(e,Ks(e),t)}var dl=ul;function gl(t,e){return Vs(t,oa(t),e)}var fl=gl,pl=Object.getOwnPropertySymbols,ml=pl?function(t){var e=[];while(t)Sr(e,oa(t)),t=Ii(t);return e}:ea,yl=ml;function bl(t,e){return Vs(t,yl(t),e)}var vl=bl;function wl(t){return Zo(t,Ks,yl)}var xl=wl,Pl=Object.prototype,Cl=Pl.hasOwnProperty;function Al(t){var e=t.length,n=new t.constructor(e);return e&&"string"==typeof t[0]&&Cl.call(t,"index")&&(n.index=t.index,n.input=t.input),n}var Ol=Al;function El(t,e){var n=e?Ei(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}var Sl=El,Ml=/\w*$/;function Tl(t){var e=new t.constructor(t.source,Ml.exec(t));return e.lastIndex=t.lastIndex,e}var jl=Tl,kl=F?F.prototype:void 0,Nl=kl?kl.valueOf:void 0;function Ll(t){return Nl?Object(Nl.call(t)):{}}var Rl=Ll,Bl="[object Boolean]",Dl="[object Date]",Il="[object Map]",Vl="[object Number]",zl="[object RegExp]",Fl="[object Set]",$l="[object String]",Gl="[object Symbol]",ql="[object ArrayBuffer]",_l="[object DataView]",Hl="[object Float32Array]",Ul="[object Float64Array]",Wl="[object Int8Array]",Jl="[object Int16Array]",Xl="[object Int32Array]",Yl="[object Uint8Array]",Zl="[object Uint8ClampedArray]",Kl="[object Uint16Array]",Ql="[object Uint32Array]";function tc(t,e,n){var r=t.constructor;switch(e){case ql:return Ei(t);case Bl:case Dl:return new r(+t);case _l:return Sl(t,n);case Hl:case Ul:case Wl:case Jl:case Xl:case Yl:case Zl:case Kl:case Ql:return Mi(t,n);case Il:return new r;case Vl:case $l:return new r(t);case zl:return jl(t);case Fl:return new r;case Gl:return Rl(t)}}var ec=tc,nc="[object Map]";function rc(t){return rt(t)&&Ga(t)==nc}var ic=rc,sc=ks["a"]&&ks["a"].isMap,oc=sc?js(sc):ic,ac=oc,lc="[object Set]";function cc(t){return rt(t)&&Ga(t)==lc}var hc=cc,uc=ks["a"]&&ks["a"].isSet,dc=uc?js(uc):hc,gc=dc,fc=1,pc=2,mc=4,yc="[object Arguments]",bc="[object Array]",vc="[object Boolean]",wc="[object Date]",xc="[object Error]",Pc="[object Function]",Cc="[object GeneratorFunction]",Ac="[object Map]",Oc="[object Number]",Ec="[object Object]",Sc="[object RegExp]",Mc="[object Set]",Tc="[object String]",jc="[object Symbol]",kc="[object WeakMap]",Nc="[object ArrayBuffer]",Lc="[object DataView]",Rc="[object Float32Array]",Bc="[object Float64Array]",Dc="[object Int8Array]",Ic="[object Int16Array]",Vc="[object Int32Array]",zc="[object Uint8Array]",Fc="[object Uint8ClampedArray]",$c="[object Uint16Array]",Gc="[object Uint32Array]",qc={};function _c(t,e,n,r,i,s){var o,a=e&fc,l=e&pc,c=e&mc;if(n&&(o=i?n(t,r,i,s):n(t)),void 0!==o)return o;if(!T(t))return t;var h=Bt(t);if(h){if(o=Ol(t),!a)return ji(t,o)}else{var u=Ga(t),d=u==Pc||u==Cc;if(Object(Wi["a"])(t))return Object(Pi["a"])(t,a);if(u==Ec||u==yc||d&&!i){if(o=l||d?{}:Gi(t),!a)return l?vl(t,dl(o,t)):fl(t,hl(o,t))}else{if(!qc[u])return i?t:{};o=ec(t,u,a)}}s||(s=new pi);var g=s.get(t);if(g)return g;s.set(t,o),gc(t)?t.forEach((function(r){o.add(_c(r,e,n,r,t,s))})):ac(t)&&t.forEach((function(r,i){o.set(i,_c(r,e,n,i,t,s))}));var f=c?l?xl:ma:l?Ks:fa,p=h?void 0:f(t);return ll(p||t,(function(r,i){p&&(i=r,r=t[i]),pr(o,i,_c(r,e,n,i,t,s))})),o}qc[yc]=qc[bc]=qc[Nc]=qc[Lc]=qc[vc]=qc[wc]=qc[Rc]=qc[Bc]=qc[Dc]=qc[Ic]=qc[Vc]=qc[Ac]=qc[Oc]=qc[Ec]=qc[Sc]=qc[Mc]=qc[Tc]=qc[jc]=qc[zc]=qc[Fc]=qc[$c]=qc[Gc]=!0,qc[xc]=qc[Pc]=qc[kc]=!1;var Hc=_c,Uc=4;function Wc(t){return Hc(t,Uc)}var Jc=Wc,Xc=1,Yc=4;function Zc(t){return Hc(t,Xc|Yc)}var Kc=Zc,Qc=Object.prototype,th=Qc.hasOwnProperty,eh=oo((function(t,e){t=Object(t);var n=-1,r=e.length,i=r>2?e[2]:void 0;i&&lo(e[0],e[1],i)&&(r=1);while(++n<r){var s=e[n],o=Ks(s),a=-1,l=o.length;while(++a<l){var c=o[a],h=t[c];(void 0===h||Ie(h,Qc[c])&&!th.call(t,c))&&(t[c]=s[c])}}return t})),nh=eh;function rh(t,e,n,r,i,s){return T(t)&&T(e)&&(s.set(e,t),io(t,e,void 0,rh,s),s["delete"](e)),t}var ih=rh,sh=ho((function(t,e,n,r){io(t,e,n,r)})),oh=sh,ah=oo((function(t){return t.push(void 0,ih),Dr(oh,void 0,t)})),lh=ah;function ch(t,...e){e.forEach(e=>{Object.getOwnPropertyNames(e.prototype).forEach(n=>{"constructor"!==n&&Object.defineProperty(t.prototype,n,Object.getOwnPropertyDescriptor(e.prototype,n))})})}const hh=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])};function uh(t,e){function n(){this.constructor=t}hh(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}class dh{}const gh=/^\s*class\s+/.test(""+dh)||/^\s*class\s*\{/.test(""+class{});function fh(t,e){let n;return gh?n=class extends e{}:(n=function(){return e.apply(this,arguments)},uh(n,e)),Object.defineProperty(n,"name",{value:t}),n}function ph(t,e){return null!=t?t:e}function mh(t,e,n){const r=null!=t?t[e]:null;return void 0!==n?ph(r,n):r}function yh(t,e,n){let r=null!=t?t[e]:null;return null==r?n:(r=+r,Number.isNaN(r)||!Number.isFinite(r)?n:r)}function bh(t,e,n){const r=null!=t?t[e]:null;return null==r?n:!!r}function vh(t){return"__proto__"===t}function wh(t,e,n="/"){let r;const i=Array.isArray(e)?e:e.split(n);if(i.length){r=t;while(i.length){const t=i.shift();if(Object(r)!==r||!t||!(t in r))return;r=r[t]}}return r}function xh(t,e,n,r="/"){const i=Array.isArray(e)?e:e.split(r),s=i.pop();if(s&&!vh(s)){let e=t;i.forEach(t=>{vh(t)||(null==e[t]&&(e[t]={}),e=e[t])}),e[s]=n}return t}function Ph(t,e,n="/"){const r=Array.isArray(e)?e.slice():e.split(n),i=r.pop();if(i)if(r.length>0){const e=wh(t,r);e&&delete e[i]}else delete t[i];return t}function Ch(t,e="/",n){const r={};Object.keys(t).forEach(i=>{const s=t[i];let o="object"===typeof s||Array.isArray(s);if(o&&n&&n(s)&&(o=!1),o){const t=Ch(s,e,n);Object.keys(t).forEach(n=>{r[i+e+n]=t[n]})}else r[i]=s});for(const i in t)Object.prototype.hasOwnProperty.call(t,i);return r}var Ah=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class Oh extends Tt{dispose(){this.off()}}Ah([E.dispose()],Oh.prototype,"dispose",null),function(t){t.dispose=E.dispose}(Oh||(Oh={})),i.applyMixins(Oh,E);function Eh(t,e,n,r){var i=t.length,s=n+(r?1:-1);while(r?s--:++s<i)if(e(t[s],s,t))return s;return-1}var Sh=Eh;function Mh(t){return t!==t}var Th=Mh;function jh(t,e,n){var r=n-1,i=t.length;while(++r<i)if(t[r]===e)return r;return-1}var kh=jh;function Nh(t,e,n){return e===e?kh(t,e,n):Sh(t,Th,n)}var Lh=Nh;function Rh(t,e){var n=null==t?0:t.length;return!!n&&Lh(t,e,0)>-1}var Bh=Rh;function Dh(t,e,n){var r=-1,i=null==t?0:t.length;while(++r<i)if(n(e,t[r]))return!0;return!1}var Ih=Dh;function Vh(){}var zh=Vh,Fh=1/0,$h=Sa&&1/No(new Sa([,-0]))[1]==Fh?function(t){return new Sa(t)}:zh,Gh=$h,qh=200;function _h(t,e,n){var r=-1,i=Bh,s=t.length,o=!0,a=[],l=a;if(n)o=!1,i=Ih;else if(s>=qh){var c=e?null:Gh(t);if(c)return No(c);o=!1,i=Ao,l=new wo}else l=e?[]:a;t:while(++r<s){var h=t[r],u=e?e(h):h;if(h=n||0!==h?h:0,o&&u===u){var d=l.length;while(d--)if(l[d]===u)continue t;e&&l.push(u),a.push(h)}else i(l,u,n)||(l!==a&&l.push(u),a.push(h))}return a}var Hh=_h;function Uh(t){return t&&t.length?Hh(t):[]}var Wh=Uh,Jh=oo((function(t){return Hh(Nr(t,1,Ui,!0))})),Xh=Jh,Yh=4294967295,Zh=Yh-1,Kh=Math.floor,Qh=Math.min;function tu(t,e,n,r){var i=0,s=null==t?0:t.length;if(0===s)return 0;e=n(e);var o=e!==e,a=null===e,l=ot(e),c=void 0===e;while(i<s){var h=Kh((i+s)/2),u=n(t[h]),d=void 0!==u,g=null===u,f=u===u,p=ot(u);if(o)var m=r||f;else m=c?f&&(r||d):a?f&&d&&(r||!g):l?f&&d&&!g&&(r||!p):!g&&!p&&(r?u<=e:u<e);m?i=h+1:s=h}return Qh(s,Zh)}var eu=tu,nu=4294967295,ru=nu>>>1;function iu(t,e,n){var r=0,i=null==t?r:t.length;if("number"==typeof e&&e===e&&i<=ru){while(r<i){var s=r+i>>>1,o=t[s];null!==o&&!ot(o)&&(n?o<=e:o<e)?r=s+1:i=s}return i}return eu(t,e,qr,n)}var su=iu;function ou(t,e){return su(t,e)}var au=ou,lu=1,cu=2;function hu(t,e,n,r){var i=n.length,s=i,o=!r;if(null==t)return!s;t=Object(t);while(i--){var a=n[i];if(o&&a[2]?a[1]!==t[a[0]]:!(a[0]in t))return!1}while(++i<s){a=n[i];var l=a[0],c=t[l],h=a[1];if(o&&a[2]){if(void 0===c&&!(l in t))return!1}else{var u=new pi;if(r)var d=r(c,h,l,t,e,u);if(!(void 0===d?Ka(h,c,lu|cu,r,u):d))return!1}}return!0}var uu=hu;function du(t){return t===t&&!T(t)}var gu=du;function fu(t){var e=fa(t),n=e.length;while(n--){var r=e[n],i=t[r];e[n]=[r,i,gu(i)]}return e}var pu=fu;function mu(t,e){return function(n){return null!=n&&(n[t]===e&&(void 0!==e||t in Object(n)))}}var yu=mu;function bu(t){var e=pu(t);return 1==e.length&&e[0][2]?yu(e[0][0],e[0][1]):function(n){return n===t||uu(n,t,e)}}var vu=bu;function wu(t,e,n){var r=null==t?void 0:ar(t,e);return void 0===r?n:r}var xu=wu,Pu=1,Cu=2;function Au(t,e){return zt(t)&&gu(e)?yu(er(t),e):function(n){var r=xu(n,t);return void 0===r&&r===e?Cr(n,t):Ka(e,r,Pu|Cu)}}var Ou=Au;function Eu(t){return function(e){return null==e?void 0:e[t]}}var Su=Eu;function Mu(t){return function(e){return ar(e,t)}}var Tu=Mu;function ju(t){return zt(t)?Su(er(t)):Tu(t)}var ku=ju;function Nu(t){return"function"==typeof t?t:null==t?qr:"object"==typeof t?Bt(t)?Ou(t[0],t[1]):vu(t):ku(t)}var Lu=Nu;function Ru(t,e,n){return eu(t,e,Lu(n,2))}var Bu=Ru;function Du(t,e){return t&&xi(t,e,fa)}var Iu=Du;function Vu(t,e){return function(n,r){if(null==n)return n;if(!_i(n))return t(n,r);var i=n.length,s=e?i:-1,o=Object(n);while(e?s--:++s<i)if(!1===r(o[s],s,o))break;return n}}var zu=Vu,Fu=zu(Iu),$u=Fu;function Gu(t,e){var n=-1,r=_i(t)?Array(t.length):[];return $u(t,(function(t,i,s){r[++n]=e(t,i,s)})),r}var qu=Gu;function _u(t,e){var n=t.length;t.sort(e);while(n--)t[n]=t[n].value;return t}var Hu=_u;function Uu(t,e){if(t!==e){var n=void 0!==t,r=null===t,i=t===t,s=ot(t),o=void 0!==e,a=null===e,l=e===e,c=ot(e);if(!a&&!c&&!s&&t>e||s&&o&&l&&!a&&!c||r&&o&&l||!n&&l||!i)return 1;if(!r&&!s&&!c&&t<e||c&&n&&i&&!r&&!s||a&&n&&i||!o&&i||!l)return-1}return 0}var Wu=Uu;function Ju(t,e,n){var r=-1,i=t.criteria,s=e.criteria,o=i.length,a=n.length;while(++r<o){var l=Wu(i[r],s[r]);if(l){if(r>=a)return l;var c=n[r];return l*("desc"==c?-1:1)}}return t.index-e.index}var Xu=Ju;function Yu(t,e,n){e=e.length?Mn(e,(function(t){return Bt(t)?function(e){return ar(e,1===t.length?t[0]:t)}:t})):[qr];var r=-1;e=Mn(e,js(Lu));var i=qu(t,(function(t,n,i){var s=Mn(e,(function(e){return e(t)}));return{criteria:s,index:++r,value:t}}));return Hu(i,(function(t,e){return Xu(t,e,n)}))}var Zu=Yu,Ku=oo((function(t,e){if(null==t)return[];var n=e.length;return n>1&&lo(t,e[0],e[1])?e=[]:n>2&&lo(e[0],e[1],e[2])&&(e=[e[0]]),Zu(t,Nr(e,1),[])})),Qu=Ku;function td(t,e,n,r){var i=-1,s=null==t?0:t.length;while(++i<s){var o=t[i];e(r,o,n(o),t)}return r}var ed=td;function nd(t,e,n,r){return $u(t,(function(t,i,s){e(r,t,n(t),s)})),r}var rd=nd;function id(t,e){return function(n,r){var i=Bt(n)?ed:rd,s=e?e():{};return i(n,t,Lu(r,2),s)}}var sd=id,od=Object.prototype,ad=od.hasOwnProperty,ld=sd((function(t,e,n){ad.call(t,n)?t[n].push(e):ur(t,n,[e])})),cd=ld,hd=200;function ud(t,e,n,r){var i=-1,s=Bh,o=!0,a=t.length,l=[],c=e.length;if(!a)return l;n&&(e=Mn(e,js(n))),r?(s=Ih,o=!1):e.length>=hd&&(s=Ao,o=!1,e=new wo(e));t:while(++i<a){var h=t[i],u=null==n?h:n(h);if(h=r||0!==h?h:0,o&&u===u){var d=c;while(d--)if(e[d]===u)continue t;l.push(h)}else s(e,u,r)||l.push(h)}return l}var dd=ud,gd=oo((function(t,e){return Ui(t)?dd(t,Nr(e,1,Ui,!0)):[]})),fd=gd;function pd(t,e,n){var r=-1,i=t.length;while(++r<i){var s=t[r],o=e(s);if(null!=o&&(void 0===a?o===o&&!ot(o):n(o,a)))var a=o,l=s}return l}var md=pd;function yd(t,e){return t>e}var bd=yd;function vd(t){return t&&t.length?md(t,qr,bd):void 0}var wd=vd,xd=0;function Pd(t){var e=++xd;return Bn(t)+e}var Cd=Pd;function Ad(t,e,n,r){var i=-1,s=null==t?0:t.length;r&&s&&(n=t[++i]);while(++i<s)n=e(n,t[i],i,t);return n}var Od=Ad;function Ed(t){return function(e){return null==t?void 0:t[e]}}var Sd=Ed,Md={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"},Td=Sd(Md),jd=Td,kd=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Nd="\\u0300-\\u036f",Ld="\\ufe20-\\ufe2f",Rd="\\u20d0-\\u20ff",Bd=Nd+Ld+Rd,Dd="["+Bd+"]",Id=RegExp(Dd,"g");function Vd(t){return t=Bn(t),t&&t.replace(kd,jd).replace(Id,"")}var zd=Vd,Fd=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function $d(t){return t.match(Fd)||[]}var Gd=$d,qd=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function _d(t){return qd.test(t)}var Hd=_d,Ud="\\ud800-\\udfff",Wd="\\u0300-\\u036f",Jd="\\ufe20-\\ufe2f",Xd="\\u20d0-\\u20ff",Yd=Wd+Jd+Xd,Zd="\\u2700-\\u27bf",Kd="a-z\\xdf-\\xf6\\xf8-\\xff",Qd="\\xac\\xb1\\xd7\\xf7",tg="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",eg="\\u2000-\\u206f",ng=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",rg="A-Z\\xc0-\\xd6\\xd8-\\xde",ig="\\ufe0e\\ufe0f",sg=Qd+tg+eg+ng,og="['’]",ag="["+sg+"]",lg="["+Yd+"]",cg="\\d+",hg="["+Zd+"]",ug="["+Kd+"]",dg="[^"+Ud+sg+cg+Zd+Kd+rg+"]",gg="\\ud83c[\\udffb-\\udfff]",fg="(?:"+lg+"|"+gg+")",pg="[^"+Ud+"]",mg="(?:\\ud83c[\\udde6-\\uddff]){2}",yg="[\\ud800-\\udbff][\\udc00-\\udfff]",bg="["+rg+"]",vg="\\u200d",wg="(?:"+ug+"|"+dg+")",xg="(?:"+bg+"|"+dg+")",Pg="(?:"+og+"(?:d|ll|m|re|s|t|ve))?",Cg="(?:"+og+"(?:D|LL|M|RE|S|T|VE))?",Ag=fg+"?",Og="["+ig+"]?",Eg="(?:"+vg+"(?:"+[pg,mg,yg].join("|")+")"+Og+Ag+")*",Sg="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Mg="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Tg=Og+Ag+Eg,jg="(?:"+[hg,mg,yg].join("|")+")"+Tg,kg=RegExp([bg+"?"+ug+"+"+Pg+"(?="+[ag,bg,"$"].join("|")+")",xg+"+"+Cg+"(?="+[ag,bg+wg,"$"].join("|")+")",bg+"?"+wg+"+"+Pg,bg+"+"+Cg,Mg,Sg,cg,jg].join("|"),"g");function Ng(t){return t.match(kg)||[]}var Lg=Ng;function Rg(t,e,n){return t=Bn(t),e=n?void 0:e,void 0===e?Hd(t)?Lg(t):Gd(t):t.match(e)||[]}var Bg=Rg,Dg="['’]",Ig=RegExp(Dg,"g");function Vg(t){return function(e){return Od(Bg(zd(e).replace(Ig,"")),t,"")}}var zg=Vg;function Fg(t,e,n){var r=-1,i=t.length;e<0&&(e=-e>i?0:i+e),n=n>i?i:n,n<0&&(n+=i),i=e>n?0:n-e>>>0,e>>>=0;var s=Array(i);while(++r<i)s[r]=t[r+e];return s}var $g=Fg;function Gg(t,e,n){var r=t.length;return n=void 0===n?r:n,!e&&n>=r?t:$g(t,e,n)}var qg=Gg,_g="\\ud800-\\udfff",Hg="\\u0300-\\u036f",Ug="\\ufe20-\\ufe2f",Wg="\\u20d0-\\u20ff",Jg=Hg+Ug+Wg,Xg="\\ufe0e\\ufe0f",Yg="\\u200d",Zg=RegExp("["+Yg+_g+Jg+Xg+"]");function Kg(t){return Zg.test(t)}var Qg=Kg;function tf(t){return t.split("")}var ef=tf,nf="\\ud800-\\udfff",rf="\\u0300-\\u036f",sf="\\ufe20-\\ufe2f",of="\\u20d0-\\u20ff",af=rf+sf+of,lf="\\ufe0e\\ufe0f",cf="["+nf+"]",hf="["+af+"]",uf="\\ud83c[\\udffb-\\udfff]",df="(?:"+hf+"|"+uf+")",gf="[^"+nf+"]",ff="(?:\\ud83c[\\udde6-\\uddff]){2}",pf="[\\ud800-\\udbff][\\udc00-\\udfff]",mf="\\u200d",yf=df+"?",bf="["+lf+"]?",vf="(?:"+mf+"(?:"+[gf,ff,pf].join("|")+")"+bf+yf+")*",wf=bf+yf+vf,xf="(?:"+[gf+hf+"?",hf,ff,pf,cf].join("|")+")",Pf=RegExp(uf+"(?="+uf+")|"+xf+wf,"g");function Cf(t){return t.match(Pf)||[]}var Af=Cf;function Of(t){return Qg(t)?Af(t):ef(t)}var Ef=Of;function Sf(t){return function(e){e=Bn(e);var n=Qg(e)?Ef(e):void 0,r=n?n[0]:e.charAt(0),i=n?qg(n,1).join(""):e.slice(1);return r[t]()+i}}var Mf=Sf,Tf=Mf("toUpperCase"),jf=Tf,kf=zg((function(t,e,n){return t+(n?" ":"")+jf(e)})),Nf=kf;function Lf(t){return jf(Bn(t).toLowerCase())}var Rf=Lf,Bf=zg((function(t,e,n){return e=e.toLowerCase(),t+(n?Rf(e):e)})),Df=Bf,If=zg((function(t,e,n){return t+(n?" ":"")+e.toUpperCase()})),Vf=If,zf=zg((function(t,e,n){return t+(n?" ":"")+e.toLowerCase()})),Ff=zf,$f=Mf("toLowerCase"),Gf=$f;const qf=t=>{const e=Object.create(null);return n=>{const r=e[n];return r||(e[n]=t(n))}},_f=qf(t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase()),Hf=qf(t=>Nf(Df(t)).replace(/ /g,"")),Uf=qf(t=>Vf(t).replace(/ /g,"_")),Wf=qf(t=>Ff(t).replace(/ /g,".")),Jf=qf(t=>Ff(t).replace(/ /g,"/")),Xf=qf(t=>jf(Ff(t))),Yf=qf(t=>Nf(Df(t)));function Zf(t){let e=2166136261,n=!1,r=t;for(let i=0,s=r.length;i<s;i+=1){let t=r.charCodeAt(i);t>127&&!n&&(r=unescape(encodeURIComponent(r)),t=r.charCodeAt(i),n=!0),e^=t,e+=(e<<1)+(e<<4)+(e<<7)+(e<<8)+(e<<24)}return e>>>0}function Kf(){let t="";const e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";for(let n=0,r=e.length;n<r;n+=1){const r=e[n],i=16*Math.random()|0,s="x"===r?i:"y"===r?3&i|8:r;t+=s.toString(16)}return t}function Qf(t,e,n){const r=Math.min(2,Math.floor(.34*t.length));let i,s=Math.floor(.4*t.length)+1,o=!1;const a=t.toLowerCase();for(const l of e){const e=n(l);if(void 0!==e&&Math.abs(e.length-a.length)<=r){const n=e.toLowerCase();if(n===a){if(e===t)continue;return l}if(o)continue;if(e.length<3)continue;const r=tp(a,n,s-1);if(void 0===r)continue;r<3?(o=!0,i=l):(s=r,i=l)}}return i}function tp(t,e,n){let r=new Array(e.length+1),i=new Array(e.length+1);const s=n+1;for(let a=0;a<=e.length;a+=1)r[a]=a;for(let a=1;a<=t.length;a+=1){const o=t.charCodeAt(a-1),l=a>n?a-n:1,c=e.length>n+a?n+a:e.length;i[0]=a;let h=a;for(let t=1;t<l;t+=1)i[t]=s;for(let t=l;t<=c;t+=1){const n=o===e.charCodeAt(t-1)?r[t-1]:Math.min(r[t]+1,i[t-1]+1,r[t-1]+2);i[t]=n,h=Math.min(h,n)}for(let t=c+1;t<=e.length;t+=1)i[t]=s;if(h>n)return;const u=r;r=i,i=u}const o=r[e.length];return o>n?void 0:o}var ep="[object Number]";function np(t){return"number"==typeof t||rt(t)&&et(t)==ep}var rp=np;function ip(t,e,n){return t===t&&(void 0!==n&&(t=t<=n?t:n),void 0!==e&&(t=t>=e?t:e)),t}var sp=ip;function op(t,e,n){return void 0===n&&(n=e,e=void 0),void 0!==n&&(n=gt(n),n=n===n?n:0),void 0!==e&&(e=gt(e),e=e===e?e:0),sp(gt(t),e,n)}var ap=op;function lp(t,e){return(t%e+e)%e}function cp(t,e){if(null==e)e=null==t?1:t,t=0;else if(e<t){const n=t;t=e,e=n}return Math.floor(Math.random()*(e-t+1)+t)}function hp(t){return"string"===typeof t&&"%"===t.slice(-1)}function up(t,e){if(null==t)return 0;let n;if("string"===typeof t){if(n=parseFloat(t),hp(t)&&(n/=100,Number.isFinite(n)))return n*e}else n=t;return Number.isFinite(n)?n>0&&n<1?n*e:n:0}function dp(t,e){function n(e){const n=new RegExp(`(?:\\d+(?:\\.\\d+)*)(${e})$`).exec(t);return n?n[1]:null}const r=parseFloat(t);if(Number.isNaN(r))return null;let i;if(null==e)i="[A-Za-z]*";else if(Array.isArray(e)){if(0===e.length)return null;i=e.join("|")}else"string"===typeof e&&(i=e);const s=n(i);return null===s?null:{unit:s,value:r}}function gp(t){if("object"===typeof t){let e=0,n=0,r=0,i=0;return null!=t.vertical&&Number.isFinite(t.vertical)&&(n=i=t.vertical),null!=t.horizontal&&Number.isFinite(t.horizontal)&&(r=e=t.horizontal),null!=t.left&&Number.isFinite(t.left)&&(e=t.left),null!=t.top&&Number.isFinite(t.top)&&(n=t.top),null!=t.right&&Number.isFinite(t.right)&&(r=t.right),null!=t.bottom&&Number.isFinite(t.bottom)&&(i=t.bottom),{top:n,right:r,bottom:i,left:e}}let e=0;return null!=t&&Number.isFinite(t)&&(e=t),{top:e,right:e,bottom:e,left:e}}let fp=!1,pp=!1,mp=!1,yp=!1,bp=!1,vp=!1,wp=!1,xp=!1,Pp=!1,Cp=!1,Ap=!1,Op=!1,Ep=!1,Sp=!1,Mp=!1,Tp=!1;if("object"===typeof navigator){const t=navigator.userAgent;fp=t.indexOf("Macintosh")>=0,pp=!!t.match(/(iPad|iPhone|iPod)/g),mp=t.indexOf("Windows")>=0,yp=t.indexOf("MSIE")>=0,bp=!!t.match(/Trident\/7\./),vp=!!t.match(/Edge\//),wp=t.indexOf("Mozilla/")>=0&&t.indexOf("MSIE")<0&&t.indexOf("Edge/")<0,Pp=t.indexOf("Chrome/")>=0&&t.indexOf("Edge/")<0,Cp=t.indexOf("Opera/")>=0||t.indexOf("OPR/")>=0,Ap=t.indexOf("Firefox/")>=0,Op=t.indexOf("AppleWebKit/")>=0&&t.indexOf("Chrome/")<0&&t.indexOf("Edge/")<0,"object"===typeof document&&(Tp=!document.createElementNS||""+document.createElementNS("http://www.w3.org/2000/svg","foreignObject")!=="[object SVGForeignObjectElement]"||t.indexOf("Opera/")>=0)}if("object"===typeof window&&(xp=null!=window.chrome&&null!=window.chrome.app&&null!=window.chrome.app.runtime,Sp=null!=window.PointerEvent&&!fp),"object"===typeof document){Ep="ontouchstart"in document.documentElement;try{const t=Object.defineProperty({},"passive",{get(){Mp=!0}}),e=document.createElement("div");e.addEventListener&&e.addEventListener("click",()=>{},t)}catch(aO){}}var jp;(function(t){t.IS_MAC=fp,t.IS_IOS=pp,t.IS_WINDOWS=mp,t.IS_IE=yp,t.IS_IE11=bp,t.IS_EDGE=vp,t.IS_NETSCAPE=wp,t.IS_CHROME_APP=xp,t.IS_CHROME=Pp,t.IS_OPERA=Cp,t.IS_FIREFOX=Ap,t.IS_SAFARI=Op,t.SUPPORT_TOUCH=Ep,t.SUPPORT_POINTER=Sp,t.SUPPORT_PASSIVE=Mp,t.NO_FOREIGNOBJECT=Tp,t.SUPPORT_FOREIGNOBJECT=!t.NO_FOREIGNOBJECT})(jp||(jp={})),function(t){function e(){const t=window.module;return null!=t&&null!=t.hot&&null!=t.hot.status?t.hot.status():"unkonwn"}function n(){return"apply"===e()}t.getHMRStatus=e,t.isApplyingHMR=n;const r={select:"input",change:"input",submit:"form",reset:"form",error:"img",load:"img",abort:"img"};function i(t){const e=document.createElement(r[t]||"div"),n="on"+t;let i=n in e;return i||(e.setAttribute(n,"return;"),i="function"===typeof e[n]),i}t.isEventSupported=i}(jp||(jp={}));const kp=/[\t\r\n\f]/g,Np=/\S+/g,Lp=t=>` ${t} `;function Rp(t){return t&&t.getAttribute&&t.getAttribute("class")||""}function Bp(t,e){if(null==t||null==e)return!1;const n=Lp(Rp(t)),r=Lp(e);return 1===t.nodeType&&n.replace(kp," ").includes(r)}function Dp(t,e){if(null!=t&&null!=e){if("function"===typeof e)return Dp(t,e(Rp(t)));if("string"===typeof e&&1===t.nodeType){const n=e.match(Np)||[],r=Lp(Rp(t)).replace(kp," ");let i=n.reduce((t,e)=>t.indexOf(Lp(e))<0?`${t}${e} `:t,r);i=i.trim(),r!==i&&t.setAttribute("class",i)}}}function Ip(t,e){if(null!=t){if("function"===typeof e)return Ip(t,e(Rp(t)));if((!e||"string"===typeof e)&&1===t.nodeType){const n=(e||"").match(Np)||[],r=Lp(Rp(t)).replace(kp," ");let i=n.reduce((t,e)=>{const n=Lp(e);return t.indexOf(n)>-1?t.replace(n," "):t},r);i=e?i.trim():"",r!==i&&t.setAttribute("class",i)}}}function Vp(t,e,n){if(null!=t&&null!=e)if(null==n||"string"!==typeof e){if("function"===typeof e)return Vp(t,e(Rp(t),n),n);if("string"===typeof e){const n=e.match(Np)||[];n.forEach(e=>{Bp(t,e)?Ip(t,e):Dp(t,e)})}}else n?Dp(t,e):Ip(t,e)}let zp=0;function Fp(){return zp+=1,"v"+zp}function $p(t){return null!=t.id&&""!==t.id||(t.id=Fp()),t.id}function Gp(t){return null!=t&&("function"===typeof t.getScreenCTM&&t instanceof SVGElement)}const qp={svg:"http://www.w3.org/2000/svg",xmlns:"http://www.w3.org/2000/xmlns/",xml:"http://www.w3.org/XML/1998/namespace",xlink:"http://www.w3.org/1999/xlink",xhtml:"http://www.w3.org/1999/xhtml"},_p="1.1";function Hp(t,e=document){return e.createElement(t)}function Up(t,e=qp.xhtml,n=document){return n.createElementNS(e,t)}function Wp(t,e=document){return Up(t,qp.svg,e)}function Jp(t){if(t){const e=`<svg xmlns="${qp.svg}" xmlns:xlink="${qp.xlink}" version="${_p}">${t}</svg>`,{documentElement:n}=Xp(e,{async:!1});return n}const e=document.createElementNS(qp.svg,"svg");return e.setAttributeNS(qp.xmlns,"xmlns:xlink",qp.xlink),e.setAttribute("version",_p),e}function Xp(t,e={}){let n;try{const r=new DOMParser;if(null!=e.async){const t=r;t.async=e.async}n=r.parseFromString(t,e.mimeType||"text/xml")}catch(r){n=void 0}if(!n||n.getElementsByTagName("parsererror").length)throw new Error("Invalid XML: "+t);return n}function Yp(t,e=!0){const n=t.nodeName;return e?n.toLowerCase():n.toUpperCase()}function Zp(t){let e=0,n=t.previousSibling;while(n)1===n.nodeType&&(e+=1),n=n.previousSibling;return e}function Kp(t,e){return t.querySelectorAll(e)}function Qp(t,e){return t.querySelector(e)}function tm(t,e,n){const r=t.ownerSVGElement;let i=t.parentNode;while(i&&i!==n&&i!==r){if(Bp(i,e))return i;i=i.parentNode}return null}function em(t,e){const n=e&&e.parentNode;return t===n||!!(n&&1===n.nodeType&&16&t.compareDocumentPosition(n))}function nm(t){if(t){const e=Array.isArray(t)?t:[t];e.forEach(t=>{t.parentNode&&t.parentNode.removeChild(t)})}}function rm(t){while(t.firstChild)t.removeChild(t.firstChild)}function im(t,e){const n=Array.isArray(e)?e:[e];n.forEach(e=>{null!=e&&t.appendChild(e)})}function sm(t,e){const n=t.firstChild;return n?om(n,e):im(t,e)}function om(t,e){const n=t.parentNode;if(n){const r=Array.isArray(e)?e:[e];r.forEach(e=>{null!=e&&n.insertBefore(e,t)})}}function am(t,e){const n=t.parentNode;if(n){const r=Array.isArray(e)?e:[e];r.forEach(e=>{null!=e&&n.insertBefore(e,t.nextSibling)})}}function lm(t,e){null!=e&&e.appendChild(t)}function cm(t){return!!t&&1===t.nodeType}function hm(t){try{return t instanceof HTMLElement}catch(e){return"object"===typeof t&&1===t.nodeType&&"object"===typeof t.style&&"object"===typeof t.ownerDocument}}function um(t,e){const n=[];let r=t.firstChild;for(;r;r=r.nextSibling)1===r.nodeType&&(e&&!Bp(r,e)||n.push(r));return n}const dm=["viewBox","attributeName","attributeType","repeatCount","textLength","lengthAdjust","gradientUnits"];function gm(t,e){return t.getAttribute(e)}function fm(t,e){const n=bm(e);n.ns?t.hasAttributeNS(n.ns,n.local)&&t.removeAttributeNS(n.ns,n.local):t.hasAttribute(e)&&t.removeAttribute(e)}function pm(t,e,n){if(null==n)return fm(t,e);const r=bm(e);r.ns&&"string"===typeof n?t.setAttributeNS(r.ns,e,n):"id"===e?t.id=""+n:t.setAttribute(e,""+n)}function mm(t,e){Object.keys(e).forEach(n=>{pm(t,n,e[n])})}function ym(t,e,n){if(null==e){const e=t.attributes,n={};for(let t=0;t<e.length;t+=1)n[e[t].name]=e[t].value;return n}if("string"===typeof e&&void 0===n)return t.getAttribute(e);"object"===typeof e?mm(t,e):pm(t,e,n)}function bm(t){if(-1!==t.indexOf(":")){const e=t.split(":");return{ns:qp[e[0]],local:e[1]}}return{ns:null,local:t}}function vm(t){const e={};return Object.keys(t).forEach(n=>{const r=dm.includes(n)?n:_f(n);e[r]=t[n]}),e}function wm(t){const e={},n=t.split(";");return n.forEach(t=>{const n=t.trim();if(n){const t=n.split("=");t.length&&(e[t[0].trim()]=t[1]?t[1].trim():"")}}),e}function xm(t,e){return Object.keys(e).forEach(n=>{if("class"===n)t[n]=t[n]?`${t[n]} ${e[n]}`:e[n];else if("style"===n){const r="object"===typeof t[n],i="object"===typeof e[n];let s,o;r&&i?(s=t[n],o=e[n]):r?(s=t[n],o=wm(e[n])):i?(s=wm(t[n]),o=e[n]):(s=wm(t[n]),o=wm(e[n])),t[n]=xm(s,o)}else t[n]=e[n]}),t}function Pm(t,e,n={}){const r=n.offset||0,s=[],o=[];let a,l,c=null;for(let h=0;h<t.length;h+=1){a=o[h]=t[h];for(let i=0,s=e.length;i<s;i+=1){const s=e[i],l=s.start+r,c=s.end+r;h>=l&&h<c&&("string"===typeof a?a=o[h]={t:t[h],attrs:s.attrs}:a.attrs=xm(xm({},a.attrs),s.attrs),n.includeAnnotationIndices&&(null==a.annotations&&(a.annotations=[]),a.annotations.push(i)))}l=o[h-1],l?i.isObject(a)&&i.isObject(l)?(c=c,JSON.stringify(a.attrs)===JSON.stringify(l.attrs)?c.t+=a.t:(s.push(c),c=a)):i.isObject(a)||i.isObject(l)?(c=c,s.push(c),c=a):c=(c||"")+a:c=a}return null!=c&&s.push(c),s}function Cm(t,e){return t?t.filter(t=>t.start<e&&e<=t.end):[]}function Am(t,e,n){return t?t.filter(t=>e>=t.start&&e<t.end||n>t.start&&n<=t.end||t.start>=e&&t.end<n):[]}function Om(t,e,n){return t&&t.forEach(t=>{t.start<e&&t.end>=e?t.end+=n:t.start>=e&&(t.start+=n,t.end+=n)}),t}function Em(t){return t.replace(/ /g," ")}var Sm;let Mm;(function(t){function e(t){const e="data:";return t.substr(0,e.length)===e}function n(t,n){if(!t||e(t))return void setTimeout(()=>n(null,t));const r=()=>{n(new Error("Failed to load image: "+t))},i=window.FileReader?t=>{if(200===t.status){const e=new FileReader;e.onload=t=>{const e=t.target.result;n(null,e)},e.onerror=r,e.readAsDataURL(t.response)}else r()}:e=>{const i=t=>{const e=32768,n=[];for(let r=0;r<t.length;r+=e)n.push(String.fromCharCode.apply(null,t.subarray(r,r+e)));return n.join("")};if(200===e.status){let r=t.split(".").pop()||"png";"svg"===r&&(r="svg+xml");const s=`data:image/${r};base64,`,o=new Uint8Array(e.response),a=s+btoa(i(o));n(null,a)}else r()},s=new XMLHttpRequest;s.responseType=window.FileReader?"blob":"arraybuffer",s.open("GET",t,!0),s.addEventListener("error",r),s.addEventListener("load",()=>i(s)),s.send()}function r(t){let e=t.replace(/\s/g,"");e=decodeURIComponent(e);const n=e.indexOf(","),r=e.slice(0,n),i=r.split(":")[1].split(";")[0],s=e.slice(n+1);let o;o=r.indexOf("base64")>=0?atob(s):unescape(encodeURIComponent(s));const a=new Uint8Array(o.length);for(let l=0;l<o.length;l+=1)a[l]=o.charCodeAt(l);return new Blob([a],{type:i})}function i(t,e){const n=window.navigator.msSaveBlob;if(n)n(t,e);else{const n=window.URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download=e,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(n)}}function s(t,e){const n=r(t);i(n,e)}function o(t){const e=t.match(/<svg[^>]*viewBox\s*=\s*(["']?)(.+?)\1[^>]*>/i);return e&&e[2]?e[2].replace(/\s+/," ").split(" "):null}function a(t){const e=parseFloat(t);return Number.isNaN(e)?null:e}function l(t,e={}){let n=null;const r=e=>(null==n&&(n=o(t)),null!=n?a(n[e]):null),i=e=>{const n=t.match(e);return n&&n[2]?a(n[2]):null};let s=e.width;if(null==s&&(s=i(/<svg[^>]*width\s*=\s*(["']?)(.+?)\1[^>]*>/i)),null==s&&(s=r(2)),null==s)throw new Error("Can not parse width from svg string");let l=e.height;if(null==l&&(l=i(/<svg[^>]*height\s*=\s*(["']?)(.+?)\1[^>]*>/i)),null==l&&(l=r(3)),null==l)throw new Error("Can not parse height from svg string");const c=encodeURIComponent(t).replace(/'/g,"%27").replace(/"/g,"%22"),h="data:image/svg+xml",u=`${h},${c}`;return u}t.isDataUrl=e,t.imageToDataUri=n,t.dataUriToBlob=r,t.downloadBlob=i,t.downloadDataUri=s,t.svgToDataUrl=l})(Sm||(Sm={}));const Tm={px(t){return t},mm(t){return Mm*t},cm(t){return Mm*t*10},in(t){return Mm*t*25.4},pt(t){return Mm*(25.4*t/72)},pc(t){return Mm*(25.4*t/6)}};var jm;(function(t){function e(t,e,n){const r=document.createElement("div"),i=r.style;i.display="inline-block",i.position="absolute",i.left="-15000px",i.top="-15000px",i.width=t+(n||"px"),i.height=e+(n||"px"),document.body.appendChild(r);const s=r.getBoundingClientRect(),o={width:s.width||0,height:s.height||0};return document.body.removeChild(r),o}function n(t,n){null==Mm&&(Mm=e("1","1","mm").width);const r=n?Tm[n]:null;return r?r(t):t}t.measure=e,t.toPx=n})(jm||(jm={}));const km=/-(.)/g;function Nm(t){return t.replace(km,(t,e)=>e.toUpperCase())}const Lm={},Rm=["webkit","ms","moz","o"],Bm="undefined"!==typeof document?document.createElement("div").style:{};function Dm(t){for(let e=0;e<Rm.length;e+=1){const n=Rm[e]+t;if(n in Bm)return n}return null}function Im(t){const e=Nm(t);if(null==Lm[e]){const t=e.charAt(0).toUpperCase()+e.slice(1);Lm[e]=e in Bm?e:Dm(t)}return Lm[e]}function Vm(t,e,n){const r=Im(e);null!=r&&(t[r]=n),t[e]=n}function zm(t,e){const n=t.ownerDocument&&t.ownerDocument.defaultView&&t.ownerDocument.defaultView.opener?t.ownerDocument.defaultView.getComputedStyle(t,null):window.getComputedStyle(t,null);return n&&e?n.getPropertyValue(e)||n[e]:n}function Fm(t){const e=zm(t);return null!=e&&("scroll"===e.overflow||"auto"===e.overflow)}const $m=function(){if("undefined"==typeof document)return function(){};const t=document;return t.selection?function(){t.selection.empty()}:window.getSelection?function(){const t=window.getSelection();t&&(t.empty?t.empty():t.removeAllRanges&&t.removeAllRanges())}:function(){}}(),Gm={animationIterationCount:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0};function qm(t){return/^--/.test(t)}function _m(t,e,n){const r=window.getComputedStyle(t,null);return n?r.getPropertyValue(e)||void 0:r[e]||t.style[e]}function Hm(t,e){return parseInt(_m(t,e),10)||0}function Um(t,e){return Gm[t]||"number"!==typeof e?e:e+"px"}function Wm(t,e,n){if("string"!==typeof e)for(const r in e)Wm(t,r,e[r]);else{const r=qm(e);if(r||(e=Im(e)),void 0===n)return _m(t,e,r);r||(n=Um(e,n));const i=t.style;r?i.setProperty(e,n):i[e]=n}}const Jm=new WeakMap;function Xm(t,e){const n=o.camelCase(e),r=Jm.get(t);if(r)return r[n]}function Ym(t,e,n){const r=o.camelCase(e),i=Jm.get(t);i?i[r]=n:Jm.set(t,{[r]:n})}function Zm(t,e,n){if(!e){const e={};return Object.keys(Jm).forEach(n=>{e[n]=Xm(t,n)}),e}if("string"===typeof e)return void 0===n?Xm(t,e):void Ym(t,e,n);for(const r in e)Zm(t,r,e[r])}const Km={class:"className",contenteditable:"contentEditable",for:"htmlFor",readonly:"readOnly",maxlength:"maxLength",tabindex:"tabIndex",colspan:"colSpan",rowspan:"rowSpan",usemap:"useMap"};function Qm(t,e,n){if(e){if("string"===typeof e)return e=Km[e]||e,arguments.length<3?t[e]:void(t[e]=n);for(const n in e)Qm(t,n,e[n])}}class ty{get[Symbol.toStringTag](){return ty.toStringTag}get type(){return this.node.nodeName}get id(){return this.node.id}set id(t){this.node.id=t}constructor(t,e,n){if(!t)throw new TypeError("Invalid element to create vector");let r;if(ty.isVector(t))r=t.node;else if("string"===typeof t)if("svg"===t.toLowerCase())r=Jp();else if("<"===t[0]){const e=Jp(t);r=document.importNode(e.firstChild,!0)}else r=document.createElementNS(qp.svg,t);else r=t;this.node=r,e&&this.setAttributes(e),n&&this.append(n)}transform(t,e){return null==t?zy(this.node):(zy(this.node,t,e),this)}translate(t,e=0,n={}){return null==t?Fy(this.node):(Fy(this.node,t,e,n),this)}rotate(t,e,n,r={}){return null==t?$y(this.node):($y(this.node,t,e,n,r),this)}scale(t,e){return null==t?Gy(this.node):(Gy(this.node,t,e),this)}getTransformToElement(t){const e=ty.toNode(t);return qy(this.node,e)}removeAttribute(t){return fm(this.node,t),this}getAttribute(t){return gm(this.node,t)}setAttribute(t,e){return pm(this.node,t,e),this}setAttributes(t){return mm(this.node,t),this}attr(t,e){return null==t?ym(this.node):"string"===typeof t&&void 0===e?ym(this.node,t):("object"===typeof t?ym(this.node,t):ym(this.node,t,e),this)}svg(){return this.node instanceof SVGSVGElement?this:ty.create(this.node.ownerSVGElement)}defs(){const t=this.svg()||this,e=t.node.getElementsByTagName("defs")[0];return e?ty.create(e):ty.create("defs").appendTo(t)}text(t,e={}){return oy(this.node,t,e),this}tagName(){return Yp(this.node)}clone(){return ty.create(this.node.cloneNode(!0))}remove(){return nm(this.node),this}empty(){return rm(this.node),this}append(t){return im(this.node,ty.toNodes(t)),this}appendTo(t){return lm(this.node,ty.isVector(t)?t.node:t),this}prepend(t){return sm(this.node,ty.toNodes(t)),this}before(t){return om(this.node,ty.toNodes(t)),this}replace(t){return this.node.parentNode&&this.node.parentNode.replaceChild(ty.toNode(t),this.node),ty.create(t)}first(){return this.node.firstChild?ty.create(this.node.firstChild):null}last(){return this.node.lastChild?ty.create(this.node.lastChild):null}get(t){const e=this.node.childNodes[t];return e?ty.create(e):null}indexOf(t){const e=Array.prototype.slice.call(this.node.childNodes);return e.indexOf(ty.toNode(t))}find(t){const e=[],n=Kp(this.node,t);if(n)for(let r=0,i=n.length;r<i;r+=1)e.push(ty.create(n[r]));return e}findOne(t){const e=Qp(this.node,t);return e?ty.create(e):null}findParentByClass(t,e){const n=tm(this.node,t,e);return n?ty.create(n):null}matches(t){const e=this.node,n=(this.node.matches,e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector||null);return n&&n.call(e,t)}contains(t){return em(this.node,ty.isVector(t)?t.node:t)}wrap(t){const e=ty.create(t),n=this.node.parentNode;return null!=n&&n.insertBefore(e.node,this.node),e.append(this)}parent(t){let e=this;if(null==e.node.parentNode)return null;if(e=ty.create(e.node.parentNode),null==t)return e;do{if("string"===typeof t?e.matches(t):e instanceof t)return e}while(e=ty.create(e.node.parentNode));return e}children(){const t=this.node.childNodes,e=[];for(let n=0;n<t.length;n+=1){const r=t[n];1===r.nodeType&&e.push(ty.create(t[n]))}return e}eachChild(t,e){const n=this.children();for(let r=0,i=n.length;r<i;r+=1)t.call(n[r],n[r],r,n),e&&n[r].eachChild(t,e);return this}index(){return Zp(this.node)}hasClass(t){return Bp(this.node,t)}addClass(t){return Dp(this.node,t),this}removeClass(t){return Ip(this.node,t),this}toggleClass(t,e){return Vp(this.node,t,e),this}toLocalPoint(t,e){return Hy(this.node,t,e)}sample(t=1){return this.node instanceof SVGPathElement?dy(this.node,t):[]}toPath(){return ty.create(Py(this.node))}toPathData(){return Cy(this.node)}}function ey(t,e){const n=ty.create(e),r=ty.create("textPath"),i=t.d;if(i&&void 0===t["xlink:href"]){const t=ty.create("path").attr("d",i).appendTo(n.defs());r.attr("xlink:href","#"+t.id)}return"object"===typeof t&&r.attr(t),r.node}function ny(t,e,n){const r=n.eol,i=n.baseSize,s=n.lineHeight;let o,a=0;const l={},c=e.length-1;for(let h=0;h<=c;h+=1){let s=e[h],l=null;if("object"===typeof s){const t=s.attrs,e=ty.create("tspan",t);o=e.node;let u=s.t;r&&h===c&&(u+=r),o.textContent=u;const d=t.class;d&&e.addClass(d),n.includeAnnotationIndices&&e.attr("annotations",s.annotations.join(",")),l=parseFloat(t["font-size"]),void 0===l&&(l=i),l&&l>a&&(a=l)}else r&&h===c&&(s+=r),o=document.createTextNode(s||" "),i&&i>a&&(a=i);t.appendChild(o)}return a&&(l.maxFontSize=a),s?l.lineHeight=s:a&&(l.lineHeight=1.2*a),l}(function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&r.node instanceof SVGElement&&"function"===typeof r.sample&&"function"===typeof r.toPath}function n(e,n,r){return new t(e,n,r)}function r(t){if("<"===t[0]){const e=Jp(t),r=[];for(let t=0,i=e.childNodes.length;t<i;t+=1){const i=e.childNodes[t];r.push(n(document.importNode(i,!0)))}return r}return[n(t)]}function i(t){return e(t)?t.node:t}function s(t){return Array.isArray(t)?t.map(t=>i(t)):[i(t)]}t.toStringTag="X6."+t.name,t.isVector=e,t.create=n,t.createVectors=r,t.toNode=i,t.toNodes=s})(ty||(ty={}));const ry=/em$/;function iy(t,e){const n=parseFloat(t);return ry.test(t)?n*e:n}function sy(t,e,n,r){if(!Array.isArray(e))return 0;const i=e.length;if(!i)return 0;let s=e[0];const o=iy(s.maxFontSize,n)||n;let a=0;const l=iy(r,n);for(let u=1;u<i;u+=1){s=e[u];const t=iy(s.lineHeight,n)||l;a+=t}const c=iy(s.maxFontSize,n)||n;let h;switch(t){case"middle":h=o/2-.15*c-a/2;break;case"bottom":h=-.25*c-a;break;case"top":default:h=.8*o;break}return h}function oy(t,e,n={}){e=l.sanitize(e);const r=n.eol;let i=n.textPath;const s=n.textVerticalAnchor,o="middle"===s||"bottom"===s||"top"===s;let a=n.x;void 0===a&&(a=t.getAttribute("x")||0);const c=n.includeAnnotationIndices;let h=n.annotations;h&&!Array.isArray(h)&&(h=[h]);const u=n.lineHeight,d="auto"===u,g=d?"1.5em":u||"1em";let f=!0;const p=t.childNodes;if(1===p.length){const t=p[0];t&&"TITLE"===t.tagName.toUpperCase()&&(f=!1)}f&&rm(t),ym(t,{"xml:space":"preserve",display:e||n.displayEmpty?null:"none"});const m=ym(t,"font-size");let y,b,v=parseFloat(m);v||(v=16,!o&&!h||m||ym(t,"font-size",""+v)),i?("string"===typeof i&&(i={d:i}),y=ey(i,t)):y=document.createDocumentFragment();let w,x=0;const P=e.split("\n"),C=[],A=P.length-1;for(let E=0;E<=A;E+=1){b=g;let t="v-line";const e=Wp("tspan");let n,s=P[E];if(s)if(h){const t=l.annotate(s,h,{offset:-x,includeAnnotationIndices:c});n=ny(e,t,{eol:E!==A&&r,baseSize:v,lineHeight:d?null:g,includeAnnotationIndices:c});const i=n.lineHeight;i&&d&&0!==E&&(b=i),0===E&&(w=.8*n.maxFontSize)}else r&&E!==A&&(s+=r),e.textContent=s;else{e.textContent="-",t+=" v-empty-line";const r=e.style;r.fillOpacity=0,r.strokeOpacity=0,h&&(n={})}n&&C.push(n),E>0&&e.setAttribute("dy",b),(E>0||i)&&e.setAttribute("x",a),e.className.baseVal=t,y.appendChild(e),x+=s.length+1}if(o)if(h)b=sy(s,C,v,g);else if("top"===s)b="0.8em";else{let t;switch(A>0?(t=parseFloat(g)||1,t*=A,ry.test(g)||(t/=v)):t=0,s){case"middle":b=.3-t/2+"em";break;case"bottom":b=-t-.3+"em";break;default:break}}else 0===s?b="0em":s?b=s:(b=0,null==t.getAttribute("y")&&t.setAttribute("y",""+(w||"0.8em")));const O=y.firstChild;O.setAttribute("dy",b),t.appendChild(y)}function ay(t,e={}){const n=document.createElement("canvas").getContext("2d");if(!t)return{width:0};const r=[],i=e["font-size"]?parseFloat(e["font-size"])+"px":"14px";return r.push(e["font-style"]||"normal"),r.push(e["font-variant"]||"normal"),r.push(e["font-weight"]||400),r.push(i),r.push(e["font-family"]||"sans-serif"),n.font=r.join(" "),n.measureText(t)}function ly(t,e,n,r={}){if(e>=n)return[t,""];const i=t.length,s={};let o=Math.round(e/n*i-1);o<0&&(o=0);while(o>=0&&o<i){const n=t.slice(0,o),i=s[n]||ay(n,r).width,a=t.slice(0,o+1),l=s[a]||ay(a,r).width;if(s[n]=i,s[a]=l,i>e)o-=1;else{if(!(l<=e))break;o+=1}}return[t.slice(0,o),t.slice(o)]}function cy(t,e,n={},r={}){const i=e.width,s=e.height,a=r.eol||"\n",l=n.fontSize||14,c=n.lineHeight?parseFloat(n.lineHeight):Math.ceil(1.4*l),h=Math.floor(s/c);if(t.indexOf(a)>-1){const i=o.uuid(),s=[];return t.split(a).map(t=>{const o=cy(t,Object.assign(Object.assign({},e),{height:Number.MAX_SAFE_INTEGER}),n,Object.assign(Object.assign({},r),{eol:i}));o&&s.push(...o.split(i))}),s.slice(0,h).join(a)}const{width:u}=ay(t,n);if(u<i)return t;const d=[];let g=t,f=u,p=r.ellipsis,m=0;p&&("string"!==typeof p&&(p="…"),m=ay(p,n).width);for(let o=0;o<h;o+=1){if(!(f>i)){d.push(g);break}{const t=o===h-1;if(t){const[t]=ly(g,i-m,f,n);d.push(p?`${t}${p}`:t)}else{const[t,e]=ly(g,i,f,n);d.push(t),g=e,f=ay(g,n).width}}}return d.join(a)}const hy=.551784;function uy(t,e,n=NaN){const r=t.getAttribute(e);if(null==r)return n;const i=parseFloat(r);return Number.isNaN(i)?n:i}function dy(t,e=1){const n=t.getTotalLength(),r=[];let i,s=0;while(s<n)i=t.getPointAtLength(s),r.push({distance:s,x:i.x,y:i.y}),s+=e;return r}function gy(t){return["M",uy(t,"x1"),uy(t,"y1"),"L",uy(t,"x2"),uy(t,"y2")].join(" ")}function fy(t){const e=yy(t);return 0===e.length?null:my(e)+" Z"}function py(t){const e=yy(t);return 0===e.length?null:my(e)}function my(t){const e=t.map(t=>`${t.x} ${t.y}`);return"M "+e.join(" L")}function yy(t){const e=[],n=t.points;if(n)for(let r=0,i=n.numberOfItems;r<i;r+=1)e.push(n.getItem(r));return e}function by(t){const e=uy(t,"cx",0),n=uy(t,"cy",0),r=uy(t,"r"),i=r*hy;return["M",e,n-r,"C",e+i,n-r,e+r,n-i,e+r,n,"C",e+r,n+i,e+i,n+r,e,n+r,"C",e-i,n+r,e-r,n+i,e-r,n,"C",e-r,n-i,e-i,n-r,e,n-r,"Z"].join(" ")}function vy(t){const e=uy(t,"cx",0),n=uy(t,"cy",0),r=uy(t,"rx"),i=uy(t,"ry")||r,s=r*hy,o=i*hy,a=["M",e,n-i,"C",e+s,n-i,e+r,n-o,e+r,n,"C",e+r,n+o,e+s,n+i,e,n+i,"C",e-s,n+i,e-r,n+o,e-r,n,"C",e-r,n-o,e-s,n-i,e,n-i,"Z"].join(" ");return a}function wy(t){return xy({x:uy(t,"x",0),y:uy(t,"y",0),width:uy(t,"width",0),height:uy(t,"height",0),rx:uy(t,"rx",0),ry:uy(t,"ry",0)})}function xy(t){let e;const n=t.x,r=t.y,i=t.width,s=t.height,o=Math.min(t.rx||t["top-rx"]||0,i/2),a=Math.min(t.rx||t["bottom-rx"]||0,i/2),l=Math.min(t.ry||t["top-ry"]||0,s/2),c=Math.min(t.ry||t["bottom-ry"]||0,s/2);return e=o||a||l||c?["M",n,r+l,"v",s-l-c,"a",a,c,0,0,0,a,c,"h",i-2*a,"a",a,c,0,0,0,a,-c,"v",-(s-c-l),"a",o,l,0,0,0,-o,-l,"h",-(i-2*o),"a",o,l,0,0,0,-o,l,"Z"]:["M",n,r,"H",n+i,"V",r+s,"H",n,"V",r,"Z"],e.join(" ")}function Py(t){const e=Wp("path");ym(e,ym(t));const n=Cy(t);return n&&e.setAttribute("d",n),e}function Cy(t){const e=t.tagName.toLowerCase();switch(e){case"path":return t.getAttribute("d");case"line":return gy(t);case"polygon":return fy(t);case"polyline":return py(t);case"ellipse":return vy(t);case"circle":return by(t);case"rect":return wy(t);default:break}throw new Error(`"${e}" cannot be converted to svg path element.`)}function Ay(t,e,n,r){const i=2*Math.PI-1e-6,s=t,o=e;let a=n,l=r;if(l<a){const t=a;a=l,l=t}const c=l-a,h=c<Math.PI?"0":"1",u=Math.cos(a),d=Math.sin(a),g=Math.cos(l),f=Math.sin(l);return c>=i?s?`M0,${o}A${o},${o} 0 1,1 0,${-o}A${o},${o} 0 1,1 0,${o}M0,${s}A${s},${s} 0 1,0 0,${-s}A${s},${s} 0 1,0 0,${s}Z`:`M0,${o}A${o},${o} 0 1,1 0,${-o}A${o},${o} 0 1,1 0,${o}Z`:s?`M${o*u},${o*d}A${o},${o} 0 ${h},1 ${o*g},${o*f}L${s*g},${s*f}A${s},${s} 0 ${h},0 ${s*u},${s*d}Z`:`M${o*u},${o*d}A${o},${o} 0 ${h},1 ${o*g},${o*f}L0,0Z`}const Oy=/(\w+)\(([^,)]+),?([^)]+)?\)/gi,Ey=/[ ,]+/,Sy=/^(\w+)\((.*)\)/;function My(t,e){const n=Wp("svg"),r=n.createSVGPoint();return r.x=t,r.y=e,r}function Ty(t){const e=Wp("svg"),n=e.createSVGMatrix();if(null!=t){const e=t,r=n;for(const t in e)r[t]=e[t]}return n}function jy(t){const e=Wp("svg");return null!=t?(t instanceof DOMMatrix||(t=Ty(t)),e.createSVGTransformFromMatrix(t)):e.createSVGTransform()}function ky(t){let e=Ty();const n=null!=t&&t.match(Oy);if(!n)return e;for(let r=0,i=n.length;r<i;r+=1){const t=n[r],i=t.match(Sy);if(i){let t,n,r,s,o,a=Ty();const l=i[2].split(Ey);switch(i[1].toLowerCase()){case"scale":t=parseFloat(l[0]),n=void 0===l[1]?t:parseFloat(l[1]),a=a.scaleNonUniform(t,n);break;case"translate":r=parseFloat(l[0]),s=parseFloat(l[1]),a=a.translate(r,s);break;case"rotate":o=parseFloat(l[0]),r=parseFloat(l[1])||0,s=parseFloat(l[2])||0,a=0!==r||0!==s?a.translate(r,s).rotate(o).translate(-r,-s):a.rotate(o);break;case"skewx":o=parseFloat(l[0]),a=a.skewX(o);break;case"skewy":o=parseFloat(l[0]),a=a.skewY(o);break;case"matrix":a.a=parseFloat(l[0]),a.b=parseFloat(l[1]),a.c=parseFloat(l[2]),a.d=parseFloat(l[3]),a.e=parseFloat(l[4]),a.f=parseFloat(l[5]);break;default:continue}e=e.multiply(a)}}return e}function Ny(t){const e=t||{},n=null!=e.a?e.a:1,r=null!=e.b?e.b:0,i=null!=e.c?e.c:0,s=null!=e.d?e.d:1,o=null!=e.e?e.e:0,a=null!=e.f?e.f:0;return`matrix(${n},${r},${i},${s},${o},${a})`}function Ly(t){let e,n,r;if(t){const i=Ey;if(t.trim().indexOf("matrix")>=0){const i=ky(t),s=By(i);e=[s.translateX,s.translateY],n=[s.rotation],r=[s.scaleX,s.scaleY];const o=[];0===e[0]&&0===e[1]||o.push(`translate(${e.join(",")})`),1===r[0]&&1===r[1]||o.push(`scale(${r.join(",")})`),0!==n[0]&&o.push(`rotate(${n[0]})`),t=o.join(" ")}else{const s=t.match(/translate\((.*?)\)/);s&&(e=s[1].split(i));const o=t.match(/rotate\((.*?)\)/);o&&(n=o[1].split(i));const a=t.match(/scale\((.*?)\)/);a&&(r=a[1].split(i))}}const i=r&&r[0]?parseFloat(r[0]):1;return{raw:t||"",translation:{tx:e&&e[0]?parseInt(e[0],10):0,ty:e&&e[1]?parseInt(e[1],10):0},rotation:{angle:n&&n[0]?parseInt(n[0],10):0,cx:n&&n[1]?parseInt(n[1],10):void 0,cy:n&&n[2]?parseInt(n[2],10):void 0},scale:{sx:i,sy:r&&r[1]?parseFloat(r[1]):i}}}function Ry(t,e){const n=e.x*t.a+e.y*t.c+0,r=e.x*t.b+e.y*t.d+0;return{x:n,y:r}}function By(t){const e=Ry(t,{x:0,y:1}),n=Ry(t,{x:1,y:0}),r=180/Math.PI*Math.atan2(e.y,e.x)-90,i=180/Math.PI*Math.atan2(n.y,n.x);return{skewX:r,skewY:i,translateX:t.e,translateY:t.f,scaleX:Math.sqrt(t.a*t.a+t.b*t.b),scaleY:Math.sqrt(t.c*t.c+t.d*t.d),rotation:r}}function Dy(t){let e,n,r,i;return t?(e=null==t.a?1:t.a,i=null==t.d?1:t.d,n=t.b,r=t.c):e=i=1,{sx:n?Math.sqrt(e*e+n*n):e,sy:r?Math.sqrt(r*r+i*i):i}}function Iy(t){let e={x:0,y:1};t&&(e=Ry(t,e));const n=180*Math.atan2(e.y,e.x)/Math.PI%360-90,r=n%360+(n<0?360:0);return{angle:r}}function Vy(t){return{tx:t&&t.e||0,ty:t&&t.f||0}}function zy(t,e,n={}){if(null==e)return ky(ym(t,"transform"));if(n.absolute)return void t.setAttribute("transform",Ny(e));const r=t.transform,i=jy(e);r.baseVal.appendItem(i)}function Fy(t,e,n=0,r={}){let i=ym(t,"transform");const s=Ly(i);if(null==e)return s.translation;i=s.raw,i=i.replace(/translate\([^)]*\)/g,"").trim();const o=r.absolute?e:s.translation.tx+e,a=r.absolute?n:s.translation.ty+n,l=`translate(${o},${a})`;t.setAttribute("transform",`${l} ${i}`.trim())}function $y(t,e,n,r,i={}){let s=ym(t,"transform");const o=Ly(s);if(null==e)return o.rotation;s=o.raw,s=s.replace(/rotate\([^)]*\)/g,"").trim(),e%=360;const a=i.absolute?e:o.rotation.angle+e,l=null!=n&&null!=r?`,${n},${r}`:"",c=`rotate(${a}${l})`;t.setAttribute("transform",`${s} ${c}`.trim())}function Gy(t,e,n){let r=ym(t,"transform");const i=Ly(r);if(null==e)return i.scale;n=null==n?e:n,r=i.raw,r=r.replace(/scale\([^)]*\)/g,"").trim();const s=`scale(${e},${n})`;t.setAttribute("transform",`${r} ${s}`.trim())}function qy(t,e){if(Gp(e)&&Gp(t)){const n=e.getScreenCTM(),r=t.getScreenCTM();if(n&&r)return n.inverse().multiply(r)}return Ty()}function _y(t,e){let n=Ty();if(Gp(e)&&Gp(t)){let r=t;const i=[];while(r&&r!==e){const t=r.getAttribute("transform")||null,e=ky(t);i.push(e),r=r.parentNode}i.reverse().forEach(t=>{n=n.multiply(t)})}return n}function Hy(t,e,n){const r=t instanceof SVGSVGElement?t:t.ownerSVGElement,i=r.createSVGPoint();i.x=e,i.y=n;try{const e=r.getScreenCTM(),n=i.matrixTransform(e.inverse()),s=qy(t,r).inverse();return n.matrixTransform(s)}catch(s){return i}}var Uy,Wy,Jy,Xy;(function(t){const e={};function n(t){return e[t]||{}}function r(t,n){e[t]=n}function i(t){delete e[t]}t.get=n,t.register=r,t.unregister=i})(Uy||(Uy={})),function(t){const e=new WeakMap;function n(t){return e.has(t)||e.set(t,{events:Object.create(null)}),e.get(t)}function r(t){return e.get(t)}function i(t){return e.delete(t)}t.ensure=n,t.get=r,t.remove=i}(Wy||(Wy={})),function(t){function e(t){t.stopPropagation()}function n(t,e,n){null!=t.addEventListener&&t.addEventListener(e,n)}function r(t,e,n){null!=t.removeEventListener&&t.removeEventListener(e,n)}t.returnTrue=()=>!0,t.returnFalse=()=>!1,t.stopPropagationCallback=e,t.addEventListener=n,t.removeEventListener=r}(Jy||(Jy={})),function(t){const e=/[^\x20\t\r\n\f]+/g,n=/^([^.]*)(?:\.(.+)|)/;function r(t){return(t||"").match(e)||[""]}function i(t){const e=n.exec(t)||[];return{originType:e[1]?e[1].trim():e[1],namespaces:e[2]?e[2].split(".").map(t=>t.trim()).sort():[]}}function s(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function o(t,e){if(e){const n=t;return null!=n.querySelector&&null!=n.querySelector(e)}return!0}t.splitType=r,t.normalizeType=i,t.isValidTarget=s,t.isValidSelector=o}(Jy||(Jy={})),function(t){let e=0;const n=new WeakMap;function r(t){return n.has(t)||(n.set(t,e),e+=1),n.get(t)}function i(t){return n.get(t)}function s(t){return n.delete(t)}function o(t,e){return n.set(t,e)}t.ensureHandlerId=r,t.getHandlerId=i,t.removeHandlerId=s,t.setHandlerId=o}(Jy||(Jy={})),function(t){function e(t,e){const n=[],r=Wy.get(t),i=r&&r.events&&r.events[e.type],s=i&&i.handlers||[],o=i?i.delegateCount:0;if(o>0&&!("click"===e.type&&"number"===typeof e.button&&e.button>=1))for(let a=e.target;a!==t;a=a.parentNode||t)if(1===a.nodeType&&("click"!==e.type||!0!==a.disabled)){const e=[],r={};for(let n=0;n<o;n+=1){const i=s[n],o=i.selector;if(null!=o&&null==r[o]){const e=t,n=[];e.querySelectorAll(o).forEach(t=>{n.push(t)}),r[o]=n.includes(a)}r[o]&&e.push(i)}e.length&&n.push({elem:a,handlers:e})}return o<s.length&&n.push({elem:t,handlers:s.slice(o)}),n}t.getHandlerQueue=e}(Jy||(Jy={})),function(t){function e(t){return null!=t&&t===t.window}t.isWindow=e}(Jy||(Jy={})),function(t){function e(t,e){const n=9===t.nodeType?t.documentElement:t,r=e&&e.parentNode;return t===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):t.compareDocumentPosition&&16&t.compareDocumentPosition(r)))}t.contains=e}(Jy||(Jy={}));class Yy{constructor(t,e){this.isDefaultPrevented=Jy.returnFalse,this.isPropagationStopped=Jy.returnFalse,this.isImmediatePropagationStopped=Jy.returnFalse,this.isSimulated=!1,this.preventDefault=()=>{const t=this.originalEvent;this.isDefaultPrevented=Jy.returnTrue,t&&!this.isSimulated&&t.preventDefault()},this.stopPropagation=()=>{const t=this.originalEvent;this.isPropagationStopped=Jy.returnTrue,t&&!this.isSimulated&&t.stopPropagation()},this.stopImmediatePropagation=()=>{const t=this.originalEvent;this.isImmediatePropagationStopped=Jy.returnTrue,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()},"string"===typeof t?this.type=t:t.type&&(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented?Jy.returnTrue:Jy.returnFalse,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.timeStamp=t.timeStamp),e&&Object.assign(this,e),this.timeStamp||(this.timeStamp=Date.now())}}(function(t){function e(e){return e instanceof t?e:new t(e)}t.create=e})(Yy||(Yy={})),function(t){function e(e,n){Object.defineProperty(t.prototype,e,{enumerable:!0,configurable:!0,get:"function"===typeof n?function(){if(this.originalEvent)return n(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})}t.addProperty=e}(Yy||(Yy={})),function(t){const e={bubbles:!0,cancelable:!0,eventPhase:!0,detail:!0,view:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pageX:!0,pageY:!0,screenX:!0,screenY:!0,toElement:!0,pointerId:!0,pointerType:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,touches:!0,changedTouches:!0,targetTouches:!0,which:!0,altKey:!0,ctrlKey:!0,metaKey:!0,shiftKey:!0};Object.keys(e).forEach(n=>t.addProperty(n,e[n]))}(Yy||(Yy={})),function(t){Uy.register("load",{noBubble:!0})}(Xy||(Xy={})),function(t){Uy.register("beforeunload",{postDispatch(t,e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}})}(Xy||(Xy={})),function(t){Uy.register("mouseenter",{delegateType:"mouseover",bindType:"mouseover",handle(t,e){let n;const r=e.relatedTarget,i=e.handleObj;return r&&(r===t||Jy.contains(t,r))||(e.type=i.originType,n=i.handler.call(t,e),e.type="mouseover"),n}}),Uy.register("mouseleave",{delegateType:"mouseout",bindType:"mouseout",handle(t,e){let n;const r=e.relatedTarget,i=e.handleObj;return r&&(r===t||Jy.contains(t,r))||(e.type=i.originType,n=i.handler.call(t,e),e.type="mouseout"),n}})}(Xy||(Xy={}));var Zy,Ky,Qy,tb=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};(function(t){let e;function n(t,n,r,s,o){if(!Jy.isValidTarget(t))return;let a;if("function"!==typeof r){const{handler:t,selector:e}=r,n=tb(r,["handler","selector"]);r=t,o=e,a=n}const l=Wy.ensure(t);let c=l.handler;null==c&&(c=l.handler=function(n,...r){return e!==n.type?i(t,n,...r):void 0});const h=Jy.ensureHandlerId(r);Jy.splitType(n).forEach(e=>{const{originType:n,namespaces:i}=Jy.normalizeType(e);if(!n)return;let u=n,d=Uy.get(u);u=(o?d.delegateType:d.bindType)||u,d=Uy.get(u);const g=Object.assign({type:u,originType:n,data:s,selector:o,guid:h,handler:r,namespace:i.join(".")},a),f=l.events;let p=f[u];p||(p=f[u]={handlers:[],delegateCount:0},d.setup&&!1!==d.setup(t,s,i,c)||Jy.addEventListener(t,u,c)),d.add&&(Jy.removeHandlerId(g.handler),d.add(t,g),Jy.setHandlerId(g.handler,h)),o?(p.handlers.splice(p.delegateCount,0,g),p.delegateCount+=1):p.handlers.push(g)})}function r(t,e,n,i,s){const o=Wy.get(t);if(!o)return;const a=o.events;a&&(Jy.splitType(e).forEach(e=>{const{originType:l,namespaces:c}=Jy.normalizeType(e);if(!l)return void Object.keys(a).forEach(s=>{r(t,s+e,n,i,!0)});let h=l;const u=Uy.get(h);h=(i?u.delegateType:u.bindType)||h;const d=a[h];if(!d)return;const g=c.length>0?new RegExp(`(^|\\.)${c.join("\\.(?:.*\\.|)")}(\\.|$)`):null,f=d.handlers.length;for(let r=d.handlers.length-1;r>=0;r-=1){const e=d.handlers[r];!s&&l!==e.originType||n&&Jy.getHandlerId(n)!==e.guid||!(null==g||e.namespace&&g.test(e.namespace))||!(null==i||i===e.selector||"**"===i&&e.selector)||(d.handlers.splice(r,1),e.selector&&(d.delegateCount-=1),u.remove&&u.remove(t,e))}f&&0===d.handlers.length&&(u.teardown&&!1!==u.teardown(t,c,o.handler)||Jy.removeEventListener(t,h,o.handler),delete a[h])}),0===Object.keys(a).length&&Wy.remove(t))}function i(t,e,...n){const r=Yy.create(e);r.delegateTarget=t;const i=Uy.get(r.type);if(i.preDispatch&&!1===i.preDispatch(t,r))return;const s=Jy.getHandlerQueue(t,r);for(let o=0,a=s.length;o<a&&!r.isPropagationStopped();o+=1){const t=s[o];r.currentTarget=t.elem;for(let e=0,i=t.handlers.length;e<i&&!r.isImmediatePropagationStopped();e+=1){const i=t.handlers[e];if(null==r.rnamespace||i.namespace&&r.rnamespace.test(i.namespace)){r.handleObj=i,r.data=i.data;const e=Uy.get(i.originType).handle,s=e?e(t.elem,r,...n):i.handler.call(t.elem,r,...n);void 0!==s&&(r.result=s,!1===s&&(r.preventDefault(),r.stopPropagation()))}}}return i.postDispatch&&i.postDispatch(t,r),r.result}function s(t,n,r,i){let s=t,o="string"===typeof t?t:t.type,a="string"===typeof t||null==s.namespace?[]:s.namespace.split(".");const l=r;if(3===l.nodeType||8===l.nodeType)return;o.indexOf(".")>-1&&(a=o.split("."),o=a.shift(),a.sort());const c=o.indexOf(":")<0&&"on"+o;s=t instanceof Yy?t:new Yy(o,"object"===typeof t?t:null),s.namespace=a.join("."),s.rnamespace=s.namespace?new RegExp(`(^|\\.)${a.join("\\.(?:.*\\.|)")}(\\.|$)`):null,s.result=void 0,s.target||(s.target=l);const h=[s];Array.isArray(n)?h.push(...n):h.push(n);const u=Uy.get(o);if(!i&&u.trigger&&!1===u.trigger(l,s,n))return;let d;const g=[l];if(!i&&!u.noBubble&&!Jy.isWindow(l)){d=u.delegateType||o;let t=l,e=l.parentNode;while(null!=e)g.push(e),t=e,e=e.parentNode;const n=l.ownerDocument||document;if(t===n){const e=t.defaultView||t.parentWindow||window;g.push(e)}}let f=l;for(let e=0,p=g.length;e<p&&!s.isPropagationStopped();e+=1){const t=g[e];f=t,s.type=e>1?d:u.bindType||o;const n=Wy.get(t);n&&n.events[s.type]&&n.handler&&n.handler.call(t,...h);const r=c&&t[c]||null;r&&Jy.isValidTarget(t)&&(s.result=r.call(t,...h),!1===s.result&&s.preventDefault())}if(s.type=o,!i&&!s.isDefaultPrevented()){const t=u.preventDefault;if((null==t||!1===t(g.pop(),s,n))&&Jy.isValidTarget(l)&&c&&"function"===typeof l[o]&&!Jy.isWindow(l)){const t=l[c];t&&(l[c]=null),e=o,s.isPropagationStopped()&&f.addEventListener(o,Jy.stopPropagationCallback),l[o](),s.isPropagationStopped()&&f.removeEventListener(o,Jy.stopPropagationCallback),e=void 0,t&&(l[c]=t)}}return s.result}t.on=n,t.off=r,t.dispatch=i,t.trigger=s})(Zy||(Zy={})),function(t){function e(t,e,n,r,i){return Qy.on(t,e,n,r,i),t}function n(t,e,n,r,i){return Qy.on(t,e,n,r,i,!0),t}function r(t,e,n,r){return Qy.off(t,e,n,r),t}function i(t,e,n,r){return Zy.trigger(e,n,t,r),t}t.on=e,t.once=n,t.off=r,t.trigger=i}(Ky||(Ky={})),function(t){function e(n,r,i,s,o,a){if("object"===typeof r)return"string"!==typeof i&&(s=s||i,i=void 0),void Object.keys(r).forEach(t=>e(n,t,i,s,r[t],a));if(null==s&&null==o?(o=i,s=i=void 0):null==o&&("string"===typeof i?(o=s,s=void 0):(o=s,s=i,i=void 0)),!1===o)o=Jy.returnFalse;else if(!o)return;if(a){const e=o;o=function(r,...i){return t.off(n,r),e.call(this,r,...i)},Jy.setHandlerId(o,Jy.ensureHandlerId(e))}Zy.on(n,r,o,s,i)}function n(t,e,r,i){const s=e;if(s&&null!=s.preventDefault&&null!=s.handleObj){const t=s.handleObj;n(s.delegateTarget,t.namespace?`${t.originType}.${t.namespace}`:t.originType,t.selector,t.handler)}else if("object"!==typeof e)!1!==r&&"function"!==typeof r||(i=r,r=void 0),!1===i&&(i=Jy.returnFalse),Zy.off(t,e,i,r);else{const i=e;Object.keys(i).forEach(e=>n(t,e,r,i[e]))}}t.on=e,t.off=n}(Qy||(Qy={}));class eb{constructor(t,e,n){this.animationFrameId=0,this.deltaX=0,this.deltaY=0,this.eventName=jp.isEventSupported("wheel")?"wheel":"mousewheel",this.target=t,this.onWheelCallback=e,this.onWheelGuard=n,this.onWheel=this.onWheel.bind(this),this.didWheel=this.didWheel.bind(this)}enable(){this.target.addEventListener(this.eventName,this.onWheel,{passive:!1})}disable(){this.target.removeEventListener(this.eventName,this.onWheel)}onWheel(t){if(null!=this.onWheelGuard&&!this.onWheelGuard(t))return;let e;this.deltaX+=t.deltaX,this.deltaY+=t.deltaY,t.preventDefault(),0===this.deltaX&&0===this.deltaY||(t.stopPropagation(),e=!0),!0===e&&0===this.animationFrameId&&(this.animationFrameId=requestAnimationFrame(()=>{this.didWheel(t)}))}didWheel(t){this.animationFrameId=0,this.onWheelCallback(t,this.deltaX,this.deltaY),this.deltaX=0,this.deltaY=0}}function nb(t){const e=t.getBoundingClientRect(),n=t.ownerDocument.defaultView;return{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}}function rb(t){const e=t.getBoundingClientRect();return e.width}function ib(t){const e=t.getBoundingClientRect();return e.height}function sb(t){const e="fixed"===_m(t,"position");let n;if(e){const e=t.getBoundingClientRect();n={left:e.left,top:e.top}}else n=nb(t);if(!e){const e=t.ownerDocument;let r=t.offsetParent||e.documentElement;while((r===e.body||r===e.documentElement)&&"static"===_m(r,"position"))r=r.parentNode;if(r!==t&&cm(r)){const t=nb(r);n.top-=t.top+Hm(r,"borderTopWidth"),n.left-=t.left+Hm(r,"borderLeftWidth")}}return{top:n.top-Hm(t,"marginTop"),left:n.left-Hm(t,"marginLeft")}}function ob(t,e=60){let n=null;return(...r)=>{n&&clearTimeout(n),n=window.setTimeout(()=>{t.apply(this,r)},e)}}function ab(t){let e=null,n=[];const r=()=>{if("static"===getComputedStyle(t).position){const e=t.style;e.position="relative"}const e=document.createElement("object");return e.onload=()=>{e.contentDocument.defaultView.addEventListener("resize",i),i()},e.style.display="block",e.style.position="absolute",e.style.top="0",e.style.left="0",e.style.height="100%",e.style.width="100%",e.style.overflow="hidden",e.style.pointerEvents="none",e.style.zIndex="-1",e.style.opacity="0",e.setAttribute("tabindex","-1"),e.type="text/html",t.appendChild(e),e.data="about:blank",e},i=ob(()=>{n.forEach(e=>e(t))}),s=t=>{e||(e=r()),-1===n.indexOf(t)&&n.push(t)},o=()=>{e&&e.parentNode&&(e.contentDocument&&e.contentDocument.defaultView.removeEventListener("resize",i),e.parentNode.removeChild(e),e=null,n=[])},a=t=>{const r=n.indexOf(t);-1!==r&&n.splice(r,1),0===n.length&&e&&o()};return{element:t,bind:s,destroy:o,unbind:a}}function lb(t){let e=null,n=[];const r=ob(()=>{n.forEach(e=>{e(t)})}),i=()=>{const e=new ResizeObserver(r);return e.observe(t),r(),e},s=t=>{e||(e=i()),-1===n.indexOf(t)&&n.push(t)},o=()=>{e&&(e.disconnect(),n=[],e=null)},a=t=>{const r=n.indexOf(t);-1!==r&&n.splice(r,1),0===n.length&&e&&o()};return{element:t,bind:s,destroy:o,unbind:a}}const cb="undefined"!==typeof ResizeObserver?lb:ab;var hb,ub,db,gb,fb;(function(t){const e=new WeakMap;function n(t){let n=e.get(t);return n||(n=cb(t),e.set(t,n),n)}function r(t){t.destroy(),e.delete(t.element)}t.bind=(t,e)=>{const r=n(t);return r.bind(e),()=>r.unbind(e)},t.clear=t=>{const e=n(t);r(e)}})(hb||(hb={}));class pb{constructor(t={}){this.comparator=t.comparator||pb.defaultComparator,this.index={},this.data=t.data||[],this.heapify()}isEmpty(){return 0===this.data.length}insert(t,e,n){const r={priority:t,value:e},i=this.data.length;return n&&(r.id=n,this.index[n]=i),this.data.push(r),this.bubbleUp(i),this}peek(){return this.data[0]?this.data[0].value:null}peekPriority(){return this.data[0]?this.data[0].priority:null}updatePriority(t,e){const n=this.index[t];if("undefined"===typeof n)throw new Error(`Node with id '${t}' was not found in the heap.`);const r=this.data,i=r[n].priority,s=this.comparator(e,i);s<0?(r[n].priority=e,this.bubbleUp(n)):s>0&&(r[n].priority=e,this.bubbleDown(n))}remove(){const t=this.data,e=t[0],n=t.pop();return e.id&&delete this.index[e.id],t.length>0&&(t[0]=n,n.id&&(this.index[n.id]=0),this.bubbleDown(0)),e?e.value:null}heapify(){for(let t=0;t<this.data.length;t+=1)this.bubbleUp(t)}bubbleUp(t){const e=this.data;let n,r,i=t;while(i>0){if(r=i-1>>>1,!(this.comparator(e[i].priority,e[r].priority)<0))break;{n=e[r],e[r]=e[i];let t=e[i].id;null!=t&&(this.index[t]=r),e[i]=n,t=e[i].id,null!=t&&(this.index[t]=i),i=r}}}bubbleDown(t){const e=this.data,n=e.length-1;let r=t;while(1){const t=1+(r<<1),i=t+1;let s=r;if(t<=n&&this.comparator(e[t].priority,e[s].priority)<0&&(s=t),i<=n&&this.comparator(e[i].priority,e[s].priority)<0&&(s=i),s===r)break;{const t=e[s];e[s]=e[r];let n=e[r].id;null!=n&&(this.index[n]=s),e[r]=t,n=e[r].id,null!=n&&(this.index[n]=r),r=s}}}}(function(t){t.defaultComparator=(t,e)=>t-e})(pb||(pb={})),function(t){function e(t,e,n=((t,e)=>1)){const r={},i={},s={},o=new pb;r[e]=0,Object.keys(t).forEach(t=>{t!==e&&(r[t]=1/0),o.insert(r[t],t,t)});while(!o.isEmpty()){const e=o.remove();s[e]=!0;const a=t[e]||[];for(let t=0;t<a.length;t+=1){const l=a[t];if(!s[l]){const t=r[e]+n(e,l);t<r[l]&&(r[l]=t,i[l]=e,o.updatePriority(l,t))}}}return i}t.run=e}(ub||(ub={}));class mb{constructor(t,e,n,r){return null==t?this.set(255,255,255,1):"number"===typeof t?this.set(t,e,n,r):"string"===typeof t?mb.fromString(t)||this:Array.isArray(t)?this.set(t):void this.set(t.r,t.g,t.b,null==t.a?1:t.a)}blend(t,e,n){this.set(t.r+(e.r-t.r)*n,t.g+(e.g-t.g)*n,t.b+(e.b-t.b)*n,t.a+(e.a-t.a)*n)}lighten(t){const e=mb.lighten(this.toArray(),t);this.r=e[0],this.g=e[1],this.b=e[2],this.a=e[3]}darken(t){this.lighten(-t)}set(t,e,n,r){const i=Array.isArray(t)?t[0]:t,s=Array.isArray(t)?t[1]:e,o=Array.isArray(t)?t[2]:n,l=Array.isArray(t)?t[3]:r;return this.r=Math.round(a.clamp(i,0,255)),this.g=Math.round(a.clamp(s,0,255)),this.b=Math.round(a.clamp(o,0,255)),this.a=null==l?1:a.clamp(l,0,1),this}toHex(){const t=["r","g","b"].map(t=>{const e=this[t].toString(16);return e.length<2?"0"+e:e});return"#"+t.join("")}toRGBA(){return this.toArray()}toHSLA(){return mb.rgba2hsla(this.r,this.g,this.b,this.a)}toCSS(t){const e=`${this.r},${this.g},${this.b},`;return t?`rgb(${e})`:`rgba(${e},${this.a})`}toGrey(){return mb.makeGrey(Math.round((this.r+this.g+this.b)/3),this.a)}toArray(){return[this.r,this.g,this.b,this.a]}toString(){return this.toCSS()}}(function(t){function e(e){return new t(e)}function n(e){return new t([...p(e),1])}function r(e){const n=e.toLowerCase().match(/^rgba?\(([\s.,0-9]+)\)/);if(n){const e=n[1].split(/\s*,\s*/).map(t=>parseInt(t,10));return new t(e)}return null}function i(t,e,n){n<0&&++n,n>1&&--n;const r=6*n;return r<1?t+(e-t)*r:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function s(e){const n=e.toLowerCase().match(/^hsla?\(([\s.,0-9]+)\)/);if(n){const e=n[2].split(/\s*,\s*/),r=(parseFloat(e[0])%360+360)%360/360,i=parseFloat(e[1])/100,s=parseFloat(e[2])/100,o=null==e[3]?1:parseInt(e[3],10);return new t(h(r,i,s,o))}return null}function o(e){if(e.startsWith("#"))return n(e);if(e.startsWith("rgb"))return r(e);const i=t.named[e];return i?n(i):s(e)}function l(e,n){return t.fromArray([e,e,e,n])}function c(t,e,n,r){const i=Array.isArray(t)?t[0]:t,s=Array.isArray(t)?t[1]:e,o=Array.isArray(t)?t[2]:n,a=Array.isArray(t)?t[3]:r,l=Math.max(i,s,o),c=Math.min(i,s,o),h=(l+c)/2;let u=0,d=0;if(c!==l){const t=l-c;switch(d=h>.5?t/(2-l-c):t/(l+c),l){case i:u=(s-o)/t+(s<o?6:0);break;case s:u=(o-i)/t+2;break;case o:u=(i-s)/t+4;break;default:break}u/=6}return[u,d,h,null==a?1:a]}function h(t,e,n,r){const s=Array.isArray(t)?t[0]:t,o=Array.isArray(t)?t[1]:e,a=Array.isArray(t)?t[2]:n,l=Array.isArray(t)?t[3]:r,c=a<=.5?a*(o+1):a+o-a*o,h=2*a-c;return[256*i(h,c,s+1/3),256*i(h,c,s),256*i(h,c,s-1/3),null==l?1:l]}function u(e){return new t(Math.round(256*Math.random()),Math.round(256*Math.random()),Math.round(256*Math.random()),e?void 0:parseFloat(Math.random().toFixed(2)))}function d(){const t="0123456789ABCDEF";let e="#";for(let n=0;n<6;n+=1)e+=t[Math.floor(16*Math.random())];return e}function g(t){return u(t).toString()}function f(t,e){if("string"===typeof t){const n="#"===t[0],[r,i,s]=p(t);return e?.299*r+.587*i+.114*s>186?"#000000":"#ffffff":`${n?"#":""}${m(255-r,255-i,255-s)}`}const n=t[0],r=t[1],i=t[2],s=t[3];return e?.299*n+.587*r+.114*i>186?[0,0,0,s]:[255,255,255,s]:[255-n,255-r,255-i,s]}function p(t){const e=0===t.indexOf("#")?t:"#"+t;let n=Number("0x"+e.substr(1));if(4!==e.length&&7!==e.length||Number.isNaN(n))throw new Error("Invalid hex color.");const r=4===e.length?4:8,i=(1<<r)-1,s=["b","g","r"].map(()=>{const t=n&i;return n>>=r,4===r?17*t:t});return[s[2],s[1],s[0]]}function m(t,e,n){const r=t=>t.length<2?"0"+t:t;return`${r(t.toString(16))}${r(e.toString(16))}${r(n.toString(16))}`}function y(t,e){return v(t,e)}function b(t,e){return v(t,-e)}function v(t,e){if("string"===typeof t){const n="#"===t[0],r=parseInt(n?t.substr(1):t,16),i=a.clamp((r>>16)+e,0,255),s=a.clamp((r>>8&255)+e,0,255),o=a.clamp((255&r)+e,0,255);return`${n?"#":""}${(o|s<<8|i<<16).toString(16)}`}const n=m(t[0],t[1],t[2]),r=p(v(n,e));return[r[0],r[1],r[2],t[3]]}t.fromArray=e,t.fromHex=n,t.fromRGBA=r,t.fromHSLA=s,t.fromString=o,t.makeGrey=l,t.rgba2hsla=c,t.hsla2rgba=h,t.random=u,t.randomHex=d,t.randomRGBA=g,t.invert=f,t.lighten=y,t.darken=b})(mb||(mb={})),function(t){t.named={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",burntsienna:"#ea7e5d",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"}}(mb||(mb={}));class yb{constructor(){this.clear()}clear(){this.map=new WeakMap,this.arr=[]}has(t){return this.map.has(t)}get(t){return this.map.get(t)}set(t,e){this.map.set(t,e),this.arr.push(t)}delete(t){const e=this.arr.indexOf(t);e>=0&&this.arr.splice(e,1);const n=this.map.get(t);return this.map.delete(t),n}each(t){this.arr.forEach(e=>{const n=this.map.get(e);t(n,e)})}dispose(){this.clear()}}(function(t){function e(t){const e=[],n=[];return Array.isArray(t)?e.push(...t):t.split("|").forEach(t=>{-1===t.indexOf("&")?e.push(t):n.push(...t.split("&"))}),{or:e,and:n}}function n(t,n){if(null!=t&&null!=n){const r=e(t),i=e(n),s=r.or.sort(),o=i.or.sort(),a=r.and.sort(),l=i.and.sort(),c=(t,e)=>t.length===e.length&&(0===t.length||t.every((t,n)=>t===e[n]));return c(s,o)&&c(a,l)}return null==t&&null==n}function r(t,n,r){if(null==n||Array.isArray(n)&&0===n.length)return!r||!0!==t.altKey&&!0!==t.ctrlKey&&!0!==t.metaKey&&!0!==t.shiftKey;const{or:i,and:s}=e(n),o=e=>{const n=e.toLowerCase()+"Key";return!0===t[n]};return i.some(t=>o(t))&&s.every(t=>o(t))}t.parse=e,t.equals=n,t.isMatch=r})(db||(db={})),function(t){t.linear=t=>t,t.quad=t=>t*t,t.cubic=t=>t*t*t,t.inout=t=>{if(t<=0)return 0;if(t>=1)return 1;const e=t*t,n=e*t;return 4*(t<.5?n:3*(t-e)+n-.75)},t.exponential=t=>Math.pow(2,10*(t-1)),t.bounce=t=>{for(let e=0,n=1;1;e+=n,n/=2)if(t>=(7-4*e)/11){const r=(11-6*e-11*t)/4;return-r*r+n*n}}}(gb||(gb={})),function(t){t.decorators={reverse(t){return e=>1-t(1-e)},reflect(t){return e=>.5*(e<.5?t(2*e):2-t(2-2*e))},clamp(t,e=0,n=1){return r=>{const i=t(r);return i<e?e:i>n?n:i}},back(t=1.70158){return e=>e*e*((t+1)*e-t)},elastic(t=1.5){return e=>Math.pow(2,10*(e-1))*Math.cos(20*Math.PI*t/3*e)}}}(gb||(gb={})),function(t){function e(t){return-1*Math.cos(t*(Math.PI/2))+1}function n(t){return Math.sin(t*(Math.PI/2))}function r(t){return-.5*(Math.cos(Math.PI*t)-1)}function i(t){return t*t}function s(t){return t*(2-t)}function o(t){return t<.5?2*t*t:(4-2*t)*t-1}function a(t){return t*t*t}function l(t){const e=t-1;return e*e*e+1}function c(t){return t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1}function h(t){return t*t*t*t}function u(t){const e=t-1;return 1-e*e*e*e}function d(t){const e=t-1;return t<.5?8*t*t*t*t:1-8*e*e*e*e}function g(t){return t*t*t*t*t}function f(t){const e=t-1;return 1+e*e*e*e*e}function p(t){const e=t-1;return t<.5?16*t*t*t*t*t:1+16*e*e*e*e*e}function m(t){return 0===t?0:Math.pow(2,10*(t-1))}function y(t){return 1===t?1:1-Math.pow(2,-10*t)}function b(t){if(0===t||1===t)return t;const e=2*t,n=e-1;return e<1?.5*Math.pow(2,10*n):.5*(2-Math.pow(2,-10*n))}function v(t){const e=t/1;return-1*(Math.sqrt(1-e*t)-1)}function w(t){const e=t-1;return Math.sqrt(1-e*e)}function x(t){const e=2*t,n=e-2;return e<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-n*n)+1)}function P(t,e=1.70158){return t*t*((e+1)*t-e)}function C(t,e=1.70158){const n=t/1-1;return n*n*((e+1)*n+e)+1}function A(t,e=1.70158){const n=2*t,r=n-2,i=1.525*e;return n<1?.5*n*n*((i+1)*n-i):.5*(r*r*((i+1)*r+i)+2)}function O(t,e=.7){if(0===t||1===t)return t;const n=t/1,r=n-1,i=1-e,s=i/(2*Math.PI)*Math.asin(1);return-Math.pow(2,10*r)*Math.sin((r-s)*(2*Math.PI)/i)}function E(t,e=.7){const n=1-e,r=2*t;if(0===t||1===t)return t;const i=n/(2*Math.PI)*Math.asin(1);return Math.pow(2,-10*r)*Math.sin((r-i)*(2*Math.PI)/n)+1}function S(t,e=.65){const n=1-e;if(0===t||1===t)return t;const r=2*t,i=r-1,s=n/(2*Math.PI)*Math.asin(1);return r<1?Math.pow(2,10*i)*Math.sin((i-s)*(2*Math.PI)/n)*-.5:Math.pow(2,-10*i)*Math.sin((i-s)*(2*Math.PI)/n)*.5+1}function M(t){const e=t/1;if(e<1/2.75)return 7.5625*e*e;if(e<2/2.75){const t=e-1.5/2.75;return 7.5625*t*t+.75}if(e<2.5/2.75){const t=e-2.25/2.75;return 7.5625*t*t+.9375}{const t=e-2.625/2.75;return 7.5625*t*t+.984375}}function T(t){return 1-M(1-t)}function j(t){return t<.5?.5*T(2*t):.5*M(2*t-1)+.5}t.easeInSine=e,t.easeOutSine=n,t.easeInOutSine=r,t.easeInQuad=i,t.easeOutQuad=s,t.easeInOutQuad=o,t.easeInCubic=a,t.easeOutCubic=l,t.easeInOutCubic=c,t.easeInQuart=h,t.easeOutQuart=u,t.easeInOutQuart=d,t.easeInQuint=g,t.easeOutQuint=f,t.easeInOutQuint=p,t.easeInExpo=m,t.easeOutExpo=y,t.easeInOutExpo=b,t.easeInCirc=v,t.easeOutCirc=w,t.easeInOutCirc=x,t.easeInBack=P,t.easeOutBack=C,t.easeInOutBack=A,t.easeInElastic=O,t.easeOutElastic=E,t.easeInOutElastic=S,t.easeOutBounce=M,t.easeInBounce=T,t.easeInOutBounce=j}(gb||(gb={})),function(t){t.number=(t,e)=>{const n=e-t;return e=>t+n*e},t.object=(t,e)=>{const n=Object.keys(t);return r=>{const i={};for(let s=n.length-1;-1!==s;s-=1){const o=n[s];i[o]=t[o]+(e[o]-t[o])*r}return i}},t.unit=(t,e)=>{const n=/(-?[0-9]*.[0-9]*)(px|em|cm|mm|in|pt|pc|%)/,r=n.exec(t),i=n.exec(e),s=i?i[1]:"",o=r?+r[1]:0,a=i?+i[1]:0,l=s.indexOf("."),c=l>0?s[1].length-l-1:0,h=a-o,u=r?r[2]:"";return t=>(o+h*t).toFixed(c)+u},t.color=(t,e)=>{const n=parseInt(t.slice(1),16),r=parseInt(e.slice(1),16),i=255&n,s=(255&r)-i,o=65280&n,a=(65280&r)-o,l=16711680&n,c=(16711680&r)-l;return t=>{const e=i+s*t&255,n=o+a*t&65280,r=l+c*t&16711680;return"#"+(1<<24|e|n|r).toString(16).slice(1)}}}(fb||(fb={}));const bb=[];function vb(t,e){const n=bb.find(e=>e.name===t);if(!(n&&(n.loadTimes+=1,n.loadTimes>1))&&!jp.isApplyingHMR()){const n=document.createElement("style");n.setAttribute("type","text/css"),n.textContent=e;const r=document.querySelector("head");r&&r.insertBefore(n,r.firstChild),bb.push({name:t,loadTimes:1,styleElement:n})}}function wb(t){const e=bb.findIndex(e=>e.name===t);if(e>-1){const t=bb[e];if(t.loadTimes-=1,t.loadTimes>0)return;let n=t.styleElement;n&&n.parentNode&&n.parentNode.removeChild(n),n=null,bb.splice(e,1)}}var xb=n("7514");class Pb{constructor(t){this.options=Object.assign({},t),this.data=this.options.data||{},this.register=this.register.bind(this),this.unregister=this.unregister.bind(this)}get names(){return Object.keys(this.data)}register(t,e,n=!1){if("object"===typeof t)return void Object.entries(t).forEach(([t,n])=>{this.register(t,n,e)});!this.exist(t)||n||jp.isApplyingHMR()||this.onDuplicated(t);const i=this.options.process,s=i?r.call(i,this,t,e):e;return this.data[t]=s,s}unregister(t){const e=t?this.data[t]:null;return delete this.data[t],e}get(t){return t?this.data[t]:null}exist(t){return!!t&&null!=this.data[t]}onDuplicated(t){try{throw this.options.onConflict&&r.call(this.options.onConflict,this,t),new Error(`${o.upperFirst(this.options.type)} with name '${t}' already registered.`)}catch(aO){throw aO}}onNotFound(t,e){throw new Error(this.getSpellingSuggestion(t,e))}getSpellingSuggestion(t,e){const n=this.getSpellingSuggestionForName(t),r=e?`${e} ${o.lowerFirst(this.options.type)}`:this.options.type;return`${o.upperFirst(r)} with name '${t}' does not exist.${n?` Did you mean '${n}'?`:""}`}getSpellingSuggestionForName(t){return o.getSpellingSuggestion(t,Object.keys(this.data),t=>t)}}(function(t){function e(e){return new t(e)}t.create=e})(Pb||(Pb={}));const Cb={color:"#aaaaaa",thickness:1,markup:"rect",update(t,e){const n=e.thickness*e.sx,r=e.thickness*e.sy;c.attr(t,{width:n,height:r,rx:n,ry:r,fill:e.color})}},Ab={color:"#aaaaaa",thickness:1,markup:"rect",update(t,e){const n=e.sx<=1?e.thickness*e.sx:e.thickness;c.attr(t,{width:n,height:n,rx:n,ry:n,fill:e.color})}},Ob={color:"rgba(224,224,224,1)",thickness:1,markup:"path",update(t,e){let n;const r=e.width,i=e.height,s=e.thickness;n=r-s>=0&&i-s>=0?["M",r,0,"H0 M0 0 V0",i].join(" "):"M 0 0 0 0",c.attr(t,{d:n,stroke:e.color,"stroke-width":e.thickness})}},Eb=[{color:"rgba(224,224,224,1)",thickness:1,markup:"path",update(t,e){let n;const r=e.width,i=e.height,s=e.thickness;n=r-s>=0&&i-s>=0?["M",r,0,"H0 M0 0 V0",i].join(" "):"M 0 0 0 0",c.attr(t,{d:n,stroke:e.color,"stroke-width":e.thickness})}},{color:"rgba(224,224,224,0.2)",thickness:3,factor:4,markup:"path",update(t,e){let n;const r=e.factor||1,i=e.width*r,s=e.height*r,o=e.thickness;n=i-o>=0&&s-o>=0?["M",i,0,"H0 M0 0 V0",s].join(" "):"M 0 0 0 0",e.width=i,e.height=s,c.attr(t,{d:n,stroke:e.color,"stroke-width":e.thickness})}}];class Sb{constructor(){this.patterns={},this.root=ty.create(c.createSvgDocument(),{width:"100%",height:"100%"},[c.createSvgElement("defs")]).node}add(t,e){const n=this.root.childNodes[0];n&&n.appendChild(e),this.patterns[t]=e,ty.create("rect",{width:"100%",height:"100%",fill:`url(#${t})`}).appendTo(this.root)}get(t){return this.patterns[t]}has(t){return null!=this.patterns[t]}}(function(t){t.presets=u,t.registry=Pb.create({type:"grid"}),t.registry.register(t.presets,!0)})(Sb||(Sb={}));const Mb=function(t){const e=document.createElement("canvas"),n=t.width,r=t.height;e.width=2*n,e.height=r;const i=e.getContext("2d");return i.drawImage(t,0,0,n,r),i.translate(2*n,0),i.scale(-1,1),i.drawImage(t,0,0,n,r),e},Tb=function(t){const e=document.createElement("canvas"),n=t.width,r=t.height;e.width=n,e.height=2*r;const i=e.getContext("2d");return i.drawImage(t,0,0,n,r),i.translate(0,2*r),i.scale(1,-1),i.drawImage(t,0,0,n,r),e},jb=function(t){const e=document.createElement("canvas"),n=t.width,r=t.height;e.width=2*n,e.height=2*r;const i=e.getContext("2d");return i.drawImage(t,0,0,n,r),i.setTransform(-1,0,0,-1,e.width,e.height),i.drawImage(t,0,0,n,r),i.setTransform(-1,0,0,1,e.width,0),i.drawImage(t,0,0,n,r),i.setTransform(1,0,0,-1,0,e.height),i.drawImage(t,0,0,n,r),e},kb=function(t,e){const n=t.width,r=t.height,i=document.createElement("canvas");i.width=3*n,i.height=3*r;const s=i.getContext("2d"),o=null!=e.angle?-e.angle:-20,a=xb["Angle"].toRad(o),l=i.width/4,c=i.height/4;for(let h=0;h<4;h+=1)for(let e=0;e<4;e+=1)(h+e)%2>0&&(s.setTransform(1,0,0,1,(2*h-1)*l,(2*e-1)*c),s.rotate(a),s.drawImage(t,-n/2,-r/2,n,r));return i};var Nb,Lb;function Rb(t,e){return null!=t?t:e}function Bb(t,e){return null!=t&&Number.isFinite(t)?t:e}function Db(t={}){const e=Rb(t.color,"blue"),n=Bb(t.width,1),r=Bb(t.margin,2),i=Bb(t.opacity,1),s=r,o=r+n;return`\n    <filter>\n      <feFlood flood-color="${e}" flood-opacity="${i}" result="colored"/>\n      <feMorphology in="SourceAlpha" result="morphedOuter" operator="dilate" radius="${o}" />\n      <feMorphology in="SourceAlpha" result="morphedInner" operator="dilate" radius="${s}" />\n      <feComposite result="morphedOuterColored" in="colored" in2="morphedOuter" operator="in"/>\n      <feComposite operator="xor" in="morphedOuterColored" in2="morphedInner" result="outline"/>\n      <feMerge>\n        <feMergeNode in="outline"/>\n        <feMergeNode in="SourceGraphic"/>\n      </feMerge>\n    </filter>\n  `.trim()}function Ib(t={}){const e=Rb(t.color,"red"),n=Bb(t.blur,0),r=Bb(t.width,1),i=Bb(t.opacity,1);return`\n      <filter>\n        <feFlood flood-color="${e}" flood-opacity="${i}" result="colored"/>\n        <feMorphology result="morphed" in="SourceGraphic" operator="dilate" radius="${r}"/>\n        <feComposite result="composed" in="colored" in2="morphed" operator="in"/>\n        <feGaussianBlur result="blured" in="composed" stdDeviation="${n}"/>\n        <feBlend in="SourceGraphic" in2="blured" mode="normal"/>\n      </filter>\n    `.trim()}function Vb(t={}){const e=Bb(t.x,2),n=null!=t.y&&Number.isFinite(t.y)?[e,t.y]:e;return`\n    <filter>\n      <feGaussianBlur stdDeviation="${n}"/>\n    </filter>\n  `.trim()}function zb(t={}){const e=Bb(t.dx,0),n=Bb(t.dy,0),r=Rb(t.color,"black"),i=Bb(t.blur,4),s=Bb(t.opacity,1);return"SVGFEDropShadowElement"in window?`<filter>\n         <feDropShadow stdDeviation="${i}" dx="${e}" dy="${n}" flood-color="${r}" flood-opacity="${s}" />\n       </filter>`.trim():`<filter>\n         <feGaussianBlur in="SourceAlpha" stdDeviation="${i}" />\n         <feOffset dx="${e}" dy="${n}" result="offsetblur" />\n         <feFlood flood-color="${r}" />\n         <feComposite in2="offsetblur" operator="in" />\n         <feComponentTransfer>\n           <feFuncA type="linear" slope="${s}" />\n         </feComponentTransfer>\n         <feMerge>\n           <feMergeNode/>\n           <feMergeNode in="SourceGraphic"/>\n         </feMerge>\n       </filter>`.trim()}function Fb(t={}){const e=Bb(t.amount,1),n=.2126+.7874*(1-e),r=.7152-.7152*(1-e),i=.0722-.0722*(1-e),s=.2126-.2126*(1-e),o=.7152+.2848*(1-e),a=.0722-.0722*(1-e),l=.2126-.2126*(1-e),c=.0722+.9278*(1-e);return`\n    <filter>\n      <feColorMatrix type="matrix" values="${n} ${r} ${i} 0 0 ${s} ${o} ${a} 0 0 ${l} ${r} ${c} 0 0 0 0 0 1 0"/>\n    </filter>\n  `.trim()}function $b(t={}){const e=Bb(t.amount,1),n=.393+.607*(1-e),r=.769-.769*(1-e),i=.189-.189*(1-e),s=.349-.349*(1-e),o=.686+.314*(1-e),a=.168-.168*(1-e),l=.272-.272*(1-e),c=.534-.534*(1-e),h=.131+.869*(1-e);return`\n      <filter>\n        <feColorMatrix type="matrix" values="${n} ${r} ${i} 0 0 ${s} ${o} ${a} 0 0 ${l} ${c} ${h} 0 0 0 0 0 1 0"/>\n      </filter>\n    `.trim()}function Gb(t={}){const e=Bb(t.amount,1);return`\n      <filter>\n        <feColorMatrix type="saturate" values="${1-e}"/>\n      </filter>\n    `.trim()}function qb(t={}){const e=Bb(t.angle,0);return`\n      <filter>\n        <feColorMatrix type="hueRotate" values="${e}"/>\n      </filter>\n    `.trim()}function _b(t={}){const e=Bb(t.amount,1),n=1-e;return`\n      <filter>\n        <feComponentTransfer>\n          <feFuncR type="table" tableValues="${e} ${n}"/>\n          <feFuncG type="table" tableValues="${e} ${n}"/>\n          <feFuncB type="table" tableValues="${e} ${n}"/>\n        </feComponentTransfer>\n      </filter>\n    `.trim()}function Hb(t={}){const e=Bb(t.amount,1);return`\n    <filter>\n      <feComponentTransfer>\n        <feFuncR type="linear" slope="${e}"/>\n        <feFuncG type="linear" slope="${e}"/>\n        <feFuncB type="linear" slope="${e}"/>\n      </feComponentTransfer>\n    </filter>\n  `.trim()}function Ub(t={}){const e=Bb(t.amount,1),n=.5-e/2;return`\n    <filter>\n     <feComponentTransfer>\n        <feFuncR type="linear" slope="${e}" intercept="${n}"/>\n        <feFuncG type="linear" slope="${e}" intercept="${n}"/>\n        <feFuncB type="linear" slope="${e}" intercept="${n}"/>\n      </feComponentTransfer>\n    </filter>\n  `.trim()}(function(t){t.presets=Object.assign({},d),t.presets["flip-x"]=Mb,t.presets["flip-y"]=Tb,t.presets["flip-xy"]=jb,t.registry=Pb.create({type:"background pattern"}),t.registry.register(t.presets,!0)})(Nb||(Nb={})),function(t){t.presets=g,t.registry=Pb.create({type:"filter"}),t.registry.register(t.presets,!0)}(Lb||(Lb={}));const Wb={xlinkHref:"xlink:href",xlinkShow:"xlink:show",xlinkRole:"xlink:role",xlinkType:"xlink:type",xlinkArcrole:"xlink:arcrole",xlinkTitle:"xlink:title",xlinkActuate:"xlink:actuate",xmlSpace:"xml:space",xmlBase:"xml:base",xmlLang:"xml:lang",preserveAspectRatio:"preserveAspectRatio",requiredExtension:"requiredExtension",requiredFeatures:"requiredFeatures",systemLanguage:"systemLanguage",externalResourcesRequired:"externalResourceRequired"},Jb={},Xb={position:bv("x","width","origin")},Yb={position:bv("y","height","origin")},Zb={position:bv("x","width","corner")},Kb={position:bv("y","height","corner")},Qb={set:vv("width","width")},tv={set:vv("height","height")},ev={set:vv("rx","width")},nv={set:vv("ry","height")},rv={set:(t=>{const e=vv(t,"width"),n=vv(t,"height");return function(t,i){const s=i.refBBox,o=s.height>s.width?e:n;return r.call(o,this,t,i)}})("r")},iv={set(t,{refBBox:e}){let n=parseFloat(t);const r=a.isPercentage(t);r&&(n/=100);const i=Math.sqrt(e.height*e.height+e.width*e.width);let s;return Number.isFinite(n)&&(s=r||n>=0&&n<=1?n*i:Math.max(n+i,0)),{r:s}}},sv={set:vv("cx","width")},ov={set:vv("cy","height")},av={set:xv({resetOffset:!0})},lv={set:xv({resetOffset:!1})},cv={set:Pv({resetOffset:!0})},hv={set:Pv({resetOffset:!1})},uv=rv,dv=av,gv=cv,fv=Xb,pv=Yb,mv=Qb,yv=tv;function bv(t,e,n){return(r,{refBBox:i})=>{if(null==r)return null;let s=parseFloat(r);const o=a.isPercentage(r);let l;if(o&&(s/=100),Number.isFinite(s)){const r=i[n];l=o||s>0&&s<1?r[t]+i[e]*s:r[t]+s}const c=new xb["Point"];return c[t]=l||0,c}}function vv(t,e){return function(n,{refBBox:r}){let i=parseFloat(n);const s=a.isPercentage(n);s&&(i/=100);const o={};if(Number.isFinite(i)){const n=s||i>=0&&i<=1?i*r[e]:Math.max(i+r[e],0);o[t]=n}return o}}function wv(t,e){const n="x6-shape",r=e&&e.resetOffset;return function(e,{elem:i,refBBox:s}){let o=c.data(i,n);if(!o||o.value!==e){const r=t(e);o={value:e,shape:r,shapeBBox:r.bbox()},c.data(i,n,o)}const a=o.shape.clone(),l=o.shapeBBox.clone(),h=l.getOrigin(),u=s.getOrigin();l.x=u.x,l.y=u.y;const d=s.getMaxScaleToFit(l,u),g=0===l.width||0===s.width?1:d.sx,f=0===l.height||0===s.height?1:d.sy;return a.scale(g,f,h),r&&a.translate(-h.x,-h.y),a}}function xv(t){function e(t){return xb["Path"].parse(t)}const n=wv(e,t);return(t,e)=>{const r=n(t,e);return{d:r.serialize()}}}function Pv(t){const e=wv(t=>new xb["Polyline"](t),t);return(t,n)=>{const r=e(t,n);return{points:r.serialize()}}}const Cv={qualify:i.isPlainObject,set(t,{view:e}){return`url(#${e.graph.defineGradient(t)})`}},Av={qualify:i.isPlainObject,set(t,{view:e}){const n=e.cell,r=Object.assign({},t);if(n.isEdge()&&"linearGradient"===r.type){const t=e,i=t.sourcePoint,s=t.targetPoint;r.id=`gradient-${r.type}-${n.id}`,r.attrs=Object.assign(Object.assign({},r.attrs),{x1:i.x,y1:i.y,x2:s.x,y2:s.y,gradientUnits:"userSpaceOnUse"}),e.graph.defs.remove(r.id)}return`url(#${e.graph.defineGradient(r)})`}},Ov={qualify(t,{attrs:e}){return null==e.textWrap||!i.isPlainObject(e.textWrap)},set(t,{view:e,elem:n,attrs:r}){const i="x6-text",s=c.data(n,i),o=t=>{try{return JSON.parse(t)}catch(e){return t}},a={x:r.x,eol:r.eol,annotations:o(r.annotations),textPath:o(r["text-path"]||r.textPath),textVerticalAnchor:r["text-vertical-anchor"]||r.textVerticalAnchor,displayEmpty:"true"===(r["display-empty"]||r.displayEmpty),lineHeight:r["line-height"]||r.lineHeight},l=r["font-size"]||r.fontSize,h=JSON.stringify([t,a]);if(l&&n.setAttribute("font-size",l),null==s||s!==h){const r=a.textPath;if(null!=r&&"object"===typeof r){const t=r.selector;if("string"===typeof t){const n=e.find(t)[0];n instanceof SVGPathElement&&(c.ensureId(n),a.textPath=Object.assign({"xlink:href":"#"+n.id},r))}}c.text(n,""+t,a),c.data(n,i,h)}}},Ev={qualify:i.isPlainObject,set(t,{view:e,elem:n,attrs:i,refBBox:s}){const o=t,l=o.width||0;a.isPercentage(l)?s.width*=parseFloat(l)/100:l<=0?s.width+=l:s.width=l;const h=o.height||0;let u;a.isPercentage(h)?s.height*=parseFloat(h)/100:h<=0?s.height+=h:s.height=h;let d=o.text;null==d&&(d=i.text||(null===n||void 0===n?void 0:n.textContent)),u=null!=d?c.breakText(""+d,s,{"font-weight":i["font-weight"]||i.fontWeight,"font-size":i["font-size"]||i.fontSize,"font-family":i["font-family"]||i.fontFamily,lineHeight:i.lineHeight},{ellipsis:o.ellipsis}):"",r.call(Ov.set,this,u,{view:e,elem:n,attrs:i,refBBox:s,cell:e.cell})}},Sv=(t,{attrs:e})=>void 0!==e.text,Mv={qualify:Sv},Tv={qualify:Sv},jv={qualify:Sv},kv={qualify:Sv},Nv={qualify:Sv},Lv={qualify:Sv},Rv={qualify(t,{elem:e}){return e instanceof SVGElement},set(t,{elem:e}){const n="x6-title",r=""+t,i=c.data(e,n);if(null==i||i!==r){c.data(e,n,r);const t=e.firstChild;if(t&&"TITLE"===t.tagName.toUpperCase()){const e=t;e.textContent=r}else{const n=document.createElementNS(e.namespaceURI,"title");n.textContent=r,e.insertBefore(n,t)}}}},Bv={offset:Vv("x","width","right")},Dv={offset:Vv("y","height","bottom")},Iv={offset(t,{refBBox:e}){return t?{x:-e.x,y:-e.y}:{x:0,y:0}}};function Vv(t,e,n){return(r,{refBBox:i})=>{const s=new xb["Point"];let o;return o="middle"===r?i[e]/2:r===n?i[e]:"number"===typeof r&&Number.isFinite(r)?r>-1&&r<1?-i[e]*r:-r:a.isPercentage(r)?i[e]*parseFloat(r)/100:0,s[t]=-(i[t]+o),s}}const zv={qualify:i.isPlainObject,set(t,{elem:e}){c.css(e,t)}},Fv={set(t,{elem:e}){e.innerHTML=""+t}},$v={qualify:i.isPlainObject,set(t,{view:e}){return`url(#${e.graph.defineFilter(t)})`}},Gv={set(t){return null!=t&&"object"===typeof t&&t.id?t.id:t}};function qv(t,e,n){let r,i;"object"===typeof e?(r=e.x,i=e.y):(r=e,i=n);const s=xb["Path"].parse(t),o=s.bbox();if(o){let t=-o.height/2-o.y,e=-o.width/2-o.x;"number"===typeof r&&(e-=r),"number"===typeof i&&(t-=i),s.translate(e,t)}return s.serialize()}var _v=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const Hv=t=>{var{size:e,width:n,height:r,offset:i,open:s}=t,o=_v(t,["size","width","height","offset","open"]);return Wv({size:e,width:n,height:r,offset:i},!0===s,!0,void 0,o)},Uv=t=>{var{size:e,width:n,height:r,offset:i,factor:s}=t,o=_v(t,["size","width","height","offset","factor"]);return Wv({size:e,width:n,height:r,offset:i},!1,!1,s,o)};function Wv(t,e,n,r=3/4,i={}){const s=t.size||10,o=t.width||s,l=t.height||s,c=new xb["Path"],h={};if(e)c.moveTo(o,0).lineTo(0,l/2).lineTo(o,l),h.fill="none";else{if(c.moveTo(0,l/2),c.lineTo(o,0),!n){const t=a.clamp(r,0,1);c.lineTo(o*t,l/2)}c.lineTo(o,l),c.close()}return Object.assign(Object.assign(Object.assign({},h),i),{tagName:"path",d:qv(c.serialize(),{x:null!=t.offset?t.offset:-o/2})})}var Jv=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const Xv=t=>{var{size:e,width:n,height:r,offset:i}=t,s=Jv(t,["size","width","height","offset"]);const o=e||10,a=n||o,l=r||o,c=new xb["Path"];return c.moveTo(0,l/2).lineTo(a/2,0).lineTo(a,l/2).lineTo(a/2,l).close(),Object.assign(Object.assign({},s),{tagName:"path",d:qv(c.serialize(),null==i?-a/2:i)})};var Yv=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const Zv=t=>{var{d:e,offsetX:n,offsetY:r}=t,i=Yv(t,["d","offsetX","offsetY"]);return Object.assign(Object.assign({},i),{tagName:"path",d:qv(e,n,r)})};var Kv=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const Qv=t=>{var{size:e,width:n,height:r,offset:i}=t,s=Kv(t,["size","width","height","offset"]);const o=e||10,a=n||o,l=r||o,c=new xb["Path"];return c.moveTo(0,0).lineTo(a,l).moveTo(0,l).lineTo(a,0),Object.assign(Object.assign({},s),{tagName:"path",fill:"none",d:qv(c.serialize(),i||-a/2)})};var tw=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const ew=t=>{var{width:e,height:n,offset:r,open:i,flip:s}=t,o=tw(t,["width","height","offset","open","flip"]);let a=n||6;const l=e||10,c=!0===i,h=!0===s,u=Object.assign(Object.assign({},o),{tagName:"path"});h&&(a=-a);const d=new xb["Path"];return d.moveTo(0,a).lineTo(l,0),c?u.fill="none":(d.lineTo(l,a),d.close()),u.d=qv(d.serialize(),{x:r||-l/2,y:a/2}),u};var nw=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const rw=t=>{var{r:e}=t,n=nw(t,["r"]);const r=e||5;return Object.assign(Object.assign({cx:r},n),{tagName:"circle",r:r})},iw=t=>{var{r:e}=t,n=nw(t,["r"]);const r=e||5,i=new xb["Path"];return i.moveTo(r,0).lineTo(r,2*r),i.moveTo(0,r).lineTo(2*r,r),{children:[Object.assign(Object.assign({},rw({r:r})),{fill:"none"}),Object.assign(Object.assign({},n),{tagName:"path",d:qv(i.serialize(),-r)})]}};var sw=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const ow=t=>{var{rx:e,ry:n}=t,r=sw(t,["rx","ry"]);const i=e||5,s=n||5;return Object.assign(Object.assign({cx:i},r),{tagName:"ellipse",rx:i,ry:s})};var aw;(function(t){t.presets=f,t.registry=Pb.create({type:"marker"}),t.registry.register(t.presets,!0)})(aw||(aw={})),function(t){t.normalize=qv}(aw||(aw={}));var lw=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};function cw(t){return"string"===typeof t||i.isPlainObject(t)}const hw={qualify:cw,set(t,{view:e,attrs:n}){return gw("marker-start",t,e,n)}},uw={qualify:cw,set(t,{view:e,attrs:n}){return gw("marker-end",t,e,n,{transform:"rotate(180)"})}},dw={qualify:cw,set(t,{view:e,attrs:n}){return gw("marker-mid",t,e,n)}};function gw(t,e,n,r,i={}){const s="string"===typeof e?{name:e}:e,{name:o,args:a}=s,l=lw(s,["name","args"]);let c=l;if(o&&"string"===typeof o){const t=aw.registry.get(o);if(!t)return aw.registry.onNotFound(o);c=t(Object.assign(Object.assign({},l),a))}const h=Object.assign(Object.assign(Object.assign({},fw(r,t)),i),c);return{[t]:`url(#${n.graph.defineMarker(h)})`}}function fw(t,e){const n={},r=t.stroke;"string"===typeof r&&(n.stroke=r,n.fill=r);let i=t.strokeOpacity;if(null==i&&(i=t["stroke-opacity"]),null==i&&(i=t.opacity),null!=i&&(n["stroke-opacity"]=i,n["fill-opacity"]=i),"marker-mid"!==e){const r=parseFloat(t.strokeWidth||t["stroke-width"]);if(Number.isFinite(r)&&r>1){const t=Math.ceil(r/2);n.refX="marker-start"===e?t:-t}}return n}const pw=(t,{view:e})=>e.cell.isEdge(),mw={qualify:pw,set(t,e){var n,r,i,s;const o=e.view,a=t.reverse||!1,l=t.stubs||0;let c;if(Number.isFinite(l)&&0!==l)if(a){let t,e;const a=o.getConnectionLength()||0;l<0?(t=(a+l)/2,e=-l):(t=l,e=a-2*l);const h=o.getConnection();c=null===(s=null===(i=null===(r=null===(n=null===h||void 0===h?void 0:h.divideAtLength(t))||void 0===n?void 0:n[1])||void 0===r?void 0:r.divideAtLength(e))||void 0===i?void 0:i[0])||void 0===s?void 0:s.serialize()}else{let t;if(l<0){const e=o.getConnectionLength()||0;t=(e+l)/2}else t=l;const e=o.getConnection();if(e){const n=e.divideAtLength(t),r=e.divideAtLength(-t);n&&r&&(c=`${n[0].serialize()} ${r[1].serialize()}`)}}return{d:c||o.getConnectionPathData()}}},yw={qualify:pw,set:Cw("getTangentAtLength",{rotate:!0})},bw={qualify:pw,set:Cw("getTangentAtLength",{rotate:!1})},vw={qualify:pw,set:Cw("getTangentAtRatio",{rotate:!0})},ww={qualify:pw,set:Cw("getTangentAtRatio",{rotate:!1})},xw=yw,Pw=vw;function Cw(t,e){const n={x:1,y:0};return(r,i)=>{let s,o;const a=i.view,l=a[t](Number(r));return l?(o=e.rotate?l.vector().vectorAngle(n):0,s=l.start):(s=a.path.start,o=0),0===o?{transform:`translate(${s.x},${s.y}')`}:{transform:`translate(${s.x},${s.y}') rotate(${o})`}}}var Aw;(function(t){function e(t,e,n){if(null!=t){if("string"===typeof t)return!0;if("function"!==typeof t.qualify||r.call(t.qualify,this,e,n))return!0}return!1}t.isValidDefinition=e})(Aw||(Aw={})),function(t){t.presets=Object.assign(Object.assign({},Wb),p),t.registry=Pb.create({type:"attribute definition"}),t.registry.register(t.presets,!0)}(Aw||(Aw={}));const Ow={prefixCls:"x6",autoInsertCSS:!0,useCSSSelector:!0,prefix(t){return`${Ow.prefixCls}-${t}`}},Ew=Ow.prefix("highlighted"),Sw={highlight(t,e,n){const r=n&&n.className||Ew;c.addClass(e,r)},unhighlight(t,e,n){const r=n&&n.className||Ew;c.removeClass(e,r)}},Mw=Ow.prefix("highlight-opacity"),Tw={highlight(t,e){c.addClass(e,Mw)},unhighlight(t,e){c.removeClass(e,Mw)}};var jw;(function(t){function e(t,e){const n=c.createSVGPoint(t.x,t.y).matrixTransform(e);return new xb["Point"](n.x,n.y)}function n(t,n){return new xb["Line"](e(t.start,n),e(t.end,n))}function r(t,n){let r=t instanceof xb["Polyline"]?t.points:t;return Array.isArray(r)||(r=[]),new xb["Polyline"](r.map(t=>e(t,n)))}function i(t,e){const n=c.createSvgElement("svg"),r=n.createSVGPoint();r.x=t.x,r.y=t.y;const i=r.matrixTransform(e);r.x=t.x+t.width,r.y=t.y;const s=r.matrixTransform(e);r.x=t.x+t.width,r.y=t.y+t.height;const o=r.matrixTransform(e);r.x=t.x,r.y=t.y+t.height;const a=r.matrixTransform(e),l=Math.min(i.x,s.x,o.x,a.x),h=Math.max(i.x,s.x,o.x,a.x),u=Math.min(i.y,s.y,o.y,a.y),d=Math.max(i.y,s.y,o.y,a.y);return new xb["Rectangle"](l,u,h-l,d-u)}function s(t,e,n){let r;const s=t.ownerSVGElement;if(!s)return new xb["Rectangle"](0,0,0,0);try{r=t.getBBox()}catch(a){r={x:t.clientLeft,y:t.clientTop,width:t.clientWidth,height:t.clientHeight}}if(e)return xb["Rectangle"].create(r);const o=c.getTransformToElement(t,n||s);return i(r,o)}function o(t,e={}){let n;const r=t.ownerSVGElement;if(!r||!c.isSVGGraphicsElement(t)){if(c.isHTMLElement(t)){const{left:e,top:n,width:r,height:i}=a(t);return new xb["Rectangle"](e,n,r,i)}return new xb["Rectangle"](0,0,0,0)}let s=e.target;const l=e.recursive;if(!l){try{n=t.getBBox()}catch(h){n={x:t.clientLeft,y:t.clientTop,width:t.clientWidth,height:t.clientHeight}}if(!s)return xb["Rectangle"].create(n);const e=c.getTransformToElement(t,s);return i(n,e)}{const e=t.childNodes,r=e.length;if(0===r)return o(t,{target:s});s||(s=t);for(let t=0;t<r;t+=1){const r=e[t];let i;i=0===r.childNodes.length?o(r,{target:s}):o(r,{target:s,recursive:!0}),n=n?n.union(i):i}return n}}function a(t){let e=0,n=0,r=0,i=0;if(t){let s=t;while(s)e+=s.offsetLeft,n+=s.offsetTop,s=s.offsetParent,s&&(e+=parseInt(c.getComputedStyle(s,"borderLeft"),10),n+=parseInt(c.getComputedStyle(s,"borderTop"),10));r=t.offsetWidth,i=t.offsetHeight}return{left:e,top:n,width:r,height:i}}function l(t){const e=e=>{const n=t.getAttribute(e),r=n?parseFloat(n):0;return Number.isNaN(r)?0:r};switch(t instanceof SVGElement&&t.nodeName.toLowerCase()){case"rect":return new xb["Rectangle"](e("x"),e("y"),e("width"),e("height"));case"circle":return new xb["Ellipse"](e("cx"),e("cy"),e("r"),e("r"));case"ellipse":return new xb["Ellipse"](e("cx"),e("cy"),e("rx"),e("ry"));case"polyline":{const e=c.getPointsFromSvgElement(t);return new xb["Polyline"](e)}case"polygon":{const e=c.getPointsFromSvgElement(t);return e.length>1&&e.push(e[0]),new xb["Polyline"](e)}case"path":{let e=t.getAttribute("d");return xb["Path"].isValid(e)||(e=xb["Path"].normalize(e)),xb["Path"].parse(e)}case"line":return new xb["Line"](e("x1"),e("y1"),e("x2"),e("y2"));default:break}return o(t)}function h(t,e,n,r){const i=xb["Point"].create(e),s=xb["Point"].create(n);if(!r){const e=t instanceof SVGSVGElement?t:t.ownerSVGElement;r=e}const a=c.scale(t);t.setAttribute("transform","");const l=o(t,{target:r}).scale(a.sx,a.sy),h=c.createSVGTransform();h.setTranslate(-l.x-l.width/2,-l.y-l.height/2);const u=c.createSVGTransform(),d=i.angleBetween(s,i.clone().translate(1,0));d&&u.setRotate(d,0,0);const g=c.createSVGTransform(),f=i.clone().move(s,l.width/2);g.setTranslate(2*i.x-f.x,2*i.y-f.y);const p=c.getTransformToElement(t,r),m=c.createSVGTransform();m.setMatrix(g.matrix.multiply(u.matrix.multiply(h.matrix.multiply(p.scale(a.sx,a.sy))))),t.setAttribute("transform",c.matrixToTransformString(m.matrix))}function u(t){if(null==t)return null;let e=t;do{let t=e.tagName;if("string"!==typeof t)return null;if(t=t.toUpperCase(),c.hasClass(e,"x6-port"))e=e.nextElementSibling;else if("G"===t)e=e.firstElementChild;else{if("TITLE"!==t)break;e=e.nextElementSibling}}while(e);return e}function d(t){const e=u(t);if(!c.isSVGGraphicsElement(e)){if(c.isHTMLElement(t)){const{left:e,top:n,width:r,height:i}=a(t);return new xb["Rectangle"](e,n,r,i)}return new xb["Rectangle"](0,0,0,0)}const n=l(e),r=n.bbox()||xb["Rectangle"].create();return r}t.normalizeMarker=qv,t.transformPoint=e,t.transformLine=n,t.transformPolyline=r,t.transformRectangle=i,t.bbox=s,t.getBBox=o,t.getBoundingOffsetRect=a,t.toGeometryShape=l,t.translateAndAutoOrient=h,t.findShapeNode=u,t.getBBoxV2=d})(jw||(jw={}));const kw={padding:3,rx:0,ry:0,attrs:{"stroke-width":3,stroke:"#FEB663"}},Nw={highlight(t,e,n){const r=Lw.getHighlighterId(e,n);if(Lw.hasCache(r))return;n=i.defaultsDeep({},n,kw);const s=ty.create(e);let o,a;try{o=s.toPathData()}catch(d){a=jw.bbox(s.node,!0),o=c.rectToPathData(Object.assign(Object.assign({},n),a))}const l=c.createSvgElement("path");if(c.attr(l,Object.assign({d:o,"pointer-events":"none","vector-effect":"non-scaling-stroke",fill:"none"},n.attrs?c.kebablizeAttrs(n.attrs):null)),t.isEdgeElement(e))c.attr(l,"d",t.getConnectionPathData());else{let e=s.getTransformToElement(t.container);const r=n.padding;if(r){null==a&&(a=jw.bbox(s.node,!0));const t=a.x+a.width/2,n=a.y+a.height/2;a=jw.transformRectangle(a,e);const i=Math.max(a.width,1),o=Math.max(a.height,1),l=(i+r)/i,h=(o+r)/o,u=c.createSVGMatrix({a:l,b:0,c:0,d:h,e:t-l*t,f:n-h*n});e=e.multiply(u)}c.transform(l,e)}c.addClass(l,Ow.prefix("highlight-stroke"));const h=t.cell,u=()=>Lw.removeHighlighter(r);h.on("removed",u),h.model&&h.model.on("reseted",u),t.container.appendChild(l),Lw.setCache(r,l)},unhighlight(t,e,n){Lw.removeHighlighter(Lw.getHighlighterId(e,n))}};var Lw,Rw;function Bw(t,e={}){return new xb["Point"](a.normalizePercentage(e.x,t.width),a.normalizePercentage(e.y,t.height))}function Dw(t,e,n){return Object.assign({angle:e,position:t.toJSON()},n)}(function(t){function e(t,e){return c.ensureId(t),t.id+JSON.stringify(e)}t.getHighlighterId=e;const n={};function r(t,e){n[t]=e}function i(t){return null!=n[t]}function s(t){const e=n[t];e&&(c.remove(e),delete n[t])}t.setCache=r,t.hasCache=i,t.removeHighlighter=s})(Lw||(Lw={})),function(t){function e(t,e){if("function"!==typeof e.highlight)throw new Error(`Highlighter '${t}' is missing required \`highlight()\` method`);if("function"!==typeof e.unhighlight)throw new Error(`Highlighter '${t}' is missing required \`unhighlight()\` method`)}t.check=e}(Rw||(Rw={})),function(t){t.presets=m,t.registry=Pb.create({type:"highlighter"}),t.registry.register(t.presets,!0)}(Rw||(Rw={}));const Iw=(t,e)=>t.map(({x:t,y:n,angle:r})=>Dw(Bw(e,{x:t,y:n}),r||0)),Vw=(t,e,n)=>{const r=n.start||0,i=n.step||20;return Fw(t,e,r,(t,e)=>(t+.5-e/2)*i)},zw=(t,e,n)=>{const r=n.start||0,i=n.step||360/t.length;return Fw(t,e,r,t=>t*i)};function Fw(t,e,n,r){const i=e.getCenter(),s=e.getTopCenter(),o=e.width/e.height,a=xb["Ellipse"].fromRect(e),l=t.length;return t.map((t,e)=>{const c=n+r(e,l),h=s.clone().rotate(-c,i).scale(o,1,i),u=t.compensateRotate?-a.tangentTheta(h):0;return(t.dx||t.dy)&&h.translate(t.dx||0,t.dy||0),t.dr&&h.move(i,t.dr),Dw(h.round(),u,t)})}var $w=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const Gw=(t,e,n)=>{const r=Bw(e,n.start||e.getOrigin()),i=Bw(e,n.end||e.getCorner());return Ww(t,r,i,n)},qw=(t,e,n)=>Ww(t,e.getTopLeft(),e.getBottomLeft(),n),_w=(t,e,n)=>Ww(t,e.getTopRight(),e.getBottomRight(),n),Hw=(t,e,n)=>Ww(t,e.getTopLeft(),e.getTopRight(),n),Uw=(t,e,n)=>Ww(t,e.getBottomLeft(),e.getBottomRight(),n);function Ww(t,e,n,r){const i=new xb["Line"](e,n),s=t.length;return t.map((t,e)=>{var{strict:n}=t,o=$w(t,["strict"]);const a=n||r.strict?(e+1)/(s+1):(e+.5)/s,l=i.pointAt(a);return(o.dx||o.dy)&&l.translate(o.dx||0,o.dy||0),Dw(l.round(),0,o)})}var Jw;(function(t){t.presets=y,t.registry=Pb.create({type:"port layout"}),t.registry.register(t.presets,!0)})(Jw||(Jw={}));const Xw={position:{x:0,y:0},angle:0,attrs:{".":{y:"0","text-anchor":"start"}}};function Yw(t,e){const{x:n,y:r,angle:s,attrs:o}=e||{};return i.defaultsDeep({},{angle:s,attrs:o,position:{x:n,y:r}},t,Xw)}const Zw=(t,e,n)=>Yw({position:e.getTopLeft()},n),Kw=(t,e,n)=>Yw({position:{x:-15,y:0},attrs:{".":{y:".3em","text-anchor":"end"}}},n),Qw=(t,e,n)=>Yw({position:{x:15,y:0},attrs:{".":{y:".3em","text-anchor":"start"}}},n),tx=(t,e,n)=>Yw({position:{x:0,y:-15},attrs:{".":{"text-anchor":"middle"}}},n),ex=(t,e,n)=>Yw({position:{x:0,y:15},attrs:{".":{y:".6em","text-anchor":"middle"}}},n),nx=(t,e,n)=>ox(t,e,!1,n),rx=(t,e,n)=>ox(t,e,!0,n),ix=(t,e,n)=>ax(t,e,!1,n),sx=(t,e,n)=>ax(t,e,!0,n);function ox(t,e,n,r){const i=null!=r.offset?r.offset:15,s=e.getCenter().theta(t),o=lx(e);let a,l,c,h,u=0;return s<o[1]||s>o[2]?(a=".3em",l=i,c=0,h="start"):s<o[0]?(a="0",l=0,c=-i,n?(u=-90,h="start"):h="middle"):s<o[3]?(a=".3em",l=-i,c=0,h="end"):(a=".6em",l=0,c=i,n?(u=90,h="start"):h="middle"),Yw({position:{x:Math.round(l),y:Math.round(c)},angle:u,attrs:{".":{y:a,"text-anchor":h}}},r)}function ax(t,e,n,r){const i=null!=r.offset?r.offset:15,s=e.getCenter().theta(t),o=lx(e);let a,l,c,h,u=0;return s<o[1]||s>o[2]?(a=".3em",l=-i,c=0,h="end"):s<o[0]?(a=".6em",l=0,c=i,n?(u=90,h="start"):h="middle"):s<o[3]?(a=".3em",l=i,c=0,h="start"):(a="0em",l=0,c=-i,n?(u=-90,h="start"):h="middle"),Yw({position:{x:Math.round(l),y:Math.round(c)},angle:u,attrs:{".":{y:a,"text-anchor":h}}},r)}function lx(t){const e=t.getCenter(),n=e.theta(t.getTopLeft()),r=e.theta(t.getBottomLeft()),i=e.theta(t.getBottomRight()),s=e.theta(t.getTopRight());return[n,s,i,r]}const cx=(t,e,n)=>ux(t.diff(e.getCenter()),!1,n),hx=(t,e,n)=>ux(t.diff(e.getCenter()),!0,n);function ux(t,e,n){const r=null!=n.offset?n.offset:20,i=new xb["Point"](0,0),s=-t.theta(i),o=t.clone().move(i,r).diff(t).round();let a,l=".3em",c=s;return(s+90)%180===0?(a=e?"end":"middle",e||-270!==s||(l="0em")):s>-270&&s<-90?(a="start",c=s-180):a="end",Yw({position:o.round().toJSON(),angle:e?c:0,attrs:{".":{y:l,"text-anchor":a}}},n)}var dx;(function(t){t.presets=b,t.registry=Pb.create({type:"port label layout"}),t.registry.register(t.presets,!0)})(dx||(dx={}));var gx,fx,px=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class mx extends Oh{get priority(){return 2}get disposeContainer(){return!0}constructor(){super(),this.cid=gx.uniqueId(),mx.views[this.cid]=this}confirmUpdate(t,e){return 0}empty(t=this.container){return c.empty(t),this}unmount(t=this.container){return c.remove(t),this}remove(t=this.container){return t===this.container?(this.removeEventListeners(document),this.onRemove(),delete mx.views[this.cid],this.disposeContainer&&this.unmount(t)):this.unmount(t),this}onRemove(){}setClass(t,e=this.container){e.classList.value=Array.isArray(t)?t.join(" "):t}addClass(t,e=this.container){return c.addClass(e,Array.isArray(t)?t.join(" "):t),this}removeClass(t,e=this.container){return c.removeClass(e,Array.isArray(t)?t.join(" "):t),this}setStyle(t,e=this.container){return c.css(e,t),this}setAttrs(t,e=this.container){return null!=t&&null!=e&&c.attr(e,t),this}findAttr(t,e=this.container){let n=e;while(n&&1===n.nodeType){const e=n.getAttribute(t);if(null!=e)return e;if(n===this.container)return null;n=n.parentNode}return null}find(t,e=this.container,n=this.selectors){return mx.find(t,e,n).elems}findOne(t,e=this.container,n=this.selectors){const r=this.find(t,e,n);return r.length>0?r[0]:null}findByAttr(t,e=this.container){let n=e;while(n&&n.getAttribute){const e=n.getAttribute(t);if((null!=e||n===this.container)&&"false"!==e)return n;n=n.parentNode}return null}getSelector(t,e){let n;if(t===this.container)return"string"===typeof e&&(n="> "+e),n;if(t){const r=c.index(t)+1;n=`${t.tagName.toLowerCase()}:nth-child(${r})`,e&&(n+=" > "+e),n=this.getSelector(t.parentNode,n)}return n}prefixClassName(t){return Ow.prefix(t)}delegateEvents(t,e){if(null==t)return this;e||this.undelegateEvents();const n=/^(\S+)\s*(.*)$/;return Object.keys(t).forEach(e=>{const r=e.match(n);if(null==r)return;const i=this.getEventHandler(t[e]);"function"===typeof i&&this.delegateEvent(r[1],r[2],i)}),this}undelegateEvents(){return c.Event.off(this.container,this.getEventNamespace()),this}delegateDocumentEvents(t,e){return this.addEventListeners(document,t,e),this}undelegateDocumentEvents(){return this.removeEventListeners(document),this}delegateEvent(t,e,n){return c.Event.on(this.container,t+this.getEventNamespace(),e,n),this}undelegateEvent(t,e,n){const r=t+this.getEventNamespace();return null==e?c.Event.off(this.container,r):"string"===typeof e?c.Event.off(this.container,r,e,n):c.Event.off(this.container,r,e),this}addEventListeners(t,e,n){if(null==e)return this;const r=this.getEventNamespace();return Object.keys(e).forEach(i=>{const s=this.getEventHandler(e[i]);"function"===typeof s&&c.Event.on(t,i+r,n,s)}),this}removeEventListeners(t){return null!=t&&c.Event.off(t,this.getEventNamespace()),this}getEventNamespace(){return`.${Ow.prefixCls}-event-${this.cid}`}getEventHandler(t){let e;if("string"===typeof t){const n=this[t];"function"===typeof n&&(e=(...t)=>n.call(this,...t))}else e=(...e)=>t.call(this,...e);return e}getEventTarget(t,e={}){const{target:n,type:r,clientX:i=0,clientY:s=0}=t;return e.fromPoint||"touchmove"===r||"touchend"===r?document.elementFromPoint(i,s):n}stopPropagation(t){return this.setEventData(t,{propagationStopped:!0}),this}isPropagationStopped(t){return!0===this.getEventData(t).propagationStopped}getEventData(t){return this.eventData(t)}setEventData(t,e){return this.eventData(t,e)}eventData(t,e){if(null==t)throw new TypeError("Event object required");let n=t.data;const r=`__${this.cid}__`;return null==e?null==n?{}:n[r]||{}:(null==n&&(n=t.data={}),null==n[r]?n[r]=Object.assign({},e):n[r]=Object.assign(Object.assign({},n[r]),e),n[r])}normalizeEvent(t){return mx.normalizeEvent(t)}dispose(){this.remove()}}px([mx.dispose()],mx.prototype,"dispose",null),function(t){function e(t,e){return e?c.createSvgElement(t||"g"):c.createElementNS(t||"div")}function n(t,e,n){if(!t||"."===t)return{elems:[e]};if(n){const e=n[t];if(e)return{elems:Array.isArray(e)?e:[e]}}if(Ow.useCSSSelector){const n=t.includes(">")?":scope "+t:t;return{isCSSSelector:!0,elems:Array.prototype.slice.call(e.querySelectorAll(n))}}return{elems:[]}}function r(t){let e=t;const n=t.originalEvent,r=n&&n.changedTouches&&n.changedTouches[0];if(r){for(const e in t)void 0===r[e]&&(r[e]=t[e]);e=r}return e}t.createElement=e,t.find=n,t.normalizeEvent=r}(mx||(mx={})),function(t){function e(e){return t.views[e]||null}t.views={},t.getView=e}(mx||(mx={})),function(t){let e=0;function n(){const t="v"+e;return e+=1,t}t.uniqueId=n}(gx||(gx={}));class yx{constructor(t){this.view=t,this.clean()}clean(){this.elemCache&&this.elemCache.dispose(),this.elemCache=new yb,this.pathCache={}}get(t){const e=this.elemCache;return e.has(t)||this.elemCache.set(t,{}),this.elemCache.get(t)}getData(t){const e=this.get(t);return e.data||(e.data={}),e.data}getMatrix(t){const e=this.get(t);if(null==e.matrix){const n=this.view.container;e.matrix=c.getTransformToParentElement(t,n)}return c.createSVGMatrix(e.matrix)}getShape(t){const e=this.get(t);return null==e.shape&&(e.shape=jw.toGeometryShape(t)),e.shape.clone()}getBoundingRect(t){const e=this.get(t);return null==e.boundingRect&&(e.boundingRect=jw.getBBoxV2(t)),e.boundingRect.clone()}}(function(t){function e(t){return null!=t&&!n(t)}function n(t){return null!=t&&"string"===typeof t}function r(t){return null==t||n(t)?t:i.cloneDeep(t)}function s(t){return(""+t).trim().replace(/[\r|\n]/g," ").replace(/>\s+</g,"><")}function o(t,e={ns:c.ns.svg}){const n=document.createDocumentFragment(),r={},i={},s=[{markup:Array.isArray(t)?t:[t],parent:n,ns:e.ns}];while(s.length>0){const t=s.pop();let e=t.ns||c.ns.svg;const n=t.markup,o=t.parent;n.forEach(t=>{const n=t.tagName;if(!n)throw new TypeError("Invalid tagName");t.ns&&(e=t.ns);const a=e?c.createElementNS(n,e):c.createElement(n),l=t.attrs;l&&c.attr(a,c.kebablizeAttrs(l));const h=t.style;h&&c.css(a,h);const u=t.className;null!=u&&a.setAttribute("class",Array.isArray(u)?u.join(" "):u),t.textContent&&(a.textContent=t.textContent);const d=t.selector;if(null!=d){if(i[d])throw new TypeError("Selector must be unique");i[d]=a}if(t.groupSelector){let e=t.groupSelector;Array.isArray(e)||(e=[e]),e.forEach(t=>{r[t]||(r[t]=[]),r[t].push(a)})}o.appendChild(a);const g=t.children;Array.isArray(g)&&s.push({ns:e,markup:g,parent:a})})}return Object.keys(r).forEach(t=>{if(i[t])throw new Error("Ambiguous group selector");i[t]=r[t]}),{fragment:n,selectors:i,groups:r}}function a(t){return t instanceof SVGElement?c.createSvgElement("g"):c.createElement("div")}function l(t){if(n(t)){const e=ty.createVectors(t),n=e.length;if(1===n)return{elem:e[0].node};if(n>1){const t=a(e[0].node);return e.forEach(e=>{t.appendChild(e.node)}),{elem:t}}return{}}const e=o(t),r=e.fragment;let i=null;return r.childNodes.length>1?(i=a(r.firstChild),i.appendChild(r)):i=r.firstChild,{elem:i,selectors:e.selectors}}function h(t){const e=ty.createVectors(t),n=document.createDocumentFragment();for(let r=0,i=e.length;r<i;r+=1){const t=e[r].node;n.appendChild(t)}return{fragment:n,selectors:{}}}t.isJSONMarkup=e,t.isStringMarkup=n,t.clone=r,t.sanitize=s,t.parseJSONMarkup=o,t.renderMarkup=l,t.parseLabelStringMarkup=h})(fx||(fx={})),function(t){function e(t,n,r){if(null!=t){let i;const s=t.tagName.toLowerCase();if(t===n)return i="string"===typeof r?`> ${s} > ${r}`:"> "+s,i;const o=t.parentNode;if(o&&o.childNodes.length>1){const e=c.index(t)+1;i=`${s}:nth-child(${e})`}else i=s;return r&&(i+=" > "+r),e(t.parentNode,n,i)}return r}t.getSelector=e}(fx||(fx={})),function(t){function e(){return"g"}function n(){return{tagName:"circle",selector:"circle",attrs:{r:10,fill:"#FFFFFF",stroke:"#000000"}}}function r(){return{tagName:"text",selector:"text",attrs:{fill:"#000000"}}}t.getPortContainerMarkup=e,t.getPortMarkup=n,t.getPortLabelMarkup=r}(fx||(fx={})),function(t){function e(){return[{tagName:"path",selector:"wrap",groupSelector:"lines",attrs:{fill:"none",cursor:"pointer",stroke:"transparent",strokeLinecap:"round"}},{tagName:"path",selector:"line",groupSelector:"lines",attrs:{fill:"none",pointerEvents:"none"}}]}t.getEdgeMarkup=e}(fx||(fx={})),function(t){function e(t=!1){return{tagName:"foreignObject",selector:"fo",children:[{ns:c.ns.xhtml,tagName:"body",selector:"foBody",attrs:{xmlns:c.ns.xhtml},style:{width:"100%",height:"100%",background:"transparent"},children:t?[]:[{tagName:"div",selector:"foContent",style:{width:"100%",height:"100%"}}]}]}}t.getForeignObjectMarkup=e}(fx||(fx={}));class bx{constructor(t){this.view=t}get cell(){return this.view.cell}getDefinition(t){return this.cell.getAttrDefinition(t)}processAttrs(t,e){let n,i,s,a;const l=[];return Object.keys(e).forEach(i=>{const s=e[i],a=this.getDefinition(i),h=r.call(Aw.isValidDefinition,this.view,a,s,{elem:t,attrs:e,cell:this.cell,view:this.view});if(a&&h)"string"===typeof a?(null==n&&(n={}),n[a]=s):null!==s&&l.push({name:i,definition:a});else{null==n&&(n={});const t=c.CASE_SENSITIVE_ATTR.includes(i)?i:o.kebabCase(i);n[t]=s}}),l.forEach(({name:t,definition:n})=>{const r=e[t],o=n;"function"===typeof o.set&&(null==i&&(i={}),i[t]=r);const l=n;"function"===typeof l.offset&&(null==s&&(s={}),s[t]=r);const c=n;"function"===typeof c.position&&(null==a&&(a={}),a[t]=r)}),{raw:e,normal:n,set:i,offset:s,position:a}}mergeProcessedAttrs(t,e){t.set=Object.assign(Object.assign({},t.set),e.set),t.position=Object.assign(Object.assign({},t.position),e.position),t.offset=Object.assign(Object.assign({},t.offset),e.offset);const n=t.normal&&t.normal.transform;null!=n&&e.normal&&(e.normal.transform=n),t.normal=e.normal}findAttrs(t,e,n,r){const o=[],a=new yb;return Object.keys(t).forEach(l=>{const c=t[l];if(!i.isPlainObject(c))return;const{isCSSSelector:h,elems:u}=mx.find(l,e,r);n[l]=u;for(let t=0,e=u.length;t<e;t+=1){const n=u[t],i=r&&r[l]===n,d=a.get(n);if(d){d.array||(o.push(n),d.array=!0,d.attrs=[d.attrs],d.priority=[d.priority]);const t=d.attrs,r=d.priority;if(i)t.unshift(c),r.unshift(-1);else{const n=s.sortedIndex(r,h?-1:e);t.splice(n,0,c),r.splice(n,0,e)}}else a.set(n,{elem:n,attrs:c,priority:i?-1:e,array:!1})}}),o.forEach(t=>{const e=a.get(t),n=e.attrs;e.attrs=n.reduceRight((t,e)=>i.merge(t,e),{})}),a}updateRelativeAttrs(t,e,n){const i=e.raw||{};let s=e.normal||{};const o=e.set,a=e.position,l=e.offset,h=()=>({elem:t,cell:this.cell,view:this.view,attrs:i,refBBox:n.clone()});if(null!=o&&Object.keys(o).forEach(t=>{const e=o[t],n=this.getDefinition(t);if(null!=n){const i=r.call(n.set,this.view,e,h());"object"===typeof i?s=Object.assign(Object.assign({},s),i):null!=i&&(s[t]=i)}}),t instanceof HTMLElement)return void this.view.setAttrs(s,t);const u=s.transform,d=u?""+u:null,g=c.transformStringToMatrix(d),f=new xb["Point"](g.e,g.f);u&&(delete s.transform,g.e=0,g.f=0);let p=!1;null!=a&&Object.keys(a).forEach(t=>{const e=a[t],n=this.getDefinition(t);if(null!=n){const t=r.call(n.position,this.view,e,h());null!=t&&(p=!0,f.translate(xb["Point"].create(t)))}}),this.view.setAttrs(s,t);let m=!1;if(null!=l){const e=this.view.getBoundingRectOfElement(t);if(e.width>0&&e.height>0){const n=jw.transformRectangle(e,g);Object.keys(l).forEach(e=>{const s=l[e],o=this.getDefinition(e);if(null!=o){const e=r.call(o.offset,this.view,s,{elem:t,cell:this.cell,view:this.view,attrs:i,refBBox:n});null!=e&&(m=!0,f.translate(xb["Point"].create(e)))}})}}(null!=u||p||m)&&(f.round(1),g.e=f.x,g.f=f.y,t.setAttribute("transform",c.matrixToTransformString(g)))}update(t,e,n){const r={},i=this.findAttrs(n.attrs||e,t,r,n.selectors),s=n.attrs?this.findAttrs(e,t,r,n.selectors):i,o=[];i.each(e=>{const i=e.elem,a=e.attrs,l=this.processAttrs(i,a);if(null==l.set&&null==l.position&&null==l.offset)this.view.setAttrs(l.normal,i);else{const e=s.get(i),c=e?e.attrs:null,h=c&&null==a.ref?c.ref:a.ref;let u;if(h){if(u=(r[h]||this.view.find(h,t,n.selectors))[0],!u)throw new Error(`"${h}" reference does not exist.`)}else u=null;const d={node:i,refNode:u,attributes:c,processedAttributes:l},g=o.findIndex(t=>t.refNode===i);g>-1?o.splice(g,0,d):o.push(d)}});const a=new yb;let l;o.forEach(e=>{const r=e.node,i=e.refNode;let s;const o=null!=i&&null!=n.rotatableNode&&c.contains(n.rotatableNode,i);if(i&&(s=a.get(i)),!s){const e=o?n.rotatableNode:t;s=i?jw.getBBox(i,{target:e}):n.rootBBox,i&&a.set(i,s)}let h;n.attrs&&e.attributes?(h=this.processAttrs(r,e.attributes),this.mergeProcessedAttrs(h,e.processedAttributes)):h=e.processedAttributes;let u=s;o&&null!=n.rotatableNode&&!n.rotatableNode.contains(r)&&(l||(l=c.transformStringToMatrix(c.attr(n.rotatableNode,"transform"))),u=jw.transformRectangle(s,l)),this.updateRelativeAttrs(r,h,u)})}}class vx{get cell(){return this.view.cell}constructor(t,e,n=[]){this.view=t;const r={},i={};let s=0;Object.keys(e).forEach(t=>{let n=e[t];Array.isArray(n)||(n=[n]),n.forEach(e=>{let n=r[e];n||(s+=1,n=r[e]=1<<s),i[t]|=n})});let o=n;if(Array.isArray(o)||(o=[o]),o.forEach(t=>{r[t]||(s+=1,r[t]=1<<s)}),s>25)throw new Error("Maximum number of flags exceeded.");this.flags=r,this.attrs=i,this.bootstrap=n}getFlag(t){const e=this.flags;return null==e?0:Array.isArray(t)?t.reduce((t,n)=>t|e[n],0):0|e[t]}hasAction(t,e){return t&this.getFlag(e)}removeAction(t,e){return t^t&this.getFlag(e)}getBootstrapFlag(){return this.getFlag(this.bootstrap)}getChangedFlag(){let t=0;return this.attrs?(Object.keys(this.attrs).forEach(e=>{this.cell.hasChanged(e)&&(t|=this.attrs[e])}),t):t}}var wx=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o},xx=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class Px extends mx{static getDefaults(){return this.defaults}static config(t){this.defaults=this.getOptions(t)}static getOptions(t){const e=(t,e)=>null!=e?s.uniq([...Array.isArray(t)?t:[t],...Array.isArray(e)?e:[e]]):Array.isArray(t)?[...t]:[t],n=i.cloneDeep(this.getDefaults()),{bootstrap:r,actions:o,events:a,documentEvents:l}=t,c=xx(t,["bootstrap","actions","events","documentEvents"]);return r&&(n.bootstrap=e(n.bootstrap,r)),o&&Object.entries(o).forEach(([t,r])=>{const i=n.actions[t];r&&i?n.actions[t]=e(i,r):r&&(n.actions[t]=e(r))}),a&&(n.events=Object.assign(Object.assign({},n.events),a)),t.documentEvents&&(n.documentEvents=Object.assign(Object.assign({},n.documentEvents),l)),i.merge(n,c)}get[Symbol.toStringTag](){return Px.toStringTag}constructor(t,e={}){super(),this.cell=t,this.options=this.ensureOptions(e),this.graph=this.options.graph,this.attr=new bx(this),this.flag=new vx(this,this.options.actions,this.options.bootstrap),this.cache=new yx(this),this.setContainer(this.ensureContainer()),this.setup(),this.init()}init(){}onRemove(){this.removeTools()}get priority(){return this.options.priority}get rootSelector(){return this.options.rootSelector}getConstructor(){return this.constructor}ensureOptions(t){return this.getConstructor().getOptions(t)}getContainerTagName(){return this.options.isSvgElement?"g":"div"}getContainerStyle(){}getContainerAttrs(){return{"data-cell-id":this.cell.id,"data-shape":this.cell.shape}}getContainerClassName(){return this.prefixClassName("cell")}ensureContainer(){return mx.createElement(this.getContainerTagName(),this.options.isSvgElement)}setContainer(t){if(this.container!==t){this.undelegateEvents(),this.container=t,null!=this.options.events&&this.delegateEvents(this.options.events);const e=this.getContainerAttrs();null!=e&&this.setAttrs(e,t);const n=this.getContainerStyle();null!=n&&this.setStyle(n,t);const r=this.getContainerClassName();null!=r&&this.addClass(r,t)}return this}isNodeView(){return!1}isEdgeView(){return!1}render(){return this}confirmUpdate(t,e={}){return 0}getBootstrapFlag(){return this.flag.getBootstrapFlag()}getFlag(t){return this.flag.getFlag(t)}hasAction(t,e){return this.flag.hasAction(t,e)}removeAction(t,e){return this.flag.removeAction(t,e)}handleAction(t,e,n,r){if(this.hasAction(t,e)){n();const i=[e];return r&&("string"===typeof r?i.push(r):i.push(...r)),this.removeAction(t,i)}return t}setup(){this.cell.on("changed",this.onCellChanged,this)}onCellChanged({options:t}){this.onAttrsChange(t)}onAttrsChange(t){let e=this.flag.getChangedFlag();!t.updated&&e&&(t.dirty&&this.hasAction(e,"update")&&(e|=this.getFlag("render")),t.toolId&&(t.async=!1),null!=this.graph&&this.graph.renderer.requestViewUpdate(this,e,t))}parseJSONMarkup(t,e){const n=fx.parseJSONMarkup(t),r=n.selectors,i=this.rootSelector;if(e&&i){if(r[i])throw new Error("Invalid root selector");r[i]=e}return n}can(t){let e=this.graph.options.interacting;if("function"===typeof e&&(e=r.call(e,this.graph,this)),"object"===typeof e){let n=e[t];return"function"===typeof n&&(n=r.call(n,this.graph,this)),!1!==n}return"boolean"===typeof e&&e}cleanCache(){return this.cache.clean(),this}getCache(t){return this.cache.get(t)}getDataOfElement(t){return this.cache.getData(t)}getMatrixOfElement(t){return this.cache.getMatrix(t)}getShapeOfElement(t){return this.cache.getShape(t)}getBoundingRectOfElement(t){return this.cache.getBoundingRect(t)}getBBoxOfElement(t){const e=this.getBoundingRectOfElement(t),n=this.getMatrixOfElement(t),r=this.getRootRotatedMatrix(),i=this.getRootTranslatedMatrix();return jw.transformRectangle(e,i.multiply(r).multiply(n))}getUnrotatedBBoxOfElement(t){const e=this.getBoundingRectOfElement(t),n=this.getMatrixOfElement(t),r=this.getRootTranslatedMatrix();return jw.transformRectangle(e,r.multiply(n))}getBBox(t={}){let e;if(t.useCellGeometry){const t=this.cell,n=t.isNode()?t.getAngle():0;e=t.getBBox().bbox(n)}else e=this.getBBoxOfElement(this.container);return this.graph.coord.localToGraphRect(e)}getRootTranslatedMatrix(){const t=this.cell,e=t.isNode()?t.getPosition():{x:0,y:0};return c.createSVGMatrix().translate(e.x,e.y)}getRootRotatedMatrix(){let t=c.createSVGMatrix();const e=this.cell,n=e.isNode()?e.getAngle():0;if(n){const r=e.getBBox(),i=r.width/2,s=r.height/2;t=t.translate(i,s).rotate(n).translate(-i,-s)}return t}findMagnet(t=this.container){return this.findByAttr("magnet",t)}updateAttrs(t,e,n={}){null==n.rootBBox&&(n.rootBBox=new xb["Rectangle"]),null==n.selectors&&(n.selectors=this.selectors),this.attr.update(t,e,n)}isEdgeElement(t){return this.cell.isEdge()&&(null==t||t===this.container)}prepareHighlight(t,e={}){const n=t||this.container;return e.partial=n===this.container,n}highlight(t,e={}){const n=this.prepareHighlight(t,e);return this.notify("cell:highlight",{magnet:n,options:e,view:this,cell:this.cell}),this.isEdgeView()?this.notify("edge:highlight",{magnet:n,options:e,view:this,edge:this.cell,cell:this.cell}):this.isNodeView()&&this.notify("node:highlight",{magnet:n,options:e,view:this,node:this.cell,cell:this.cell}),this}unhighlight(t,e={}){const n=this.prepareHighlight(t,e);return this.notify("cell:unhighlight",{magnet:n,options:e,view:this,cell:this.cell}),this.isNodeView()?this.notify("node:unhighlight",{magnet:n,options:e,view:this,node:this.cell,cell:this.cell}):this.isEdgeView()&&this.notify("edge:unhighlight",{magnet:n,options:e,view:this,edge:this.cell,cell:this.cell}),this}notifyUnhighlight(t,e){}getEdgeTerminal(t,e,n,r,i){const s=this.cell,o=this.findAttr("port",t),a=t.getAttribute("data-selector"),l={cell:s.id};return null!=a&&(l.magnet=a),null!=o?(l.port=o,s.isNode()&&(s.hasPort(o)||null!=a||(l.selector=this.getSelector(t)))):null==a&&this.container!==t&&(l.selector=this.getSelector(t)),l}getMagnetFromEdgeTerminal(t){const e=this.cell,n=this.container,r=t.port;let i,s=t.magnet;return null!=r&&e.isNode()&&e.hasPort(r)?i=this.findPortElem(r,s)||n:(s||(s=t.selector),s||null==r||(s=`[port="${r}"]`),i=this.findOne(s,n,this.selectors)),i}hasTools(t){const e=this.tools;return null!=e&&(null==t||e.name===t)}addTools(t){if(this.removeTools(),t){if(!this.can("toolsAddable"))return this;const e=Cx.isToolsView(t)?t:new Cx(t);this.tools=e,e.config({view:this}),e.mount()}return this}updateTools(t={}){return this.tools&&this.tools.update(t),this}removeTools(){return this.tools&&(this.tools.remove(),this.tools=null),this}hideTools(){return this.tools&&this.tools.hide(),this}showTools(){return this.tools&&this.tools.show(),this}renderTools(){const t=this.cell.getTools();return this.addTools(t),this}notify(t,e){return this.trigger(t,e),this.graph.trigger(t,e),this}getEventArgs(t,e,n){const r=this,i=r.cell;return null==e||null==n?{e:t,view:r,cell:i}:{e:t,x:e,y:n,view:r,cell:i}}onClick(t,e,n){this.notify("cell:click",this.getEventArgs(t,e,n))}onDblClick(t,e,n){this.notify("cell:dblclick",this.getEventArgs(t,e,n))}onContextMenu(t,e,n){this.notify("cell:contextmenu",this.getEventArgs(t,e,n))}onMouseDown(t,e,n){this.cell.model&&(this.cachedModelForMouseEvent=this.cell.model,this.cachedModelForMouseEvent.startBatch("mouse")),this.notify("cell:mousedown",this.getEventArgs(t,e,n))}onMouseUp(t,e,n){this.notify("cell:mouseup",this.getEventArgs(t,e,n)),this.cachedModelForMouseEvent&&(this.cachedModelForMouseEvent.stopBatch("mouse",{cell:this.cell}),this.cachedModelForMouseEvent=null)}onMouseMove(t,e,n){this.notify("cell:mousemove",this.getEventArgs(t,e,n))}onMouseOver(t){this.notify("cell:mouseover",this.getEventArgs(t))}onMouseOut(t){this.notify("cell:mouseout",this.getEventArgs(t))}onMouseEnter(t){this.notify("cell:mouseenter",this.getEventArgs(t))}onMouseLeave(t){this.notify("cell:mouseleave",this.getEventArgs(t))}onMouseWheel(t,e,n,r){this.notify("cell:mousewheel",Object.assign({delta:r},this.getEventArgs(t,e,n)))}onCustomEvent(t,e,n,r){this.notify("cell:customevent",Object.assign({name:e},this.getEventArgs(t,n,r))),this.notify(e,Object.assign({},this.getEventArgs(t,n,r)))}onMagnetMouseDown(t,e,n,r){}onMagnetDblClick(t,e,n,r){}onMagnetContextMenu(t,e,n,r){}onLabelMouseDown(t,e,n){}checkMouseleave(t){const e=this.getEventTarget(t,{fromPoint:!0}),n=this.graph.findViewByElem(e);n!==this&&(this.onMouseLeave(t),n&&n.onMouseEnter(t))}dispose(){this.cell.off("changed",this.onCellChanged,this)}}Px.defaults={isSvgElement:!0,rootSelector:"root",priority:0,bootstrap:[],actions:{}},wx([Px.dispose()],Px.prototype,"dispose",null),function(t){t.Flag=vx,t.Attr=bx}(Px||(Px={})),function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"===typeof r.isNodeView&&"function"===typeof r.isEdgeView&&"function"===typeof r.confirmUpdate}t.toStringTag="X6."+t.name,t.isCellView=e}(Px||(Px={})),function(t){function e(t){return function(e){e.config({priority:t})}}function n(t){return function(e){e.config({bootstrap:t})}}t.priority=e,t.bootstrap=n}(Px||(Px={})),function(t){t.registry=Pb.create({type:"view"})}(Px||(Px={}));class Cx extends mx{get name(){return this.options.name}get graph(){return this.cellView.graph}get cell(){return this.cellView.cell}get[Symbol.toStringTag](){return Cx.toStringTag}constructor(t={}){super(),this.svgContainer=this.createContainer(!0,t),this.htmlContainer=this.createContainer(!1,t),this.config(t)}createContainer(t,e){const n=t?mx.createElement("g",!0):mx.createElement("div",!1);return c.addClass(n,this.prefixClassName("cell-tools")),e.className&&c.addClass(n,e.className),n}config(t){if(this.options=Object.assign(Object.assign({},this.options),t),!Px.isCellView(t.view)||t.view===this.cellView)return this;this.cellView=t.view,this.cell.isEdge()?(c.addClass(this.svgContainer,this.prefixClassName("edge-tools")),c.addClass(this.htmlContainer,this.prefixClassName("edge-tools"))):this.cell.isNode()&&(c.addClass(this.svgContainer,this.prefixClassName("node-tools")),c.addClass(this.htmlContainer,this.prefixClassName("node-tools"))),this.svgContainer.setAttribute("data-cell-id",this.cell.id),this.htmlContainer.setAttribute("data-cell-id",this.cell.id),this.name&&(this.svgContainer.setAttribute("data-tools-name",this.name),this.htmlContainer.setAttribute("data-tools-name",this.name));const e=this.options.items;if(!Array.isArray(e))return this;this.tools=[];const n=[];e.forEach(t=>{if(Cx.ToolItem.isToolItem(t))"vertices"===t.name?n.unshift(t):n.push(t);else{const e="object"===typeof t?t.name:t;"vertices"===e?n.unshift(t):n.push(t)}});for(let r=0;r<n.length;r+=1){const t=n[r];let e;if(Cx.ToolItem.isToolItem(t))e=t;else{const n="object"===typeof t?t.name:t,r="object"===typeof t&&t.args||{};if(n)if(this.cell.isNode()){const t=Wx.registry.get(n);if(!t)return Wx.registry.onNotFound(n);e=new t(r)}else if(this.cell.isEdge()){const t=Jx.registry.get(n);if(!t)return Jx.registry.onNotFound(n);e=new t(r)}}if(e){e.config(this.cellView,this),e.render();const t=!1!==e.options.isSVGElement?this.svgContainer:this.htmlContainer;t.appendChild(e.container),this.tools.push(e)}}return this}update(t={}){const e=this.tools;return e&&e.forEach(e=>{t.toolId!==e.cid&&e.isVisible()&&e.update()}),this}focus(t){const e=this.tools;return e&&e.forEach(e=>{t===e?e.show():e.hide()}),this}blur(t){const e=this.tools;return e&&e.forEach(e=>{e===t||e.isVisible()||(e.show(),e.update())}),this}hide(){return this.focus(null)}show(){return this.blur(null)}remove(){const t=this.tools;return t&&(t.forEach(t=>t.remove()),this.tools=null),c.remove(this.svgContainer),c.remove(this.htmlContainer),super.remove()}mount(){const t=this.tools,e=this.cellView;if(e&&t){const n=t.some(t=>!1!==t.options.isSVGElement),r=t.some(t=>!1===t.options.isSVGElement);if(n){const t=this.options.local?e.container:e.graph.view.decorator;t.appendChild(this.svgContainer)}r&&this.graph.container.appendChild(this.htmlContainer)}return this}}(function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&null!=r.graph&&null!=r.cell&&"function"===typeof r.config&&"function"===typeof r.update&&"function"===typeof r.focus&&"function"===typeof r.blur&&"function"===typeof r.show&&"function"===typeof r.hide}t.toStringTag="X6."+t.name,t.isToolsView=e})(Cx||(Cx={})),function(t){class e extends mx{static getDefaults(){return this.defaults}static config(t){this.defaults=this.getOptions(t)}static getOptions(t){return i.merge(i.cloneDeep(this.getDefaults()),t)}get graph(){return this.cellView.graph}get cell(){return this.cellView.cell}get name(){return this.options.name}get[Symbol.toStringTag](){return e.toStringTag}constructor(t={}){super(),this.visible=!0,this.options=this.getOptions(t),this.container=mx.createElement(this.options.tagName||"g",!1!==this.options.isSVGElement),c.addClass(this.container,this.prefixClassName("cell-tool")),"string"===typeof this.options.className&&c.addClass(this.container,this.options.className),this.init()}init(){}getOptions(t){const e=this.constructor;return e.getOptions(t)}delegateEvents(){return this.options.events&&super.delegateEvents(this.options.events),this}config(t,e){return this.cellView=t,this.parent=e,this.stamp(this.container),this.cell.isEdge()?c.addClass(this.container,this.prefixClassName("edge-tool")):this.cell.isNode()&&c.addClass(this.container,this.prefixClassName("node-tool")),this.name&&this.container.setAttribute("data-tool-name",this.name),this.delegateEvents(),this}render(){this.empty();const t=this.options.markup;if(t){const e=fx.parseJSONMarkup(t);this.container.appendChild(e.fragment),this.childNodes=e.selectors}return this.onRender(),this}onRender(){}update(){return this}stamp(t){t&&t.setAttribute("data-cell-id",this.cellView.cell.id)}show(){return this.container.style.display="",this.visible=!0,this}hide(){return this.container.style.display="none",this.visible=!1,this}isVisible(){return this.visible}focus(){const t=this.options.focusOpacity;return null!=t&&Number.isFinite(t)&&(this.container.style.opacity=""+t),this.parent.focus(this),this}blur(){return this.container.style.opacity="",this.parent.blur(this),this}guard(t){return null==this.graph||null==this.cellView||this.graph.view.guard(t,this.cellView)}}e.defaults={isSVGElement:!0,tagName:"g"},t.ToolItem=e,function(t){let e=0;function n(t){return t?o.pascalCase(t):(e+=1,"CustomTool"+e)}function r(t){const e=i.createClass(n(t.name),this);return e.config(t),e}t.define=r}(e=t.ToolItem||(t.ToolItem={})),function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&null!=r.graph&&null!=r.cell&&"function"===typeof r.config&&"function"===typeof r.update&&"function"===typeof r.focus&&"function"===typeof r.blur&&"function"===typeof r.show&&"function"===typeof r.hide&&"function"===typeof r.isVisible}t.toStringTag="X6."+t.name,t.isToolItem=e}(e=t.ToolItem||(t.ToolItem={}))}(Cx||(Cx={}));const Ax=t=>t;function Ox(t,e){return 0===e?"0%":Math.round(t/e*100)+"%"}function Ex(t){const e=(e,n,r,i)=>n.isEdgeElement(r)?Mx(t,e,n,r,i):Sx(t,e,n,r,i);return e}function Sx(t,e,n,r,i){const s=n.cell,o=s.getAngle(),a=n.getUnrotatedBBoxOfElement(r),l=s.getBBox().getCenter(),c=xb["Point"].create(i).rotate(o,l);let h=c.x-a.x,u=c.y-a.y;return t&&(h=Ox(h,a.width),u=Ox(u,a.height)),e.anchor={name:"topLeft",args:{dx:h,dy:u,rotate:!0}},e}function Mx(t,e,n,r,i){const s=n.getConnection();if(!s)return e;const o=s.closestPointLength(i);if(t){const t=s.length();e.anchor={name:"ratio",args:{ratio:o/t}}}else e.anchor={name:"length",args:{length:o}};return e}const Tx=Ex(!0),jx=Ex(!1);var kx;function Nx(t,e,n,i){const s=r.call(kx.presets.pinRelative,this.graph,{},e,n,t,this.cell,i,{});return s.anchor}function Lx(t,e){return e?t.cell.getBBox():t.cell.isEdge()?t.getConnection().bbox():t.getUnrotatedBBoxOfElement(t.container)}(function(t){t.presets=v,t.registry=Pb.create({type:"connection strategy"}),t.registry.register(t.presets,!0)})(kx||(kx={}));class Rx extends Cx.ToolItem{onRender(){c.addClass(this.container,this.prefixClassName("cell-tool-button")),this.update()}update(){return this.updatePosition(),this}updatePosition(){const t=this.cellView,e=t.cell.isEdge()?this.getEdgeMatrix():this.getNodeMatrix();c.transform(this.container,e,{absolute:!0})}getNodeMatrix(){const t=this.cellView,e=this.options;let{x:n=0,y:r=0}=e;const{offset:i,useCellGeometry:s,rotate:o}=e;let l=Lx(t,s);const h=t.cell.getAngle();o||(l=l.bbox(h));let u=0,d=0;"number"===typeof i?(u=i,d=i):"object"===typeof i&&(u=i.x,d=i.y),n=a.normalizePercentage(n,l.width),r=a.normalizePercentage(r,l.height);let g=c.createSVGMatrix().translate(l.x+l.width/2,l.y+l.height/2);return o&&(g=g.rotate(h)),g=g.translate(n+u-l.width/2,r+d-l.height/2),g}getEdgeMatrix(){const t=this.cellView,e=this.options,{offset:n=0,distance:r=0,rotate:i}=e;let s,o,l;const h=a.normalizePercentage(r,1);s=h>=0&&h<=1?t.getTangentAtRatio(h):t.getTangentAtLength(h),s?(o=s.start,l=s.vector().vectorAngle(new xb["Point"](1,0))||0):(o=t.getConnection().start,l=0);let u=c.createSVGMatrix().translate(o.x,o.y).rotate(l);return u="object"===typeof n?u.translate(n.x||0,n.y||0):u.translate(0,n),i||(u=u.rotate(-l)),u}onMouseDown(t){if(this.guard(t))return;t.stopPropagation(),t.preventDefault();const e=this.options.onClick;"function"===typeof e&&r.call(e,this.cellView,{e:t,view:this.cellView,cell:this.cellView.cell,btn:this})}}(function(t){t.config({name:"button",useCellGeometry:!0,events:{mousedown:"onMouseDown",touchstart:"onMouseDown"}})})(Rx||(Rx={})),function(t){t.Remove=t.define({name:"button-remove",markup:[{tagName:"circle",selector:"button",attrs:{r:7,fill:"#FF1D00",cursor:"pointer"}},{tagName:"path",selector:"icon",attrs:{d:"M -3 -3 3 3 M -3 3 3 -3",fill:"none",stroke:"#FFFFFF","stroke-width":2,"pointer-events":"none"}}],distance:60,offset:0,useCellGeometry:!0,onClick({view:t,btn:e}){e.parent.remove(),t.cell.remove({ui:!0,toolId:e.cid})}})}(Rx||(Rx={}));var Bx=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class Dx extends Cx.ToolItem{onRender(){if(c.addClass(this.container,this.prefixClassName("cell-tool-boundary")),this.options.attrs){const t=this.options.attrs,{class:e}=t,n=Bx(t,["class"]);c.attr(this.container,c.kebablizeAttrs(n)),e&&c.addClass(this.container,e)}this.update()}update(){const t=this.cellView,e=this.options,{useCellGeometry:n,rotate:r}=e,i=a.normalizeSides(e.padding);let s=Lx(t,n).moveAndExpand({x:-i.left,y:-i.top,width:i.left+i.right,height:i.top+i.bottom});const o=t.cell;if(o.isNode()){const t=o.getAngle();if(t)if(r){const e=o.getBBox().getCenter();c.rotate(this.container,t,e.x,e.y,{absolute:!0})}else s=s.bbox(t)}return c.attr(this.container,s.toJSON()),this}}(function(t){t.config({name:"boundary",tagName:"rect",padding:10,useCellGeometry:!0,attrs:{fill:"none",stroke:"#333","stroke-width":.5,"stroke-dasharray":"5, 5","pointer-events":"none"}})})(Dx||(Dx={}));class Ix extends Cx.ToolItem{constructor(){super(...arguments),this.handles=[]}get vertices(){return this.cellView.cell.getVertices()}onRender(){return this.addClass(this.prefixClassName("edge-tool-vertices")),this.options.addable&&this.updatePath(),this.resetHandles(),this.renderHandles(),this}update(){const t=this.vertices;return t.length===this.handles.length?this.updateHandles():(this.resetHandles(),this.renderHandles()),this.options.addable&&this.updatePath(),this}resetHandles(){const t=this.handles;this.handles=[],t&&t.forEach(t=>{this.stopHandleListening(t),t.remove()})}renderHandles(){const t=this.vertices;for(let e=0,n=t.length;e<n;e+=1){const n=t[e],r=this.options.createHandle,i=this.options.processHandle,s=r({index:e,graph:this.graph,guard:t=>this.guard(t),attrs:this.options.attrs||{}});i&&i(s),s.updatePosition(n.x,n.y),this.stamp(s.container),this.container.appendChild(s.container),this.handles.push(s),this.startHandleListening(s)}}updateHandles(){const t=this.vertices;for(let e=0,n=t.length;e<n;e+=1){const n=t[e],r=this.handles[e];r&&r.updatePosition(n.x,n.y)}}updatePath(){const t=this.childNodes.connection;t&&t.setAttribute("d",this.cellView.getConnectionPathData())}startHandleListening(t){const e=this.cellView;e.can("vertexMovable")&&(t.on("change",this.onHandleChange,this),t.on("changing",this.onHandleChanging,this),t.on("changed",this.onHandleChanged,this)),e.can("vertexDeletable")&&t.on("remove",this.onHandleRemove,this)}stopHandleListening(t){const e=this.cellView;e.can("vertexMovable")&&(t.off("change",this.onHandleChange,this),t.off("changing",this.onHandleChanging,this),t.off("changed",this.onHandleChanged,this)),e.can("vertexDeletable")&&t.off("remove",this.onHandleRemove,this)}getNeighborPoints(t){const e=this.cellView,n=this.vertices,r=t>0?n[t-1]:e.sourceAnchor,i=t<n.length-1?n[t+1]:e.targetAnchor;return{prev:xb["Point"].create(r),next:xb["Point"].create(i)}}getMouseEventArgs(t){const e=this.normalizeEvent(t),{x:n,y:r}=this.graph.snapToGrid(e.clientX,e.clientY);return{e:e,x:n,y:r}}onHandleChange({e:t}){this.focus();const e=this.cellView;if(e.cell.startBatch("move-vertex",{ui:!0,toolId:this.cid}),!this.options.stopPropagation){const{e:n,x:r,y:i}=this.getMouseEventArgs(t);this.eventData(n,{start:{x:r,y:i}}),e.notifyMouseDown(n,r,i)}}onHandleChanging({handle:t,e:e}){const n=this.cellView,r=t.options.index,{e:i,x:s,y:o}=this.getMouseEventArgs(e),a={x:s,y:o};this.snapVertex(a,r),n.cell.setVertexAt(r,a,{ui:!0,toolId:this.cid}),t.updatePosition(a.x,a.y),this.options.stopPropagation||n.notifyMouseMove(i,s,o)}stopBatch(t){this.cell.stopBatch("move-vertex",{ui:!0,toolId:this.cid}),t&&this.cell.stopBatch("add-vertex",{ui:!0,toolId:this.cid})}onHandleChanged({e:t}){const e=this.options,n=this.cellView;if(e.addable&&this.updatePath(),!e.removeRedundancies)return void this.stopBatch(this.eventData(t).vertexAdded);const r=n.removeRedundantLinearVertices({ui:!0,toolId:this.cid});r&&this.render(),this.blur(),this.stopBatch(this.eventData(t).vertexAdded);const{e:i,x:s,y:o}=this.getMouseEventArgs(t);if(!this.options.stopPropagation){n.notifyMouseUp(i,s,o);const{start:t}=this.eventData(i);if(t){const{x:e,y:r}=t;e===s&&r===o&&n.onClick(i,s,o)}}n.checkMouseleave(i),e.onChanged&&e.onChanged({edge:n.cell,edgeView:n})}snapVertex(t,e){const n=this.options.snapRadius||0;if(n>0){const r=this.getNeighborPoints(e),i=r.prev,s=r.next;Math.abs(t.x-i.x)<n?t.x=i.x:Math.abs(t.x-s.x)<n&&(t.x=s.x),Math.abs(t.y-i.y)<n?t.y=r.prev.y:Math.abs(t.y-s.y)<n&&(t.y=s.y)}}onHandleRemove({handle:t,e:e}){if(this.options.removable){const n=t.options.index,r=this.cellView;r.cell.removeVertexAt(n,{ui:!0}),this.options.addable&&this.updatePath(),r.checkMouseleave(this.normalizeEvent(e))}}allowAddVertex(t){const e=this.guard(t),n=this.options.addable&&this.cellView.can("vertexAddable"),r=!this.options.modifiers||db.isMatch(t,this.options.modifiers);return!e&&n&&r}onPathMouseDown(t){const e=this.cellView;if(!this.allowAddVertex(t))return;t.stopPropagation(),t.preventDefault();const n=this.normalizeEvent(t),r=this.graph.snapToGrid(n.clientX,n.clientY).toJSON();e.cell.startBatch("add-vertex",{ui:!0,toolId:this.cid});const i=e.getVertexIndex(r.x,r.y);this.snapVertex(r,i),e.cell.insertVertex(r,i,{ui:!0,toolId:this.cid}),this.render();const s=this.handles[i];this.eventData(n,{vertexAdded:!0}),s.onMouseDown(n)}onRemove(){this.resetHandles()}}(function(t){class e extends mx{get graph(){return this.options.graph}constructor(t){super(),this.options=t,this.render(),this.delegateEvents({mousedown:"onMouseDown",touchstart:"onMouseDown",dblclick:"onDoubleClick"})}render(){this.container=mx.createElement("circle",!0);const e=this.options.attrs;if("function"===typeof e){const n=t.getDefaults();this.setAttrs(Object.assign(Object.assign({},n.attrs),e(this)))}else this.setAttrs(e);this.addClass(this.prefixClassName("edge-tool-vertex"))}updatePosition(t,e){this.setAttrs({cx:t,cy:e})}onMouseDown(t){this.options.guard(t)||(t.stopPropagation(),t.preventDefault(),this.graph.view.undelegateEvents(),this.delegateDocumentEvents({mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},t.data),this.emit("change",{e:t,handle:this}))}onMouseMove(t){this.emit("changing",{e:t,handle:this})}onMouseUp(t){this.emit("changed",{e:t,handle:this}),this.undelegateDocumentEvents(),this.graph.view.delegateEvents()}onDoubleClick(t){this.emit("remove",{e:t,handle:this})}}t.Handle=e})(Ix||(Ix={})),function(t){const e=Ow.prefix("edge-tool-vertex-path");t.config({name:"vertices",snapRadius:20,addable:!0,removable:!0,removeRedundancies:!0,stopPropagation:!0,attrs:{r:6,fill:"#333",stroke:"#fff",cursor:"move","stroke-width":2},createHandle:e=>new t.Handle(e),markup:[{tagName:"path",selector:"connection",className:e,attrs:{fill:"none",stroke:"transparent","stroke-width":10,cursor:"pointer"}}],events:{["mousedown ."+e]:"onPathMouseDown",["touchstart ."+e]:"onPathMouseDown"}})}(Ix||(Ix={}));class Vx extends Cx.ToolItem{constructor(){super(...arguments),this.handles=[]}get vertices(){return this.cellView.cell.getVertices()}update(){return this.render(),this}onRender(){c.addClass(this.container,this.prefixClassName("edge-tool-segments")),this.resetHandles();const t=this.cellView,e=[...this.vertices];e.unshift(t.sourcePoint),e.push(t.targetPoint);for(let n=0,r=e.length;n<r-1;n+=1){const t=e[n],r=e[n+1],i=this.renderHandle(t,r,n);this.stamp(i.container),this.handles.push(i)}return this}renderHandle(t,e,n){const r=this.options.createHandle({index:n,graph:this.graph,guard:t=>this.guard(t),attrs:this.options.attrs||{}});return this.options.processHandle&&this.options.processHandle(r),this.updateHandle(r,t,e),this.container.appendChild(r.container),this.startHandleListening(r),r}startHandleListening(t){t.on("change",this.onHandleChange,this),t.on("changing",this.onHandleChanging,this),t.on("changed",this.onHandleChanged,this)}stopHandleListening(t){t.off("change",this.onHandleChange,this),t.off("changing",this.onHandleChanging,this),t.off("changed",this.onHandleChanged,this)}resetHandles(){const t=this.handles;this.handles=[],t&&t.forEach(t=>{this.stopHandleListening(t),t.remove()})}shiftHandleIndexes(t){const e=this.handles;for(let n=0,r=e.length;n<r;n+=1)e[n].options.index+=t}resetAnchor(t,e){const n=this.cellView.cell,r={ui:!0,toolId:this.cid};e?n.prop([t,"anchor"],e,r):n.removeProp([t,"anchor"],r)}snapHandle(t,e,n){const r=t.options.axis,i=t.options.index,s=this.cellView,o=s.cell,a=o.getVertices(),l=a[i-2]||n.sourceAnchor,c=a[i+1]||n.targetAnchor,h=this.options.snapRadius;return Math.abs(e[r]-l[r])<h?e[r]=l[r]:Math.abs(e[r]-c[r])<h&&(e[r]=c[r]),e}onHandleChanging({handle:t,e:e}){const n=this.graph,s=this.options,o=this.cellView,a=s.anchor,l=t.options.axis,c=t.options.index-1,h=this.getEventData(e),u=this.normalizeEvent(e),d=n.snapToGrid(u.clientX,u.clientY),g=this.snapHandle(t,d.clone(),h),f=i.cloneDeep(this.vertices);let p=f[c],m=f[c+1];const y=o.sourceView,b=o.sourceBBox;let v=!1,w=!1;if(p?0===c?b.containsPoint(p)?(f.shift(),this.shiftHandleIndexes(-1),v=!0):(p[l]=g[l],w=!0):p[l]=g[l]:(p=o.sourceAnchor.toJSON(),p[l]=g[l],b.containsPoint(p)?v=!0:(f.unshift(p),this.shiftHandleIndexes(1),w=!0)),"function"===typeof a&&y){if(v){const t=h.sourceAnchor.clone();t[l]=g[l];const e=r.call(a,o,t,y,o.sourceMagnet||y.container,"source",o,this);this.resetAnchor("source",e)}w&&this.resetAnchor("source",h.sourceAnchorDef)}const x=o.targetView,P=o.targetBBox;let C=!1,A=!1;if(m?c===f.length-2?P.containsPoint(m)?(f.pop(),C=!0):(m[l]=g[l],A=!0):m[l]=g[l]:(m=o.targetAnchor.toJSON(),m[l]=g[l],P.containsPoint(m)?C=!0:(f.push(m),A=!0)),"function"===typeof a&&x){if(C){const t=h.targetAnchor.clone();t[l]=g[l];const e=r.call(a,o,t,x,o.targetMagnet||x.container,"target",o,this);this.resetAnchor("target",e)}A&&this.resetAnchor("target",h.targetAnchorDef)}xb["Point"].equalPoints(f,this.vertices)||this.cellView.cell.setVertices(f,{ui:!0,toolId:this.cid}),this.updateHandle(t,p,m,0),s.stopPropagation||o.notifyMouseMove(u,d.x,d.y)}onHandleChange({handle:t,e:e}){const n=this.options,r=this.handles,s=this.cellView,o=t.options.index;if(Array.isArray(r)){for(let t=0,e=r.length;t<e;t+=1)t!==o&&r[t].hide();if(this.focus(),this.setEventData(e,{sourceAnchor:s.sourceAnchor.clone(),targetAnchor:s.targetAnchor.clone(),sourceAnchorDef:i.cloneDeep(this.cell.prop(["source","anchor"])),targetAnchorDef:i.cloneDeep(this.cell.prop(["target","anchor"]))}),this.cell.startBatch("move-segment",{ui:!0,toolId:this.cid}),!n.stopPropagation){const t=this.normalizeEvent(e),n=this.graph.snapToGrid(t.clientX,t.clientY);s.notifyMouseDown(t,n.x,n.y)}}}onHandleChanged({e:t}){const e=this.options,n=this.cellView;e.removeRedundancies&&n.removeRedundantLinearVertices({ui:!0,toolId:this.cid});const r=this.normalizeEvent(t),i=this.graph.snapToGrid(r.clientX,r.clientY);this.render(),this.blur(),this.cell.stopBatch("move-segment",{ui:!0,toolId:this.cid}),e.stopPropagation||n.notifyMouseUp(r,i.x,i.y),n.checkMouseleave(r),e.onChanged&&e.onChanged({edge:n.cell,edgeView:n})}updateHandle(t,e,n,r=0){const i=this.options.precision||0,s=Math.abs(e.x-n.x)<i,o=Math.abs(e.y-n.y)<i;if(s||o){const i=new xb["Line"](e,n),o=i.length();if(o<this.options.threshold)t.hide();else{const e=i.getCenter(),n=s?"x":"y";e[n]+=r||0;const o=i.vector().vectorAngle(new xb["Point"](1,0));t.updatePosition(e.x,e.y,o,this.cellView),t.show(),t.options.axis=n}}else t.hide()}onRemove(){this.resetHandles()}}(function(t){class e extends mx{constructor(t){super(),this.options=t,this.render(),this.delegateEvents({mousedown:"onMouseDown",touchstart:"onMouseDown"})}render(){this.container=mx.createElement("rect",!0);const e=this.options.attrs;if("function"===typeof e){const n=t.getDefaults();this.setAttrs(Object.assign(Object.assign({},n.attrs),e(this)))}else this.setAttrs(e);this.addClass(this.prefixClassName("edge-tool-segment"))}updatePosition(t,e,n,r){const i=r.getClosestPoint(new xb["Point"](t,e))||new xb["Point"](t,e);let s=c.createSVGMatrix().translate(i.x,i.y);if(i.equals({x:t,y:e}))s=s.rotate(n);else{const n=new xb["Line"](t,e,i.x,i.y);let r=n.vector().vectorAngle(new xb["Point"](1,0));0!==r&&(r+=90),s=s.rotate(r)}this.setAttrs({transform:c.matrixToTransformString(s),cursor:n%180===0?"row-resize":"col-resize"})}onMouseDown(t){this.options.guard(t)||(this.trigger("change",{e:t,handle:this}),t.stopPropagation(),t.preventDefault(),this.options.graph.view.undelegateEvents(),this.delegateDocumentEvents({mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},t.data))}onMouseMove(t){this.emit("changing",{e:t,handle:this})}onMouseUp(t){this.emit("changed",{e:t,handle:this}),this.undelegateDocumentEvents(),this.options.graph.view.delegateEvents()}show(){this.container.style.display=""}hide(){this.container.style.display="none"}}t.Handle=e})(Vx||(Vx={})),function(t){t.config({name:"segments",precision:.5,threshold:40,snapRadius:10,stopPropagation:!0,removeRedundancies:!0,attrs:{width:20,height:8,x:-10,y:-4,rx:4,ry:4,fill:"#333",stroke:"#fff","stroke-width":2},createHandle:e=>new t.Handle(e),anchor:Nx})}(Vx||(Vx={}));class zx extends Cx.ToolItem{get type(){return this.options.type}onRender(){c.addClass(this.container,this.prefixClassName(`edge-tool-${this.type}-anchor`)),this.toggleArea(!1),this.update()}update(){const t=this.type,e=this.cellView,n=e.getTerminalView(t);return n?(this.updateAnchor(),this.updateArea(),this.container.style.display=""):this.container.style.display="none",this}updateAnchor(){const t=this.childNodes;if(!t)return;const e=t.anchor;if(!e)return;const n=this.type,r=this.cellView,i=this.options,s=r.getTerminalAnchor(n),o=r.cell.prop([n,"anchor"]);e.setAttribute("transform",`translate(${s.x}, ${s.y})`);const a=o?i.customAnchorAttrs:i.defaultAnchorAttrs;a&&Object.keys(a).forEach(t=>{e.setAttribute(t,a[t])})}updateArea(){const t=this.childNodes;if(!t)return;const e=t.area;if(!e)return;const n=this.type,r=this.cellView,i=r.getTerminalView(n);if(i){const t=i.cell,s=r.getTerminalMagnet(n);let o,a,l,h=this.options.areaPadding||0;Number.isFinite(h)||(h=0),i.isEdgeElement(s)?(o=i.getBBox(),a=0,l=o.getCenter()):(o=i.getUnrotatedBBoxOfElement(s),a=t.getAngle(),l=o.getCenter(),a&&l.rotate(-a,t.getBBox().getCenter())),o.inflate(h),c.attr(e,{x:-o.width/2,y:-o.height/2,width:o.width,height:o.height,transform:`translate(${l.x}, ${l.y}) rotate(${a})`})}}toggleArea(t){if(this.childNodes){const e=this.childNodes.area;e&&(e.style.display=t?"":"none")}}onMouseDown(t){this.guard(t)||(t.stopPropagation(),t.preventDefault(),this.graph.view.undelegateEvents(),this.options.documentEvents&&this.delegateDocumentEvents(this.options.documentEvents),this.focus(),this.toggleArea(this.options.restrictArea),this.cell.startBatch("move-anchor",{ui:!0,toolId:this.cid}))}resetAnchor(t){const e=this.type,n=this.cell;t?n.prop([e,"anchor"],t,{rewrite:!0,ui:!0,toolId:this.cid}):n.removeProp([e,"anchor"],{ui:!0,toolId:this.cid})}onMouseMove(t){const e=this.type,n=this.cellView,i=n.getTerminalView(e);if(null==i)return;const s=this.normalizeEvent(t),o=i.cell,a=n.getTerminalMagnet(e);let l=this.graph.coord.clientToLocalPoint(s.clientX,s.clientY);const c=this.options.snap;if("function"===typeof c){const t=r.call(c,n,l,i,a,e,n,this);l=xb["Point"].create(t)}if(this.options.restrictArea)if(i.isEdgeElement(a)){const t=i.getClosestPoint(l);t&&(l=t)}else{const t=i.getUnrotatedBBoxOfElement(a),e=o.getAngle(),n=o.getBBox().getCenter(),r=l.clone().rotate(e,n);t.containsPoint(r)||(l=t.getNearestPointToPoint(r).rotate(-e,n))}let h;const u=this.options.anchor;"function"===typeof u&&(h=r.call(u,n,l,i,a,e,n,this)),this.resetAnchor(h),this.update()}onMouseUp(t){this.graph.view.delegateEvents(),this.undelegateDocumentEvents(),this.blur(),this.toggleArea(!1);const e=this.cellView;this.options.removeRedundancies&&e.removeRedundantLinearVertices({ui:!0,toolId:this.cid}),this.cell.stopBatch("move-anchor",{ui:!0,toolId:this.cid})}onDblClick(){const t=this.options.resetAnchor;t&&this.resetAnchor(!0===t?void 0:t),this.update()}}(function(t){t.config({tagName:"g",markup:[{tagName:"circle",selector:"anchor",attrs:{cursor:"pointer"}},{tagName:"rect",selector:"area",attrs:{"pointer-events":"none",fill:"none",stroke:"#33334F","stroke-dasharray":"2,4",rx:5,ry:5}}],events:{mousedown:"onMouseDown",touchstart:"onMouseDown",dblclick:"onDblClick"},documentEvents:{mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},customAnchorAttrs:{"stroke-width":4,stroke:"#33334F",fill:"#FFFFFF",r:5},defaultAnchorAttrs:{"stroke-width":2,stroke:"#FFFFFF",fill:"#33334F",r:6},areaPadding:6,snapRadius:10,resetAnchor:!0,restrictArea:!0,removeRedundancies:!0,anchor:Nx,snap(t,e,n,r,i,s){const o=s.options.snapRadius||0,a="source"===r,l=a?0:-1,c=this.cell.getVertexAt(l)||this.getTerminalAnchor(a?"target":"source");return c&&(Math.abs(c.x-t.x)<o&&(t.x=c.x),Math.abs(c.y-t.y)<o&&(t.y=c.y)),t}})})(zx||(zx={}));const Fx=zx.define({name:"source-anchor",type:"source"}),$x=zx.define({name:"target-anchor",type:"target"});var Gx=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class qx extends Cx.ToolItem{get type(){return this.options.type}get ratio(){return this.options.ratio}init(){if(this.options.attrs){const t=this.options.attrs,{class:e}=t,n=Gx(t,["class"]);this.setAttrs(n,this.container),e&&c.addClass(this.container,e)}}onRender(){c.addClass(this.container,this.prefixClassName(`edge-tool-${this.type}-arrowhead`)),this.update()}update(){const t=this.ratio,e=this.cellView,n=e.getTangentAtRatio(t),r=n?n.start:e.getPointAtRatio(t),i=n&&n.vector().vectorAngle(new xb["Point"](1,0))||0;if(!r)return this;const s=c.createSVGMatrix().translate(r.x,r.y).rotate(i);return c.transform(this.container,s,{absolute:!0}),this}onMouseDown(t){if(this.guard(t))return;t.stopPropagation(),t.preventDefault();const e=this.cellView;if(e.can("arrowheadMovable")){e.cell.startBatch("move-arrowhead",{ui:!0,toolId:this.cid});const n=this.graph.snapToGrid(t.clientX,t.clientY),r=e.prepareArrowheadDragging(this.type,{x:n.x,y:n.y,options:Object.assign(Object.assign({},this.options),{toolId:this.cid})});this.cellView.setEventData(t,r),this.delegateDocumentEvents(this.options.documentEvents,t.data),e.graph.view.undelegateEvents(),this.container.style.pointerEvents="none"}this.focus()}onMouseMove(t){const e=this.normalizeEvent(t),n=this.graph.snapToGrid(e.clientX,e.clientY);this.cellView.onMouseMove(e,n.x,n.y),this.update()}onMouseUp(t){this.undelegateDocumentEvents();const e=this.normalizeEvent(t),n=this.cellView,r=this.graph.snapToGrid(e.clientX,e.clientY);n.onMouseUp(e,r.x,r.y),this.graph.view.delegateEvents(),this.blur(),this.container.style.pointerEvents="",n.cell.stopBatch("move-arrowhead",{ui:!0,toolId:this.cid})}}(function(t){t.config({tagName:"path",isSVGElement:!0,events:{mousedown:"onMouseDown",touchstart:"onMouseDown"},documentEvents:{mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"}})})(qx||(qx={}));const _x=qx.define({name:"source-arrowhead",type:"source",ratio:0,attrs:{d:"M 10 -8 -10 0 10 8 Z",fill:"#333",stroke:"#fff","stroke-width":2,cursor:"move"}}),Hx=qx.define({name:"target-arrowhead",type:"target",ratio:1,attrs:{d:"M -10 -8 10 0 -10 8 Z",fill:"#333",stroke:"#fff","stroke-width":2,cursor:"move"}});class Ux extends Cx.ToolItem{constructor(){super(...arguments),this.labelIndex=-1,this.distance=.5,this.dblClick=this.onCellDblClick.bind(this)}onRender(){const t=this.cellView;t&&t.on("cell:dblclick",this.dblClick)}createElement(){const t=[this.prefixClassName((this.cell.isEdge()?"edge":"node")+"-tool-editor"),this.prefixClassName("cell-tool-editor")];this.editor=Cx.createElement("div",!1),this.addClass(t,this.editor),this.editor.contentEditable="true",this.container.appendChild(this.editor)}removeElement(){this.undelegateDocumentEvents(),this.editor&&(this.container.removeChild(this.editor),this.editor=null)}updateEditor(){const{cell:t,editor:e}=this;if(!e)return;const{style:n}=e;t.isNode()?this.updateNodeEditorTransform():t.isEdge()&&this.updateEdgeEditorTransform();const{attrs:r}=this.options;n.fontSize=r.fontSize+"px",n.fontFamily=r.fontFamily,n.color=r.color,n.backgroundColor=r.backgroundColor;const i=this.getCellText()||"";return e.innerText=i,this.setCellText(""),this}updateNodeEditorTransform(){const{graph:t,cell:e,editor:n}=this;if(!n)return;let r=xb["Point"].create(),i=20,s="",{x:o,y:l}=this.options;const{width:c,height:h}=this.options;if("undefined"!==typeof o&&"undefined"!==typeof l){const t=e.getBBox();o=a.normalizePercentage(o,t.width),l=a.normalizePercentage(l,t.height),r=t.topLeft.translate(o,l),i=t.width-2*o}else{const t=e.getBBox();r=t.center,i=t.width-4,s="translate(-50%, -50%)"}const u=t.scale(),{style:d}=n;r=t.localToGraph(r),d.left=r.x+"px",d.top=r.y+"px",d.transform=`scale(${u.sx}, ${u.sy}) ${s}`,d.minWidth=i+"px","number"===typeof c&&(d.width=c+"px"),"number"===typeof h&&(d.height=h+"px")}updateEdgeEditorTransform(){if(!this.event)return;const{graph:t,editor:e}=this;if(!e)return;let n=xb["Point"].create(),r=20;const{style:i}=e,s=this.event.target,o=s.parentElement,a=o&&c.hasClass(o,this.prefixClassName("edge-label"));if(a){const t=o.getAttribute("data-index")||"0";this.labelIndex=parseInt(t,10);const e=o.getAttribute("transform"),{translation:i}=c.parseTransformString(e);n=new xb["Point"](i.tx,i.ty),r=jw.getBBox(s).width}else{if(!this.options.labelAddable)return this;n=t.clientToLocal(xb["Point"].create(this.event.clientX,this.event.clientY));const e=this.cellView,r=e.path.closestPointLength(n);this.distance=r,this.labelIndex=-1}n=t.localToGraph(n);const l=t.scale();i.left=n.x+"px",i.top=n.y+"px",i.minWidth=r+"px",i.transform=`scale(${l.sx}, ${l.sy}) translate(-50%, -50%)`}onDocumentMouseUp(t){if(this.editor&&t.target!==this.editor){const t=this.editor.innerText.replace(/\n$/,"")||"";this.setCellText(""!==t?t:null),this.removeElement()}}onCellDblClick({e:t}){this.editor||(t.stopPropagation(),this.removeElement(),this.event=t,this.createElement(),this.updateEditor(),this.autoFocus(),this.delegateDocumentEvents(this.options.documentEvents))}onMouseDown(t){t.stopPropagation()}autoFocus(){setTimeout(()=>{this.editor&&(this.editor.focus(),this.selectText())})}selectText(){if(window.getSelection&&this.editor){const t=document.createRange(),e=window.getSelection();t.selectNodeContents(this.editor),e.removeAllRanges(),e.addRange(t)}}getCellText(){const{getText:t}=this.options;if("function"===typeof t)return r.call(t,this.cellView,{cell:this.cell,index:this.labelIndex});if("string"===typeof t){if(this.cell.isNode())return this.cell.attr(t);if(this.cell.isEdge()&&-1!==this.labelIndex)return this.cell.prop(`labels/${this.labelIndex}/attrs/${t}`)}}setCellText(t){const e=this.options.setText;if("function"!==typeof e){if("string"===typeof e){if(this.cell.isNode())return void(null!==t&&this.cell.attr(e,t));if(this.cell.isEdge()){const n=this.cell;if(-1===this.labelIndex){if(t){const r={position:{distance:this.distance},attrs:{}};i.setByPath(r,"attrs/"+e,t),n.appendLabel(r)}}else null!==t?n.prop(`labels/${this.labelIndex}/attrs/${e}`,t):"number"===typeof this.labelIndex&&n.removeLabelAt(this.labelIndex)}}}else r.call(e,this.cellView,{cell:this.cell,value:t,index:this.labelIndex,distance:this.distance})}onRemove(){const t=this.cellView;t&&t.off("cell:dblclick",this.dblClick),this.removeElement()}}(function(t){t.config({tagName:"div",isSVGElement:!1,events:{mousedown:"onMouseDown",touchstart:"onMouseDown"},documentEvents:{mouseup:"onDocumentMouseUp",touchend:"onDocumentMouseUp",touchcancel:"onDocumentMouseUp"}})})(Ux||(Ux={})),function(t){t.NodeEditor=t.define({attrs:{fontSize:14,fontFamily:"Arial, helvetica, sans-serif",color:"#000",backgroundColor:"#fff"},getText:"text/text",setText:"text/text"}),t.EdgeEditor=t.define({attrs:{fontSize:14,fontFamily:"Arial, helvetica, sans-serif",color:"#000",backgroundColor:"#fff"},labelAddable:!0,getText:"label/text",setText:"label/text"})}(Ux||(Ux={}));var Wx,Jx,Xx=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};(function(t){t.presets={boundary:Dx,button:Rx,"button-remove":Rx.Remove,"node-editor":Ux.NodeEditor},t.registry=Pb.create({type:"node tool",process(t,e){if("function"===typeof e)return e;let n=Cx.ToolItem;const{inherit:r}=e,i=Xx(e,["inherit"]);if(r){const t=this.get(r);null==t?this.onNotFound(r,"inherited"):n=t}return null==i.name&&(i.name=t),n.define.call(n,i)}}),t.registry.register(t.presets,!0)})(Wx||(Wx={})),function(t){t.presets={boundary:Dx,vertices:Ix,segments:Vx,button:Rx,"button-remove":Rx.Remove,"source-anchor":Fx,"target-anchor":$x,"source-arrowhead":_x,"target-arrowhead":Hx,"edge-editor":Ux.EdgeEditor},t.registry=Pb.create({type:"edge tool",process(t,e){if("function"===typeof e)return e;let n=Cx.ToolItem;const{inherit:r}=e,i=Xx(e,["inherit"]);if(r){const t=this.get(r);null==t?this.onNotFound(r,"inherited"):n=t}return null==i.name&&(i.name=t),n.define.call(n,i)}}),t.registry.register(t.presets,!0)}(Jx||(Jx={}));const Yx=sP("center"),Zx=sP("topCenter"),Kx=sP("bottomCenter"),Qx=sP("leftMiddle"),tP=sP("rightMiddle"),eP=sP("topLeft"),nP=sP("topRight"),rP=sP("bottomLeft"),iP=sP("bottomRight");function sP(t){return function(e,n,r,i={}){const s=i.rotate?e.getUnrotatedBBoxOfElement(n):e.getBBoxOfElement(n),o=s[t];o.x+=a.normalizePercentage(i.dx,s.width),o.y+=a.normalizePercentage(i.dy,s.height);const l=e.cell;return i.rotate?o.rotate(-l.getAngle(),l.getBBox().getCenter()):o}}function oP(t){return function(e,n,r,i){if(r instanceof Element){const s=this.graph.findViewByElem(r);let o;if(s)if(s.isEdgeElement(r)){const t=null!=i.fixedAt?i.fixedAt:"50%";o=aP(s,t)}else o=s.getBBoxOfElement(r).getCenter();else o=new xb["Point"];return t.call(this,e,n,o,i)}return t.apply(this,arguments)}}function aP(t,e){const n=a.isPercentage(e),r="string"===typeof e?parseFloat(e):e;return n?t.getPointAtRatio(r/100):t.getPointAtLength(r)}const lP=function(t,e,n,r){const i=xb["Angle"].normalize(t.cell.getAngle()),s=t.getBBoxOfElement(e),o=s.getCenter(),a=s.getTopLeft(),l=s.getBottomRight();let c=r.padding;if(Number.isFinite(c)||(c=0),a.y+c<=n.y&&n.y<=l.y-c){const t=n.y-o.y;o.x+=0===i||180===i?0:1*t/Math.tan(xb["Angle"].toRad(i)),o.y+=t}else if(a.x+c<=n.x&&n.x<=l.x-c){const t=n.x-o.x;o.y+=90===i||270===i?0:t*Math.tan(xb["Angle"].toRad(i)),o.x+=t}return o},cP=oP(lP),hP=function(t,e,n,r,i){const s=t.cell.getConnectionPoint(this.cell,i);return(r.dx||r.dy)&&s.translate(r.dx||0,r.dy||0),s},uP=function(t,e,n,r){let i,s,o=0;const a=t.cell;r.rotate?(i=t.getUnrotatedBBoxOfElement(e),s=a.getBBox().getCenter(),o=a.getAngle()):i=t.getBBoxOfElement(e);const l=r.padding;null!=l&&Number.isFinite(l)&&i.inflate(l),r.rotate&&n.rotate(o,s);const c=i.getNearestSideToPoint(n);let h;switch(c){case"left":h=i.getLeftMiddle();break;case"right":h=i.getRightMiddle();break;case"top":h=i.getTopCenter();break;case"bottom":h=i.getBottomCenter();break;default:break}const u=r.direction;return"H"===u?"top"!==c&&"bottom"!==c||(h=n.x<=i.x+i.width?i.getLeftMiddle():i.getRightMiddle()):"V"===u&&(h=n.y<=i.y+i.height?i.getTopCenter():i.getBottomCenter()),r.rotate?h.rotate(-o,s):h},dP=oP(uP);var gP;(function(t){t.presets=w,t.registry=Pb.create({type:"node endpoint"}),t.registry.register(t.presets,!0)})(gP||(gP={}));const fP=function(t,e,n,r){let i=null!=r.ratio?r.ratio:.5;return i>1&&(i/=100),t.getPointAtRatio(i)},pP=function(t,e,n,r){const i=null!=r.length?r.length:20;return t.getPointAtLength(i)},mP=function(t,e,n,r){const i=t.getClosestPoint(n);return null!=i?i:new xb["Point"]},yP=oP(mP),bP=function(t,e,n,i){const s=1e6,o=t.getConnection(),a=t.getConnectionSubdivisions(),l=new xb["Line"](n.clone().translate(0,s),n.clone().translate(0,-s)),c=new xb["Line"](n.clone().translate(s,0),n.clone().translate(-s,0)),h=l.intersect(o,{segmentSubdivisions:a}),u=c.intersect(o,{segmentSubdivisions:a}),d=[];return h&&d.push(...h),u&&d.push(...u),d.length>0?n.closest(d):null!=i.fallbackAt?aP(t,i.fallbackAt):r.call(mP,this,t,e,n,i)},vP=oP(bP);var wP;function xP(t,e,n){let r;if("object"===typeof n){if(Number.isFinite(n.y)){const r=new xb["Line"](e,t),{start:i,end:s}=r.parallel(n.y);e=i,t=s}r=n.x}else r=n;if(null==r||!Number.isFinite(r))return t;const i=t.distance(e);return 0===r&&i>0?t:t.move(e,-Math.min(r,i-1))}function PP(t){const e=t.getAttribute("stroke-width");return null===e?0:parseFloat(e)||0}function CP(t){if(null==t)return null;let e=t;do{let t=e.tagName;if("string"!==typeof t)return null;if(t=t.toUpperCase(),"G"===t)e=e.firstElementChild;else{if("TITLE"!==t)break;e=e.nextElementSibling}}while(e);return e}(function(t){t.presets=x,t.registry=Pb.create({type:"edge endpoint"}),t.registry.register(t.presets,!0)})(wP||(wP={}));const AP=function(t,e,n,r){const i=e.getBBoxOfElement(n);r.stroked&&i.inflate(PP(n)/2);const s=t.intersect(i),o=s&&s.length?t.start.closest(s):t.end;return xP(o,t.start,r.offset)},OP=function(t,e,n,i,s){const o=e.cell,a=o.isNode()?o.getAngle():0;if(0===a)return r.call(AP,this,t,e,n,i,s);const l=e.getUnrotatedBBoxOfElement(n);i.stroked&&l.inflate(PP(n)/2);const c=l.getCenter(),h=t.clone().rotate(a,c),u=h.setLength(1e6).intersect(l),d=u&&u.length?h.start.closest(u).rotate(-a,c):t.end;return xP(d,t.start,i.offset)},EP=function(t,e,n,r){let s,o;const a=t.end,l=r.selector;if(s="string"===typeof l?e.findOne(l):Array.isArray(l)?i.getByPath(n,l):CP(n),!c.isSVGGraphicsElement(s)){if(s===n||!c.isSVGGraphicsElement(n))return a;s=n}const h=e.getShapeOfElement(s),u=e.getMatrixOfElement(s),d=e.getRootTranslatedMatrix(),g=e.getRootRotatedMatrix(),f=d.multiply(g).multiply(u),p=f.inverse(),m=jw.transformLine(t,p),y=m.start.clone(),b=e.getDataOfElement(s);if(!1===r.insideout){null==b.shapeBBox&&(b.shapeBBox=h.bbox());const t=b.shapeBBox;if(null!=t&&t.containsPoint(y))return a}let v;if(!0===r.extrapolate&&m.setLength(1e6),xb["Path"].isPath(h)){const t=r.precision||2;null==b.segmentSubdivisions&&(b.segmentSubdivisions=h.getSegmentSubdivisions({precision:t})),v={precision:t,segmentSubdivisions:b.segmentSubdivisions},o=m.intersect(h,v)}else o=m.intersect(h);o?Array.isArray(o)&&(o=y.closest(o)):!0===r.sticky&&(o=xb["Rectangle"].isRectangle(h)?h.getNearestPointToPoint(y):xb["Ellipse"].isEllipse(h)?h.intersectsWithLineFromCenterToPoint(y):h.closestPoint(y,v));const w=o?jw.transformPoint(o,f):a;let x=r.offset||0;return!1!==r.stroked&&("object"===typeof x?(x=Object.assign({},x),null==x.x&&(x.x=0),x.x+=PP(s)/2):x+=PP(s)/2),xP(w,t.start,x)};function SP(t,e,n=0){const{start:r,end:i}=t;let s,o,a,l;switch(e){case"left":l="x",s=i,o=r,a=-1;break;case"right":l="x",s=r,o=i,a=1;break;case"top":l="y",s=i,o=r,a=-1;break;case"bottom":l="y",s=r,o=i,a=1;break;default:return}r[l]<i[l]?s[l]=o[l]:o[l]=s[l],Number.isFinite(n)&&(s[l]+=a*n,o[l]+=a*n)}const MP=function(t,e,n,r){const{alignOffset:i,align:s}=r;return s&&SP(t,s,i),xP(t.end,t.start,r.offset)};var TP;(function(t){t.presets=P,t.registry=Pb.create({type:"connection point"}),t.registry.register(t.presets,!0)})(TP||(TP={}));const jP=function(t){return[...t]},kP=function(t,e,n){const r=e.side||"bottom",i=a.normalizeSides(e.padding||40),s=n.sourceBBox,o=n.targetBBox,l=s.getCenter(),c=o.getCenter();let h,u,d;switch(r){case"top":d=-1,h="y",u="height";break;case"left":d=-1,h="x",u="width";break;case"right":d=1,h="x",u="width";break;case"bottom":default:d=1,h="y",u="height";break}return l[h]+=d*(s[u]/2+i[r]),c[h]+=d*(o[u]/2+i[r]),d*(l[h]-c[h])>0?c[h]=l[h]:l[h]=c[h],[l.toJSON(),...t,c.toJSON()]};function NP(t){return new xb["Rectangle"](t.x,t.y,0,0)}function LP(t={}){const e=a.normalizeSides(t.padding||20);return{x:-e.left,y:-e.top,width:e.left+e.right,height:e.top+e.bottom}}function RP(t,e={}){return t.sourceBBox.clone().moveAndExpand(LP(e))}function BP(t,e={}){return t.targetBBox.clone().moveAndExpand(LP(e))}function DP(t,e={}){if(t.sourceAnchor)return t.sourceAnchor;const n=RP(t,e);return n.getCenter()}function IP(t,e={}){if(t.targetAnchor)return t.targetAnchor;const n=BP(t,e);return n.getCenter()}const VP=function(t,e,n){let r=RP(n,e),i=BP(n,e);const s=DP(n,e),o=IP(n,e);r=r.union(NP(s)),i=i.union(NP(o));const a=t.map(t=>xb["Point"].create(t));a.unshift(s),a.push(o);let l=null;const c=[];for(let h=0,u=a.length-1;h<u;h+=1){let t=null;const n=a[h],s=a[h+1],o=null!=zP.getBearing(n,s);if(0===h)h+1===u?r.intersectsWithRect(i.clone().inflate(1))?t=zP.insideNode(n,s,r,i):o||(t=zP.nodeToNode(n,s,r,i)):r.containsPoint(s)?t=zP.insideNode(n,s,r,NP(s).moveAndExpand(LP(e))):o||(t=zP.nodeToVertex(n,s,r));else if(h+1===u){const r=o&&zP.getBearing(s,n)===l;i.containsPoint(n)||r?t=zP.insideNode(n,s,NP(n).moveAndExpand(LP(e)),i,l):o||(t=zP.vertexToNode(n,s,i,l))}else o||(t=zP.vertexToVertex(n,s,l));t?(c.push(...t.points),l=t.direction):l=zP.getBearing(n,s),h+1<u&&c.push(s)}return c};var zP;(function(t){const e={N:"S",S:"N",E:"W",W:"E"},n={N:-Math.PI/2*3,S:-Math.PI/2,E:0,W:Math.PI};function r(t,e,n){let r=new xb["Point"](t.x,e.y);return n.containsPoint(r)&&(r=new xb["Point"](e.x,t.y)),r}function i(t,e){return t["W"===e||"E"===e?"width":"height"]}function o(t,e){return t.x===e.x?t.y>e.y?"N":"S":t.y===e.y?t.x>e.x?"W":"E":null}function a(t,n,r){const i=new xb["Point"](t.x,n.y),s=new xb["Point"](n.x,t.y),a=o(t,i),l=o(t,s),c=r?e[r]:null,h=a===r||a!==c&&(l===c||l!==r)?i:s;return{points:[h],direction:o(h,n)}}function l(t,e,n){const i=r(t,e,n);return{points:[i],direction:o(i,e)}}function c(t,e,n,a){const l=[new xb["Point"](t.x,e.y),new xb["Point"](e.x,t.y)],c=l.filter(t=>!n.containsPoint(t)),h=c.filter(e=>o(e,t)!==a);let u;if(h.length>0)return u=h.filter(e=>o(t,e)===a).pop(),u=u||h[0],{points:[u],direction:o(u,e)};{u=s.difference(l,c)[0];const h=xb["Point"].create(e).move(u,-i(n,a)/2),d=r(h,t,n);return{points:[d,h],direction:o(h,e)}}}function h(t,e,n,r){let s=l(e,t,r);const c=s.points[0];if(n.containsPoint(c)){s=l(t,e,n);const h=s.points[0];if(r.containsPoint(h)){const u=xb["Point"].create(t).move(h,-i(n,o(t,h))/2),d=xb["Point"].create(e).move(c,-i(r,o(e,c))/2),g=new xb["Line"](u,d).getCenter(),f=l(t,g,n),p=a(g,e,f.direction);s.points=[f.points[0],p.points[0]],s.direction=p.direction}}return s}function u(t,e,i,s,a){const l=i.union(s).inflate(1),c=l.getCenter(),h=c.distance(e)>c.distance(t),u=h?e:t,d=h?t:e;let g,f,p,m;a?(g=xb["Point"].fromPolar(l.width+l.height,n[a],u),g=l.getNearestPointToPoint(g).move(g,-1)):g=l.getNearestPointToPoint(u).move(u,1),f=r(g,d,l),g.round().equals(f.round())?(f=xb["Point"].fromPolar(l.width+l.height,xb["Angle"].toRad(g.theta(u))+Math.PI/2,d),f=l.getNearestPointToPoint(f).move(d,1).round(),p=r(g,f,l),m=h?[f,p,g]:[g,p,f]):m=h?[f,g]:[g,f];const y=o(h?g:f,e);return{points:m,direction:y}}t.getBBoxSize=i,t.getBearing=o,t.vertexToVertex=a,t.nodeToVertex=l,t.vertexToNode=c,t.nodeToNode=h,t.insideNode=u})(zP||(zP={}));const FP={step:10,maxLoopCount:2e3,precision:1,maxDirectionChange:90,perpendicular:!0,excludeTerminals:[],excludeNodes:[],excludeShapes:[],startDirections:["top","right","bottom","left"],endDirections:["top","right","bottom","left"],directionMap:{top:{x:0,y:-1},right:{x:1,y:0},bottom:{x:0,y:1},left:{x:-1,y:0}},cost(){const t=$P(this.step,this);return t},directions(){const t=$P(this.step,this),e=$P(this.cost,this);return[{cost:e,offsetX:t,offsetY:0},{cost:e,offsetX:-t,offsetY:0},{cost:e,offsetX:0,offsetY:t},{cost:e,offsetX:0,offsetY:-t}]},penalties(){const t=$P(this.step,this);return{0:0,45:t/2,90:t/2}},paddingBox(){const t=$P(this.step,this);return{x:-t,y:-t,width:2*t,height:2*t}},fallbackRouter:VP,draggingRouter:null,snapToGrid:!0};function $P(t,e){return"function"===typeof t?t.call(e):t}function GP(t){const e=Object.keys(t).reduce((e,n)=>{const r=e;return r[n]="fallbackRouter"===n||"draggingRouter"===n||"fallbackRoute"===n?t[n]:$P(t[n],t),e},{});if(e.padding){const t=a.normalizeSides(e.padding);e.paddingBox={x:-t.left,y:-t.top,width:t.left+t.right,height:t.top+t.bottom}}return e.directions.forEach(t=>{const e=new xb["Point"](0,0),n=new xb["Point"](t.offsetX,t.offsetY);t.angle=xb["Angle"].normalize(e.theta(n))}),e}const qP=1,_P=2;class HP{constructor(){this.items=[],this.hash={},this.values={}}add(t,e){this.hash[t]?this.items.splice(this.items.indexOf(t),1):this.hash[t]=qP,this.values[t]=e;const n=s.sortedIndexBy(this.items,t,t=>this.values[t]);this.items.splice(n,0,t)}pop(){const t=this.items.shift();return t&&(this.hash[t]=_P),t}isOpen(t){return this.hash[t]===qP}isClose(t){return this.hash[t]===_P}isEmpty(){return 0===this.items.length}}class UP{constructor(t){this.options=t,this.mapGridSize=100,this.map={}}build(t,e){const n=this.options,r=n.excludeTerminals.reduce((n,r)=>{const i=e[r];if(i){const e=t.getCell(i.cell);e&&n.push(e)}return n},[]);let i=[];const o=t.getCell(e.getSourceCellId());o&&(i=s.union(i,o.getAncestors().map(t=>t.id)));const a=t.getCell(e.getTargetCellId());a&&(i=s.union(i,a.getAncestors().map(t=>t.id)));const l=this.mapGridSize;return t.getNodes().reduce((t,e)=>{const s=r.some(t=>t.id===e.id),o=!!e.shape&&n.excludeShapes.includes(e.shape),a=n.excludeNodes.some(t=>"string"===typeof t?e.id===t:t===e),c=i.includes(e.id),h=o||s||a||c;if(e.isVisible()&&!h){const r=e.getBBox().moveAndExpand(n.paddingBox),i=r.getOrigin().snapToGrid(l),s=r.getCorner().snapToGrid(l);for(let e=i.x;e<=s.x;e+=l)for(let n=i.y;n<=s.y;n+=l){const i=new xb["Point"](e,n).toString();null==t[i]&&(t[i]=[]),t[i].push(r)}}return t},this.map),this}isAccessible(t){const e=t.clone().snapToGrid(this.mapGridSize).toString(),n=this.map[e];return!n||n.every(e=>!e.containsPoint(t))}}function WP(t,e){const n=t.sourceBBox.clone();return e&&e.paddingBox?n.moveAndExpand(e.paddingBox):n}function JP(t,e){const n=t.targetBBox.clone();return e&&e.paddingBox?n.moveAndExpand(e.paddingBox):n}function XP(t,e){if(t.sourceAnchor)return t.sourceAnchor;const n=WP(t,e);return n.getCenter()}function YP(t,e){if(t.targetAnchor)return t.targetAnchor;const n=JP(t,e);return n.getCenter()}function ZP(t,e,n,r,i){const s=360/n,o=t.theta(KP(t,e,r,i)),a=xb["Angle"].normalize(o+s/2);return s*Math.floor(a/s)}function KP(t,e,n,r){const i=r.step,s=e.x-t.x,o=e.y-t.y,a=s/n.x,l=o/n.y,c=a*i,h=l*i;return new xb["Point"](t.x+c,t.y+h)}function QP(t,e){const n=Math.abs(t-e);return n>180?360-n:n}function tC(t,e){const n=e.step;return e.directions.forEach(e=>{e.gridOffsetX=e.offsetX/n*t.x,e.gridOffsetY=e.offsetY/n*t.y}),e.directions}function eC(t,e,n){return{source:e.clone(),x:nC(n.x-e.x,t),y:nC(n.y-e.y,t)}}function nC(t,e){if(!t)return e;const n=Math.abs(t),r=Math.round(n/e);if(!r)return n;const i=r*e,s=n-i,o=s/r;return e+o}function rC(t,e){const n=e.source,r=xb["GeometryUtil"].snapToGrid(t.x-n.x,e.x)+n.x,i=xb["GeometryUtil"].snapToGrid(t.y-n.y,e.y)+n.y;return new xb["Point"](r,i)}function iC(t,e){return t.round(e)}function sC(t,e,n){return iC(rC(t.clone(),e),n)}function oC(t){return t.toString()}function aC(t){return new xb["Point"](0===t.x?0:Math.abs(t.x)/t.x,0===t.y?0:Math.abs(t.y)/t.y)}function lC(t,e){let n=1/0;for(let r=0,i=e.length;r<i;r+=1){const i=t.manhattanDistance(e[r]);i<n&&(n=i)}return n}function cC(t,e,n,r,i){const s=i.precision,o=i.directionMap,a=t.diff(e.getCenter()),l=Object.keys(o).reduce((i,l)=>{if(n.includes(l)){const n=o[l],c=new xb["Point"](t.x+n.x*(Math.abs(a.x)+e.width),t.y+n.y*(Math.abs(a.y)+e.height)),h=new xb["Line"](t,c),u=h.intersect(e)||[];let d,g=null;for(let e=0;e<u.length;e+=1){const n=u[e],r=t.squaredDistance(n);(null==d||r>d)&&(d=r,g=n)}if(g){let t=sC(g,r,s);e.containsPoint(t)&&(t=sC(t.translate(n.x*r.x,n.y*r.y),r,s)),i.push(t)}}return i},[]);return e.containsPoint(t)||l.push(sC(t,r,s)),l}function hC(t,e,n,r,i){const s=[];let o,a=aC(i.diff(n)),l=oC(n),c=t[l];while(c){o=e[l];const n=aC(o.diff(c));n.equals(a)||(s.unshift(o),a=n),l=oC(c),c=t[l]}const h=e[l],u=aC(h.diff(r));return u.equals(a)||s.unshift(h),s}function uC(t,e,n,i,s){const o=s.precision;let a,l;a=xb["Rectangle"].isRectangle(e)?iC(XP(t,s).clone(),o):iC(e.clone(),o),l=xb["Rectangle"].isRectangle(n)?iC(YP(t,s).clone(),o):iC(n.clone(),o);const c=eC(s.step,a,l),h=a,u=l;let d,g;if(d=xb["Rectangle"].isRectangle(e)?cC(h,e,s.startDirections,c,s):[h],g=xb["Rectangle"].isRectangle(n)?cC(l,n,s.endDirections,c,s):[u],d=d.filter(t=>i.isAccessible(t)),g=g.filter(t=>i.isAccessible(t)),d.length>0&&g.length>0){const t=new HP,e={},n={},r={};for(let i=0,s=d.length;i<s;i+=1){const n=d[i],s=oC(n);t.add(s,lC(n,g)),e[s]=n,r[s]=0}const a=s.previousDirectionAngle,l=void 0===a;let f,p;const m=tC(c,s),y=m.length,b=g.reduce((t,e)=>{const n=oC(e);return t.push(n),t},[]),v=xb["Point"].equalPoints(d,g);let w=s.maxLoopCount;while(!t.isEmpty()&&w>0){const d=t.pop(),x=e[d],P=n[d],C=r[d],A=x.equals(h),O=null==P;let E;E=O?l?A?null:ZP(h,x,y,c,s):a:ZP(P,x,y,c,s);const S=O&&v;if(!S&&b.indexOf(d)>=0)return s.previousDirectionAngle=E,hC(n,e,x,h,u);for(let a=0;a<y;a+=1){f=m[a];const h=f.angle;if(p=QP(E,h),(!l||!A)&&p>s.maxDirectionChange)continue;const d=sC(x.clone().translate(f.gridOffsetX||0,f.gridOffsetY||0),c,o),v=oC(d);if(t.isClose(v)||!i.isAccessible(d))continue;if(b.indexOf(v)>=0){const t=d.equals(u);if(!t){const t=ZP(d,u,y,c,s),e=QP(h,t);if(e>s.maxDirectionChange)continue}}const w=f.cost,P=A?0:s.penalties[p],O=C+w+P;(!t.isOpen(v)||O<r[v])&&(e[v]=d,n[v]=x,r[v]=O,t.add(v,O+lC(d,g)))}w-=1}}return s.fallbackRoute?r.call(s.fallbackRoute,this,h,u,s):null}function dC(t,e=10){if(t.length<=1)return t;for(let n=0,r=t.length;n<r-1;n+=1){const r=t[n],i=t[n+1];if(r.x===i.x){const t=e*Math.round(r.x/e);r.x!==t&&(r.x=t,i.x=t)}else if(r.y===i.y){const t=e*Math.round(r.y/e);r.y!==t&&(r.y=t,i.y=t)}}return t}const gC=function(t,e,n){const i=GP(e),s=WP(n,i),o=JP(n,i),a=XP(n,i),l=new UP(i).build(n.graph.model,n.cell),c=t.map(t=>xb["Point"].create(t)),h=[];let u,d,g=a;for(let f=0,p=c.length;f<=p;f+=1){let e=null;if(u=d||s,d=c[f],null==d){d=o;const t=n.cell,l=null==t.getSourceCellId()||null==t.getTargetCellId();if(l&&"function"===typeof i.draggingRouter){const t=u===s?a:u,o=d.getOrigin();e=r.call(i.draggingRouter,n,t,o,i)}}if(null==e&&(e=uC(n,u,d,l,i)),null===e)return console.warn("Unable to execute manhattan algorithm, use orth instead"),r.call(i.fallbackRouter,this,t,i,n);const p=e[0];p&&p.equals(g)&&e.shift(),g=e[e.length-1]||g,h.push(...e)}return i.snapToGrid?dC(h,n.graph.grid.getGridSize()):h},fC=function(t,e,n){return r.call(gC,this,t,Object.assign(Object.assign({},FP),e),n)},pC={maxDirectionChange:45,directions(){const t=$P(this.step,this),e=$P(this.cost,this),n=Math.ceil(Math.sqrt(t*t<<1));return[{cost:e,offsetX:t,offsetY:0},{cost:n,offsetX:t,offsetY:t},{cost:e,offsetX:0,offsetY:t},{cost:n,offsetX:-t,offsetY:t},{cost:e,offsetX:-t,offsetY:0},{cost:n,offsetX:-t,offsetY:-t},{cost:e,offsetX:0,offsetY:-t},{cost:n,offsetX:t,offsetY:-t}]},fallbackRoute(t,e,n){const r=t.theta(e),i=[];let s={x:e.x,y:t.y},o={x:t.x,y:e.y};if(r%180>90){const t=s;s=o,o=t}const a=r%90<45?s:o,l=new xb["Line"](t,a),c=90*Math.ceil(r/90),h=xb["Point"].fromPolar(l.squaredLength(),xb["Angle"].toRad(c+135),a),u=new xb["Line"](e,h),d=l.intersectsWithLine(u),g=d||e,f=d?g:t,p=360/n.directions.length,m=f.theta(e),y=xb["Angle"].normalize(m+p/2),b=p*Math.floor(y/p);return n.previousDirectionAngle=b,g&&i.push(g.round()),i.push(e),i}},mC=function(t,e,n){return r.call(fC,this,t,Object.assign(Object.assign({},pC),e),n)},yC=function(t,e,n){const r=e.offset||32,i=null==e.min?16:e.min;let s=0,o=e.direction;const a=n.sourceBBox,l=n.targetBBox,c=a.getCenter(),h=l.getCenter();if("number"===typeof r&&(s=r),null==o){let t=l.left-a.right,e=l.top-a.bottom;t>=0&&e>=0?o=t>=e?"L":"T":t<=0&&e>=0?(t=a.left-l.right,o=t>=0&&t>=e?"R":"T"):t>=0&&e<=0?(e=a.top-l.bottom,o=e>=0?t>=e?"L":"B":"L"):(t=a.left-l.right,e=a.top-l.bottom,o=t>=0&&e>=0?t>=e?"R":"B":t<=0&&e>=0?"B":t>=0&&e<=0||Math.abs(t)>Math.abs(e)?"R":"B")}let u,d,g;"H"===o?o=h.x-c.x>=0?"L":"R":"V"===o&&(o=h.y-c.y>=0?"T":"B"),"center"===r&&("L"===o?s=(l.left-a.right)/2:"R"===o?s=(a.left-l.right)/2:"T"===o?s=(l.top-a.bottom)/2:"B"===o&&(s=(a.top-l.bottom)/2));const f="L"===o||"R"===o;if(f){if(h.y===c.y)return[...t];g="L"===o?1:-1,u="x",d="width"}else{if(h.x===c.x)return[...t];g="T"===o?1:-1,u="y",d="height"}const p=c.clone(),m=h.clone();if(p[u]+=g*(a[d]/2+s),m[u]-=g*(l[d]/2+s),f){const t=p.x,e=m.x,n=a.width/2+i,r=l.width/2+i;h.x>c.x?e<=t&&(p.x=Math.max(e,c.x+n),m.x=Math.min(t,h.x-r)):e>=t&&(p.x=Math.min(e,c.x-n),m.x=Math.max(t,h.x+r))}else{const t=p.y,e=m.y,n=a.height/2+i,r=l.height/2+i;h.y>c.y?e<=t&&(p.y=Math.max(e,c.y+n),m.y=Math.min(t,h.y-r)):e>=t&&(p.y=Math.min(e,c.y-n),m.y=Math.max(t,h.y+r))}return[p.toJSON(),...t,m.toJSON()]};function bC(t,e){if(null!=e&&!1!==e){const n="boolean"===typeof e?0:e;if(n>0){const e=xb["Point"].create(t[1]).move(t[2],n),r=xb["Point"].create(t[1]).move(t[0],n);return[e.toJSON(),...t,r.toJSON()]}{const e=t[1];return[Object.assign({},e),...t,Object.assign({},e)]}}return t}const vC=function(t,e,n){const r=e.width||50,i=e.height||80,s=i/2,o=e.angle||"auto",a=n.sourceAnchor,l=n.targetAnchor,c=n.sourceBBox,h=n.targetBBox;if(a.equals(l)){const t=t=>{const e=xb["Angle"].toRad(t),n=Math.sin(e),i=Math.cos(e),o=new xb["Point"](a.x+i*r,a.y+n*r),l=new xb["Point"](o.x-i*s,o.y-n*s),c=l.clone().rotate(-90,o),h=l.clone().rotate(90,o);return[c.toJSON(),o.toJSON(),h.toJSON()]},n=t=>{const e=a.clone().move(t,-1),n=new xb["Line"](e,t);return!c.containsPoint(t)&&!c.intersectsWithLine(n)},i=[0,90,180,270,45,135,225,315];if("number"===typeof o)return bC(t(o),e.merge);const l=c.getCenter();if(l.equals(a))return bC(t(0),e.merge);const h=l.angleBetween(a,l.clone().translate(1,0));let u=t(h);if(n(u[1]))return bC(u,e.merge);for(let r=1,s=i.length;r<s;r+=1)if(u=t(h+i[r]),n(u[1]))return bC(u,e.merge);return bC(u,e.merge)}{const t=new xb["Line"](a,l);let i=t.parallel(-r),o=i.getCenter(),u=i.start.clone().move(i.end,s),d=i.end.clone().move(i.start,s);const g=t.parallel(-1),f=new xb["Line"](g.start,o),p=new xb["Line"](g.end,o);if((c.containsPoint(o)||h.containsPoint(o)||c.intersectsWithLine(f)||c.intersectsWithLine(p)||h.intersectsWithLine(f)||h.intersectsWithLine(p))&&(i=t.parallel(r),o=i.getCenter(),u=i.start.clone().move(i.end,s),d=i.end.clone().move(i.start,s)),e.merge){const t=new xb["Line"](a,l),e=new xb["Line"](o,t.center).setLength(Number.MAX_SAFE_INTEGER),r=c.intersectsWithLine(e),i=h.intersectsWithLine(e),s=r?Array.isArray(r)?r:[r]:[];i&&(Array.isArray(i)?s.push(...i):s.push(i));const u=t.center.closest(s);u?(n.sourceAnchor=u.clone(),n.targetAnchor=u.clone()):(n.sourceAnchor=t.center.clone(),n.targetAnchor=t.center.clone())}return bC([u.toJSON(),o.toJSON(),d.toJSON()],e.merge)}};var wC;(function(t){t.presets=C,t.registry=Pb.create({type:"router"}),t.registry.register(t.presets,!0)})(wC||(wC={}));const xC=function(t,e,n,r={}){const i=[t,...n,e],s=new xb["Polyline"](i),o=new xb["Path"](s);return r.raw?o:o.serialize()},PC=function(t,e,n,r={}){const i=3===n.length?0:1,s=xb["Point"].create(n[0+i]),o=xb["Point"].create(n[2+i]),a=xb["Point"].create(n[1+i]);if(!xb["Point"].equals(t,e)){const n=new xb["Point"]((t.x+e.x)/2,(t.y+e.y)/2),r=n.angleBetween(xb["Point"].create(t).rotate(90,n),a);r>1&&(s.rotate(180-r,n),o.rotate(180-r,n),a.rotate(180-r,n))}const l=`\n     M ${t.x} ${t.y}\n     Q ${s.x} ${s.y} ${a.x} ${a.y}\n     Q ${o.x} ${o.y} ${e.x} ${e.y}\n  `;return r.raw?xb["Path"].parse(l):l},CC=function(t,e,n,r={}){const i=new xb["Path"];i.appendSegment(xb["Path"].createSegment("M",t));const s=1/3,o=2/3,a=r.radius||10;let l,c;for(let h=0,u=n.length;h<u;h+=1){const r=xb["Point"].create(n[h]),u=n[h-1]||t,d=n[h+1]||e;l=c||r.distance(u)/2,c=r.distance(d)/2;const g=-Math.min(a,l),f=-Math.min(a,c),p=r.clone().move(u,g).round(),m=r.clone().move(d,f).round(),y=new xb["Point"](s*p.x+o*r.x,o*r.y+s*p.y),b=new xb["Point"](s*m.x+o*r.x,o*r.y+s*m.y);i.appendSegment(xb["Path"].createSegment("L",p)),i.appendSegment(xb["Path"].createSegment("C",y,b,m))}return i.appendSegment(xb["Path"].createSegment("L",e)),r.raw?i:i.serialize()},AC=function(t,e,n,r={}){let i,s=r.direction;if(n&&0!==n.length){const r=[t,...n,e],s=xb["Curve"].throughPoints(r);i=new xb["Path"](s)}else if(i=new xb["Path"],i.appendSegment(xb["Path"].createSegment("M",t)),s||(s=Math.abs(t.x-e.x)>=Math.abs(t.y-e.y)?"H":"V"),"H"===s){const n=(t.x+e.x)/2;i.appendSegment(xb["Path"].createSegment("C",n,t.y,n,e.y,e.x,e.y))}else{const n=(t.y+e.y)/2;i.appendSegment(xb["Path"].createSegment("C",t.x,n,e.x,n,e.x,e.y))}return r.raw?i:i.serialize()},OC=1,EC=1/3,SC=2/3;function MC(t){let e=t.graph._jumpOverUpdateList;if(null==e&&(e=t.graph._jumpOverUpdateList=[],t.graph.on("cell:mouseup",()=>{const e=t.graph._jumpOverUpdateList;setTimeout(()=>{for(let t=0;t<e.length;t+=1)e[t].update()})}),t.graph.on("model:reseted",()=>{e=t.graph._jumpOverUpdateList=[]})),e.indexOf(t)<0){e.push(t);const n=()=>e.splice(e.indexOf(t),1);t.cell.once("change:connector",n),t.cell.once("removed",n)}}function TC(t,e,n=[]){const r=[t,...n,e],i=[];return r.forEach((t,e)=>{const n=r[e+1];null!=n&&i.push(new xb["Line"](t,n))}),i}function jC(t,e){const n=[];return e.forEach(e=>{const r=t.intersectsWithLine(e);r&&n.push(r)}),n}function kC(t,e){return new xb["Line"](t,e).squaredLength()}function NC(t,e,n){return e.reduce((r,i,s)=>{if(DC.includes(i))return r;const o=r.pop()||t,a=xb["Point"].create(i).move(o.start,-n);let l=xb["Point"].create(i).move(o.start,+n);const c=e[s+1];if(null!=c){const t=l.distance(c);t<=n&&(l=c.move(o.start,t),DC.push(c))}else{const t=a.distance(o.end);if(t<2*n+OC)return r.push(o),r}const h=l.distance(o.start);if(h<2*n+OC)return r.push(o),r;const u=new xb["Line"](a,l);return BC.push(u),r.push(new xb["Line"](o.start,a),u,new xb["Line"](l,o.end)),r},[])}function LC(t,e,n,r){const i=new xb["Path"];let s;return s=xb["Path"].createSegment("M",t[0].start),i.appendSegment(s),t.forEach((o,a)=>{if(BC.includes(o)){let t,r,a,l;if("arc"===n){t=-90,r=o.start.diff(o.end);const e=r.x<0||0===r.x&&r.y<0;e&&(t+=180);const n=o.getCenter(),c=new xb["Line"](n,o.end).rotate(t,n);let h;h=new xb["Line"](o.start,n),a=h.pointAt(2/3).rotate(t,o.start),l=c.pointAt(1/3).rotate(-t,c.end),s=xb["Path"].createSegment("C",a,l,c.end),i.appendSegment(s),h=new xb["Line"](n,o.end),a=c.pointAt(1/3).rotate(t,c.end),l=h.pointAt(1/3).rotate(-t,o.end),s=xb["Path"].createSegment("C",a,l,o.end),i.appendSegment(s)}else if("gap"===n)s=xb["Path"].createSegment("M",o.end),i.appendSegment(s);else if("cubic"===n){t=o.start.theta(o.end);const n=.6*e;let c=1.35*e;r=o.start.diff(o.end);const h=r.x<0||0===r.x&&r.y<0;h&&(c*=-1),a=new xb["Point"](o.start.x+n,o.start.y+c).rotate(t,o.start),l=new xb["Point"](o.end.x-n,o.end.y+c).rotate(t,o.end),s=xb["Path"].createSegment("C",a,l,o.end),i.appendSegment(s)}}else{const e=t[a+1];0===r||!e||BC.includes(e)?(s=xb["Path"].createSegment("L",o.end),i.appendSegment(s)):RC(r,i,o.end,o.start,e.end)}}),i}function RC(t,e,n,r,i){const s=n.distance(r)/2,o=n.distance(i)/2,a=-Math.min(t,s),l=-Math.min(t,o),c=n.clone().move(r,a).round(),h=n.clone().move(i,l).round(),u=new xb["Point"](EC*c.x+SC*n.x,SC*n.y+EC*c.y),d=new xb["Point"](EC*h.x+SC*n.x,SC*n.y+EC*h.y);let g;g=xb["Path"].createSegment("L",c),e.appendSegment(g),g=xb["Path"].createSegment("C",u,d,h),e.appendSegment(g)}let BC,DC;const IC=function(t,e,n,r={}){BC=[],DC=[],MC(this);const i=r.size||5,s=r.type||"arc",o=r.radius||0,a=r.ignoreConnectors||["smooth"],l=this.graph,c=l.model,h=c.getEdges();if(1===h.length)return LC(TC(t,e,n),i,s,o);const u=this.cell,d=h.indexOf(u),g=l.options.connecting.connector||{},f=h.filter((t,e)=>{const n=t.getConnector()||g;return!a.includes(n.name)&&(!(e>d)||"jumpover"!==n.name)}),p=f.map(t=>l.findViewByCell(t)),m=TC(t,e,n),y=p.map(t=>null==t?[]:t===this?m:TC(t.sourcePoint,t.targetPoint,t.routePoints)),b=[];m.forEach(t=>{const e=f.reduce((e,n,r)=>{if(n!==u){const n=jC(t,y[r]);e.push(...n)}return e},[]).sort((e,n)=>kC(t.start,e)-kC(t.start,n));e.length>0?b.push(...NC(t,e,i)):b.push(t)});const v=LC(b,i,s,o);return BC=[],DC=[],r.raw?v:v.serialize()};var VC;(function(t){t.presets=A,t.registry=Pb.create({type:"connector"}),t.registry.register(t.presets,!0)})(VC||(VC={}));var zC=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class FC extends Oh{constructor(t={}){super(),this.pending=!1,this.changing=!1,this.data={},this.mutate(i.cloneDeep(t)),this.changed={}}mutate(t,e={}){const n=!0===e.unset,r=!0===e.silent,s=[],o=this.changing;this.changing=!0,o||(this.previous=i.cloneDeep(this.data),this.changed={});const a=this.data,l=this.previous,c=this.changed;if(Object.keys(t).forEach(e=>{const r=e,o=t[r];i.isEqual(a[r],o)||s.push(r),i.isEqual(l[r],o)?delete c[r]:c[r]=o,n?delete a[r]:a[r]=o}),!r&&s.length>0&&(this.pending=!0,this.pendingOptions=e,s.forEach(t=>{this.emit("change:*",{key:t,options:e,store:this,current:a[t],previous:l[t]})})),o)return this;if(!r)while(this.pending)this.pending=!1,this.emit("changed",{current:a,previous:l,store:this,options:this.pendingOptions});return this.pending=!1,this.changing=!1,this.pendingOptions=null,this}get(t,e){if(null==t)return this.data;const n=this.data[t];return null==n?e:n}getPrevious(t){if(this.previous){const e=this.previous[t];return null==e?void 0:e}}set(t,e,n){return null!=t&&("object"===typeof t?this.mutate(t,e):this.mutate({[t]:e},n)),this}remove(t,e){const n=void 0,r={};let i;if("string"===typeof t)r[t]=n,i=e;else if(Array.isArray(t))t.forEach(t=>r[t]=n),i=e;else{for(const t in this.data)r[t]=n;i=t}return this.mutate(r,Object.assign(Object.assign({},i),{unset:!0})),this}getByPath(t){return i.getByPath(this.data,t,"/")}setByPath(t,e,n={}){const r="/",s=Array.isArray(t)?[...t]:t.split(r),o=Array.isArray(t)?t.join(r):t,a=s[0],l=s.length;if(n.propertyPath=o,n.propertyValue=e,n.propertyPathArray=s,1===l)this.set(a,e,n);else{const o={};let c=o,h=a;for(let t=1;t<l;t+=1){const e=s[t],n=Number.isFinite(Number(e));c=c[h]=n?[]:{},h=e}i.setByPath(o,s,e,r);const u=i.cloneDeep(this.data);n.rewrite&&i.unsetByPath(u,t,r);const d=i.merge(u,o);this.set(a,d[a],n)}return this}removeByPath(t,e){const n=Array.isArray(t)?t:t.split("/"),r=n[0];if(1===n.length)this.remove(r,e);else{const t=n.slice(1),s=i.cloneDeep(this.get(r));s&&i.unsetByPath(s,t),this.set(r,s,e)}return this}hasChanged(t){return null==t?Object.keys(this.changed).length>0:t in this.changed}getChanges(t){if(null==t)return this.hasChanged()?i.cloneDeep(this.changed):null;const e=this.changing?this.previous:this.data,n={};let r;for(const s in t){const o=t[s];i.isEqual(e[s],o)||(n[s]=o,r=!0)}return r?i.cloneDeep(n):null}toJSON(){return i.cloneDeep(this.data)}clone(){const t=this.constructor;return new t(this.data)}dispose(){this.off(),this.data={},this.previous={},this.changed={},this.pending=!1,this.changing=!1,this.pendingOptions=null,this.trigger("disposed",{store:this})}}zC([Oh.dispose()],FC.prototype,"dispose",null);class $C{constructor(t){this.cell=t,this.ids={},this.cache={}}get(){return Object.keys(this.ids)}start(t,e,n={},r="/"){const s=this.cell.getPropByPath(t),o=i.defaults(n,$C.defaultOptions),a=this.getTiming(o.timing),l=this.getInterp(o.interp,s,e);let c=0;const h=Array.isArray(t)?t.join(r):t,u=Array.isArray(t)?t:t.split(r),d=()=>{const t=(new Date).getTime();0===c&&(c=t);const e=t-c;let r=e/o.duration;r<1?this.ids[h]=requestAnimationFrame(d):r=1;const i=l(a(r));this.cell.setPropByPath(u,i),n.progress&&n.progress(Object.assign({progress:r,currentValue:i},this.getArgs(h))),1===r&&(this.cell.notify("transition:complete",this.getArgs(h)),n.complete&&n.complete(this.getArgs(h)),this.cell.notify("transition:finish",this.getArgs(h)),n.finish&&n.finish(this.getArgs(h)),this.clean(h))};return setTimeout(()=>{this.stop(t,void 0,r),this.cache[h]={startValue:s,targetValue:e,options:o},this.ids[h]=requestAnimationFrame(d),this.cell.notify("transition:start",this.getArgs(h)),n.start&&n.start(this.getArgs(h))},n.delay),this.stop.bind(this,t,r,n)}stop(t,e={},n="/"){const r=Array.isArray(t)?t:t.split(n);return Object.keys(this.ids).filter(t=>i.isEqual(r,t.split(n).slice(0,r.length))).forEach(t=>{cancelAnimationFrame(this.ids[t]);const n=this.cache[t],r=this.getArgs(t),i=Object.assign(Object.assign({},n.options),e),s=i.jumpedToEnd;s&&null!=n.targetValue&&(this.cell.setPropByPath(t,n.targetValue),this.cell.notify("transition:end",Object.assign({},r)),this.cell.notify("transition:complete",Object.assign({},r)),i.complete&&i.complete(Object.assign({},r)));const o=Object.assign({jumpedToEnd:s},r);this.cell.notify("transition:stop",Object.assign({},o)),i.stop&&i.stop(Object.assign({},o)),this.cell.notify("transition:finish",Object.assign({},r)),i.finish&&i.finish(Object.assign({},r)),this.clean(t)}),this}clean(t){delete this.ids[t],delete this.cache[t]}getTiming(t){return"string"===typeof t?gb[t]:t}getInterp(t,e,n){return t?t(e,n):"number"===typeof n?fb.number(e,n):"string"===typeof n?"#"===n[0]?fb.color(e,n):fb.unit(e,n):fb.object(e,n)}getArgs(t){const e=this.cache[t];return{path:t,startValue:e.startValue,targetValue:e.targetValue,cell:this.cell}}}(function(t){t.defaultOptions={delay:10,duration:100,timing:"linear"}})($C||($C={}));var GC,qC=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o},_C=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class HC extends Oh{static config(t){const{markup:e,propHooks:n,attrHooks:r}=t,s=_C(t,["markup","propHooks","attrHooks"]);null!=e&&(this.markup=e),n&&(this.propHooks=this.propHooks.slice(),Array.isArray(n)?this.propHooks.push(...n):"function"===typeof n?this.propHooks.push(n):Object.values(n).forEach(t=>{"function"===typeof t&&this.propHooks.push(t)})),r&&(this.attrHooks=Object.assign(Object.assign({},this.attrHooks),r)),this.defaults=i.merge({},this.defaults,s)}static getMarkup(){return this.markup}static getDefaults(t){return t?this.defaults:i.cloneDeep(this.defaults)}static getAttrHooks(){return this.attrHooks}static applyPropHooks(t,e){return this.propHooks.reduce((e,n)=>n?r.call(n,t,e):e,e)}get[Symbol.toStringTag](){return HC.toStringTag}constructor(t={}){super();const e=this.constructor,n=e.getDefaults(!0),r=i.merge({},this.preprocess(n),this.preprocess(t));this.id=r.id||o.uuid(),this.store=new FC(r),this.animation=new $C(this),this.setup(),this.init(),this.postprocess(t)}init(){}get model(){return this._model}set model(t){this._model!==t&&(this._model=t)}preprocess(t,e){const n=t.id,r=this.constructor,i=r.applyPropHooks(this,t);return null==n&&!0!==e&&(i.id=o.uuid()),i}postprocess(t){}setup(){this.store.on("change:*",t=>{const{key:e,current:n,previous:r,options:i}=t;this.notify("change:*",{key:e,options:i,current:n,previous:r,cell:this}),this.notify("change:"+e,{options:i,current:n,previous:r,cell:this});const s=e;"source"!==s&&"target"!==s||this.notify("change:terminal",{type:s,current:n,previous:r,options:i,cell:this})}),this.store.on("changed",({options:t})=>this.notify("changed",{options:t,cell:this}))}notify(t,e){this.trigger(t,e);const n=this.model;return n&&(n.notify("cell:"+t,e),this.isNode()?n.notify("node:"+t,Object.assign(Object.assign({},e),{node:this})):this.isEdge()&&n.notify("edge:"+t,Object.assign(Object.assign({},e),{edge:this}))),this}isNode(){return!1}isEdge(){return!1}isSameStore(t){return this.store===t.store}get view(){return this.store.get("view")}get shape(){return this.store.get("shape","")}getProp(t,e){return null==t?this.store.get():this.store.get(t,e)}setProp(t,e,n){if("string"===typeof t)this.store.set(t,e,n);else{const n=this.preprocess(t,!0);this.store.set(i.merge({},this.getProp(),n),e),this.postprocess(t)}return this}removeProp(t,e){return"string"===typeof t||Array.isArray(t)?this.store.removeByPath(t,e):this.store.remove(e),this}hasChanged(t){return null==t?this.store.hasChanged():this.store.hasChanged(t)}getPropByPath(t){return this.store.getByPath(t)}setPropByPath(t,e,n={}){return this.model&&("children"===t?this._children=e?e.map(t=>this.model.getCell(t)).filter(t=>null!=t):null:"parent"===t&&(this._parent=e?this.model.getCell(e):null)),this.store.setByPath(t,e,n),this}removePropByPath(t,e={}){const n=Array.isArray(t)?t:t.split("/");return"attrs"===n[0]&&(e.dirty=!0),this.store.removeByPath(n,e),this}prop(t,e,n){return null==t?this.getProp():"string"===typeof t||Array.isArray(t)?1===arguments.length?this.getPropByPath(t):null==e?this.removePropByPath(t,n||{}):this.setPropByPath(t,e,n||{}):this.setProp(t,e||{})}previous(t){return this.store.getPrevious(t)}get zIndex(){return this.getZIndex()}set zIndex(t){null==t?this.removeZIndex():this.setZIndex(t)}getZIndex(){return this.store.get("zIndex")}setZIndex(t,e={}){return this.store.set("zIndex",t,e),this}removeZIndex(t={}){return this.store.remove("zIndex",t),this}toFront(t={}){const e=this.model;if(e){let n,r=e.getMaxZIndex();t.deep?(n=this.getDescendants({deep:!0,breadthFirst:!0}),n.unshift(this)):n=[this],r=r-n.length+1;const i=e.total();let s=e.indexOf(this)!==i-n.length;s||(s=n.some((t,e)=>t.getZIndex()!==r+e)),s&&this.batchUpdate("to-front",()=>{r+=n.length,n.forEach((e,n)=>{e.setZIndex(r+n,t)})})}return this}toBack(t={}){const e=this.model;if(e){let n,r=e.getMinZIndex();t.deep?(n=this.getDescendants({deep:!0,breadthFirst:!0}),n.unshift(this)):n=[this];let i=0!==e.indexOf(this);i||(i=n.some((t,e)=>t.getZIndex()!==r+e)),i&&this.batchUpdate("to-back",()=>{r-=n.length,n.forEach((e,n)=>{e.setZIndex(r+n,t)})})}return this}get markup(){return this.getMarkup()}set markup(t){null==t?this.removeMarkup():this.setMarkup(t)}getMarkup(){let t=this.store.get("markup");if(null==t){const e=this.constructor;t=e.getMarkup()}return t}setMarkup(t,e={}){return this.store.set("markup",t,e),this}removeMarkup(t={}){return this.store.remove("markup",t),this}get attrs(){return this.getAttrs()}set attrs(t){null==t?this.removeAttrs():this.setAttrs(t)}getAttrs(){const t=this.store.get("attrs");return t?Object.assign({},t):{}}setAttrs(t,e={}){if(null==t)this.removeAttrs(e);else{const n=t=>this.store.set("attrs",t,e);if(!0===e.overwrite)n(t);else{const r=this.getAttrs();!1===e.deep?n(Object.assign(Object.assign({},r),t)):n(i.merge({},r,t))}}return this}replaceAttrs(t,e={}){return this.setAttrs(t,Object.assign(Object.assign({},e),{overwrite:!0}))}updateAttrs(t,e={}){return this.setAttrs(t,Object.assign(Object.assign({},e),{deep:!1}))}removeAttrs(t={}){return this.store.remove("attrs",t),this}getAttrDefinition(t){if(!t)return null;const e=this.constructor,n=e.getAttrHooks()||{};let r=n[t]||Aw.registry.get(t);if(!r){const e=o.camelCase(t);r=n[e]||Aw.registry.get(e)}return r||null}getAttrByPath(t){return null==t||""===t?this.getAttrs():this.getPropByPath(this.prefixAttrPath(t))}setAttrByPath(t,e,n={}){return this.setPropByPath(this.prefixAttrPath(t),e,n),this}removeAttrByPath(t,e={}){return this.removePropByPath(this.prefixAttrPath(t),e),this}prefixAttrPath(t){return Array.isArray(t)?["attrs"].concat(t):"attrs/"+t}attr(t,e,n){return null==t?this.getAttrByPath():"string"===typeof t||Array.isArray(t)?1===arguments.length?this.getAttrByPath(t):null==e?this.removeAttrByPath(t,n||{}):this.setAttrByPath(t,e,n||{}):this.setAttrs(t,e||{})}get visible(){return this.isVisible()}set visible(t){this.setVisible(t)}setVisible(t,e={}){return this.store.set("visible",t,e),this}isVisible(){return!1!==this.store.get("visible")}show(t={}){return this.isVisible()||this.setVisible(!0,t),this}hide(t={}){return this.isVisible()&&this.setVisible(!1,t),this}toggleVisible(t,e={}){const n="boolean"===typeof t?t:!this.isVisible(),r="boolean"===typeof t?e:t;return n?this.show(r):this.hide(r),this}get data(){return this.getData()}set data(t){this.setData(t)}getData(){return this.store.get("data")}setData(t,e={}){if(null==t)this.removeData(e);else{const n=t=>this.store.set("data",t,e);if(!0===e.overwrite)n(t);else{const r=this.getData();!1===e.deep?n("object"===typeof t?Object.assign(Object.assign({},r),t):t):n(i.merge({},r,t))}}return this}replaceData(t,e={}){return this.setData(t,Object.assign(Object.assign({},e),{overwrite:!0}))}updateData(t,e={}){return this.setData(t,Object.assign(Object.assign({},e),{deep:!1}))}removeData(t={}){return this.store.remove("data",t),this}get parent(){return this.getParent()}get children(){return this.getChildren()}getParentId(){return this.store.get("parent")}getParent(){const t=this.getParentId();if(t&&this.model){const e=this.model.getCell(t);return this._parent=e,e}return null}getChildren(){const t=this.store.get("children");if(t&&t.length&&this.model){const e=t.map(t=>{var e;return null===(e=this.model)||void 0===e?void 0:e.getCell(t)}).filter(t=>null!=t);return this._children=e,[...e]}return null}hasParent(){return null!=this.parent}isParentOf(t){return null!=t&&t.getParent()===this}isChildOf(t){return null!=t&&this.getParent()===t}eachChild(t,e){return this.children&&this.children.forEach(t,e),this}filterChild(t,e){return this.children?this.children.filter(t,e):[]}getChildCount(){return null==this.children?0:this.children.length}getChildIndex(t){return null==this.children?-1:this.children.indexOf(t)}getChildAt(t){return null!=this.children&&t>=0?this.children[t]:null}getAncestors(t={}){const e=[];let n=this.getParent();while(n)e.push(n),n=!1!==t.deep?n.getParent():null;return e}getDescendants(t={}){if(!1!==t.deep){if(t.breadthFirst){const t=[],e=this.getChildren()||[];while(e.length>0){const n=e.shift(),r=n.getChildren();t.push(n),r&&e.push(...r)}return t}{const e=this.getChildren()||[];return e.forEach(n=>{e.push(...n.getDescendants(t))}),e}}return this.getChildren()||[]}isDescendantOf(t,e={}){if(null==t)return!1;if(!1!==e.deep){let e=this.getParent();while(e){if(e===t)return!0;e=e.getParent()}return!1}return this.isChildOf(t)}isAncestorOf(t,e={}){return null!=t&&t.isDescendantOf(this,e)}contains(t){return this.isAncestorOf(t)}getCommonAncestor(...t){return HC.getCommonAncestor(this,...t)}setParent(t,e={}){return this._parent=t,t?this.store.set("parent",t.id,e):this.store.remove("parent",e),this}setChildren(t,e={}){return this._children=t,null!=t?this.store.set("children",t.map(t=>t.id),e):this.store.remove("children",e),this}unembed(t,e={}){const n=this.children;if(null!=n&&null!=t){const r=this.getChildIndex(t);-1!==r&&(n.splice(r,1),t.setParent(null,e),this.setChildren(n,e))}return this}embed(t,e={}){return t.addTo(this,e),this}addTo(t,e={}){return HC.isCell(t)?t.addChild(this,e):t.addCell(this,e),this}insertTo(t,e,n={}){return t.insertChild(this,e,n),this}addChild(t,e={}){return this.insertChild(t,void 0,e)}insertChild(t,e,n={}){if(null!=t&&t!==this){const r=t.getParent(),i=this!==r;let s=e;if(null==s&&(s=this.getChildCount(),i||(s-=1)),r){const e=r.getChildren();if(e){const i=e.indexOf(t);i>=0&&(t.setParent(null,n),e.splice(i,1),r.setChildren(e,n))}}let o=this.children;if(null==o?(o=[],o.push(t)):o.splice(s,0,t),t.setParent(this,n),this.setChildren(o,n),i&&this.model){const t=this.model.getIncomingEdges(this),e=this.model.getOutgoingEdges(this);t&&t.forEach(t=>t.updateParent(n)),e&&e.forEach(t=>t.updateParent(n))}this.model&&this.model.addCell(t,n)}return this}removeFromParent(t={}){const e=this.getParent();if(null!=e){const n=e.getChildIndex(this);e.removeChildAt(n,t)}return this}removeChild(t,e={}){const n=this.getChildIndex(t);return this.removeChildAt(n,e)}removeChildAt(t,e={}){const n=this.getChildAt(t),r=this.children;return null!=r&&null!=n&&(this.unembed(n,e),n.remove(e)),n}remove(t={}){return this.batchUpdate("remove",()=>{const e=this.getParent();e&&e.removeChild(this,t),!1!==t.deep&&this.eachChild(e=>e.remove(t)),this.model&&this.model.removeCell(this,t)}),this}transition(t,e,n={},r="/"){return this.animation.start(t,e,n,r)}stopTransition(t,e,n="/"){return this.animation.stop(t,e,n),this}getTransitions(){return this.animation.get()}translate(t,e,n){return this}scale(t,e,n,r){return this}addTools(t,e,n){const r=Array.isArray(t)?t:[t],s="string"===typeof e?e:null,o="object"===typeof e?e:"object"===typeof n?n:{};if(o.reset)return this.setTools({name:s,items:r,local:o.local},o);let a=i.cloneDeep(this.getTools());return null==a||null==s||a.name===s?(null==a&&(a={}),a.items||(a.items=[]),a.name=s,a.items=[...a.items,...r],this.setTools(Object.assign({},a),o)):void 0}setTools(t,e={}){return null==t?this.removeTools():this.store.set("tools",HC.normalizeTools(t),e),this}getTools(){return this.store.get("tools")}removeTools(t={}){return this.store.remove("tools",t),this}hasTools(t){const e=this.getTools();return null!=e&&(null==t||e.name===t)}hasTool(t){const e=this.getTools();return null!=e&&e.items.some(e=>"string"===typeof e?e===t:e.name===t)}removeTool(t,e={}){const n=i.cloneDeep(this.getTools());if(n){let r=!1;const i=n.items.slice(),s=t=>{i.splice(t,1),r=!0};if("number"===typeof t)s(t);else for(let e=i.length-1;e>=0;e-=1){const n=i[e],r="string"===typeof n?n===t:n.name===t;r&&s(e)}r&&(n.items=i,this.setTools(n,e))}return this}getBBox(t){return new xb["Rectangle"]}getConnectionPoint(t,e){return new xb["Point"]}toJSON(t={}){const e=Object.assign({},this.store.get()),n=Object.prototype.toString,r=this.isNode()?"node":this.isEdge()?"edge":"cell";if(!e.shape){const t=this.constructor;throw new Error(`Unable to serialize ${r} missing "shape" prop, check the ${r} "${t.name||n.call(t)}"`)}const s=this.constructor,o=!0===t.diff,a=e.attrs||{},l=s.getDefaults(!0),c=o?this.preprocess(l,!0):l,h=c.attrs||{},u={};Object.entries(e).forEach(([t,s])=>{if(null!=s&&!Array.isArray(s)&&"object"===typeof s&&!i.isPlainObject(s))throw new Error(`Can only serialize ${r} with plain-object props, but got a "${n.call(s)}" type of key "${t}" on ${r} "${this.id}"`);if("attrs"!==t&&"shape"!==t&&o){const n=c[t];i.isEqual(s,n)&&delete e[t]}}),Object.keys(a).forEach(t=>{const e=a[t],n=h[t];Object.keys(e).forEach(r=>{const s=e[r],o=n?n[r]:null;null==s||"object"!==typeof s||Array.isArray(s)?null!=n&&i.isEqual(o,s)||(null==u[t]&&(u[t]={}),u[t][r]=s):Object.keys(s).forEach(e=>{const a=s[e];if(null==n||null==o||!i.isObject(o)||!i.isEqual(o[e],a)){null==u[t]&&(u[t]={}),null==u[t][r]&&(u[t][r]={});const n=u[t][r];n[e]=a}})})});const d=Object.assign(Object.assign({},e),{attrs:i.isEmpty(u)?void 0:u});null==d.attrs&&delete d.attrs;const g=d;return 0===g.angle&&delete g.angle,i.cloneDeep(g)}clone(t={}){if(!t.deep){const e=Object.assign({},this.store.get());t.keepId||delete e.id,delete e.parent,delete e.children;const n=this.constructor;return new n(e)}const e=HC.deepClone(this);return e[this.id]}findView(t){return t.findViewByCell(this)}startBatch(t,e={},n=this.model){return this.notify("batch:start",{name:t,data:e,cell:this}),n&&n.startBatch(t,Object.assign(Object.assign({},e),{cell:this})),this}stopBatch(t,e={},n=this.model){return n&&n.stopBatch(t,Object.assign(Object.assign({},e),{cell:this})),this.notify("batch:stop",{name:t,data:e,cell:this}),this}batchUpdate(t,e,n){const r=this.model;this.startBatch(t,n,r);const i=e();return this.stopBatch(t,n,r),i}dispose(){this.removeFromParent(),this.store.dispose()}}HC.defaults={},HC.attrHooks={},HC.propHooks=[],qC([Oh.dispose()],HC.prototype,"dispose",null),function(t){function e(t){return"string"===typeof t?{items:[t]}:Array.isArray(t)?{items:t}:t.items?t:{items:[t]}}t.normalizeTools=e}(HC||(HC={})),function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"===typeof r.isNode&&"function"===typeof r.isEdge&&"function"===typeof r.prop&&"function"===typeof r.attr}t.toStringTag="X6."+t.name,t.isCell=e}(HC||(HC={})),function(t){function e(...t){const e=t.filter(t=>null!=t).map(t=>t.getAncestors()).sort((t,e)=>t.length-e.length),n=e.shift();return n.find(t=>e.every(e=>e.includes(t)))||null}function n(t,e={}){let n=null;for(let r=0,i=t.length;r<i;r+=1){const i=t[r];let s=i.getBBox(e);if(s){if(i.isNode()){const t=i.getAngle();null!=t&&0!==t&&(s=s.bbox(t))}n=null==n?s:n.union(s)}}return n}function r(e){const n=[e,...e.getDescendants({deep:!0})];return t.cloneCells(n)}function i(t){const e=s.uniq(t),n=e.reduce((t,e)=>(t[e.id]=e.clone(),t),{});return e.forEach(t=>{const e=n[t.id];if(e.isEdge()){const t=e.getSourceCellId(),r=e.getTargetCellId();t&&n[t]&&e.setSource(Object.assign(Object.assign({},e.getSource()),{cell:n[t].id})),r&&n[r]&&e.setTarget(Object.assign(Object.assign({},e.getTarget()),{cell:n[r].id}))}const r=t.getParent();r&&n[r.id]&&e.setParent(n[r.id]);const i=t.getChildren();if(i&&i.length){const t=i.reduce((t,e)=>(n[e.id]&&t.push(n[e.id]),t),[]);t.length>0&&e.setChildren(t)}}),n}t.getCommonAncestor=e,t.getCellsBBox=n,t.deepClone=r,t.cloneCells=i}(HC||(HC={})),function(t){t.config({propHooks(e){var{tools:n}=e,r=_C(e,["tools"]);return n&&(r.tools=t.normalizeTools(n)),r}})}(HC||(HC={})),function(t){let e,n;function r(t,r){return r?null!=e&&e.exist(t):null!=n&&n.exist(t)}function i(t){e=t}function s(t){n=t}t.exist=r,t.setEdgeRegistry=i,t.setNodeRegistry=s}(GC||(GC={}));class UC{constructor(t){this.ports=[],this.groups={},this.init(i.cloneDeep(t))}getPorts(){return this.ports}getGroup(t){return null!=t?this.groups[t]:null}getPortsByGroup(t){return this.ports.filter(e=>e.group===t||null==e.group&&null==t)}getPortsLayoutByGroup(t,e){const n=this.getPortsByGroup(t),r=t?this.getGroup(t):null,i=r?r.position:null,s=i?i.name:null;let o;if(null!=s){const t=Jw.registry.get(s);if(null==t)return Jw.registry.onNotFound(s);o=t}else o=Jw.presets.left;const a=n.map(t=>t&&t.position&&t.position.args||{}),l=i&&i.args||{},c=o(a,e,l);return c.map((t,r)=>{const i=n[r];return{portLayout:t,portId:i.id,portSize:i.size,portAttrs:i.attrs,labelSize:i.label.size,labelLayout:this.getPortLabelLayout(i,xb["Point"].create(t.position),e)}})}init(t){const{groups:e,items:n}=t;null!=e&&Object.keys(e).forEach(t=>{this.groups[t]=this.parseGroup(e[t])}),Array.isArray(n)&&n.forEach(t=>{this.ports.push(this.parsePort(t))})}parseGroup(t){return Object.assign(Object.assign({},t),{label:this.getLabel(t,!0),position:this.getPortPosition(t.position,!0)})}parsePort(t){const e=Object.assign({},t),n=this.getGroup(t.group)||{};return e.markup=e.markup||n.markup,e.attrs=i.merge({},n.attrs,e.attrs),e.position=this.createPosition(n,e),e.label=i.merge({},n.label,this.getLabel(e)),e.zIndex=this.getZIndex(n,e),e.size=Object.assign(Object.assign({},n.size),e.size),e}getZIndex(t,e){return"number"===typeof e.zIndex?e.zIndex:"number"===typeof t.zIndex||"auto"===t.zIndex?t.zIndex:"auto"}createPosition(t,e){return i.merge({name:"left",args:{}},t.position,{args:e.args})}getPortPosition(t,e=!1){if(null==t){if(e)return{name:"left",args:{}}}else{if("string"===typeof t)return{name:t,args:{}};if(Array.isArray(t))return{name:"absolute",args:{x:t[0],y:t[1]}};if("object"===typeof t)return t}return{args:{}}}getPortLabelPosition(t,e=!1){if(null==t){if(e)return{name:"left",args:{}}}else{if("string"===typeof t)return{name:t,args:{}};if("object"===typeof t)return t}return{args:{}}}getLabel(t,e=!1){const n=t.label||{};return n.position=this.getPortLabelPosition(n.position,e),n}getPortLabelLayout(t,e,n){const r=t.label.position.name||"left",i=t.label.position.args||{},s=dx.registry.get(r)||dx.presets.left;return s?s(e,n,i):null}}var WC=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class JC extends HC{get[Symbol.toStringTag](){return JC.toStringTag}constructor(t={}){super(t),this.initPorts()}preprocess(t,e){const{x:n,y:r,width:i,height:s}=t,o=WC(t,["x","y","width","height"]);if(null!=n||null!=r){const t=o.position;o.position=Object.assign(Object.assign({},t),{x:null!=n?n:t?t.x:0,y:null!=r?r:t?t.y:0})}if(null!=i||null!=s){const t=o.size;o.size=Object.assign(Object.assign({},t),{width:null!=i?i:t?t.width:0,height:null!=s?s:t?t.height:0})}return super.preprocess(o,e)}isNode(){return!0}size(t,e,n){return void 0===t?this.getSize():"number"===typeof t?this.setSize(t,e,n):this.setSize(t,e)}getSize(){const t=this.store.get("size");return t?Object.assign({},t):{width:1,height:1}}setSize(t,e,n){return"object"===typeof t?this.resize(t.width,t.height,e):this.resize(t,e,n),this}resize(t,e,n={}){this.startBatch("resize",n);const r=n.direction;if(r){const i=this.getSize();switch(r){case"left":case"right":e=i.height;break;case"top":case"bottom":t=i.width;break;default:break}const s={right:0,"top-right":0,top:1,"top-left":1,left:2,"bottom-left":2,bottom:3,"bottom-right":3};let o=s[r];const a=xb["Angle"].normalize(this.getAngle()||0);n.absolute&&(o+=Math.floor((a+45)/90),o%=4);const l=this.getBBox();let c;c=0===o?l.getBottomLeft():1===o?l.getCorner():2===o?l.getTopRight():l.getOrigin();const h=c.clone().rotate(-a,l.getCenter()),u=Math.sqrt(t*t+e*e)/2;let d=o*Math.PI/2;d+=Math.atan(o%2===0?e/t:t/e),d-=xb["Angle"].toRad(a);const g=xb["Point"].fromPolar(u,d,h),f=g.clone().translate(t/-2,e/-2);this.store.set("size",{width:t,height:e},n),this.setPosition(f.x,f.y,n)}else this.store.set("size",{width:t,height:e},n);return this.stopBatch("resize",n),this}scale(t,e,n,r={}){const i=this.getBBox().scale(t,e,null==n?void 0:n);return this.startBatch("scale",r),this.setPosition(i.x,i.y,r),this.resize(i.width,i.height,r),this.stopBatch("scale"),this}position(t,e,n){return"number"===typeof t?this.setPosition(t,e,n):this.getPosition(t)}getPosition(t={}){if(t.relative){const t=this.getParent();if(null!=t&&t.isNode()){const e=this.getPosition(),n=t.getPosition();return{x:e.x-n.x,y:e.y-n.y}}}const e=this.store.get("position");return e?Object.assign({},e):{x:0,y:0}}setPosition(t,e,n={}){let r,i,s;if("object"===typeof t?(r=t.x,i=t.y,s=e||{}):(r=t,i=e,s=n||{}),s.relative){const t=this.getParent();if(null!=t&&t.isNode()){const e=t.getPosition();r+=e.x,i+=e.y}}if(s.deep){const t=this.getPosition();this.translate(r-t.x,i-t.y,s)}else this.store.set("position",{x:r,y:i},s);return this}translate(t=0,e=0,n={}){if(0===t&&0===e)return this;n.translateBy=n.translateBy||this.id;const r=this.getPosition();if(null!=n.restrict&&n.translateBy===this.id){const i=this.getBBox({deep:!0}),s=n.restrict,o=r.x-i.x,a=r.y-i.y,l=Math.max(s.x+o,Math.min(s.x+s.width+o-i.width,r.x+t)),c=Math.max(s.y+a,Math.min(s.y+s.height+a-i.height,r.y+e));t=l-r.x,e=c-r.y}const i={x:r.x+t,y:r.y+e};return n.tx=t,n.ty=e,n.transition?("object"!==typeof n.transition&&(n.transition={}),this.transition("position",i,Object.assign(Object.assign({},n.transition),{interp:fb.object})),this.eachChild(r=>{var i;const s=null===(i=n.exclude)||void 0===i?void 0:i.includes(r);s||r.translate(t,e,n)})):(this.startBatch("translate",n),this.store.set("position",i,n),this.eachChild(r=>{var i;const s=null===(i=n.exclude)||void 0===i?void 0:i.includes(r);s||r.translate(t,e,n)}),this.stopBatch("translate",n)),this}angle(t,e){return null==t?this.getAngle():this.rotate(t,e)}getAngle(){return this.store.get("angle",0)}rotate(t,e={}){const n=this.getAngle();if(e.center){const r=this.getSize(),i=this.getPosition(),s=this.getBBox().getCenter();s.rotate(n-t,e.center);const o=s.x-r.width/2-i.x,a=s.y-r.height/2-i.y;this.startBatch("rotate",{angle:t,options:e}),this.setPosition(i.x+o,i.y+a,e),this.rotate(t,Object.assign(Object.assign({},e),{center:null})),this.stopBatch("rotate")}else this.store.set("angle",e.absolute?t:(n+t)%360,e);return this}getBBox(t={}){if(t.deep){const t=this.getDescendants({deep:!0,breadthFirst:!0});return t.push(this),HC.getCellsBBox(t)}return xb["Rectangle"].fromPositionAndSize(this.getPosition(),this.getSize())}getConnectionPoint(t,e){const n=this.getBBox(),r=n.getCenter(),i=t.getTerminal(e);if(null==i)return r;const s=i.port;if(!s||!this.hasPort(s))return r;const o=this.getPort(s);if(!o||!o.group)return r;const a=this.getPortsPosition(o.group),l=a[s].position,c=xb["Point"].create(l).translate(n.getOrigin()),h=this.getAngle();return h&&c.rotate(-h,r),c}fit(t={}){const e=this.getChildren()||[],n=e.filter(t=>t.isNode());if(0===n.length)return this;this.startBatch("fit-embeds",t),t.deep&&n.forEach(e=>e.fit(t));let{x:r,y:i,width:s,height:o}=HC.getCellsBBox(n);const l=a.normalizeSides(t.padding);return r-=l.left,i-=l.top,s+=l.left+l.right,o+=l.bottom+l.top,this.store.set({position:{x:r,y:i},size:{width:s,height:o}},t),this.stopBatch("fit-embeds"),this}get portContainerMarkup(){return this.getPortContainerMarkup()}set portContainerMarkup(t){this.setPortContainerMarkup(t)}getDefaultPortContainerMarkup(){return this.store.get("defaultPortContainerMarkup")||fx.getPortContainerMarkup()}getPortContainerMarkup(){return this.store.get("portContainerMarkup")||this.getDefaultPortContainerMarkup()}setPortContainerMarkup(t,e={}){return this.store.set("portContainerMarkup",fx.clone(t),e),this}get portMarkup(){return this.getPortMarkup()}set portMarkup(t){this.setPortMarkup(t)}getDefaultPortMarkup(){return this.store.get("defaultPortMarkup")||fx.getPortMarkup()}getPortMarkup(){return this.store.get("portMarkup")||this.getDefaultPortMarkup()}setPortMarkup(t,e={}){return this.store.set("portMarkup",fx.clone(t),e),this}get portLabelMarkup(){return this.getPortLabelMarkup()}set portLabelMarkup(t){this.setPortLabelMarkup(t)}getDefaultPortLabelMarkup(){return this.store.get("defaultPortLabelMarkup")||fx.getPortLabelMarkup()}getPortLabelMarkup(){return this.store.get("portLabelMarkup")||this.getDefaultPortLabelMarkup()}setPortLabelMarkup(t,e={}){return this.store.set("portLabelMarkup",fx.clone(t),e),this}get ports(){const t=this.store.get("ports",{items:[]});return null==t.items&&(t.items=[]),t}getPorts(){return i.cloneDeep(this.ports.items)}getPortsByGroup(t){return this.getPorts().filter(e=>e.group===t)}getPort(t){return i.cloneDeep(this.ports.items.find(e=>e.id&&e.id===t))}getPortAt(t){return this.ports.items[t]||null}hasPorts(){return this.ports.items.length>0}hasPort(t){return-1!==this.getPortIndex(t)}getPortIndex(t){const e="string"===typeof t?t:t.id;return null!=e?this.ports.items.findIndex(t=>t.id===e):-1}getPortsPosition(t){const e=this.getSize(),n=this.port.getPortsLayoutByGroup(t,new xb["Rectangle"](0,0,e.width,e.height));return n.reduce((t,e)=>{const n=e.portLayout;return t[e.portId]={position:Object.assign({},n.position),angle:n.angle||0},t},{})}getPortProp(t,e){return this.getPropByPath(this.prefixPortPath(t,e))}setPortProp(t,e,n,r){if("string"===typeof e||Array.isArray(e)){const i=this.prefixPortPath(t,e),s=n;return this.setPropByPath(i,s,r)}const i=this.prefixPortPath(t),s=e;return this.setPropByPath(i,s,n)}removePortProp(t,e,n){return"string"===typeof e||Array.isArray(e)?this.removePropByPath(this.prefixPortPath(t,e),n):this.removePropByPath(this.prefixPortPath(t),e)}portProp(t,e,n,r){return null==e?this.getPortProp(t):"string"===typeof e||Array.isArray(e)?2===arguments.length?this.getPortProp(t,e):null==n?this.removePortProp(t,e,r):this.setPortProp(t,e,n,r):this.setPortProp(t,e,n)}prefixPortPath(t,e){const n=this.getPortIndex(t);if(-1===n)throw new Error(`Unable to find port with id: "${t}"`);return null==e||""===e?["ports","items",""+n]:Array.isArray(e)?["ports","items",""+n,...e]:`ports/items/${n}/${e}`}addPort(t,e){const n=[...this.ports.items];return n.push(t),this.setPropByPath("ports/items",n,e),this}addPorts(t,e){return this.setPropByPath("ports/items",[...this.ports.items,...t],e),this}insertPort(t,e,n){const r=[...this.ports.items];return r.splice(t,0,e),this.setPropByPath("ports/items",r,n),this}removePort(t,e={}){return this.removePortAt(this.getPortIndex(t),e)}removePortAt(t,e={}){if(t>=0){const n=[...this.ports.items];n.splice(t,1),e.rewrite=!0,this.setPropByPath("ports/items",n,e)}return this}removePorts(t,e){let n;if(Array.isArray(t)){if(n=e||{},t.length){n.rewrite=!0;const e=[...this.ports.items],r=e.filter(e=>!t.some(t=>{const n="string"===typeof t?t:t.id;return e.id===n}));this.setPropByPath("ports/items",r,n)}}else n=t||{},n.rewrite=!0,this.setPropByPath("ports/items",[],n);return this}getParsedPorts(){return this.port.getPorts()}getParsedGroups(){return this.port.groups}getPortsLayoutByGroup(t,e){return this.port.getPortsLayoutByGroup(t,e)}initPorts(){this.updatePortData(),this.on("change:ports",()=>{this.processRemovedPort(),this.updatePortData()})}processRemovedPort(){const t=this.ports,e={};t.items.forEach(t=>{t.id&&(e[t.id]=!0)});const n={},r=this.store.getPrevious("ports")||{items:[]};r.items.forEach(t=>{t.id&&!e[t.id]&&(n[t.id]=!0)});const s=this.model;if(s&&!i.isEmpty(n)){const t=s.getConnectedEdges(this,{incoming:!0});t.forEach(t=>{const e=t.getTargetPortId();e&&n[e]&&t.remove()});const e=s.getConnectedEdges(this,{outgoing:!0});e.forEach(t=>{const e=t.getSourcePortId();e&&n[e]&&t.remove()})}}validatePorts(){const t={},e=[];return this.ports.items.forEach(n=>{"object"!==typeof n&&e.push(`Invalid port ${n}.`),null==n.id&&(n.id=this.generatePortId()),t[n.id]&&e.push("Duplicitied port id."),t[n.id]=!0}),e}generatePortId(){return o.uuid()}updatePortData(){const t=this.validatePorts();if(t.length>0)throw this.store.set("ports",this.store.getPrevious("ports")),new Error(t.join(" "));const e=this.port?this.port.getPorts():null;this.port=new UC(this.ports);const n=this.port.getPorts(),r=e?n.filter(t=>e.find(e=>e.id===t.id)?null:t):[...n],i=e?e.filter(t=>n.find(e=>e.id===t.id)?null:t):[];r.length>0&&this.notify("ports:added",{added:r,cell:this,node:this}),i.length>0&&this.notify("ports:removed",{removed:i,cell:this,node:this})}}JC.defaults={angle:0,position:{x:0,y:0},size:{width:1,height:1}},function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"===typeof r.isNode&&"function"===typeof r.isEdge&&"function"===typeof r.prop&&"function"===typeof r.attr&&"function"===typeof r.size&&"function"===typeof r.position}t.toStringTag="X6."+t.name,t.isNode=e}(JC||(JC={})),function(t){t.config({propHooks(t){var{ports:e}=t,n=WC(t,["ports"]);return e&&(n.ports=Array.isArray(e)?{items:e}:e),n}})}(JC||(JC={})),function(t){t.registry=Pb.create({type:"node",process(e,n){if(GC.exist(e,!0))throw new Error(`Node with name '${e}' was registered by anthor Edge`);if("function"===typeof n)return n.config({shape:e}),n;let r=t;const{inherit:i}=n,s=WC(n,["inherit"]);if(i)if("string"===typeof i){const t=this.get(i);null==t?this.onNotFound(i,"inherited"):r=t}else r=i;null==s.constructorName&&(s.constructorName=e);const o=r.define.call(r,s);return o.config({shape:e}),o}}),GC.setNodeRegistry(t.registry)}(JC||(JC={})),function(t){let e=0;function n(t){return t?o.pascalCase(t):(e+=1,"CustomNode"+e)}function r(e){const{constructorName:r,overwrite:s}=e,o=WC(e,["constructorName","overwrite"]),a=i.createClass(n(r||o.shape),this);return a.config(o),o.shape&&t.registry.register(o.shape,a,s),a}function s(e){const n=e.shape||"rect",r=t.registry.get(n);return r?new r(e):t.registry.onNotFound(n)}t.define=r,t.create=s}(JC||(JC={}));var XC=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class YC extends HC{get[Symbol.toStringTag](){return YC.toStringTag}constructor(t={}){super(t)}preprocess(t,e){const{source:n,sourceCell:r,sourcePort:i,sourcePoint:s,target:o,targetCell:a,targetPort:l,targetPoint:c}=t,h=XC(t,["source","sourceCell","sourcePort","sourcePoint","target","targetCell","targetPort","targetPoint"]),u=h,d=t=>"string"===typeof t||"number"===typeof t;if(null!=n)if(HC.isCell(n))u.source={cell:n.id};else if(d(n))u.source={cell:n};else if(xb["Point"].isPoint(n))u.source=n.toJSON();else if(Array.isArray(n))u.source={x:n[0],y:n[1]};else{const t=n.cell;HC.isCell(t)?u.source=Object.assign(Object.assign({},n),{cell:t.id}):u.source=n}if(null!=r||null!=i){let t=u.source;if(null!=r){const e=d(r)?r:r.id;t?t.cell=e:t=u.source={cell:e}}null!=i&&t&&(t.port=i)}else null!=s&&(u.source=xb["Point"].create(s).toJSON());if(null!=o)if(HC.isCell(o))u.target={cell:o.id};else if(d(o))u.target={cell:o};else if(xb["Point"].isPoint(o))u.target=o.toJSON();else if(Array.isArray(o))u.target={x:o[0],y:o[1]};else{const t=o.cell;HC.isCell(t)?u.target=Object.assign(Object.assign({},o),{cell:t.id}):u.target=o}if(null!=a||null!=l){let t=u.target;if(null!=a){const e=d(a)?a:a.id;t?t.cell=e:t=u.target={cell:e}}null!=l&&t&&(t.port=l)}else null!=c&&(u.target=xb["Point"].create(c).toJSON());return super.preprocess(u,e)}setup(){super.setup(),this.on("change:labels",t=>this.onLabelsChanged(t)),this.on("change:vertices",t=>this.onVertexsChanged(t))}isEdge(){return!0}disconnect(t={}){return this.store.set({source:{x:0,y:0},target:{x:0,y:0}},t),this}get source(){return this.getSource()}set source(t){this.setSource(t)}getSource(){return this.getTerminal("source")}getSourceCellId(){return this.source.cell}getSourcePortId(){return this.source.port}setSource(t,e,n={}){return this.setTerminal("source",t,e,n)}get target(){return this.getTarget()}set target(t){this.setTarget(t)}getTarget(){return this.getTerminal("target")}getTargetCellId(){return this.target.cell}getTargetPortId(){return this.target.port}setTarget(t,e,n={}){return this.setTerminal("target",t,e,n)}getTerminal(t){return Object.assign({},this.store.get(t))}setTerminal(t,e,n,r={}){if(HC.isCell(e))return this.store.set(t,i.merge({},n,{cell:e.id}),r),this;const s=e;return xb["Point"].isPoint(e)||null!=s.x&&null!=s.y?(this.store.set(t,i.merge({},n,{x:s.x,y:s.y}),r),this):(this.store.set(t,i.cloneDeep(e),r),this)}getSourcePoint(){return this.getTerminalPoint("source")}getTargetPoint(){return this.getTerminalPoint("target")}getTerminalPoint(t){const e=this[t];if(xb["Point"].isPointLike(e))return xb["Point"].create(e);const n=this.getTerminalCell(t);return n?n.getConnectionPoint(this,t):new xb["Point"]}getSourceCell(){return this.getTerminalCell("source")}getTargetCell(){return this.getTerminalCell("target")}getTerminalCell(t){if(this.model){const e="source"===t?this.getSourceCellId():this.getTargetCellId();if(e)return this.model.getCell(e)}return null}getSourceNode(){return this.getTerminalNode("source")}getTargetNode(){return this.getTerminalNode("target")}getTerminalNode(t){let e=this;const n={};while(e&&e.isEdge()){if(n[e.id])return null;n[e.id]=!0,e=e.getTerminalCell(t)}return e&&e.isNode()?e:null}get router(){return this.getRouter()}set router(t){null==t?this.removeRouter():this.setRouter(t)}getRouter(){return this.store.get("router")}setRouter(t,e,n){return"object"===typeof t?this.store.set("router",t,e):this.store.set("router",{name:t,args:e},n),this}removeRouter(t={}){return this.store.remove("router",t),this}get connector(){return this.getConnector()}set connector(t){null==t?this.removeConnector():this.setConnector(t)}getConnector(){return this.store.get("connector")}setConnector(t,e,n){return"object"===typeof t?this.store.set("connector",t,e):this.store.set("connector",{name:t,args:e},n),this}removeConnector(t={}){return this.store.remove("connector",t)}getDefaultLabel(){const t=this.constructor,e=this.store.get("defaultLabel")||t.defaultLabel||{};return i.cloneDeep(e)}get labels(){return this.getLabels()}set labels(t){this.setLabels(t)}getLabels(){return[...this.store.get("labels",[])].map(t=>this.parseLabel(t))}setLabels(t,e={}){return this.store.set("labels",Array.isArray(t)?t:[t],e),this}insertLabel(t,e,n={}){const r=this.getLabels(),i=r.length;let s=null!=e&&Number.isFinite(e)?e:i;return s<0&&(s=i+s+1),r.splice(s,0,this.parseLabel(t)),this.setLabels(r,n)}appendLabel(t,e={}){return this.insertLabel(t,-1,e)}getLabelAt(t){const e=this.getLabels();return null!=t&&Number.isFinite(t)?this.parseLabel(e[t]):null}setLabelAt(t,e,n={}){if(null!=t&&Number.isFinite(t)){const r=this.getLabels();r[t]=this.parseLabel(e),this.setLabels(r,n)}return this}removeLabelAt(t,e={}){const n=this.getLabels(),r=null!=t&&Number.isFinite(t)?t:-1,i=n.splice(r,1);return this.setLabels(n,e),i.length?i[0]:null}parseLabel(t){if("string"===typeof t){const e=this.constructor;return e.parseStringLabel(t)}return t}onLabelsChanged({previous:t,current:e}){const n=t&&e?e.filter(e=>t.find(t=>e===t||i.isEqual(e,t))?null:e):e?[...e]:[],r=t&&e?t.filter(t=>e.find(e=>t===e||i.isEqual(t,e))?null:t):t?[...t]:[];n.length>0&&this.notify("labels:added",{added:n,cell:this,edge:this}),r.length>0&&this.notify("labels:removed",{removed:r,cell:this,edge:this})}get vertices(){return this.getVertices()}set vertices(t){this.setVertices(t)}getVertices(){return[...this.store.get("vertices",[])]}setVertices(t,e={}){const n=Array.isArray(t)?t:[t];return this.store.set("vertices",n.map(t=>xb["Point"].toJSON(t)),e),this}insertVertex(t,e,n={}){const r=this.getVertices(),i=r.length;let s=null!=e&&Number.isFinite(e)?e:i;return s<0&&(s=i+s+1),r.splice(s,0,xb["Point"].toJSON(t)),this.setVertices(r,n)}appendVertex(t,e={}){return this.insertVertex(t,-1,e)}getVertexAt(t){if(null!=t&&Number.isFinite(t)){const e=this.getVertices();return e[t]}return null}setVertexAt(t,e,n={}){if(null!=t&&Number.isFinite(t)){const r=this.getVertices();r[t]=e,this.setVertices(r,n)}return this}removeVertexAt(t,e={}){const n=this.getVertices(),r=null!=t&&Number.isFinite(t)?t:-1;return n.splice(r,1),this.setVertices(n,e)}onVertexsChanged({previous:t,current:e}){const n=t&&e?e.filter(e=>t.find(t=>xb["Point"].equals(e,t))?null:e):e?[...e]:[],r=t&&e?t.filter(t=>e.find(e=>xb["Point"].equals(t,e))?null:t):t?[...t]:[];n.length>0&&this.notify("vertexs:added",{added:n,cell:this,edge:this}),r.length>0&&this.notify("vertexs:removed",{removed:r,cell:this,edge:this})}getDefaultMarkup(){return this.store.get("defaultMarkup")||fx.getEdgeMarkup()}getMarkup(){return super.getMarkup()||this.getDefaultMarkup()}translate(t,e,n={}){return n.translateBy=n.translateBy||this.id,n.tx=t,n.ty=e,this.applyToPoints(n=>({x:(n.x||0)+t,y:(n.y||0)+e}),n)}scale(t,e,n,r={}){return this.applyToPoints(r=>xb["Point"].create(r).scale(t,e,n).toJSON(),r)}applyToPoints(t,e={}){const n={},r=this.getSource(),i=this.getTarget();xb["Point"].isPointLike(r)&&(n.source=t(r)),xb["Point"].isPointLike(i)&&(n.target=t(i));const s=this.getVertices();return s.length>0&&(n.vertices=s.map(t)),this.store.set(n,e),this}getBBox(){return this.getPolyline().bbox()}getConnectionPoint(){return this.getPolyline().pointAt(.5)}getPolyline(){const t=[this.getSourcePoint(),...this.getVertices().map(t=>xb["Point"].create(t)),this.getTargetPoint()];return new xb["Polyline"](t)}updateParent(t){let e=null;const n=this.getSourceCell(),r=this.getTargetCell(),i=this.getParent();return n&&r&&(e=n===r||n.isDescendantOf(r)?r:r.isDescendantOf(n)?n:HC.getCommonAncestor(n,r)),i&&e&&e.id!==i.id&&i.unembed(this,t),!e||i&&i.id===e.id||e.embed(this,t),e}hasLoop(t={}){const e=this.getSource(),n=this.getTarget(),r=e.cell,i=n.cell;if(!r||!i)return!1;let s=r===i;if(!s&&t.deep&&this._model){const e=this.getSourceCell(),n=this.getTargetCell();e&&n&&(s=e.isAncestorOf(n,t)||n.isAncestorOf(e,t))}return s}getFragmentAncestor(){const t=[this,this.getSourceNode(),this.getTargetNode()].filter(t=>null!=t);return this.getCommonAncestor(...t)}isFragmentDescendantOf(t){const e=this.getFragmentAncestor();return!!e&&(e.id===t.id||e.isDescendantOf(t))}}YC.defaults={},function(t){function e(t,e){const n=t,r=e;return n.cell===r.cell&&(n.port===r.port||null==n.port&&null==r.port)}t.equalTerminals=e}(YC||(YC={})),function(t){function e(t){return{attrs:{label:{text:t}}}}t.defaultLabel={markup:[{tagName:"rect",selector:"body"},{tagName:"text",selector:"label"}],attrs:{text:{fill:"#000",fontSize:14,textAnchor:"middle",textVerticalAnchor:"middle",pointerEvents:"none"},rect:{ref:"label",fill:"#fff",rx:3,ry:3,refWidth:1,refHeight:1,refX:0,refY:0}},position:{distance:.5}},t.parseStringLabel=e}(YC||(YC={})),function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"===typeof r.isNode&&"function"===typeof r.isEdge&&"function"===typeof r.prop&&"function"===typeof r.attr&&"function"===typeof r.disconnect&&"function"===typeof r.getSource&&"function"===typeof r.getTarget}t.toStringTag="X6."+t.name,t.isEdge=e}(YC||(YC={})),function(t){t.registry=Pb.create({type:"edge",process(e,n){if(GC.exist(e,!1))throw new Error(`Edge with name '${e}' was registered by anthor Node`);if("function"===typeof n)return n.config({shape:e}),n;let r=t;const{inherit:i="edge"}=n,s=XC(n,["inherit"]);if("string"===typeof i){const t=this.get(i||"edge");null==t&&i?this.onNotFound(i,"inherited"):r=t}else r=i;null==s.constructorName&&(s.constructorName=e);const o=r.define.call(r,s);return o.config({shape:e}),o}}),GC.setEdgeRegistry(t.registry)}(YC||(YC={})),function(t){let e=0;function n(t){return t?o.pascalCase(t):(e+=1,"CustomEdge"+e)}function r(e){const{constructorName:r,overwrite:s}=e,o=XC(e,["constructorName","overwrite"]),a=i.createClass(n(r||o.shape),this);return a.config(o),o.shape&&t.registry.register(o.shape,a,s),a}function s(e){const n=e.shape||"edge",r=t.registry.get(n);return r?new r(e):t.registry.onNotFound(n)}t.define=r,t.create=s}(YC||(YC={})),function(t){const e="basic.edge";t.config({shape:e,propHooks(e){const{label:n,vertices:r}=e,i=XC(e,["label","vertices"]);if(n){null==i.labels&&(i.labels=[]);const e="string"===typeof n?t.parseStringLabel(n):n;i.labels.push(e)}return r&&Array.isArray(r)&&(i.vertices=r.map(t=>xb["Point"].create(t).toJSON())),i}}),t.registry.register(e,t)}(YC||(YC={}));var ZC=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class KC extends Oh{constructor(t,e={}){super(),this.length=0,this.comparator=e.comparator||"zIndex",this.clean(),t&&this.reset(t,{silent:!0})}toJSON(){return this.cells.map(t=>t.toJSON())}add(t,e,n){let r,i;"number"===typeof e?(r=e,i=Object.assign({merge:!1},n)):(r=this.length,i=Object.assign({merge:!1},e)),r>this.length&&(r=this.length),r<0&&(r+=this.length+1);const s=Array.isArray(t)?t:[t],o=this.comparator&&"number"!==typeof e&&!1!==i.sort,a=this.comparator||null;let l=!1;const c=[],h=[];return s.forEach(t=>{const e=this.get(t);e?i.merge&&!t.isSameStore(e)&&(e.setProp(t.getProp(),n),h.push(e),o&&!l&&(l=null==a||"function"===typeof a?e.hasChanged():"string"===typeof a?e.hasChanged(a):a.some(t=>e.hasChanged(t)))):(c.push(t),this.reference(t))}),c.length&&(o&&(l=!0),this.cells.splice(r,0,...c),this.length=this.cells.length),l&&this.sort({silent:!0}),i.silent||(c.forEach((t,e)=>{const n={cell:t,index:r+e,options:i};this.trigger("added",n),i.dryrun||t.notify("added",Object.assign({},n))}),l&&this.trigger("sorted"),(c.length||h.length)&&this.trigger("updated",{added:c,merged:h,removed:[],options:i})),this}remove(t,e={}){const n=Array.isArray(t)?t:[t],r=this.removeCells(n,e);return!e.silent&&r.length>0&&this.trigger("updated",{options:e,removed:r,added:[],merged:[]}),Array.isArray(t)?r:r[0]}removeCells(t,e){const n=[];for(let r=0;r<t.length;r+=1){const i=this.get(t[r]);if(null==i)continue;const s=this.cells.indexOf(i);this.cells.splice(s,1),this.length-=1,delete this.map[i.id],n.push(i),this.unreference(i),e.dryrun||i.remove(),e.silent||(this.trigger("removed",{cell:i,index:s,options:e}),e.dryrun||i.notify("removed",{cell:i,index:s,options:e}))}return n}reset(t,e={}){const n=this.cells.slice();if(n.forEach(t=>this.unreference(t)),this.clean(),this.add(t,Object.assign({silent:!0},e)),!e.silent){const t=this.cells.slice();this.trigger("reseted",{options:e,previous:n,current:t});const r=[],i=[];t.forEach(t=>{const e=n.some(e=>e.id===t.id);e||r.push(t)}),n.forEach(e=>{const n=t.some(t=>t.id===e.id);n||i.push(e)}),this.trigger("updated",{options:e,added:r,removed:i,merged:[]})}return this}push(t,e){return this.add(t,this.length,e)}pop(t){const e=this.at(this.length-1);return this.remove(e,t)}unshift(t,e){return this.add(t,0,e)}shift(t){const e=this.at(0);return this.remove(e,t)}get(t){if(null==t)return null;const e="string"===typeof t||"number"===typeof t?t:t.id;return this.map[e]||null}has(t){return null!=this.get(t)}at(t){return t<0&&(t+=this.length),this.cells[t]||null}first(){return this.at(0)}last(){return this.at(-1)}indexOf(t){return this.cells.indexOf(t)}toArray(){return this.cells.slice()}sort(t={}){return null!=this.comparator&&(this.cells=s.sortBy(this.cells,this.comparator),t.silent||this.trigger("sorted")),this}clone(){const t=this.constructor;return new t(this.cells.slice(),{comparator:this.comparator})}reference(t){this.map[t.id]=t,t.on("*",this.notifyCellEvent,this)}unreference(t){t.off("*",this.notifyCellEvent,this),delete this.map[t.id]}notifyCellEvent(t,e){const n=e.cell;this.trigger("cell:"+t,e),n&&(n.isNode()?this.trigger("node:"+t,Object.assign(Object.assign({},e),{node:n})):n.isEdge()&&this.trigger("edge:"+t,Object.assign(Object.assign({},e),{edge:n})))}clean(){this.length=0,this.cells=[],this.map={}}dispose(){this.reset([])}}ZC([KC.dispose()],KC.prototype,"dispose",null);var QC=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class tA extends Oh{get[Symbol.toStringTag](){return tA.toStringTag}constructor(t=[]){super(),this.batches={},this.addings=new WeakMap,this.nodes={},this.edges={},this.outgoings={},this.incomings={},this.collection=new KC(t),this.setup()}notify(t,e){this.trigger(t,e);const n=this.graph;return n&&("sorted"===t||"reseted"===t||"updated"===t?n.trigger("model:"+t,e):n.trigger(t,e)),this}setup(){const t=this.collection;t.on("sorted",()=>this.notify("sorted",null)),t.on("updated",t=>this.notify("updated",t)),t.on("cell:change:zIndex",()=>this.sortOnChangeZ()),t.on("added",({cell:t})=>{this.onCellAdded(t)}),t.on("removed",t=>{const e=t.cell;this.onCellRemoved(e,t.options),this.notify("cell:removed",t),e.isNode()?this.notify("node:removed",Object.assign(Object.assign({},t),{node:e})):e.isEdge()&&this.notify("edge:removed",Object.assign(Object.assign({},t),{edge:e}))}),t.on("reseted",t=>{this.onReset(t.current),this.notify("reseted",t)}),t.on("edge:change:source",({edge:t})=>this.onEdgeTerminalChanged(t,"source")),t.on("edge:change:target",({edge:t})=>{this.onEdgeTerminalChanged(t,"target")})}sortOnChangeZ(){this.collection.sort()}onCellAdded(t){const e=t.id;t.isEdge()?(t.updateParent(),this.edges[e]=!0,this.onEdgeTerminalChanged(t,"source"),this.onEdgeTerminalChanged(t,"target")):this.nodes[e]=!0}onCellRemoved(t,e){const n=t.id;if(t.isEdge()){delete this.edges[n];const e=t.getSource(),r=t.getTarget();if(e&&e.cell){const t=this.outgoings[e.cell],r=t?t.indexOf(n):-1;r>=0&&(t.splice(r,1),0===t.length&&delete this.outgoings[e.cell])}if(r&&r.cell){const t=this.incomings[r.cell],e=t?t.indexOf(n):-1;e>=0&&(t.splice(e,1),0===t.length&&delete this.incomings[r.cell])}}else delete this.nodes[n];e.clear||(e.disconnectEdges?this.disconnectConnectedEdges(t,e):this.removeConnectedEdges(t,e)),t.model===this&&(t.model=null)}onReset(t){this.nodes={},this.edges={},this.outgoings={},this.incomings={},t.forEach(t=>this.onCellAdded(t))}onEdgeTerminalChanged(t,e){const n="source"===e?this.outgoings:this.incomings,r=t.previous(e);if(r&&r.cell){const e=HC.isCell(r.cell)?r.cell.id:r.cell,i=n[e],s=i?i.indexOf(t.id):-1;s>=0&&(i.splice(s,1),0===i.length&&delete n[e])}const i=t.getTerminal(e);if(i&&i.cell){const e=HC.isCell(i.cell)?i.cell.id:i.cell,r=n[e]||[],s=r.indexOf(t.id);-1===s&&r.push(t.id),n[e]=r}}prepareCell(t,e){return t.model||e&&e.dryrun||(t.model=this),null==t.zIndex&&t.setZIndex(this.getMaxZIndex()+1,{silent:!0}),t}resetCells(t,e={}){return t.map(t=>this.prepareCell(t,Object.assign(Object.assign({},e),{dryrun:!0}))),this.collection.reset(t,e),t.map(t=>this.prepareCell(t,{options:e})),this}clear(t={}){const e=this.getCells();if(0===e.length)return this;const n=Object.assign(Object.assign({},t),{clear:!0});return this.batchUpdate("clear",()=>{const t=e.sort((t,e)=>{const n=t.isEdge()?1:2,r=e.isEdge()?1:2;return n-r});while(t.length>0){const e=t.shift();e&&e.remove(n)}},n),this}addNode(t,e={}){const n=JC.isNode(t)?t:this.createNode(t);return this.addCell(n,e),n}updateNode(t,e={}){const n=this.createNode(t),r=n.getProp();return n.dispose(),this.updateCell(r,e)}createNode(t){return JC.create(t)}addEdge(t,e={}){const n=YC.isEdge(t)?t:this.createEdge(t);return this.addCell(n,e),n}createEdge(t){return YC.create(t)}updateEdge(t,e={}){const n=this.createEdge(t),r=n.getProp();return n.dispose(),this.updateCell(r,e)}addCell(t,e={}){return Array.isArray(t)?this.addCells(t,e):(this.collection.has(t)||this.addings.has(t)||(this.addings.set(t,!0),this.collection.add(this.prepareCell(t,e),e),t.eachChild(t=>this.addCell(t,e)),this.addings.delete(t)),this)}addCells(t,e={}){const n=t.length;if(0===n)return this;const r=Object.assign(Object.assign({},e),{position:n-1,maxPosition:n-1});return this.startBatch("add",Object.assign(Object.assign({},r),{cells:t})),t.forEach(t=>{this.addCell(t,r),r.position-=1}),this.stopBatch("add",Object.assign(Object.assign({},r),{cells:t})),this}updateCell(t,e={}){const n=t.id&&this.getCell(t.id);return!!n&&this.batchUpdate("update",()=>(Object.entries(t).forEach(([t,r])=>n.setProp(t,r,e)),!0),t)}removeCell(t,e={}){const n="string"===typeof t?this.getCell(t):t;return n&&this.has(n)?this.collection.remove(n,e):null}updateCellId(t,e){if(t.id===e)return;this.startBatch("update",{id:e}),t.prop("id",e);const n=t.clone({keepId:!0});this.addCell(n);const r=this.getConnectedEdges(t);return r.forEach(n=>{const r=n.getSourceCell(),i=n.getTargetCell();r===t&&n.setSource(Object.assign(Object.assign({},n.getSource()),{cell:e})),i===t&&n.setTarget(Object.assign(Object.assign({},n.getTarget()),{cell:e}))}),this.removeCell(t),this.stopBatch("update",{id:e}),n}removeCells(t,e={}){return t.length?this.batchUpdate("remove",()=>t.map(t=>this.removeCell(t,e))):[]}removeConnectedEdges(t,e={}){const n=this.getConnectedEdges(t);return n.forEach(t=>{t.remove(e)}),n}disconnectConnectedEdges(t,e={}){const n="string"===typeof t?t:t.id;this.getConnectedEdges(t).forEach(t=>{const r=t.getSourceCellId(),i=t.getTargetCellId();r===n&&t.setSource({x:0,y:0},e),i===n&&t.setTarget({x:0,y:0},e)})}has(t){return this.collection.has(t)}total(){return this.collection.length}indexOf(t){return this.collection.indexOf(t)}getCell(t){return this.collection.get(t)}getCells(){return this.collection.toArray()}getFirstCell(){return this.collection.first()}getLastCell(){return this.collection.last()}getMinZIndex(){const t=this.collection.first();return t&&t.getZIndex()||0}getMaxZIndex(){const t=this.collection.last();return t&&t.getZIndex()||0}getCellsFromCache(t){return t?Object.keys(t).map(t=>this.getCell(t)).filter(t=>null!=t):[]}getNodes(){return this.getCellsFromCache(this.nodes)}getEdges(){return this.getCellsFromCache(this.edges)}getOutgoingEdges(t){const e="string"===typeof t?t:t.id,n=this.outgoings[e];return n?n.map(t=>this.getCell(t)).filter(t=>t&&t.isEdge()):null}getIncomingEdges(t){const e="string"===typeof t?t:t.id,n=this.incomings[e];return n?n.map(t=>this.getCell(t)).filter(t=>t&&t.isEdge()):null}getConnectedEdges(t,e={}){const n=[],r="string"===typeof t?this.getCell(t):t;if(null==r)return n;const i={},s=e.indirect;let o=e.incoming,a=e.outgoing;null==o&&null==a&&(o=a=!0);const l=(t,e)=>{const r=e?this.getOutgoingEdges(t):this.getIncomingEdges(t);if(null!=r&&r.forEach(t=>{i[t.id]||(n.push(t),i[t.id]=!0,s&&(o&&l(t,!1),a&&l(t,!0)))}),s&&t.isEdge()){const r=e?t.getTargetCell():t.getSourceCell();r&&r.isEdge()&&(i[r.id]||(n.push(r),l(r,e)))}};if(a&&l(r,!0),o&&l(r,!1),e.deep){const t=r.getDescendants({deep:!0}),s={};t.forEach(t=>{t.isNode()&&(s[t.id]=!0)});const l=(t,r)=>{const o=r?this.getOutgoingEdges(t.id):this.getIncomingEdges(t.id);null!=o&&o.forEach(t=>{if(!i[t.id]){const r=t.getSourceCell(),o=t.getTargetCell();if(!e.enclosed&&r&&s[r.id]&&o&&s[o.id])return;n.push(t),i[t.id]=!0}})};t.forEach(t=>{t.isEdge()||(a&&l(t,!0),o&&l(t,!1))})}return n}isBoundary(t,e){const n="string"===typeof t?this.getCell(t):t,r=e?this.getIncomingEdges(n):this.getOutgoingEdges(n);return null==r||0===r.length}getBoundaryNodes(t){const e=[];return Object.keys(this.nodes).forEach(n=>{if(this.isBoundary(n,t)){const t=this.getCell(n);t&&e.push(t)}}),e}getRoots(){return this.getBoundaryNodes(!0)}getLeafs(){return this.getBoundaryNodes(!1)}isRoot(t){return this.isBoundary(t,!0)}isLeaf(t){return this.isBoundary(t,!1)}getNeighbors(t,e={}){let n=e.incoming,r=e.outgoing;null==n&&null==r&&(n=r=!0);const i=this.getConnectedEdges(t,e),s=i.reduce((i,s)=>{const o=s.hasLoop(e),a=s.getSourceCell(),l=s.getTargetCell();return n&&a&&a.isNode()&&!i[a.id]&&(!o&&(a===t||e.deep&&a.isDescendantOf(t))||(i[a.id]=a)),r&&l&&l.isNode()&&!i[l.id]&&(!o&&(l===t||e.deep&&l.isDescendantOf(t))||(i[l.id]=l)),i},{});if(t.isEdge()){if(n){const e=t.getSourceCell();e&&e.isNode()&&!s[e.id]&&(s[e.id]=e)}if(r){const e=t.getTargetCell();e&&e.isNode()&&!s[e.id]&&(s[e.id]=e)}}return Object.keys(s).map(t=>s[t])}isNeighbor(t,e,n={}){let r=n.incoming,i=n.outgoing;return null==r&&null==i&&(r=i=!0),this.getConnectedEdges(t,n).some(t=>{const n=t.getSourceCell(),s=t.getTargetCell();return!(!r||!n||n.id!==e.id)||!(!i||!s||s.id!==e.id)})}getSuccessors(t,e={}){const n=[];return this.search(t,(r,i)=>{r!==t&&this.matchDistance(i,e.distance)&&n.push(r)},Object.assign(Object.assign({},e),{outgoing:!0})),n}isSuccessor(t,e,n={}){let r=!1;return this.search(t,(i,s)=>{if(i===e&&i!==t&&this.matchDistance(s,n.distance))return r=!0,!1},Object.assign(Object.assign({},n),{outgoing:!0})),r}getPredecessors(t,e={}){const n=[];return this.search(t,(r,i)=>{r!==t&&this.matchDistance(i,e.distance)&&n.push(r)},Object.assign(Object.assign({},e),{incoming:!0})),n}isPredecessor(t,e,n={}){let r=!1;return this.search(t,(i,s)=>{if(i===e&&i!==t&&this.matchDistance(s,n.distance))return r=!0,!1},Object.assign(Object.assign({},n),{incoming:!0})),r}matchDistance(t,e){return null==e||("function"===typeof e?e(t):!(!Array.isArray(e)||!e.includes(t))||t===e)}getCommonAncestor(...t){const e=[];return t.forEach(t=>{t&&(Array.isArray(t)?e.push(...t):e.push(t))}),HC.getCommonAncestor(...e)}getSubGraph(t,e={}){const n=[],r={},i=[],s=[],o=t=>{r[t.id]||(n.push(t),r[t.id]=t,t.isEdge()&&s.push(t),t.isNode()&&i.push(t))};return t.forEach(t=>{if(o(t),e.deep){const e=t.getDescendants({deep:!0});e.forEach(t=>o(t))}}),s.forEach(t=>{const e=t.getSourceCell(),s=t.getTargetCell();e&&!r[e.id]&&(n.push(e),r[e.id]=e,e.isNode()&&i.push(e)),s&&!r[s.id]&&(n.push(s),r[s.id]=s,s.isNode()&&i.push(s))}),i.forEach(t=>{const i=this.getConnectedEdges(t,e);i.forEach(t=>{const e=t.getSourceCell(),i=t.getTargetCell();!r[t.id]&&e&&r[e.id]&&i&&r[i.id]&&(n.push(t),r[t.id]=t)})}),n}cloneSubGraph(t,e={}){const n=this.getSubGraph(t,e);return this.cloneCells(n)}cloneCells(t){return HC.cloneCells(t)}getNodesFromPoint(t,e){const n="number"===typeof t?{x:t,y:e||0}:t;return this.getNodes().filter(t=>t.getBBox().containsPoint(n))}getNodesInArea(t,e,n,r,i){const s="number"===typeof t?new xb["Rectangle"](t,e,n,r):xb["Rectangle"].create(t),o="number"===typeof t?i:e,a=o&&o.strict;return this.getNodes().filter(t=>{const e=t.getBBox();return a?s.containsRect(e):s.isIntersectWithRect(e)})}getEdgesInArea(t,e,n,r,i){const s="number"===typeof t?new xb["Rectangle"](t,e,n,r):xb["Rectangle"].create(t),o="number"===typeof t?i:e,a=o&&o.strict;return this.getEdges().filter(t=>{const e=t.getBBox();return 0===e.width?e.inflate(1,0):0===e.height&&e.inflate(0,1),a?s.containsRect(e):s.isIntersectWithRect(e)})}getNodesUnderNode(t,e={}){const n=t.getBBox(),r=null==e.by||"bbox"===e.by?this.getNodesInArea(n):this.getNodesFromPoint(n[e.by]);return r.filter(e=>t.id!==e.id&&!e.isDescendantOf(t))}getAllCellsBBox(){return this.getCellsBBox(this.getCells())}getCellsBBox(t,e={}){return HC.getCellsBBox(t,e)}search(t,e,n={}){n.breadthFirst?this.breadthFirstSearch(t,e,n):this.depthFirstSearch(t,e,n)}breadthFirstSearch(t,e,n={}){const i=[],s={},o={};i.push(t),o[t.id]=0;while(i.length>0){const t=i.shift();if(null==t||s[t.id])continue;if(s[t.id]=!0,!1===r.call(e,this,t,o[t.id]))continue;const a=this.getNeighbors(t,n);a.forEach(e=>{o[e.id]=o[t.id]+1,i.push(e)})}}depthFirstSearch(t,e,n={}){const i=[],s={},o={};i.push(t),o[t.id]=0;while(i.length>0){const t=i.pop();if(null==t||s[t.id])continue;if(s[t.id]=!0,!1===r.call(e,this,t,o[t.id]))continue;const a=this.getNeighbors(t,n),l=i.length;a.forEach(e=>{o[e.id]=o[t.id]+1,i.splice(l,0,e)})}}getShortestPath(t,e,n={}){const r={};this.getEdges().forEach(t=>{const e=t.getSourceCellId(),i=t.getTargetCellId();e&&i&&(r[e]||(r[e]=[]),r[i]||(r[i]=[]),r[e].push(i),n.directed||r[i].push(e))});const i="string"===typeof t?t:t.id,s=ub.run(r,i,n.weight),o=[];let a="string"===typeof e?e:e.id;s[a]&&o.push(a);while(a=s[a])o.unshift(a);return o}translate(t,e,n){return this.getCells().filter(t=>!t.hasParent()).forEach(r=>r.translate(t,e,n)),this}resize(t,e,n){return this.resizeCells(t,e,this.getCells(),n)}resizeCells(t,e,n,r={}){const i=this.getCellsBBox(n);if(i){const s=Math.max(t/i.width,0),o=Math.max(e/i.height,0),a=i.getOrigin();n.forEach(t=>t.scale(s,o,a,r))}return this}toJSON(t={}){return tA.toJSON(this.getCells(),t)}parseJSON(t){return tA.fromJSON(t)}fromJSON(t,e={}){const n=this.parseJSON(t);return this.resetCells(n,e),this}startBatch(t,e={}){return this.batches[t]=(this.batches[t]||0)+1,this.notify("batch:start",{name:t,data:e}),this}stopBatch(t,e={}){return this.batches[t]=(this.batches[t]||0)-1,this.notify("batch:stop",{name:t,data:e}),this}batchUpdate(t,e,n={}){this.startBatch(t,n);const r=e();return this.stopBatch(t,n),r}hasActiveBatch(t=Object.keys(this.batches)){const e=Array.isArray(t)?t:[t];return e.some(t=>this.batches[t]>0)}dispose(){this.collection.dispose()}}QC([tA.dispose()],tA.prototype,"dispose",null),function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"===typeof r.addNode&&"function"===typeof r.addEdge&&null!=r.collection}t.toStringTag="X6."+t.name,t.isModel=e}(tA||(tA={})),function(t){function e(t,e={}){return{cells:t.map(t=>t.toJSON(e))}}function n(t){const e=[];return Array.isArray(t)?e.push(...t):(t.cells&&e.push(...t.cells),t.nodes&&t.nodes.forEach(t=>{null==t.shape&&(t.shape="rect"),e.push(t)}),t.edges&&t.edges.forEach(t=>{null==t.shape&&(t.shape="edge"),e.push(t)})),e.map(t=>{const e=t.shape;if(e){if(JC.registry.exist(e))return JC.create(t);if(YC.registry.exist(e))return YC.create(t)}throw new Error("The `shape` should be specified when creating a node/edge instance")})}t.toJSON=e,t.fromJSON=n}(tA||(tA={}));var eA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class nA extends JC{get label(){return this.getLabel()}set label(t){this.setLabel(t)}getLabel(){return this.getAttrByPath("text/text")}setLabel(t,e){return null==t?this.removeLabel():this.setAttrByPath("text/text",t,e),this}removeLabel(){return this.removeAttrByPath("text/text"),this}}(function(t){t.bodyAttr={fill:"#ffffff",stroke:"#333333",strokeWidth:2},t.labelAttr={fontSize:14,fill:"#000000",refX:.5,refY:.5,textAnchor:"middle",textVerticalAnchor:"middle",fontFamily:"Arial, helvetica, sans-serif"},t.config({attrs:{text:Object.assign({},t.labelAttr)},propHooks(t){const{label:e}=t,n=eA(t,["label"]);return e&&i.setByPath(n,"attrs/text/text",e),n},visible:!0})})(nA||(nA={}));var rA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};function iA(t,e="body"){return[{tagName:t,selector:e},{tagName:"text",selector:"label"}]}function sA(t="xlink:href"){const e=e=>{const{imageUrl:n,imageWidth:r,imageHeight:i}=e,s=rA(e,["imageUrl","imageWidth","imageHeight"]);if(null!=n||null!=r||null!=i){const e=()=>{if(s.attrs){const e=s.attrs.image;null!=n&&(e[t]=n),null!=r&&(e.width=r),null!=i&&(e.height=i),s.attrs.image=e}};s.attrs?(null==s.attrs.image&&(s.attrs.image={}),e()):(s.attrs={image:{}},e())}return s};return e}function oA(t,e,n={}){const r={constructorName:t,markup:iA(t,n.selector),attrs:{[t]:Object.assign({},nA.bodyAttr)}},s=n.parent||nA;return s.define(i.merge(r,e,{shape:t}))}const aA=oA("rect",{attrs:{body:{refWidth:"100%",refHeight:"100%"}}}),lA=YC.define({shape:"edge",markup:[{tagName:"path",selector:"wrap",groupSelector:"lines",attrs:{fill:"none",cursor:"pointer",stroke:"transparent",strokeLinecap:"round"}},{tagName:"path",selector:"line",groupSelector:"lines",attrs:{fill:"none",pointerEvents:"none"}}],attrs:{lines:{connection:!0,strokeLinejoin:"round"},wrap:{strokeWidth:10},line:{stroke:"#333",strokeWidth:2,targetMarker:"classic"}}}),cA=oA("ellipse",{attrs:{body:{refCx:"50%",refCy:"50%",refRx:"50%",refRy:"50%"}}});var hA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class uA extends nA{get points(){return this.getPoints()}set points(t){this.setPoints(t)}getPoints(){return this.getAttrByPath("body/refPoints")}setPoints(t,e){return null==t?this.removePoints():this.setAttrByPath("body/refPoints",uA.pointsToString(t),e),this}removePoints(){return this.removeAttrByPath("body/refPoints"),this}}(function(t){function e(t){return"string"===typeof t?t:t.map(t=>Array.isArray(t)?t.join(","):xb["Point"].isPointLike(t)?`${t.x}, ${t.y}`:"").join(" ")}t.pointsToString=e,t.config({propHooks(t){const{points:n}=t,r=hA(t,["points"]);if(n){const t=e(n);t&&i.setByPath(r,"attrs/body/refPoints",t)}return r}})})(uA||(uA={}));const dA=oA("polygon",{},{parent:uA}),gA=oA("polyline",{},{parent:uA});var fA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const pA=nA.define({shape:"path",markup:[{tagName:"rect",selector:"bg"},{tagName:"path",selector:"body"},{tagName:"text",selector:"label"}],attrs:{bg:{refWidth:"100%",refHeight:"100%",fill:"none",stroke:"none",pointerEvents:"all"},body:{fill:"none",stroke:"#000",strokeWidth:2}},propHooks(t){const{path:e}=t,n=fA(t,["path"]);return e&&i.setByPath(n,"attrs/body/refD",e),n}});var mA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};const yA=nA.define({shape:"text-block",markup:[{tagName:"rect",selector:"body"},jp.SUPPORT_FOREIGNOBJECT?{tagName:"foreignObject",selector:"foreignObject",children:[{tagName:"div",ns:c.ns.xhtml,selector:"label",style:{width:"100%",height:"100%",position:"static",backgroundColor:"transparent",textAlign:"center",margin:0,padding:"0px 5px",boxSizing:"border-box",display:"flex",alignItems:"center",justifyContent:"center"}}]}:{tagName:"text",selector:"label",attrs:{textAnchor:"middle"}}],attrs:{body:Object.assign(Object.assign({},nA.bodyAttr),{refWidth:"100%",refHeight:"100%"}),foreignObject:{refWidth:"100%",refHeight:"100%"},label:{style:{fontSize:14}}},propHooks(t){const{text:e}=t,n=mA(t,["text"]);return e&&i.setByPath(n,"attrs/label/text",e),n},attrHooks:{text:{set(t,{cell:e,view:n,refBBox:i,elem:s,attrs:o}){if(!(s instanceof HTMLElement)){const a=o.style||{},l={text:t,width:-5,height:"100%"},c=Object.assign({textVerticalAnchor:"middle"},a),h=Aw.presets.textWrap;return r.call(h.set,this,l,{cell:e,view:n,elem:s,refBBox:i,attrs:c}),{fill:a.color||null}}s.textContent=t},position(t,{refBBox:e,elem:n}){if(n instanceof SVGElement)return e.getCenter()}}}}),bA=oA("image",{attrs:{image:{refWidth:"100%",refHeight:"100%"}},propHooks:sA()},{selector:"image"}),vA=oA("circle",{attrs:{body:{refCx:"50%",refCy:"50%",refR:"50%"}}});class wA extends Px{constructor(){super(...arguments),this.portsCache={}}get[Symbol.toStringTag](){return wA.toStringTag}getContainerClassName(){const t=[super.getContainerClassName(),this.prefixClassName("node")];return this.can("nodeMovable")||t.push(this.prefixClassName("node-immovable")),t.join(" ")}updateClassName(t){const e=t.target;if(e.hasAttribute("magnet")){const t=this.prefixClassName("port-unconnectable");this.can("magnetConnectable")?c.removeClass(e,t):c.addClass(e,t)}else{const t=this.prefixClassName("node-immovable");this.can("nodeMovable")?this.removeClass(t):this.addClass(t)}}isNodeView(){return!0}confirmUpdate(t,e={}){let n=t;return this.hasAction(n,"ports")&&(this.removePorts(),this.cleanPortsCache()),this.hasAction(n,"render")?(this.render(),n=this.removeAction(n,["render","update","resize","translate","rotate","ports","tools"])):(n=this.handleAction(n,"resize",()=>this.resize(),"update"),n=this.handleAction(n,"update",()=>this.update(),Ow.useCSSSelector?"ports":null),n=this.handleAction(n,"translate",()=>this.translate()),n=this.handleAction(n,"rotate",()=>this.rotate()),n=this.handleAction(n,"ports",()=>this.renderPorts()),n=this.handleAction(n,"tools",()=>{this.getFlag("tools")===t?this.renderTools():this.updateTools(e)})),n}update(t){this.cleanCache(),Ow.useCSSSelector&&this.removePorts();const e=this.cell,n=e.getSize(),r=e.getAttrs();this.updateAttrs(this.container,r,{attrs:t===r?null:t,rootBBox:new xb["Rectangle"](0,0,n.width,n.height),selectors:this.selectors}),Ow.useCSSSelector&&this.renderPorts()}renderMarkup(){const t=this.cell.markup;if(t){if("string"===typeof t)throw new TypeError("Not support string markup.");return this.renderJSONMarkup(t)}throw new TypeError("Invalid node markup.")}renderJSONMarkup(t){const e=this.parseJSONMarkup(t,this.container);this.selectors=e.selectors,this.container.appendChild(e.fragment)}render(){return this.empty(),this.renderMarkup(),this.resize(),this.updateTransform(),Ow.useCSSSelector||this.renderPorts(),this.renderTools(),this}resize(){this.cell.getAngle()&&this.rotate(),this.update()}translate(){this.updateTransform()}rotate(){this.updateTransform()}getTranslationString(){const t=this.cell.getPosition();return`translate(${t.x},${t.y})`}getRotationString(){const t=this.cell.getAngle();if(t){const e=this.cell.getSize();return`rotate(${t},${e.width/2},${e.height/2})`}}updateTransform(){let t=this.getTranslationString();const e=this.getRotationString();e&&(t+=" "+e),this.container.setAttribute("transform",t)}findPortElem(t,e){const n=t?this.portsCache[t]:null;if(!n)return null;const r=n.portContentElement,i=n.portContentSelectors||{};return this.findOne(e,r,i)}cleanPortsCache(){this.portsCache={}}removePorts(){Object.values(this.portsCache).forEach(t=>{c.remove(t.portElement)})}renderPorts(){const t=this.container,e=[];t.childNodes.forEach(t=>{e.push(t)});const n=this.cell.getParsedPorts(),r=s.groupBy(n,"zIndex"),i="auto";r[i]&&r[i].forEach(n=>{const r=this.getPortElement(n);t.append(r),e.push(r)}),Object.keys(r).forEach(t=>{if(t!==i){const n=parseInt(t,10);this.appendPorts(r[t],n,e)}}),this.updatePorts()}appendPorts(t,e,n){const r=t.map(t=>this.getPortElement(t));n[e]||e<0?c.before(n[Math.max(e,0)],r):c.append(this.container,r)}getPortElement(t){const e=this.portsCache[t.id];return e?e.portElement:this.createPortElement(t)}createPortElement(t){let e=fx.renderMarkup(this.cell.getPortContainerMarkup());const n=e.elem;if(null==n)throw new Error("Invalid port container markup.");e=fx.renderMarkup(this.getPortMarkup(t));const r=e.elem,i=e.selectors;if(null==r)throw new Error("Invalid port markup.");this.setAttrs({port:t.id,"port-group":t.group},r);let s="x6-port";t.group&&(s+=" x6-port-"+t.group),c.addClass(n,s),c.addClass(n,"x6-port"),c.addClass(r,"x6-port-body"),n.appendChild(r);let o,a,l=i;const h=this.existPortLabel(t);if(h){if(e=fx.renderMarkup(this.getPortLabelMarkup(t.label)),o=e.elem,a=e.selectors,null==o)throw new Error("Invalid port label markup.");if(i&&a){for(const t in a)if(i[t]&&t!==this.rootSelector)throw new Error("Selectors within port must be unique.");l=Object.assign(Object.assign({},i),a)}c.addClass(o,"x6-port-label"),n.appendChild(o)}return this.portsCache[t.id]={portElement:n,portSelectors:l,portLabelElement:o,portLabelSelectors:a,portContentElement:r,portContentSelectors:i},this.graph.options.onPortRendered&&this.graph.options.onPortRendered({port:t,node:this.cell,container:n,selectors:l,labelContainer:o,labelSelectors:a,contentContainer:r,contentSelectors:i}),n}updatePorts(){const t=this.cell.getParsedGroups(),e=Object.keys(t);0===e.length?this.updatePortGroup():e.forEach(t=>this.updatePortGroup(t))}updatePortGroup(t){const e=xb["Rectangle"].fromSize(this.cell.getSize()),n=this.cell.getPortsLayoutByGroup(t,e);for(let r=0,i=n.length;r<i;r+=1){const t=n[r],e=t.portId,i=this.portsCache[e]||{},s=t.portLayout;if(this.applyPortTransform(i.portElement,s),null!=t.portAttrs){const e={selectors:i.portSelectors||{}};t.portSize&&(e.rootBBox=xb["Rectangle"].fromSize(t.portSize)),this.updateAttrs(i.portElement,t.portAttrs,e)}const o=t.labelLayout;if(o&&i.portLabelElement&&(this.applyPortTransform(i.portLabelElement,o,-(s.angle||0)),o.attrs)){const e={selectors:i.portLabelSelectors||{}};t.labelSize&&(e.rootBBox=xb["Rectangle"].fromSize(t.labelSize)),this.updateAttrs(i.portLabelElement,o.attrs,e)}}}applyPortTransform(t,e,n=0){const r=e.angle,i=e.position,s=c.createSVGMatrix().rotate(n).translate(i.x||0,i.y||0).rotate(r||0);c.transform(t,s,{absolute:!0})}getPortMarkup(t){return t.markup||this.cell.portMarkup}getPortLabelMarkup(t){return t.markup||this.cell.portLabelMarkup}existPortLabel(t){return t.attrs&&t.attrs.text}getEventArgs(t,e,n){const r=this,i=r.cell,s=i;return null==e||null==n?{e:t,view:r,node:i,cell:s}:{e:t,x:e,y:n,view:r,node:i,cell:s}}getPortEventArgs(t,e,n){const r=this,i=r.cell,s=i;return n?{e:t,x:n.x,y:n.y,view:r,node:i,cell:s,port:e}:{e:t,view:r,node:i,cell:s,port:e}}notifyMouseDown(t,e,n){super.onMouseDown(t,e,n),this.notify("node:mousedown",this.getEventArgs(t,e,n))}notifyMouseMove(t,e,n){super.onMouseMove(t,e,n),this.notify("node:mousemove",this.getEventArgs(t,e,n))}notifyMouseUp(t,e,n){super.onMouseUp(t,e,n),this.notify("node:mouseup",this.getEventArgs(t,e,n))}notifyPortEvent(t,e,n){const r=this.findAttr("port",e.target);if(r){const i=e.type;"node:port:mouseenter"===t?e.type="mouseenter":"node:port:mouseleave"===t&&(e.type="mouseleave"),this.notify(t,this.getPortEventArgs(e,r,n)),e.type=i}}onClick(t,e,n){super.onClick(t,e,n),this.notify("node:click",this.getEventArgs(t,e,n)),this.notifyPortEvent("node:port:click",t,{x:e,y:n})}onDblClick(t,e,n){super.onDblClick(t,e,n),this.notify("node:dblclick",this.getEventArgs(t,e,n)),this.notifyPortEvent("node:port:dblclick",t,{x:e,y:n})}onContextMenu(t,e,n){super.onContextMenu(t,e,n),this.notify("node:contextmenu",this.getEventArgs(t,e,n)),this.notifyPortEvent("node:port:contextmenu",t,{x:e,y:n})}onMouseDown(t,e,n){this.isPropagationStopped(t)||(this.notifyMouseDown(t,e,n),this.notifyPortEvent("node:port:mousedown",t,{x:e,y:n}),this.startNodeDragging(t,e,n))}onMouseMove(t,e,n){const r=this.getEventData(t),i=r.action;if("magnet"===i)this.dragMagnet(t,e,n);else{if("move"===i){const i=r,s=i.targetView||this;s.dragNode(t,e,n),s.notify("node:moving",{e:t,x:e,y:n,view:s,cell:s.cell,node:s.cell})}this.notifyMouseMove(t,e,n),this.notifyPortEvent("node:port:mousemove",t,{x:e,y:n})}this.setEventData(t,r)}onMouseUp(t,e,n){const r=this.getEventData(t),i=r.action;if("magnet"===i)this.stopMagnetDragging(t,e,n);else if(this.notifyMouseUp(t,e,n),this.notifyPortEvent("node:port:mouseup",t,{x:e,y:n}),"move"===i){const i=r,s=i.targetView||this;s.stopNodeDragging(t,e,n)}const s=r.targetMagnet;s&&this.onMagnetClick(t,s,e,n),this.checkMouseleave(t)}onMouseOver(t){super.onMouseOver(t),this.notify("node:mouseover",this.getEventArgs(t)),this.notifyPortEvent("node:port:mouseenter",t),this.notifyPortEvent("node:port:mouseover",t)}onMouseOut(t){super.onMouseOut(t),this.notify("node:mouseout",this.getEventArgs(t)),this.notifyPortEvent("node:port:mouseleave",t),this.notifyPortEvent("node:port:mouseout",t)}onMouseEnter(t){this.updateClassName(t),super.onMouseEnter(t),this.notify("node:mouseenter",this.getEventArgs(t))}onMouseLeave(t){super.onMouseLeave(t),this.notify("node:mouseleave",this.getEventArgs(t))}onMouseWheel(t,e,n,r){super.onMouseWheel(t,e,n,r),this.notify("node:mousewheel",Object.assign({delta:r},this.getEventArgs(t,e,n)))}onMagnetClick(t,e,n,r){const i=this.graph,s=i.view.getMouseMovedCount(t);s>i.options.clickThreshold||this.notify("node:magnet:click",Object.assign({magnet:e},this.getEventArgs(t,n,r)))}onMagnetDblClick(t,e,n,r){this.notify("node:magnet:dblclick",Object.assign({magnet:e},this.getEventArgs(t,n,r)))}onMagnetContextMenu(t,e,n,r){this.notify("node:magnet:contextmenu",Object.assign({magnet:e},this.getEventArgs(t,n,r)))}onMagnetMouseDown(t,e,n,r){this.startMagnetDragging(t,n,r)}onCustomEvent(t,e,n,r){this.notify("node:customevent",Object.assign({name:e},this.getEventArgs(t,n,r))),super.onCustomEvent(t,e,n,r)}prepareEmbedding(t){const e=this.graph,n=this.getEventData(t),r=n.cell||this.cell,i=e.findViewByCell(r),s=e.snapToGrid(t.clientX,t.clientY);this.notify("node:embed",{e:t,node:r,view:i,cell:r,x:s.x,y:s.y,currentParent:r.getParent()})}processEmbedding(t,e){const n=e.cell||this.cell,i=e.graph||this.graph,o=i.options.embedding,a=o.findParent;let l="function"===typeof a?r.call(a,i,{view:this,node:this.cell}).filter(t=>HC.isCell(t)&&this.cell.id!==t.id&&!t.isDescendantOf(this.cell)):i.model.getNodesUnderNode(n,{by:a});if(o.frontOnly&&l.length>0){const t=s.groupBy(l,"zIndex"),e=s.max(Object.keys(t).map(t=>parseInt(t,10)));e&&(l=t[e])}l=l.filter(t=>t.visible);let c=null;const h=e.candidateEmbedView,u=o.validate;for(let s=l.length-1;s>=0;s-=1){const t=l[s];if(h&&h.cell.id===t.id){c=h;break}{const e=t.findView(i);if(u&&r.call(u,i,{child:this.cell,parent:e.cell,childView:this,parentView:e})){c=e;break}}}this.clearEmbedding(e),c&&c.highlight(null,{type:"embedding"}),e.candidateEmbedView=c;const d=i.snapToGrid(t.clientX,t.clientY);this.notify("node:embedding",{e:t,cell:n,node:n,view:i.findViewByCell(n),x:d.x,y:d.y,currentParent:n.getParent(),candidateParent:c?c.cell:null})}clearEmbedding(t){const e=t.candidateEmbedView;e&&(e.unhighlight(null,{type:"embedding"}),t.candidateEmbedView=null)}finalizeEmbedding(t,e){this.graph.startBatch("embedding");const n=e.cell||this.cell,r=e.graph||this.graph,i=r.findViewByCell(n),s=n.getParent(),o=e.candidateEmbedView;if(o?(o.unhighlight(null,{type:"embedding"}),e.candidateEmbedView=null,null!=s&&s.id===o.cell.id||o.cell.insertChild(n,void 0,{ui:!0})):s&&s.unembed(n,{ui:!0}),r.model.getConnectedEdges(n,{deep:!0}).forEach(t=>{t.updateParent({ui:!0})}),i&&o){const e=r.snapToGrid(t.clientX,t.clientY);i.notify("node:embedded",{e:t,cell:n,x:e.x,y:e.y,node:n,view:r.findViewByCell(n),previousParent:s,currentParent:n.getParent()})}this.graph.stopBatch("embedding")}getDelegatedView(){let t=this.cell,e=this;while(e){if(t.isEdge())break;if(!t.hasParent()||e.can("stopDelegateOnDragging"))return e;t=t.getParent(),e=this.graph.findViewByCell(t)}return null}validateMagnet(t,e,n){if("passive"!==e.getAttribute("magnet")){const i=this.graph.options.connecting.validateMagnet;return!i||r.call(i,this.graph,{e:n,magnet:e,view:t,cell:t.cell})}return!1}startMagnetDragging(t,e,n){if(!this.can("magnetConnectable"))return;t.stopPropagation();const r=t.currentTarget,i=this.graph;this.setEventData(t,{targetMagnet:r}),this.validateMagnet(this,r,t)?(i.options.magnetThreshold<=0&&this.startConnectting(t,r,e,n),this.setEventData(t,{action:"magnet"}),this.stopPropagation(t)):this.onMouseDown(t,e,n),i.view.delegateDragEvents(t,this)}startConnectting(t,e,n,r){this.graph.model.startBatch("add-edge");const i=this.createEdgeFromMagnet(e,n,r);i.setEventData(t,i.prepareArrowheadDragging("target",{x:n,y:r,isNewEdge:!0,fallbackAction:"remove"})),this.setEventData(t,{edgeView:i}),i.notifyMouseDown(t,n,r)}getDefaultEdge(t,e){let n;const i=this.graph.options.connecting.createEdge;return i&&(n=r.call(i,this.graph,{sourceMagnet:e,sourceView:t,sourceCell:t.cell})),n}createEdgeFromMagnet(t,e,n){const r=this.graph,i=r.model,s=this.getDefaultEdge(this,t);return s.setSource(Object.assign(Object.assign({},s.getSource()),this.getEdgeTerminal(t,e,n,s,"source"))),s.setTarget(Object.assign(Object.assign({},s.getTarget()),{x:e,y:n})),s.addTo(i,{async:!1,ui:!0}),s.findView(r)}dragMagnet(t,e,n){const r=this.getEventData(t),i=r.edgeView;if(i)i.onMouseMove(t,e,n),this.autoScrollGraph(t.clientX,t.clientY);else{const i=this.graph,s=i.options.magnetThreshold,o=this.getEventTarget(t),a=r.targetMagnet;if("onleave"===s){if(a===o||a.contains(o))return}else if(i.view.getMouseMovedCount(t)<=s)return;this.startConnectting(t,a,e,n)}}stopMagnetDragging(t,e,n){const r=this.eventData(t),i=r.edgeView;i&&(i.onMouseUp(t,e,n),this.graph.model.stopBatch("add-edge"))}notifyUnhandledMouseDown(t,e,n){this.notify("node:unhandled:mousedown",{e:t,x:e,y:n,view:this,cell:this.cell,node:this.cell})}notifyNodeMove(t,e,n,r,i){let s=[i];const o=this.graph.getPlugin("selection");if(o&&o.isSelectionMovable()){const t=o.getSelectedCells();t.includes(i)&&(s=t.filter(t=>t.isNode()))}s.forEach(i=>{this.notify(t,{e:e,x:n,y:r,cell:i,node:i,view:i.findView(this.graph)})})}getRestrictArea(t){const e=this.graph.options.translating.restrict,n="function"===typeof e?r.call(e,this.graph,t):e;return"number"===typeof n?this.graph.transform.getGraphArea().inflate(n):!0===n?this.graph.transform.getGraphArea():n||null}startNodeDragging(t,e,n){const r=this.getDelegatedView();if(null==r||!r.can("nodeMovable"))return this.notifyUnhandledMouseDown(t,e,n);this.setEventData(t,{targetView:r,action:"move"});const i=xb["Point"].create(r.cell.getPosition());r.setEventData(t,{moving:!1,offset:i.diff(e,n),restrict:this.getRestrictArea(r)})}dragNode(t,e,n){const r=this.cell,i=this.graph,s=i.getGridSize(),o=this.getEventData(t),a=o.offset,l=o.restrict;o.moving||(o.moving=!0,this.addClass("node-moving"),this.notifyNodeMove("node:move",t,e,n,this.cell)),this.autoScrollGraph(t.clientX,t.clientY);const c=xb["GeometryUtil"].snapToGrid(e+a.x,s),h=xb["GeometryUtil"].snapToGrid(n+a.y,s);r.setPosition(c,h,{restrict:l,deep:!0,ui:!0}),i.options.embedding.enabled&&(o.embedding||(this.prepareEmbedding(t),o.embedding=!0),this.processEmbedding(t,o))}stopNodeDragging(t,e,n){const r=this.getEventData(t);r.embedding&&this.finalizeEmbedding(t,r),r.moving&&(this.removeClass("node-moving"),this.notifyNodeMove("node:moved",t,e,n,this.cell)),r.moving=!1,r.embedding=!1}autoScrollGraph(t,e){const n=this.graph.getPlugin("scroller");n&&n.autoScroll(t,e)}}(function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"===typeof r.isNodeView&&"function"===typeof r.isEdgeView&&"function"===typeof r.confirmUpdate&&"function"===typeof r.update&&"function"===typeof r.findPortElem&&"function"===typeof r.resize&&"function"===typeof r.rotate&&"function"===typeof r.translate}t.toStringTag="X6."+t.name,t.isNodeView=e})(wA||(wA={})),wA.config({isSvgElement:!0,priority:0,bootstrap:["render"],actions:{view:["render"],markup:["render"],attrs:["update"],size:["resize","ports","tools"],angle:["rotate","tools"],position:["translate","tools"],ports:["ports"],tools:["tools"]}}),wA.registry.register("node",wA,!0);var xA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class PA extends Px{constructor(){super(...arguments),this.POINT_ROUNDING=2,this.labelDestroyFn={}}get[Symbol.toStringTag](){return PA.toStringTag}getContainerClassName(){return[super.getContainerClassName(),this.prefixClassName("edge")].join(" ")}get sourceBBox(){const t=this.sourceView;if(!t){const t=this.cell.getSource();return new xb["Rectangle"](t.x,t.y)}const e=this.sourceMagnet;return t.isEdgeElement(e)?new xb["Rectangle"](this.sourceAnchor.x,this.sourceAnchor.y):t.getBBoxOfElement(e||t.container)}get targetBBox(){const t=this.targetView;if(!t){const t=this.cell.getTarget();return new xb["Rectangle"](t.x,t.y)}const e=this.targetMagnet;return t.isEdgeElement(e)?new xb["Rectangle"](this.targetAnchor.x,this.targetAnchor.y):t.getBBoxOfElement(e||t.container)}isEdgeView(){return!0}confirmUpdate(t,e={}){let n=t;if(this.hasAction(n,"source")){if(!this.updateTerminalProperties("source"))return n;n=this.removeAction(n,"source")}if(this.hasAction(n,"target")){if(!this.updateTerminalProperties("target"))return n;n=this.removeAction(n,"target")}return this.hasAction(n,"render")?(this.render(),n=this.removeAction(n,["render","update","labels","tools"]),n):(n=this.handleAction(n,"update",()=>this.update(e)),n=this.handleAction(n,"labels",()=>this.onLabelsChange(e)),n=this.handleAction(n,"tools",()=>this.renderTools()),n)}render(){return this.empty(),this.renderMarkup(),this.labelContainer=null,this.renderLabels(),this.update(),this.renderTools(),this}renderMarkup(){const t=this.cell.markup;if(t){if("string"===typeof t)throw new TypeError("Not support string markup.");return this.renderJSONMarkup(t)}throw new TypeError("Invalid edge markup.")}renderJSONMarkup(t){const e=this.parseJSONMarkup(t,this.container);this.selectors=e.selectors,this.container.append(e.fragment)}customizeLabels(){if(this.labelContainer){const t=this.cell,e=t.labels;for(let n=0,r=e.length;n<r;n+=1){const r=e[n],i=this.labelCache[n],s=this.labelSelectors[n],o=this.graph.options.onEdgeLabelRendered;if(o){const e=o({edge:t,label:r,container:i,selectors:s});e&&(this.labelDestroyFn[n]=e)}}}}destroyCustomizeLabels(){const t=this.cell.labels;if(this.labelCache&&this.labelSelectors&&this.labelDestroyFn)for(let e=0,n=t.length;e<n;e+=1){const n=this.labelDestroyFn[e],r=this.labelCache[e],i=this.labelSelectors[e];n&&r&&i&&n({edge:this.cell,label:t[e],container:r,selectors:i})}this.labelDestroyFn={}}renderLabels(){const t=this.cell,e=t.getLabels(),n=e.length;let r=this.labelContainer;if(this.labelCache={},this.labelSelectors={},n<=0)return r&&r.parentNode&&r.parentNode.removeChild(r),this;r?this.empty(r):(r=c.createSvgElement("g"),this.addClass(this.prefixClassName("edge-labels"),r),this.labelContainer=r);for(let i=0,s=e.length;i<s;i+=1){const n=e[i],s=this.normalizeLabelMarkup(this.parseLabelMarkup(n.markup));let o,a;if(s)o=s.node,a=s.selectors;else{const e=t.getDefaultLabel(),n=this.normalizeLabelMarkup(this.parseLabelMarkup(e.markup));o=n.node,a=n.selectors}o.setAttribute("data-index",""+i),r.appendChild(o);const l=this.rootSelector;if(a[l])throw new Error("Ambiguous label root selector.");a[l]=o,this.labelCache[i]=o,this.labelSelectors[i]=a}return null==r.parentNode&&this.container.appendChild(r),this.updateLabels(),this.customizeLabels(),this}onLabelsChange(t={}){this.destroyCustomizeLabels(),this.shouldRerenderLabels(t)?this.renderLabels():this.updateLabels(),this.updateLabelPositions()}shouldRerenderLabels(t={}){const e=this.cell.previous("labels");if(null==e)return!0;if("propertyPathArray"in t&&"propertyValue"in t){const n=t.propertyPathArray||[],r=n.length;if(r>1){const s=n[1];if(e[s]){if(2===r)return"object"===typeof t.propertyValue&&i.has(t.propertyValue,"markup");if("markup"!==n[2])return!1}}}return!0}parseLabelMarkup(t){return t?"string"===typeof t?this.parseLabelStringMarkup(t):this.parseJSONMarkup(t):null}parseLabelStringMarkup(t){const e=ty.createVectors(t),n=document.createDocumentFragment();for(let r=0,i=e.length;r<i;r+=1){const t=e[r].node;n.appendChild(t)}return{fragment:n,selectors:{}}}normalizeLabelMarkup(t){if(null==t)return;const e=t.fragment;if(!(e instanceof DocumentFragment)||!e.hasChildNodes())throw new Error("Invalid label markup.");let n;const r=e.childNodes;return n=r.length>1||"G"!==r[0].nodeName.toUpperCase()?ty.create("g").append(e):ty.create(r[0]),n.addClass(this.prefixClassName("edge-label")),{node:n.node,selectors:t.selectors}}updateLabels(){if(this.labelContainer){const t=this.cell,e=t.labels,n=this.can("edgeLabelMovable"),r=t.getDefaultLabel();for(let s=0,o=e.length;s<o;s+=1){const t=this.labelCache[s],o=this.labelSelectors[s];t.setAttribute("cursor",n?"move":"default");const a=e[s],l=i.merge({},r.attrs,a.attrs);this.updateAttrs(t,l,{selectors:o,rootBBox:a.size?xb["Rectangle"].fromSize(a.size):void 0})}}}renderTools(){const t=this.cell.getTools();return this.addTools(t),this}update(t={}){this.cleanCache(),this.updateConnection(t);const e=this.cell.getAttrs(),{text:n}=e,r=xA(e,["text"]);return null!=r&&this.updateAttrs(this.container,r,{selectors:this.selectors}),this.updateLabelPositions(),this.updateTools(t),this}removeRedundantLinearVertices(t={}){const e=this.cell,n=e.getVertices(),r=[this.sourceAnchor,...n,this.targetAnchor],i=r.length,s=new xb["Polyline"](r);s.simplify({threshold:.01});const o=s.points.map(t=>t.toJSON()),a=o.length;return i===a?0:(e.setVertices(o.slice(1,a-1),t),i-a)}getTerminalView(t){switch(t){case"source":return this.sourceView||null;case"target":return this.targetView||null;default:throw new Error(`Unknown terminal type '${t}'`)}}getTerminalAnchor(t){switch(t){case"source":return xb["Point"].create(this.sourceAnchor);case"target":return xb["Point"].create(this.targetAnchor);default:throw new Error(`Unknown terminal type '${t}'`)}}getTerminalConnectionPoint(t){switch(t){case"source":return xb["Point"].create(this.sourcePoint);case"target":return xb["Point"].create(this.targetPoint);default:throw new Error(`Unknown terminal type '${t}'`)}}getTerminalMagnet(t,e={}){switch(t){case"source":{if(e.raw)return this.sourceMagnet;const t=this.sourceView;return t?this.sourceMagnet||t.container:null}case"target":{if(e.raw)return this.targetMagnet;const t=this.targetView;return t?this.targetMagnet||t.container:null}default:throw new Error(`Unknown terminal type '${t}'`)}}updateConnection(t={}){const e=this.cell;if(t.translateBy&&e.isFragmentDescendantOf(t.translateBy)){const e=t.tx||0,n=t.ty||0;this.routePoints=new xb["Polyline"](this.routePoints).translate(e,n).points,this.translateConnectionPoints(e,n),this.path.translate(e,n)}else{const t=e.getVertices(),n=this.findAnchors(t);this.sourceAnchor=n.source,this.targetAnchor=n.target,this.routePoints=this.findRoutePoints(t);const r=this.findConnectionPoints(this.routePoints,this.sourceAnchor,this.targetAnchor);this.sourcePoint=r.source,this.targetPoint=r.target;const i=this.findMarkerPoints(this.routePoints,this.sourcePoint,this.targetPoint);this.path=this.findPath(this.routePoints,i.source||this.sourcePoint,i.target||this.targetPoint)}this.cleanCache()}findAnchors(t){const e=this.cell,n=e.source,r=e.target,i=t[0],s=t[t.length-1];return r.priority&&!n.priority?this.findAnchorsOrdered("target",s,"source",i):this.findAnchorsOrdered("source",i,"target",s)}findAnchorsOrdered(t,e,n,r){let i,s;const o=this.cell,a=o[t],l=o[n],c=this.getTerminalView(t),h=this.getTerminalView(n),u=this.getTerminalMagnet(t),d=this.getTerminalMagnet(n);if(c){let n;n=e?xb["Point"].create(e):h?d:xb["Point"].create(l),i=this.getAnchor(a.anchor,c,u,n,t)}else i=xb["Point"].create(a);if(h){const t=xb["Point"].create(r||i);s=this.getAnchor(l.anchor,h,d,t,n)}else s=xb["Point"].isPointLike(l)?xb["Point"].create(l):new xb["Point"];return{[t]:i,[n]:s}}getAnchor(t,e,n,i,s){const o=e.isEdgeElement(n),a=this.graph.options.connecting;let l,c="string"===typeof t?{name:t}:t;if(!c){const t=o?("source"===s?a.sourceEdgeAnchor:a.targetEdgeAnchor)||a.edgeAnchor:("source"===s?a.sourceAnchor:a.targetAnchor)||a.anchor;c="string"===typeof t?{name:t}:t}if(!c)throw new Error("Anchor should be specified.");const h=c.name;if(o){const t=wP.registry.get(h);if("function"!==typeof t)return wP.registry.onNotFound(h);l=r.call(t,this,e,n,i,c.args||{},s)}else{const t=gP.registry.get(h);if("function"!==typeof t)return gP.registry.onNotFound(h);l=r.call(t,this,e,n,i,c.args||{},s)}return l?l.round(this.POINT_ROUNDING):new xb["Point"]}findRoutePoints(t=[]){const e=this.graph.options.connecting.router||wC.presets.normal,n=this.cell.getRouter()||e;let i;if("function"===typeof n)i=r.call(n,this,t,{},this);else{const e="string"===typeof n?n:n.name,s="string"===typeof n?{}:n.args||{},o=e?wC.registry.get(e):wC.presets.normal;if("function"!==typeof o)return wC.registry.onNotFound(e);i=r.call(o,this,t,s,this)}return null==i?t.map(t=>xb["Point"].create(t)):i.map(t=>xb["Point"].create(t))}findConnectionPoints(t,e,n){const r=this.cell,i=this.graph.options.connecting,s=r.getSource(),o=r.getTarget(),a=this.sourceView,l=this.targetView,c=t[0],h=t[t.length-1];let u,d;if(a&&!a.isEdgeElement(this.sourceMagnet)){const t=this.sourceMagnet||a.container,r=c||n,o=new xb["Line"](r,e),l=s.connectionPoint||i.sourceConnectionPoint||i.connectionPoint;u=this.getConnectionPoint(l,a,t,o,"source")}else u=e;if(l&&!l.isEdgeElement(this.targetMagnet)){const t=this.targetMagnet||l.container,r=o.connectionPoint||i.targetConnectionPoint||i.connectionPoint,s=h||e,a=new xb["Line"](s,n);d=this.getConnectionPoint(r,l,t,a,"target")}else d=n;return{source:u,target:d}}getConnectionPoint(t,e,n,i,s){const o=i.end;if(null==t)return o;const a="string"===typeof t?t:t.name,l="string"===typeof t?{}:t.args,c=TP.registry.get(a);if("function"!==typeof c)return TP.registry.onNotFound(a);const h=r.call(c,this,i,e,n,l||{},s);return h?h.round(this.POINT_ROUNDING):o}findMarkerPoints(t,e,n){const r=t=>{const e=this.cell.getAttrs(),n=Object.keys(e);for(let r=0,i=n.length;r<i;r+=1){const i=e[n[r]];if(i[t+"Marker"]||i[t+"-marker"]){const t=i.strokeWidth||i["stroke-width"];if(t)return parseFloat(t);break}}return null},i=t[0],s=t[t.length-1];let o,a;const l=r("source");l&&(o=e.clone().move(i||n,-l));const c=r("target");return c&&(a=n.clone().move(s||e,-c)),this.sourceMarkerPoint=o||e.clone(),this.targetMarkerPoint=a||n.clone(),{source:o,target:a}}findPath(t,e,n){const i=this.cell.getConnector()||this.graph.options.connecting.connector;let s,o,a;if("string"===typeof i?s=i:(s=i.name,o=i.args),s){const t=VC.registry.get(s);if("function"!==typeof t)return VC.registry.onNotFound(s);a=t}else a=VC.presets.normal;const l=r.call(a,this,e,n,t,Object.assign(Object.assign({},o),{raw:!0}),this);return"string"===typeof l?xb["Path"].parse(l):l}translateConnectionPoints(t,e){this.sourcePoint.translate(t,e),this.targetPoint.translate(t,e),this.sourceAnchor.translate(t,e),this.targetAnchor.translate(t,e),this.sourceMarkerPoint.translate(t,e),this.targetMarkerPoint.translate(t,e)}updateLabelPositions(){if(null==this.labelContainer)return this;const t=this.path;if(!t)return this;const e=this.cell,n=e.getLabels();if(0===n.length)return this;const r=e.getDefaultLabel(),s=this.normalizeLabelPosition(r.position);for(let o=0,a=n.length;o<a;o+=1){const t=n[o],e=this.labelCache[o];if(!e)continue;const r=this.normalizeLabelPosition(t.position),a=i.merge({},s,r),l=this.getLabelTransformationMatrix(a);e.setAttribute("transform",c.matrixToTransformString(l))}return this}updateTerminalProperties(t){const e=this.cell,n=this.graph,r=e[t],i=r&&r.cell,s=t+"View";if(!i)return this[s]=null,this.updateTerminalMagnet(t),!0;const o=n.getCellById(i);if(!o)throw new Error(`Edge's ${t} node with id "${i}" not exists`);const a=o.findView(n);return!!a&&(this[s]=a,this.updateTerminalMagnet(t),!0)}updateTerminalMagnet(t){const e=t+"Magnet",n=this.getTerminalView(t);if(n){let r=n.getMagnetFromEdgeTerminal(this.cell[t]);r===n.container&&(r=null),this[e]=r}else this[e]=null}getLabelPositionAngle(t){const e=this.cell.getLabelAt(t);return e&&e.position&&"object"===typeof e.position&&e.position.angle||0}getLabelPositionArgs(t){const e=this.cell.getLabelAt(t);if(e&&e.position&&"object"===typeof e.position)return e.position.options}getDefaultLabelPositionArgs(){const t=this.cell.getDefaultLabel();if(t&&t.position&&"object"===typeof t.position)return t.position.options}mergeLabelPositionArgs(t,e){return null===t?null:void 0===t?null===e?null:e:i.merge({},e,t)}getConnection(){return null!=this.path?this.path.clone():null}getConnectionPathData(){if(null==this.path)return"";const t=this.cache.pathCache;return i.has(t,"data")||(t.data=this.path.serialize()),t.data||""}getConnectionSubdivisions(){if(null==this.path)return null;const t=this.cache.pathCache;return i.has(t,"segmentSubdivisions")||(t.segmentSubdivisions=this.path.getSegmentSubdivisions()),t.segmentSubdivisions}getConnectionLength(){if(null==this.path)return 0;const t=this.cache.pathCache;return i.has(t,"length")||(t.length=this.path.length({segmentSubdivisions:this.getConnectionSubdivisions()})),t.length}getPointAtLength(t){return null==this.path?null:this.path.pointAtLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getPointAtRatio(t){return null==this.path?null:(a.isPercentage(t)&&(t=parseFloat(t)/100),this.path.pointAt(t,{segmentSubdivisions:this.getConnectionSubdivisions()}))}getTangentAtLength(t){return null==this.path?null:this.path.tangentAtLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getTangentAtRatio(t){return null==this.path?null:this.path.tangentAt(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getClosestPoint(t){return null==this.path?null:this.path.closestPoint(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getClosestPointLength(t){return null==this.path?null:this.path.closestPointLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getClosestPointRatio(t){return null==this.path?null:this.path.closestPointNormalizedLength(t,{segmentSubdivisions:this.getConnectionSubdivisions()})}getLabelPosition(t,e,n,r){const i={distance:0};let s,o=0;"number"===typeof n?(o=n,s=r):s=n,null!=s&&(i.options=s);const a=s&&s.absoluteOffset,l=!(s&&s.absoluteDistance),c=s&&s.absoluteDistance&&s.reverseDistance,h=this.path,u={segmentSubdivisions:this.getConnectionSubdivisions()},d=new xb["Point"](t,e),g=h.closestPointT(d,u),f=this.getConnectionLength()||0;let p,m,y=h.lengthAtT(g,u);if(l&&(y=f>0?y/f:0),c&&(y=-1*(f-y)||1),i.distance=y,a||(p=h.tangentAtT(g)),p)m=p.pointOffset(d);else{const t=h.pointAtT(g),e=d.diff(t);m={x:e.x,y:e.y}}return i.offset=m,i.angle=o,i}normalizeLabelPosition(t){return"number"===typeof t?{distance:t}:t}getLabelTransformationMatrix(t){const e=this.normalizeLabelPosition(t),n=e.options||{},r=e.angle||0,i=e.distance,s=i>0&&i<=1;let o=0;const a={x:0,y:0},l=e.offset;l&&("number"===typeof l?o=l:(null!=l.x&&(a.x=l.x),null!=l.y&&(a.y=l.y)));const h=0!==a.x||0!==a.y||0===o,u=n.keepGradient,d=n.ensureLegibility,g=this.path,f={segmentSubdivisions:this.getConnectionSubdivisions()},p=s?i*this.getConnectionLength():i,m=g.tangentAtLength(p,f);let y,b=r;if(m){if(h)y=m.start,y.translate(a);else{const t=m.clone();t.rotate(-90,m.start),t.setLength(o),y=t.end}u&&(b=m.angle()+r,d&&(b=xb["Angle"].normalize((b+90)%180-90)))}else y=g.start,h&&y.translate(a);return c.createSVGMatrix().translate(y.x,y.y).rotate(b)}getVertexIndex(t,e){const n=this.cell,r=n.getVertices(),i=this.getClosestPointLength(new xb["Point"](t,e));let s=0;if(null!=i)for(const o=r.length;s<o;s+=1){const t=r[s],e=this.getClosestPointLength(t);if(null!=e&&i<e)break}return s}getEventArgs(t,e,n){const r=this,i=r.cell,s=i;return null==e||null==n?{e:t,view:r,edge:i,cell:s}:{e:t,x:e,y:n,view:r,edge:i,cell:s}}notifyUnhandledMouseDown(t,e,n){this.notify("edge:unhandled:mousedown",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})}notifyMouseDown(t,e,n){super.onMouseDown(t,e,n),this.notify("edge:mousedown",this.getEventArgs(t,e,n))}notifyMouseMove(t,e,n){super.onMouseMove(t,e,n),this.notify("edge:mousemove",this.getEventArgs(t,e,n))}notifyMouseUp(t,e,n){super.onMouseUp(t,e,n),this.notify("edge:mouseup",this.getEventArgs(t,e,n))}onClick(t,e,n){super.onClick(t,e,n),this.notify("edge:click",this.getEventArgs(t,e,n))}onDblClick(t,e,n){super.onDblClick(t,e,n),this.notify("edge:dblclick",this.getEventArgs(t,e,n))}onContextMenu(t,e,n){super.onContextMenu(t,e,n),this.notify("edge:contextmenu",this.getEventArgs(t,e,n))}onMouseDown(t,e,n){this.notifyMouseDown(t,e,n),this.startEdgeDragging(t,e,n)}onMouseMove(t,e,n){const r=this.getEventData(t);switch(r.action){case"drag-label":this.dragLabel(t,e,n);break;case"drag-arrowhead":this.dragArrowhead(t,e,n);break;case"drag-edge":this.dragEdge(t,e,n);break;default:break}return this.notifyMouseMove(t,e,n),r}onMouseUp(t,e,n){const r=this.getEventData(t);switch(r.action){case"drag-label":this.stopLabelDragging(t,e,n);break;case"drag-arrowhead":this.stopArrowheadDragging(t,e,n);break;case"drag-edge":this.stopEdgeDragging(t,e,n);break;default:break}return this.notifyMouseUp(t,e,n),this.checkMouseleave(t),r}onMouseOver(t){super.onMouseOver(t),this.notify("edge:mouseover",this.getEventArgs(t))}onMouseOut(t){super.onMouseOut(t),this.notify("edge:mouseout",this.getEventArgs(t))}onMouseEnter(t){super.onMouseEnter(t),this.notify("edge:mouseenter",this.getEventArgs(t))}onMouseLeave(t){super.onMouseLeave(t),this.notify("edge:mouseleave",this.getEventArgs(t))}onMouseWheel(t,e,n,r){super.onMouseWheel(t,e,n,r),this.notify("edge:mousewheel",Object.assign({delta:r},this.getEventArgs(t,e,n)))}onCustomEvent(t,e,n,r){const i=c.findParentByClass(t.target,"edge-tool",this.container);if(i){if(t.stopPropagation(),this.can("useEdgeTools")){if("edge:remove"===e)return void this.cell.remove({ui:!0});this.notify("edge:customevent",Object.assign({name:e},this.getEventArgs(t,n,r)))}this.notifyMouseDown(t,n,r)}else this.notify("edge:customevent",Object.assign({name:e},this.getEventArgs(t,n,r))),super.onCustomEvent(t,e,n,r)}onLabelMouseDown(t,e,n){this.notifyMouseDown(t,e,n),this.startLabelDragging(t,e,n);const r=this.getEventData(t).stopPropagation;r&&t.stopPropagation()}startEdgeDragging(t,e,n){this.can("edgeMovable")?this.setEventData(t,{x:e,y:n,moving:!1,action:"drag-edge"}):this.notifyUnhandledMouseDown(t,e,n)}dragEdge(t,e,n){const r=this.getEventData(t);r.moving||(r.moving=!0,this.addClass("edge-moving"),this.notify("edge:move",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})),this.cell.translate(e-r.x,n-r.y,{ui:!0}),this.setEventData(t,{x:e,y:n}),this.notify("edge:moving",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})}stopEdgeDragging(t,e,n){const r=this.getEventData(t);r.moving&&(this.removeClass("edge-moving"),this.notify("edge:moved",{e:t,x:e,y:n,view:this,cell:this.cell,edge:this.cell})),r.moving=!1}prepareArrowheadDragging(t,e){const n=this.getTerminalMagnet(t),r={action:"drag-arrowhead",x:e.x,y:e.y,isNewEdge:!0===e.isNewEdge,terminalType:t,initialMagnet:n,initialTerminal:i.clone(this.cell[t]),fallbackAction:e.fallbackAction||"revert",getValidateConnectionArgs:this.createValidateConnectionArgs(t),options:e.options};return this.beforeArrowheadDragging(r),r}createValidateConnectionArgs(t){const e=[];let n;e[4]=t,e[5]=this;let r=0,i=0;"source"===t?(r=2,n="target"):(i=2,n="source");const s=this.cell[n],o=s.cell;if(o){let t;const n=e[r]=this.graph.findViewByCell(o);n&&(t=n.getMagnetFromEdgeTerminal(s),t===n.container&&(t=void 0)),e[r+1]=t}return(t,n)=>(e[i]=t,e[i+1]=t.container===n?void 0:n,e)}beforeArrowheadDragging(t){t.zIndex=this.cell.zIndex,this.cell.toFront();const e=this.container.style;t.pointerEvents=e.pointerEvents,e.pointerEvents="none",this.graph.options.connecting.highlight&&this.highlightAvailableMagnets(t)}afterArrowheadDragging(t){null!=t.zIndex&&(this.cell.setZIndex(t.zIndex,{ui:!0}),t.zIndex=null);const e=this.container;e.style.pointerEvents=t.pointerEvents||"",this.graph.options.connecting.highlight&&this.unhighlightAvailableMagnets(t)}validateConnection(t,e,n,i,s,o,a){const l=this.graph.options.connecting,c=l.allowLoop,h=l.allowNode,u=l.allowEdge,d=l.allowPort,g=l.allowMulti,f=l.validateConnection,p=o?o.cell:null,m="target"===s?n:t,y="target"===s?i:e;let b=!0;const v=l=>{const c="source"===s?a?a.port:null:p?p.getSourcePortId():null,h="target"===s?a?a.port:null:p?p.getTargetPortId():null;return r.call(l,this.graph,{edge:p,edgeView:o,sourceView:t,targetView:n,sourcePort:c,targetPort:h,sourceMagnet:e,targetMagnet:i,sourceCell:t?t.cell:null,targetCell:n?n.cell:null,type:s})};if(null!=c&&("boolean"===typeof c?c||t!==n||(b=!1):b=v(c)),b&&null!=d&&("boolean"===typeof d?!d&&y&&(b=!1):b=v(d)),b&&null!=u&&("boolean"===typeof u?!u&&PA.isEdgeView(m)&&(b=!1):b=v(u)),b&&null!=h&&null==y&&("boolean"===typeof h?!h&&wA.isNodeView(m)&&(b=!1):b=v(h)),b&&null!=g&&o){const t=o.cell,e="source"===s?a:t.getSource(),n="target"===s?a:t.getTarget(),r=a?this.graph.getCellById(a.cell):null;if(e&&n&&e.cell&&n.cell&&r)if("function"===typeof g)b=v(g);else{const t=this.graph.model.getConnectedEdges(r,{outgoing:"source"===s,incoming:"target"===s});if(t.length)if("withPort"===g){const r=t.some(t=>{const r=t.getSource(),i=t.getTarget();return r&&i&&r.cell===e.cell&&i.cell===n.cell&&null!=r.port&&r.port===e.port&&null!=i.port&&i.port===n.port});r&&(b=!1)}else if(!g){const r=t.some(t=>{const r=t.getSource(),i=t.getTarget();return r&&i&&r.cell===e.cell&&i.cell===n.cell});r&&(b=!1)}}}return b&&null!=f&&(b=v(f)),b}allowConnectToBlank(t){const e=this.graph,n=e.options.connecting,i=n.allowBlank;if("function"!==typeof i)return!!i;const s=e.findViewByCell(t),o=t.getSourceCell(),a=t.getTargetCell(),l=e.findViewByCell(o),c=e.findViewByCell(a);return r.call(i,e,{edge:t,edgeView:s,sourceCell:o,targetCell:a,sourceView:l,targetView:c,sourcePort:t.getSourcePortId(),targetPort:t.getTargetPortId(),sourceMagnet:s.sourceMagnet,targetMagnet:s.targetMagnet})}validateEdge(t,e,n){const i=this.graph;if(!this.allowConnectToBlank(t)){const e=t.getSourceCellId(),n=t.getTargetCellId();if(!e||!n)return!1}const s=i.options.connecting.validateEdge;return!s||r.call(s,i,{edge:t,type:e,previous:n})}arrowheadDragging(t,e,n,r){r.x=e,r.y=n,r.currentTarget!==t&&(r.currentMagnet&&r.currentView&&r.currentView.unhighlight(r.currentMagnet,{type:"magnetAdsorbed"}),r.currentView=this.graph.findViewByElem(t),r.currentView?(r.currentMagnet=r.currentView.findMagnet(t),r.currentMagnet&&this.validateConnection(...r.getValidateConnectionArgs(r.currentView,r.currentMagnet),r.currentView.getEdgeTerminal(r.currentMagnet,e,n,this.cell,r.terminalType))?r.currentView.highlight(r.currentMagnet,{type:"magnetAdsorbed"}):r.currentMagnet=null):r.currentMagnet=null),r.currentTarget=t,this.cell.prop(r.terminalType,{x:e,y:n},Object.assign(Object.assign({},r.options),{ui:!0}))}arrowheadDragged(t,e,n){const r=t.currentView,i=t.currentMagnet;if(!i||!r)return;r.unhighlight(i,{type:"magnetAdsorbed"});const s=t.terminalType,o=r.getEdgeTerminal(i,e,n,this.cell,s);this.cell.setTerminal(s,o,{ui:!0})}snapArrowhead(t,e,n){const r=this.graph,{snap:i,allowEdge:s}=r.options.connecting,o="object"===typeof i&&i.radius||50,a="object"===typeof i&&i.anchor||"center",l=r.renderer.findViewsInArea({x:t-o,y:e-o,width:2*o,height:2*o},{nodeOnly:!0});if(s){const n=r.renderer.findEdgeViewsFromPoint({x:t,y:e},o).filter(t=>t!==this);l.push(...n)}const c=n.closestView||null,h=n.closestMagnet||null;let u;n.closestView=null,n.closestMagnet=null;let d=Number.MAX_SAFE_INTEGER;const g=new xb["Point"](t,e);let f;l.forEach(r=>{if("false"!==r.container.getAttribute("magnet")){if(r.isNodeView())u="center"===a?r.cell.getBBox().getCenter().distance(g):r.cell.getBBox().getNearestPointToPoint(g).distance(g);else if(r.isEdgeView()){const t=r.getClosestPoint(g);u=t?t.distance(g):Number.MAX_SAFE_INTEGER}u<o&&u<d&&(h===r.container||this.validateConnection(...n.getValidateConnectionArgs(r,null),r.getEdgeTerminal(r.container,t,e,this.cell,n.terminalType)))&&(d=u,n.closestView=r,n.closestMagnet=r.container)}r.container.querySelectorAll("[magnet]").forEach(i=>{if("false"!==i.getAttribute("magnet")){const s=r.getBBoxOfElement(i);u=g.distance(s.getCenter()),u<o&&u<d&&(h===i||this.validateConnection(...n.getValidateConnectionArgs(r,i),r.getEdgeTerminal(i,t,e,this.cell,n.terminalType)))&&(d=u,n.closestView=r,n.closestMagnet=i)}})});const p=n.terminalType,m=n.closestView,y=n.closestMagnet,b=h!==y;if(c&&b&&c.unhighlight(h,{type:"magnetAdsorbed"}),m){if(!b)return;m.highlight(y,{type:"magnetAdsorbed"}),f=m.getEdgeTerminal(y,t,e,this.cell,p)}else f={x:t,y:e};this.cell.setTerminal(p,f,{},Object.assign(Object.assign({},n.options),{ui:!0}))}snapArrowheadEnd(t){const e=t.closestView,n=t.closestMagnet;e&&n&&(e.unhighlight(n,{type:"magnetAdsorbed"}),t.currentMagnet=e.findMagnet(n)),t.closestView=null,t.closestMagnet=null}finishEmbedding(t){this.graph.options.embedding.enabled&&this.cell.updateParent()&&(t.zIndex=null)}fallbackConnection(t){switch(t.fallbackAction){case"remove":this.cell.remove({ui:!0});break;case"revert":default:this.cell.prop(t.terminalType,t.initialTerminal,{ui:!0});break}}notifyConnectionEvent(t,e){const n=t.terminalType,r=t.initialTerminal,i=this.cell[n],s=i&&!YC.equalTerminals(r,i);if(s){const s=this.graph,o=r,a=o.cell?s.getCellById(o.cell):null,l=o.port,c=a?s.findViewByCell(a):null,h=a||t.isNewEdge?null:xb["Point"].create(r).toJSON(),u=i,d=u.cell?s.getCellById(u.cell):null,g=u.port,f=d?s.findViewByCell(d):null,p=d?null:xb["Point"].create(i).toJSON();this.notify("edge:connected",{e:e,previousCell:a,previousPort:l,previousView:c,previousPoint:h,currentCell:d,currentView:f,currentPort:g,currentPoint:p,previousMagnet:t.initialMagnet,currentMagnet:t.currentMagnet,edge:this.cell,view:this,type:n,isNew:t.isNewEdge})}}highlightAvailableMagnets(t){const e=this.graph,n=e.model.getCells();t.marked={};for(let r=0,i=n.length;r<i;r+=1){const i=e.findViewByCell(n[r]);if(!i||i.cell.id===this.cell.id)continue;const s=Array.prototype.slice.call(i.container.querySelectorAll("[magnet]"));"false"!==i.container.getAttribute("magnet")&&s.push(i.container);const o=s.filter(e=>this.validateConnection(...t.getValidateConnectionArgs(i,e),i.getEdgeTerminal(e,t.x,t.y,this.cell,t.terminalType)));if(o.length>0){for(let t=0,e=o.length;t<e;t+=1)i.highlight(o[t],{type:"magnetAvailable"});i.highlight(null,{type:"nodeAvailable"}),t.marked[i.cell.id]=o}}}unhighlightAvailableMagnets(t){const e=t.marked||{};Object.keys(e).forEach(t=>{const n=this.graph.findViewByCell(t);if(n){const r=e[t];r.forEach(t=>{n.unhighlight(t,{type:"magnetAvailable"})}),n.unhighlight(null,{type:"nodeAvailable"})}}),t.marked=null}startArrowheadDragging(t,e,n){if(!this.can("arrowheadMovable"))return void this.notifyUnhandledMouseDown(t,e,n);const r=t.target,i=r.getAttribute("data-terminal"),s=this.prepareArrowheadDragging(i,{x:e,y:n});this.setEventData(t,s)}dragArrowhead(t,e,n){const r=this.getEventData(t);this.graph.options.connecting.snap?this.snapArrowhead(e,n,r):this.arrowheadDragging(this.getEventTarget(t),e,n,r)}stopArrowheadDragging(t,e,n){const r=this.graph,i=this.getEventData(t);r.options.connecting.snap?this.snapArrowheadEnd(i):this.arrowheadDragged(i,e,n);const s=this.validateEdge(this.cell,i.terminalType,i.initialTerminal);s?(this.finishEmbedding(i),this.notifyConnectionEvent(i,t)):this.fallbackConnection(i),this.afterArrowheadDragging(i)}startLabelDragging(t,e,n){if(this.can("edgeLabelMovable")){const e=t.currentTarget,n=parseInt(e.getAttribute("data-index"),10),r=this.getLabelPositionAngle(n),i=this.getLabelPositionArgs(n),s=this.getDefaultLabelPositionArgs(),o=this.mergeLabelPositionArgs(i,s);this.setEventData(t,{index:n,positionAngle:r,positionArgs:o,stopPropagation:!0,action:"drag-label"})}else this.setEventData(t,{stopPropagation:!0});this.graph.view.delegateDragEvents(t,this)}dragLabel(t,e,n){const r=this.getEventData(t),s=this.cell.getLabelAt(r.index),o=i.merge({},s,{position:this.getLabelPosition(e,n,r.positionAngle,r.positionArgs)});this.cell.setLabelAt(r.index,o)}stopLabelDragging(t,e,n){}}(function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag],r=e;return(null==n||n===t.toStringTag)&&"function"===typeof r.isNodeView&&"function"===typeof r.isEdgeView&&"function"===typeof r.confirmUpdate&&"function"===typeof r.update&&"function"===typeof r.getConnection}t.toStringTag="X6."+t.name,t.isEdgeView=e})(PA||(PA={})),PA.config({isSvgElement:!0,priority:1,bootstrap:["render","source","target"],actions:{view:["render"],markup:["render"],attrs:["update"],source:["source","update"],target:["target","update"],router:["update"],connector:["update"],labels:["labels"],defaultLabel:["labels"],tools:["tools"],vertices:["vertices","update"]}}),PA.registry.register("edge",PA,!0);var CA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class AA extends mx{get disposeContainer(){return!1}get options(){return this.graph.options}constructor(t){super(),this.graph=t;const{selectors:e,fragment:n}=fx.parseJSONMarkup(AA.markup);this.background=e.background,this.grid=e.grid,this.svg=e.svg,this.defs=e.defs,this.viewport=e.viewport,this.primer=e.primer,this.stage=e.stage,this.decorator=e.decorator,this.overlay=e.overlay,this.container=this.options.container,this.restore=AA.snapshoot(this.container),c.addClass(this.container,this.prefixClassName("graph")),c.append(this.container,n),this.delegateEvents()}delegateEvents(){const t=this.constructor;return super.delegateEvents(t.events),this}guard(t,e){return"mousedown"===t.type&&2===t.button||(!(!this.options.guard||!this.options.guard(t,e))||(t.data&&void 0!==t.data.guarded?t.data.guarded:!(e&&e.cell&&HC.isCell(e.cell))&&(this.svg!==t.target&&this.container!==t.target&&!this.svg.contains(t.target))))}findView(t){return this.graph.findViewByElem(t)}onDblClick(t){this.options.preventDefaultDblClick&&t.preventDefault();const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onDblClick(e,r.x,r.y):this.graph.trigger("blank:dblclick",{e:e,x:r.x,y:r.y})}onClick(t){if(this.getMouseMovedCount(t)<=this.options.clickThreshold){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onClick(e,r.x,r.y):this.graph.trigger("blank:click",{e:e,x:r.x,y:r.y})}}isPreventDefaultContextMenu(t){let e=this.options.preventDefaultContextMenu;return"function"===typeof e&&(e=r.call(e,this.graph,{view:t})),e}onContextMenu(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.isPreventDefaultContextMenu(n)&&t.preventDefault(),this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onContextMenu(e,r.x,r.y):this.graph.trigger("blank:contextmenu",{e:e,x:r.x,y:r.y})}delegateDragEvents(t,e){null==t.data&&(t.data={}),this.setEventData(t,{currentView:e||null,mouseMovedCount:0,startPosition:{x:t.clientX,y:t.clientY}});const n=this.constructor;this.delegateDocumentEvents(n.documentEvents,t.data),this.undelegateEvents()}getMouseMovedCount(t){const e=this.getEventData(t);return e.mouseMovedCount||0}onMouseDown(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;this.options.preventDefaultMouseDown&&t.preventDefault();const r=this.graph.snapToGrid(e.clientX,e.clientY);n?n.onMouseDown(e,r.x,r.y):(this.options.preventDefaultBlankAction&&["touchstart"].includes(e.type)&&t.preventDefault(),this.graph.trigger("blank:mousedown",{e:e,x:r.x,y:r.y})),this.delegateDragEvents(e,n)}onMouseMove(t){const e=this.getEventData(t),n=e.startPosition;if(n&&n.x===t.clientX&&n.y===t.clientY)return;null==e.mouseMovedCount&&(e.mouseMovedCount=0),e.mouseMovedCount+=1;const r=e.mouseMovedCount;if(r<=this.options.moveThreshold)return;const i=this.normalizeEvent(t),s=this.graph.snapToGrid(i.clientX,i.clientY),o=e.currentView;o?o.onMouseMove(i,s.x,s.y):this.graph.trigger("blank:mousemove",{e:i,x:s.x,y:s.y}),this.setEventData(i,e)}onMouseUp(t){this.undelegateDocumentEvents();const e=this.normalizeEvent(t),n=this.graph.snapToGrid(e.clientX,e.clientY),r=this.getEventData(t),i=r.currentView;if(i?i.onMouseUp(e,n.x,n.y):this.graph.trigger("blank:mouseup",{e:e,x:n.x,y:n.y}),!t.isPropagationStopped()){const e=new c.EventObject(t,{type:"click",data:t.data});this.onClick(e)}t.stopImmediatePropagation(),this.delegateEvents()}onMouseOver(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(!this.guard(e,n))if(n)n.onMouseOver(e);else{if(this.container===e.target)return;this.graph.trigger("blank:mouseover",{e:e})}}onMouseOut(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(!this.guard(e,n))if(n)n.onMouseOut(e);else{if(this.container===e.target)return;this.graph.trigger("blank:mouseout",{e:e})}}onMouseEnter(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.findViewByElem(e.relatedTarget);if(n){if(r===n)return;n.onMouseEnter(e)}else{if(r)return;this.graph.trigger("graph:mouseenter",{e:e})}}onMouseLeave(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=this.graph.findViewByElem(e.relatedTarget);if(n){if(r===n)return;n.onMouseLeave(e)}else{if(r)return;this.graph.trigger("graph:mouseleave",{e:e})}}onMouseWheel(t){const e=this.normalizeEvent(t),n=this.findView(e.target);if(this.guard(e,n))return;const r=e.originalEvent,i=this.graph.snapToGrid(r.clientX,r.clientY),s=Math.max(-1,Math.min(1,r.wheelDelta||-r.detail));n?n.onMouseWheel(e,i.x,i.y,s):this.graph.trigger("blank:mousewheel",{e:e,delta:s,x:i.x,y:i.y})}onCustomEvent(t){const e=t.currentTarget,n=e.getAttribute("event")||e.getAttribute("data-event");if(n){const r=this.findView(e);if(r){const e=this.normalizeEvent(t);if(this.guard(e,r))return;const i=this.graph.snapToGrid(e.clientX,e.clientY);r.onCustomEvent(e,n,i.x,i.y)}}}handleMagnetEvent(t,e){const n=t.currentTarget,i=n.getAttribute("magnet");if(i&&"false"!==i.toLowerCase()){const i=this.findView(n);if(i){const s=this.normalizeEvent(t);if(this.guard(s,i))return;const o=this.graph.snapToGrid(s.clientX,s.clientY);r.call(e,this.graph,i,s,n,o.x,o.y)}}}onMagnetMouseDown(t){this.handleMagnetEvent(t,(t,e,n,r,i)=>{t.onMagnetMouseDown(e,n,r,i)})}onMagnetDblClick(t){this.handleMagnetEvent(t,(t,e,n,r,i)=>{t.onMagnetDblClick(e,n,r,i)})}onMagnetContextMenu(t){const e=this.findView(t.target);this.isPreventDefaultContextMenu(e)&&t.preventDefault(),this.handleMagnetEvent(t,(t,e,n,r,i)=>{t.onMagnetContextMenu(e,n,r,i)})}onLabelMouseDown(t){const e=t.currentTarget,n=this.findView(e);if(n){const e=this.normalizeEvent(t);if(this.guard(e,n))return;const r=this.graph.snapToGrid(e.clientX,e.clientY);n.onLabelMouseDown(e,r.x,r.y)}}onImageDragStart(){return!1}dispose(){this.undelegateEvents(),this.undelegateDocumentEvents(),this.restore(),this.restore=()=>{}}}CA([mx.dispose()],AA.prototype,"dispose",null),function(t){const e=Ow.prefixCls+"-graph";function n(t){const e=t.cloneNode();return t.childNodes.forEach(t=>e.appendChild(t)),()=>{c.empty(t);while(t.attributes.length>0)t.removeAttribute(t.attributes[0].name);for(let n=0,r=e.attributes.length;n<r;n+=1){const r=e.attributes[n];t.setAttribute(r.name,r.value)}e.childNodes.forEach(e=>t.appendChild(e))}}t.markup=[{ns:c.ns.xhtml,tagName:"div",selector:"background",className:e+"-background"},{ns:c.ns.xhtml,tagName:"div",selector:"grid",className:e+"-grid"},{ns:c.ns.svg,tagName:"svg",selector:"svg",className:e+"-svg",attrs:{width:"100%",height:"100%","xmlns:xlink":c.ns.xlink},children:[{tagName:"defs",selector:"defs"},{tagName:"g",selector:"viewport",className:e+"-svg-viewport",children:[{tagName:"g",selector:"primer",className:e+"-svg-primer"},{tagName:"g",selector:"stage",className:e+"-svg-stage"},{tagName:"g",selector:"decorator",className:e+"-svg-decorator"},{tagName:"g",selector:"overlay",className:e+"-svg-overlay"}]}]}],t.snapshoot=n}(AA||(AA={})),function(t){const e=Ow.prefixCls;t.events={dblclick:"onDblClick",contextmenu:"onContextMenu",touchstart:"onMouseDown",mousedown:"onMouseDown",mouseover:"onMouseOver",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",mousewheel:"onMouseWheel",DOMMouseScroll:"onMouseWheel",[`mouseenter  .${e}-cell`]:"onMouseEnter",[`mouseleave  .${e}-cell`]:"onMouseLeave",[`mouseenter  .${e}-cell-tools`]:"onMouseEnter",[`mouseleave  .${e}-cell-tools`]:"onMouseLeave",[`mousedown   .${e}-cell [event]`]:"onCustomEvent",[`touchstart  .${e}-cell [event]`]:"onCustomEvent",[`mousedown   .${e}-cell [data-event]`]:"onCustomEvent",[`touchstart  .${e}-cell [data-event]`]:"onCustomEvent",[`dblclick    .${e}-cell [magnet]`]:"onMagnetDblClick",[`contextmenu .${e}-cell [magnet]`]:"onMagnetContextMenu",[`mousedown   .${e}-cell [magnet]`]:"onMagnetMouseDown",[`touchstart  .${e}-cell [magnet]`]:"onMagnetMouseDown",[`dblclick    .${e}-cell [data-magnet]`]:"onMagnetDblClick",[`contextmenu .${e}-cell [data-magnet]`]:"onMagnetContextMenu",[`mousedown   .${e}-cell [data-magnet]`]:"onMagnetMouseDown",[`touchstart  .${e}-cell [data-magnet]`]:"onMagnetMouseDown",[`dragstart   .${e}-cell image`]:"onImageDragStart",[`mousedown   .${e}-edge .${e}-edge-label`]:"onLabelMouseDown",[`touchstart  .${e}-edge .${e}-edge-label`]:"onLabelMouseDown"},t.documentEvents={mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"}}(AA||(AA={}));const OA=".x6-graph {\n  position: relative;\n  overflow: hidden;\n  outline: none;\n  touch-action: none;\n}\n.x6-graph-background,\n.x6-graph-grid,\n.x6-graph-svg {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n.x6-graph-background-stage,\n.x6-graph-grid-stage,\n.x6-graph-svg-stage {\n  user-select: none;\n}\n.x6-graph.x6-graph-pannable {\n  cursor: grab;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n}\n.x6-graph.x6-graph-panning {\n  cursor: grabbing;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n  user-select: none;\n}\n.x6-node {\n  cursor: move;\n  /* stylelint-disable-next-line */\n}\n.x6-node.x6-node-immovable {\n  cursor: default;\n}\n.x6-node * {\n  -webkit-user-drag: none;\n}\n.x6-node .scalable * {\n  vector-effect: non-scaling-stroke;\n}\n.x6-node [magnet='true'] {\n  cursor: crosshair;\n  transition: opacity 0.3s;\n}\n.x6-node [magnet='true']:hover {\n  opacity: 0.7;\n}\n.x6-node foreignObject {\n  display: block;\n  overflow: visible;\n  background-color: transparent;\n}\n.x6-node foreignObject > body {\n  position: static;\n  width: 100%;\n  height: 100%;\n  margin: 0;\n  padding: 0;\n  overflow: visible;\n  background-color: transparent;\n}\n.x6-edge .source-marker,\n.x6-edge .target-marker {\n  vector-effect: non-scaling-stroke;\n}\n.x6-edge .connection {\n  stroke-linejoin: round;\n  fill: none;\n}\n.x6-edge .connection-wrap {\n  cursor: move;\n  opacity: 0;\n  fill: none;\n  stroke: #000;\n  stroke-width: 15;\n  stroke-linecap: round;\n  stroke-linejoin: round;\n}\n.x6-edge .connection-wrap:hover {\n  opacity: 0.4;\n  stroke-opacity: 0.4;\n}\n.x6-edge .vertices {\n  cursor: move;\n  opacity: 0;\n}\n.x6-edge .vertices .vertex {\n  fill: #1abc9c;\n}\n.x6-edge .vertices .vertex :hover {\n  fill: #34495e;\n  stroke: none;\n}\n.x6-edge .vertices .vertex-remove {\n  cursor: pointer;\n  fill: #fff;\n}\n.x6-edge .vertices .vertex-remove-area {\n  cursor: pointer;\n  opacity: 0.1;\n}\n.x6-edge .vertices .vertex-group:hover .vertex-remove-area {\n  opacity: 1;\n}\n.x6-edge .arrowheads {\n  cursor: move;\n  opacity: 0;\n}\n.x6-edge .arrowheads .arrowhead {\n  fill: #1abc9c;\n}\n.x6-edge .arrowheads .arrowhead :hover {\n  fill: #f39c12;\n  stroke: none;\n}\n.x6-edge .tools {\n  cursor: pointer;\n  opacity: 0;\n}\n.x6-edge .tools .tool-options {\n  display: none;\n}\n.x6-edge .tools .tool-remove circle {\n  fill: #f00;\n}\n.x6-edge .tools .tool-remove path {\n  fill: #fff;\n}\n.x6-edge:hover .vertices,\n.x6-edge:hover .arrowheads,\n.x6-edge:hover .tools {\n  opacity: 1;\n}\n.x6-highlight-opacity {\n  opacity: 0.3;\n}\n.x6-cell-tool-editor {\n  position: relative;\n  display: inline-block;\n  min-height: 1em;\n  margin: 0;\n  padding: 0;\n  line-height: 1;\n  white-space: normal;\n  text-align: center;\n  vertical-align: top;\n  overflow-wrap: normal;\n  outline: none;\n  transform-origin: 0 0;\n  -webkit-user-drag: none;\n}\n.x6-edge-tool-editor {\n  border: 1px solid #275fc5;\n  border-radius: 2px;\n}\n";class EA extends E{get options(){return this.graph.options}get model(){return this.graph.model}get view(){return this.graph.view}constructor(t){super(),this.graph=t,this.init()}init(){}}var SA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class MA extends EA{init(){Ow.autoInsertCSS&&h.ensure("core",OA)}dispose(){h.clean("core")}}SA([MA.dispose()],MA.prototype,"dispose",null);var TA,jA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};(function(t){function e(e){const{grid:n,panning:r,mousewheel:s,embedding:o}=e,a=jA(e,["grid","panning","mousewheel","embedding"]),l=e.container;if(null==l)throw new Error("Ensure the container of the graph is specified and valid");null==a.width&&(a.width=l.clientWidth),null==a.height&&(a.height=l.clientHeight);const c=i.merge({},t.defaults,a),h={size:10,visible:!1};c.grid="number"===typeof n?{size:n,visible:!1}:"boolean"===typeof n?Object.assign(Object.assign({},h),{visible:n}):Object.assign(Object.assign({},h),n);const u=["panning","mousewheel","embedding"];return u.forEach(t=>{const n=e[t];"boolean"===typeof n?c[t].enabled=n:c[t]=Object.assign(Object.assign({},c[t]),n)}),c}t.get=e})(TA||(TA={})),function(t){t.defaults={x:0,y:0,scaling:{min:.01,max:16},grid:{size:10,visible:!1},background:!1,panning:{enabled:!1,eventTypes:["leftMouseDown"]},mousewheel:{enabled:!1,factor:1.2,zoomAtMousePosition:!0},highlighting:{default:{name:"stroke",args:{padding:3}},nodeAvailable:{name:"className",args:{className:Ow.prefix("available-node")}},magnetAvailable:{name:"className",args:{className:Ow.prefix("available-magnet")}}},connecting:{snap:!1,allowLoop:!0,allowNode:!0,allowEdge:!1,allowPort:!0,allowBlank:!0,allowMulti:!0,highlight:!1,anchor:"center",edgeAnchor:"ratio",connectionPoint:"boundary",router:"normal",connector:"normal",validateConnection({type:t,sourceView:e,targetView:n}){const r="target"===t?n:e;return null!=r},createEdge(){return new lA}},translating:{restrict:!1},embedding:{enabled:!1,findParent:"bbox",frontOnly:!0,validate:()=>!0},moveThreshold:0,clickThreshold:0,magnetThreshold:0,preventDefaultDblClick:!0,preventDefaultMouseDown:!1,preventDefaultContextMenu:!0,preventDefaultBlankAction:!0,interacting:{edgeLabelMovable:!1},async:!0,virtual:!1,guard:()=>!1}}(TA||(TA={}));var kA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o},NA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class LA extends EA{get elem(){return this.view.grid}get grid(){return this.options.grid}init(){this.startListening(),this.draw(this.grid)}startListening(){this.graph.on("scale",this.update,this),this.graph.on("translate",this.update,this)}stopListening(){this.graph.off("scale",this.update,this),this.graph.off("translate",this.update,this)}setVisible(t){this.grid.visible!==t&&(this.grid.visible=t,this.update())}getGridSize(){return this.grid.size}setGridSize(t){this.grid.size=Math.max(t,1),this.update()}show(){this.setVisible(!0),this.update()}hide(){this.setVisible(!1),this.update()}clear(){this.elem.style.backgroundImage=""}draw(t){this.clear(),this.instance=null,Object.assign(this.grid,t),this.patterns=this.resolveGrid(t),this.update()}update(t={}){const e=this.grid.size;if(e<=1||!this.grid.visible)return this.clear();const n=this.graph.matrix(),r=this.getInstance(),i=Array.isArray(t)?t:[t];this.patterns.forEach((t,s)=>{const o="pattern_"+s,a=n.a||1,l=n.d||1,{update:h,markup:u}=t,d=NA(t,["update","markup"]),g=Object.assign(Object.assign(Object.assign({},d),i[s]),{sx:a,sy:l,ox:n.e||0,oy:n.f||0,width:e*a,height:e*l});r.has(o)||r.add(o,ty.create("pattern",{id:o,patternUnits:"userSpaceOnUse"},ty.createVectors(u)).node);const f=r.get(o);"function"===typeof h&&h(f.childNodes[0],g);let p=g.ox%g.width;p<0&&(p+=g.width);let m=g.oy%g.height;m<0&&(m+=g.height),c.attr(f,{x:p,y:m,width:g.width,height:g.height})});const s=(new XMLSerializer).serializeToString(r.root),o=`url(data:image/svg+xml;base64,${btoa(s)})`;this.elem.style.backgroundImage=o}getInstance(){return this.instance||(this.instance=new Sb),this.instance}resolveGrid(t){if(!t)return[];const e=t.type;if(null==e)return[Object.assign(Object.assign({},Sb.presets.dot),t.args)];const n=Sb.registry.get(e);if(n){let e=t.args||[];return Array.isArray(e)||(e=[e]),Array.isArray(n)?n.map((t,n)=>Object.assign(Object.assign({},t),e[n])):[Object.assign(Object.assign({},n),e[0])]}return Sb.registry.onNotFound(e)}dispose(){this.stopListening(),this.clear()}}kA([EA.dispose()],LA.prototype,"dispose",null);class RA extends EA{get container(){return this.graph.view.container}get viewport(){return this.graph.view.viewport}get stage(){return this.graph.view.stage}init(){this.resize()}getMatrix(){const t=this.viewport.getAttribute("transform");return t!==this.viewportTransformString&&(this.viewportMatrix=this.viewport.getCTM(),this.viewportTransformString=t),c.createSVGMatrix(this.viewportMatrix)}setMatrix(t){const e=c.createSVGMatrix(t),n=c.matrixToTransformString(e);this.viewport.setAttribute("transform",n),this.viewportMatrix=e,this.viewportTransformString=n}resize(t,e){let n=void 0===t?this.options.width:t,r=void 0===e?this.options.height:e;this.options.width=n,this.options.height=r,"number"===typeof n&&(n=Math.round(n)),"number"===typeof r&&(r=Math.round(r)),this.container.style.width=null==n?"":n+"px",this.container.style.height=null==r?"":r+"px";const i=this.getComputedSize();return this.graph.trigger("resize",Object.assign({},i)),this}getComputedSize(){let t=this.options.width,e=this.options.height;return a.isNumber(t)||(t=this.container.clientWidth),a.isNumber(e)||(e=this.container.clientHeight),{width:t,height:e}}getScale(){return c.matrixToScale(this.getMatrix())}scale(t,e=t,n=0,r=0){if(t=this.clampScale(t),e=this.clampScale(e),n||r){const i=this.getTranslation(),s=i.tx-n*(t-1),o=i.ty-r*(e-1);s===i.tx&&o===i.ty||this.translate(s,o)}const i=this.getMatrix();return i.a=t,i.d=e,this.setMatrix(i),this.graph.trigger("scale",{sx:t,sy:e,ox:n,oy:r}),this}clampScale(t){const e=this.graph.options.scaling;return a.clamp(t,e.min||.01,e.max||16)}getZoom(){return this.getScale().sx}zoom(t,e){e=e||{};let n=t,r=t;const i=this.getScale(),s=this.getComputedSize();let o=s.width/2,a=s.height/2;if(e.absolute||(n+=i.sx,r+=i.sy),e.scaleGrid&&(n=Math.round(n/e.scaleGrid)*e.scaleGrid,r=Math.round(r/e.scaleGrid)*e.scaleGrid),e.maxScale&&(n=Math.min(e.maxScale,n),r=Math.min(e.maxScale,r)),e.minScale&&(n=Math.max(e.minScale,n),r=Math.max(e.minScale,r)),e.center&&(o=e.center.x,a=e.center.y),n=this.clampScale(n),r=this.clampScale(r),o||a){const t=this.getTranslation(),e=o-(o-t.tx)*(n/i.sx),s=a-(a-t.ty)*(r/i.sy);e===t.tx&&s===t.ty||this.translate(e,s)}return this.scale(n,r),this}getRotation(){return c.matrixToRotation(this.getMatrix())}rotate(t,e,n){if(null==e||null==n){const t=jw.getBBox(this.stage);e=t.width/2,n=t.height/2}const r=this.getMatrix().translate(e,n).rotate(t).translate(-e,-n);return this.setMatrix(r),this}getTranslation(){return c.matrixToTranslation(this.getMatrix())}translate(t,e){const n=this.getMatrix();n.e=t||0,n.f=e||0,this.setMatrix(n);const r=this.getTranslation();return this.options.x=r.tx,this.options.y=r.ty,this.graph.trigger("translate",Object.assign({},r)),this}setOrigin(t,e){return this.translate(t||0,e||0)}fitToContent(t,e,n,r){if("object"===typeof t){const i=t;t=i.gridWidth||1,e=i.gridHeight||1,n=i.padding||0,r=i}else t=t||1,e=e||1,n=n||0,null==r&&(r={});const i=a.normalizeSides(n),s=r.border||0,o=r.contentArea?xb["Rectangle"].create(r.contentArea):this.getContentArea(r);s>0&&o.inflate(s);const l=this.getScale(),c=this.getTranslation(),h=l.sx,u=l.sy;o.x*=h,o.y*=u,o.width*=h,o.height*=u;let d=Math.max(Math.ceil((o.width+o.x)/t),1)*t,g=Math.max(Math.ceil((o.height+o.y)/e),1)*e,f=0,p=0;("negative"===r.allowNewOrigin&&o.x<0||"positive"===r.allowNewOrigin&&o.x>=0||"any"===r.allowNewOrigin)&&(f=Math.ceil(-o.x/t)*t,f+=i.left,d+=f),("negative"===r.allowNewOrigin&&o.y<0||"positive"===r.allowNewOrigin&&o.y>=0||"any"===r.allowNewOrigin)&&(p=Math.ceil(-o.y/e)*e,p+=i.top,g+=p),d+=i.right,g+=i.bottom,d=Math.max(d,r.minWidth||0),g=Math.max(g,r.minHeight||0),d=Math.min(d,r.maxWidth||Number.MAX_SAFE_INTEGER),g=Math.min(g,r.maxHeight||Number.MAX_SAFE_INTEGER);const m=this.getComputedSize(),y=d!==m.width||g!==m.height,b=f!==c.tx||p!==c.ty;return b&&this.translate(f,p),y&&this.resize(d,g),new xb["Rectangle"](-f/h,-p/u,d/h,g/u)}scaleContentToFit(t={}){this.scaleContentToFitImpl(t)}scaleContentToFitImpl(t={},e=!0){let n,r;if(t.contentArea){const e=t.contentArea;n=this.graph.localToGraph(e),r=xb["Point"].create(e)}else n=this.getContentBBox(t),r=this.graph.graphToLocal(n);if(!n.width||!n.height)return;const i=a.normalizeSides(t.padding),s=t.minScale||0,o=t.maxScale||Number.MAX_SAFE_INTEGER,l=t.minScaleX||s,c=t.maxScaleX||o,h=t.minScaleY||s,u=t.maxScaleY||o;let d;if(t.viewportArea)d=t.viewportArea;else{const t=this.getComputedSize(),e=this.getTranslation();d={x:e.tx,y:e.ty,width:t.width,height:t.height}}d=xb["Rectangle"].create(d).moveAndExpand({x:i.left,y:i.top,width:-i.left-i.right,height:-i.top-i.bottom});const g=this.getScale();let f=d.width/n.width*g.sx,p=d.height/n.height*g.sy;!1!==t.preserveAspectRatio&&(f=p=Math.min(f,p));const m=t.scaleGrid;if(m&&(f=m*Math.floor(f/m),p=m*Math.floor(p/m)),f=a.clamp(f,l,c),p=a.clamp(p,h,u),this.scale(f,p),e){const t=this.options,e=d.x-r.x*f-t.x,n=d.y-r.y*p-t.y;this.translate(e,n)}}getContentArea(t={}){return!1!==t.useCellGeometry?this.model.getAllCellsBBox()||new xb["Rectangle"]:jw.getBBox(this.stage)}getContentBBox(t={}){return this.graph.localToGraph(this.getContentArea(t))}getGraphArea(){const t=xb["Rectangle"].fromSize(this.getComputedSize());return this.graph.graphToLocal(t)}zoomToRect(t,e={}){const n=xb["Rectangle"].create(t),r=this.graph;e.contentArea=n,null==e.viewportArea&&(e.viewportArea={x:r.options.x,y:r.options.y,width:this.options.width,height:this.options.height}),this.scaleContentToFitImpl(e,!1);const i=n.getCenter();return this.centerPoint(i.x,i.y),this}zoomToFit(t={}){return this.zoomToRect(this.getContentArea(t),t)}centerPoint(t,e){const n=this.getComputedSize(),r=this.getScale(),i=this.getTranslation(),s=n.width/2,o=n.height/2;t="number"===typeof t?t:s,e="number"===typeof e?e:o,t=s-t*r.sx,e=o-e*r.sy,i.tx===t&&i.ty===e||this.translate(t,e)}centerContent(t){const e=this.graph.getContentArea(t),n=e.getCenter();this.centerPoint(n.x,n.y)}centerCell(t){return this.positionCell(t,"center")}positionPoint(t,e,n){const r=this.getComputedSize();e=a.normalizePercentage(e,Math.max(0,r.width)),e<0&&(e=r.width+e),n=a.normalizePercentage(n,Math.max(0,r.height)),n<0&&(n=r.height+n);const i=this.getTranslation(),s=this.getScale(),o=e-t.x*s.sx,l=n-t.y*s.sy;i.tx===o&&i.ty===l||this.translate(o,l)}positionRect(t,e){const n=xb["Rectangle"].create(t);switch(e){case"center":return this.positionPoint(n.getCenter(),"50%","50%");case"top":return this.positionPoint(n.getTopCenter(),"50%",0);case"top-right":return this.positionPoint(n.getTopRight(),"100%",0);case"right":return this.positionPoint(n.getRightMiddle(),"100%","50%");case"bottom-right":return this.positionPoint(n.getBottomRight(),"100%","100%");case"bottom":return this.positionPoint(n.getBottomCenter(),"50%","100%");case"bottom-left":return this.positionPoint(n.getBottomLeft(),0,"100%");case"left":return this.positionPoint(n.getLeftMiddle(),0,"50%");case"top-left":return this.positionPoint(n.getTopLeft(),0,0);default:return this}}positionCell(t,e){const n=t.getBBox();return this.positionRect(n,e)}positionContent(t,e){const n=this.graph.getContentArea(e);return this.positionRect(n,t)}}var BA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class DA extends EA{get elem(){return this.view.background}init(){this.startListening(),this.options.background&&this.draw(this.options.background)}startListening(){this.graph.on("scale",this.update,this),this.graph.on("translate",this.update,this)}stopListening(){this.graph.off("scale",this.update,this),this.graph.off("translate",this.update,this)}updateBackgroundImage(t={}){let e=t.size||"auto auto",n=t.position||"center";const r=this.graph.transform.getScale(),i=this.graph.translate();if("object"===typeof n){const t=i.tx+r.sx*(n.x||0),e=i.ty+r.sy*(n.y||0);n=`${t}px ${e}px`}"object"===typeof e&&(e=xb["Rectangle"].fromSize(e).scale(r.sx,r.sy),e=`${e.width}px ${e.height}px`),this.elem.style.backgroundSize=e,this.elem.style.backgroundPosition=n}drawBackgroundImage(t,e={}){if(!(t instanceof HTMLImageElement))return void(this.elem.style.backgroundImage="");const n=this.optionsCache;if(n&&n.image!==e.image)return;let r;const s=e.opacity,o=e.size;let a=e.repeat||"no-repeat";const l=Nb.registry.get(a);if("function"===typeof l){const n=e.quality||1;t.width*=n,t.height*=n;const i=l(t,e);if(!(i instanceof HTMLCanvasElement))throw new Error("Background pattern must return an HTML Canvas instance");r=i.toDataURL("image/png"),a=e.repeat&&a!==e.repeat?e.repeat:"repeat","object"===typeof o?(o.width*=i.width/t.width,o.height*=i.height/t.height):void 0===o&&(e.size={width:i.width/n,height:i.height/n})}else r=t.src,void 0===o&&(e.size={width:t.width,height:t.height});null!=n&&"object"===typeof e.size&&e.image===n.image&&e.repeat===n.repeat&&e.quality===n.quality&&(n.size=i.clone(e.size));const c=this.elem.style;c.backgroundImage=`url(${r})`,c.backgroundRepeat=a,c.opacity=null==s||s>=1?"":""+s,this.updateBackgroundImage(e)}updateBackgroundColor(t){this.elem.style.backgroundColor=t||""}updateBackgroundOptions(t){this.graph.options.background=t}update(){this.optionsCache&&this.updateBackgroundImage(this.optionsCache)}draw(t){const e=t||{};if(this.updateBackgroundOptions(t),this.updateBackgroundColor(e.color),e.image){this.optionsCache=i.clone(e);const n=document.createElement("img");n.onload=()=>this.drawBackgroundImage(n,t),n.setAttribute("crossorigin","anonymous"),n.src=e.image}else this.drawBackgroundImage(null),this.optionsCache=null}clear(){this.draw()}dispose(){this.clear(),this.stopListening()}}BA([EA.dispose()],DA.prototype,"dispose",null);var IA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class VA extends EA{get widgetOptions(){return this.options.panning}get pannable(){return this.widgetOptions&&!0===this.widgetOptions.enabled}init(){this.onRightMouseDown=this.onRightMouseDown.bind(this),this.onSpaceKeyDown=this.onSpaceKeyDown.bind(this),this.onSpaceKeyUp=this.onSpaceKeyUp.bind(this),this.startListening(),this.updateClassName()}startListening(){this.graph.on("blank:mousedown",this.onMouseDown,this),this.graph.on("node:unhandled:mousedown",this.onMouseDown,this),this.graph.on("edge:unhandled:mousedown",this.onMouseDown,this),c.Event.on(this.graph.container,"mousedown",this.onRightMouseDown),c.Event.on(document.body,{keydown:this.onSpaceKeyDown,keyup:this.onSpaceKeyUp}),this.mousewheelHandle=new c.MouseWheelHandle(this.graph.container,this.onMouseWheel.bind(this),this.allowMouseWheel.bind(this)),this.mousewheelHandle.enable()}stopListening(){this.graph.off("blank:mousedown",this.onMouseDown,this),this.graph.off("node:unhandled:mousedown",this.onMouseDown,this),this.graph.off("edge:unhandled:mousedown",this.onMouseDown,this),c.Event.off(this.graph.container,"mousedown",this.onRightMouseDown),c.Event.off(document.body,{keydown:this.onSpaceKeyDown,keyup:this.onSpaceKeyUp}),this.mousewheelHandle&&this.mousewheelHandle.disable()}allowPanning(t,e){return t.spaceKey=this.isSpaceKeyPressed,this.pannable&&db.isMatch(t,this.widgetOptions.modifiers,e)}startPanning(t){const e=this.view.normalizeEvent(t);this.clientX=e.clientX,this.clientY=e.clientY,this.panning=!0,this.updateClassName(),c.Event.on(document.body,{"mousemove.panning touchmove.panning":this.pan.bind(this),"mouseup.panning touchend.panning":this.stopPanning.bind(this),"mouseleave.panning":this.stopPanning.bind(this)}),c.Event.on(window,"mouseup.panning",this.stopPanning.bind(this))}pan(t){const e=this.view.normalizeEvent(t),n=e.clientX-this.clientX,r=e.clientY-this.clientY;this.clientX=e.clientX,this.clientY=e.clientY,this.graph.translateBy(n,r)}stopPanning(t){this.panning=!1,this.updateClassName(),c.Event.off(document.body,".panning"),c.Event.off(window,".panning")}updateClassName(){const t=this.view.container,e=this.view.prefixClassName("graph-panning"),n=this.view.prefixClassName("graph-pannable");this.pannable?this.panning?(c.addClass(t,e),c.removeClass(t,n)):(c.removeClass(t,e),c.addClass(t,n)):(c.removeClass(t,e),c.removeClass(t,n))}onMouseDown({e:t}){if(!this.allowBlankMouseDown(t))return;const e=this.graph.getPlugin("selection"),n=e&&e.allowRubberband(t,!0);(this.allowPanning(t,!0)||this.allowPanning(t)&&!n)&&this.startPanning(t)}onRightMouseDown(t){const e=this.widgetOptions.eventTypes;(null===e||void 0===e?void 0:e.includes("rightMouseDown"))&&2===t.button&&this.allowPanning(t,!0)&&this.startPanning(t)}onMouseWheel(t,e,n){this.graph.translateBy(-e,-n)}onSpaceKeyDown(t){32===t.which&&(this.isSpaceKeyPressed=!0)}onSpaceKeyUp(t){32===t.which&&(this.isSpaceKeyPressed=!1)}allowBlankMouseDown(t){const e=this.widgetOptions.eventTypes;return(null===e||void 0===e?void 0:e.includes("leftMouseDown"))&&0===t.button||(null===e||void 0===e?void 0:e.includes("mouseWheelDown"))&&1===t.button}allowMouseWheel(t){var e;return this.pannable&&!t.ctrlKey&&(null===(e=this.widgetOptions.eventTypes)||void 0===e?void 0:e.includes("mouseWheel"))}autoPanning(t,e){const n=10,r=this.graph.getGraphArea();let i=0,s=0;t<=r.left+n&&(i=-n),e<=r.top+n&&(s=-n),t>=r.right-n&&(i=n),e>=r.bottom-n&&(s=n),0===i&&0===s||this.graph.translateBy(-i,-s)}enablePanning(){this.pannable||(this.widgetOptions.enabled=!0,this.updateClassName())}disablePanning(){this.pannable&&(this.widgetOptions.enabled=!1,this.updateClassName())}dispose(){this.stopListening()}}IA([EA.dispose()],VA.prototype,"dispose",null);var zA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class FA extends EA{constructor(){super(...arguments),this.cumulatedFactor=1}get widgetOptions(){return this.options.mousewheel}init(){this.container=this.graph.container,this.target=this.widgetOptions.global?document:this.container,this.mousewheelHandle=new c.MouseWheelHandle(this.target,this.onMouseWheel.bind(this),this.allowMouseWheel.bind(this)),this.widgetOptions.enabled&&this.enable(!0)}get disabled(){return!0!==this.widgetOptions.enabled}enable(t){(this.disabled||t)&&(this.widgetOptions.enabled=!0,this.mousewheelHandle.enable())}disable(){this.disabled||(this.widgetOptions.enabled=!1,this.mousewheelHandle.disable())}allowMouseWheel(t){const e=this.widgetOptions.guard;return(null==e||e(t))&&db.isMatch(t,this.widgetOptions.modifiers)}onMouseWheel(t){const e=this.widgetOptions.guard;if((null==e||e(t))&&db.isMatch(t,this.widgetOptions.modifiers)){const e=this.widgetOptions.factor||1.2;null==this.currentScale&&(this.startPos={x:t.clientX,y:t.clientY},this.currentScale=this.graph.transform.getScale().sx);const n=t.deltaY;n<0?this.currentScale<.15?this.cumulatedFactor=(this.currentScale+.01)/this.currentScale:(this.cumulatedFactor=Math.round(this.currentScale*e*20)/20/this.currentScale,1===this.cumulatedFactor&&(this.cumulatedFactor=1.05)):this.currentScale<=.15?this.cumulatedFactor=(this.currentScale-.01)/this.currentScale:(this.cumulatedFactor=Math.round(this.currentScale*(1/e)*20)/20/this.currentScale,1===this.cumulatedFactor&&(this.cumulatedFactor=.95)),this.cumulatedFactor=Math.max(.01,Math.min(this.currentScale*this.cumulatedFactor,160)/this.currentScale);const r=this.currentScale;let i=this.graph.transform.clampScale(r*this.cumulatedFactor);const s=this.widgetOptions.minScale||Number.MIN_SAFE_INTEGER,o=this.widgetOptions.maxScale||Number.MAX_SAFE_INTEGER;if(i=a.clamp(i,s,o),i!==r)if(this.widgetOptions.zoomAtMousePosition){const t=!!this.graph.getPlugin("scroller"),e=t?this.graph.clientToLocal(this.startPos):this.graph.clientToGraph(this.startPos);this.graph.zoom(i,{absolute:!0,center:e.clone()})}else this.graph.zoom(i,{absolute:!0});this.currentScale=null,this.cumulatedFactor=1}}dispose(){this.disable()}}zA([E.dispose()],FA.prototype,"dispose",null);var $A,GA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class qA extends EA{init(){this.resetRenderArea=r.throttle(this.resetRenderArea,200,{leading:!0}),this.resetRenderArea(),this.startListening()}startListening(){this.graph.on("translate",this.resetRenderArea,this),this.graph.on("scale",this.resetRenderArea,this),this.graph.on("resize",this.resetRenderArea,this)}stopListening(){this.graph.off("translate",this.resetRenderArea,this),this.graph.off("scale",this.resetRenderArea,this),this.graph.off("resize",this.resetRenderArea,this)}enableVirtualRender(){this.options.virtual=!0,this.resetRenderArea()}disableVirtualRender(){this.options.virtual=!1,this.graph.renderer.setRenderArea(void 0)}resetRenderArea(){if(this.options.virtual){const t=this.graph.getGraphArea();this.graph.renderer.setRenderArea(t)}}dispose(){this.stopListening()}}GA([EA.dispose()],qA.prototype,"dispose",null);class _A{constructor(){this.isFlushing=!1,this.isFlushPending=!1,this.scheduleId=0,this.queue=[],this.frameInterval=33,this.initialTime=Date.now()}queueJob(t){if(t.priority&$A.PRIOR)t.cb();else{const e=this.findInsertionIndex(t);e>=0&&this.queue.splice(e,0,t)}}queueFlush(){this.isFlushing||this.isFlushPending||(this.isFlushPending=!0,this.scheduleJob())}queueFlushSync(){this.isFlushing||this.isFlushPending||(this.isFlushPending=!0,this.flushJobsSync())}clearJobs(){this.queue.length=0,this.isFlushing=!1,this.isFlushPending=!1,this.cancelScheduleJob()}flushJobs(){this.isFlushPending=!1,this.isFlushing=!0;const t=this.getCurrentTime();let e;while(e=this.queue.shift())if(e.cb(),this.getCurrentTime()-t>=this.frameInterval)break;this.isFlushing=!1,this.queue.length&&this.queueFlush()}flushJobsSync(){let t;this.isFlushPending=!1,this.isFlushing=!0;while(t=this.queue.shift())try{t.cb()}catch(e){console.log(e)}this.isFlushing=!1}findInsertionIndex(t){let e=0,n=this.queue.length,r=n-1;const i=t.priority;while(e<=r){const t=(r-e>>1)+e;i<=this.queue[t].priority?e=t+1:(n=t,r=t-1)}return n}scheduleJob(){"requestIdleCallback"in window?(this.scheduleId&&this.cancelScheduleJob(),this.scheduleId=window.requestIdleCallback(this.flushJobs.bind(this),{timeout:100})):(this.scheduleId&&this.cancelScheduleJob(),this.scheduleId=window.setTimeout(this.flushJobs.bind(this)))}cancelScheduleJob(){"cancelIdleCallback"in window?(this.scheduleId&&window.cancelIdleCallback(this.scheduleId),this.scheduleId=0):(this.scheduleId&&clearTimeout(this.scheduleId),this.scheduleId=0)}getCurrentTime(){const t="object"===typeof performance&&"function"===typeof performance.now;return t?performance.now():Date.now()-this.initialTime}}(function(t){t[t["Update"]=2]="Update",t[t["RenderEdge"]=4]="RenderEdge",t[t["RenderNode"]=8]="RenderNode",t[t["PRIOR"]=1048576]="PRIOR"})($A||($A={}));var HA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class UA extends E{get model(){return this.graph.model}get container(){return this.graph.view.stage}constructor(t){super(),this.views={},this.willRemoveViews={},this.queue=new _A,this.graph=t,this.init()}init(){this.startListening(),this.renderViews(this.model.getCells())}startListening(){this.model.on("reseted",this.onModelReseted,this),this.model.on("cell:added",this.onCellAdded,this),this.model.on("cell:removed",this.onCellRemoved,this),this.model.on("cell:change:zIndex",this.onCellZIndexChanged,this),this.model.on("cell:change:visible",this.onCellVisibleChanged,this)}stopListening(){this.model.off("reseted",this.onModelReseted,this),this.model.off("cell:added",this.onCellAdded,this),this.model.off("cell:removed",this.onCellRemoved,this),this.model.off("cell:change:zIndex",this.onCellZIndexChanged,this),this.model.off("cell:change:visible",this.onCellVisibleChanged,this)}onModelReseted({options:t}){this.queue.clearJobs(),this.removeZPivots(),this.resetViews();const e=this.model.getCells();this.renderViews(e,Object.assign(Object.assign({},t),{queue:e.map(t=>t.id)}))}onCellAdded({cell:t,options:e}){this.renderViews([t],e)}onCellRemoved({cell:t}){this.removeViews([t])}onCellZIndexChanged({cell:t,options:e}){const n=this.views[t.id];n&&this.requestViewUpdate(n.view,UA.FLAG_INSERT,e,$A.Update,!0)}onCellVisibleChanged({cell:t,current:e}){this.toggleVisible(t,!!e)}requestViewUpdate(t,e,n={},r=$A.Update,i=!0){const s=t.cell.id,o=this.views[s];if(!o)return;o.flag=e,o.options=n;const a=t.hasAction(e,["translate","resize","rotate"]);(a||!1===n.async)&&(r=$A.PRIOR,i=!1),this.queue.queueJob({id:s,priority:r,cb:()=>{this.renderViewInArea(t,e,n);const r=n.queue;if(r){const e=r.indexOf(t.cell.id);e>=0&&r.splice(e,1),0===r.length&&this.graph.trigger("render:done")}}});const l=this.getEffectedEdges(t);l.forEach(t=>{this.requestViewUpdate(t.view,t.flag,n,r,!1)}),i&&this.flush()}setRenderArea(t){this.renderArea=t,this.flushWaitingViews()}isViewMounted(t){if(null==t)return!1;const e=this.views[t.cell.id];return!!e&&e.state===UA.ViewState.MOUNTED}renderViews(t,e={}){t.sort((t,e)=>t.isNode()&&e.isEdge()?-1:0),t.forEach(t=>{const n=t.id,r=this.views;let i=0,s=r[n];if(s)i=UA.FLAG_INSERT;else{const r=this.createCellView(t);r&&(r.graph=this.graph,i=UA.FLAG_INSERT|r.getBootstrapFlag(),s={view:r,flag:i,options:e,state:UA.ViewState.CREATED},this.views[n]=s)}s&&this.requestViewUpdate(s.view,i,e,this.getRenderPriority(s.view),!1)}),this.flush()}renderViewInArea(t,e,n={}){const r=t.cell,i=r.id,s=this.views[i];if(!s)return;let o=0;this.isUpdatable(t)||s.state===UA.ViewState.MOUNTED?(o=this.updateView(t,e,n),s.flag=o):s.state=UA.ViewState.WAITING,o&&r.isEdge()&&0===(o&t.getFlag(["source","target"]))&&this.queue.queueJob({id:i,priority:$A.RenderEdge,cb:()=>{this.updateView(t,e,n)}})}removeViews(t){t.forEach(t=>{const e=t.id,n=this.views[e];n&&(this.willRemoveViews[e]=n,delete this.views[e],this.queue.queueJob({id:e,priority:this.getRenderPriority(n.view),cb:()=>{this.removeView(n.view)}}))}),this.flush()}flush(){this.graph.options.async?this.queue.queueFlush():this.queue.queueFlushSync()}flushWaitingViews(){Object.values(this.views).forEach(t=>{if(t&&t.state===UA.ViewState.WAITING){const{view:e,flag:n,options:r}=t;this.requestViewUpdate(e,n,r,this.getRenderPriority(e),!1)}}),this.flush()}updateView(t,e,n={}){if(null==t)return 0;if(Px.isCellView(t)){if(e&UA.FLAG_REMOVE)return this.removeView(t.cell),0;e&UA.FLAG_INSERT&&(this.insertView(t),e^=UA.FLAG_INSERT)}return e?t.confirmUpdate(e,n):0}insertView(t){const e=this.views[t.cell.id];if(e){const n=t.cell.getZIndex(),r=this.addZPivot(n);this.container.insertBefore(t.container,r),t.cell.isVisible()||this.toggleVisible(t.cell,!1),e.state=UA.ViewState.MOUNTED,this.graph.trigger("view:mounted",{view:t})}}resetViews(){this.willRemoveViews=Object.assign(Object.assign({},this.views),this.willRemoveViews),Object.values(this.willRemoveViews).forEach(t=>{t&&this.removeView(t.view)}),this.views={},this.willRemoveViews={}}removeView(t){const e=t.cell,n=this.willRemoveViews[e.id];n&&t&&(n.view.remove(),delete this.willRemoveViews[e.id],this.graph.trigger("view:unmounted",{view:t}))}toggleVisible(t,e){const n=this.model.getConnectedEdges(t);for(let i=0,s=n.length;i<s;i+=1){const t=n[i];if(e){const e=t.getSourceCell(),n=t.getTargetCell();if(e&&!e.isVisible()||n&&!n.isVisible())continue;this.toggleVisible(t,!0)}else this.toggleVisible(t,!1)}const r=this.views[t.id];r&&c.css(r.view.container,{display:e?"unset":"none"})}addZPivot(t=0){null==this.zPivots&&(this.zPivots={});const e=this.zPivots;let n=e[t];if(n)return n;n=e[t]=document.createComment("z-index:"+(t+1));let r=-1/0;for(const s in e){const e=+s;e<t&&e>r&&(r=e)}const i=this.container;if(r!==-1/0){const t=e[r];i.insertBefore(n,t.nextSibling)}else i.insertBefore(n,i.firstChild);return n}removeZPivots(){this.zPivots&&Object.values(this.zPivots).forEach(t=>{t&&t.parentNode&&t.parentNode.removeChild(t)}),this.zPivots={}}createCellView(t){const e={graph:this.graph},n=this.graph.options.createCellView;if(n){const i=r.call(n,this.graph,t);if(i)return new i(t,e);if(null===i)return null}const i=t.view;if(null!=i&&"string"===typeof i){const n=Px.registry.get(i);return n?new n(t,e):Px.registry.onNotFound(i)}return t.isNode()?new wA(t,e):t.isEdge()?new PA(t,e):null}getEffectedEdges(t){const e=[],n=t.cell,r=this.model.getConnectedEdges(n);for(let i=0,s=r.length;i<s;i+=1){const t=r[i],s=this.views[t.id];if(!s)continue;const o=s.view;if(!this.isViewMounted(o))continue;const a=["update"];t.getTargetCell()===n&&a.push("target"),t.getSourceCell()===n&&a.push("source"),e.push({id:t.id,view:o,flag:o.getFlag(a)})}return e}isUpdatable(t){if(t.isNodeView())return!this.renderArea||this.renderArea.isIntersectWithRect(t.cell.getBBox());if(t.isEdgeView()){const e=t.cell,n=e.getSourceCell(),r=e.getTargetCell();if(this.renderArea&&n&&r)return this.renderArea.isIntersectWithRect(n.getBBox())||this.renderArea.isIntersectWithRect(r.getBBox())}return!0}getRenderPriority(t){return t.cell.isNode()?$A.RenderNode:$A.RenderEdge}dispose(){this.stopListening(),Object.keys(this.views).forEach(t=>{this.views[t].view.dispose()}),this.views={}}}HA([E.dispose()],UA.prototype,"dispose",null),function(t){t.FLAG_INSERT=1<<30,t.FLAG_REMOVE=1<<29,t.FLAG_RENDER=67108863}(UA||(UA={})),function(t){let e;(function(t){t[t["CREATED"]=0]="CREATED",t[t["MOUNTED"]=1]="MOUNTED",t[t["WAITING"]=2]="WAITING"})(e=t.ViewState||(t.ViewState={}))}(UA||(UA={}));var WA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class JA extends EA{constructor(){super(...arguments),this.schedule=new UA(this.graph)}requestViewUpdate(t,e,n={}){this.schedule.requestViewUpdate(t,e,n)}isViewMounted(t){return this.schedule.isViewMounted(t)}setRenderArea(t){this.schedule.setRenderArea(t)}findViewByElem(t){if(null==t)return null;const e=this.options.container,n="string"===typeof t?e.querySelector(t):t instanceof Element?t:t[0];if(n){const t=this.graph.view.findAttr("data-cell-id",n);if(t){const e=this.schedule.views;if(e[t])return e[t].view}}return null}findViewByCell(t){if(null==t)return null;const e=HC.isCell(t)?t.id:t,n=this.schedule.views;return n[e]?n[e].view:null}findViewsFromPoint(t){const e={x:t.x,y:t.y};return this.model.getCells().map(t=>this.findViewByCell(t)).filter(t=>null!=t&&jw.getBBox(t.container,{target:this.view.stage}).containsPoint(e))}findEdgeViewsFromPoint(t,e=5){return this.model.getEdges().map(t=>this.findViewByCell(t)).filter(n=>{if(null!=n){const r=n.getClosestPoint(t);if(r)return r.distance(t)<=e}return!1})}findViewsInArea(t,e={}){const n=xb["Rectangle"].create(t);return this.model.getCells().map(t=>this.findViewByCell(t)).filter(t=>{if(t){if(e.nodeOnly&&!t.isNodeView())return!1;const r=jw.getBBox(t.container,{target:this.view.stage});return 0===r.width?r.inflate(1,0):0===r.height&&r.inflate(0,1),e.strict?n.containsRect(r):n.isIntersectWithRect(r)}return!1})}dispose(){this.schedule.dispose()}}WA([EA.dispose()],JA.prototype,"dispose",null);var XA=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class YA extends EA{get cid(){return this.graph.view.cid}get svg(){return this.view.svg}get defs(){return this.view.defs}isDefined(t){return null!=this.svg.getElementById(t)}filter(t){let e=t.id;const n=t.name;if(e||(e=`filter-${n}-${this.cid}-${o.hashcode(JSON.stringify(t))}`),!this.isDefined(e)){const r=Lb.registry.get(n);if(null==r)return Lb.registry.onNotFound(n);const i=r(t.args||{}),s=Object.assign(Object.assign({x:-1,y:-1,width:3,height:3,filterUnits:"objectBoundingBox"},t.attrs),{id:e});ty.create(fx.sanitize(i),s).appendTo(this.defs)}return e}gradient(t){let e=t.id;const n=t.type;if(e||(e=`gradient-${n}-${this.cid}-${o.hashcode(JSON.stringify(t))}`),!this.isDefined(e)){const r=t.stops,i=r.map(t=>{const e=null!=t.opacity&&Number.isFinite(t.opacity)?t.opacity:1;return`<stop offset="${t.offset}" stop-color="${t.color}" stop-opacity="${e}"/>`}),s=`<${n}>${i.join("")}</${n}>`,o=Object.assign({id:e},t.attrs);ty.create(s,o).appendTo(this.defs)}return e}marker(t){const{id:e,refX:n,refY:r,markerUnits:i,markerOrient:s,tagName:a,children:l}=t,h=XA(t,["id","refX","refY","markerUnits","markerOrient","tagName","children"]);let u=e;if(u||(u=`marker-${this.cid}-${o.hashcode(JSON.stringify(t))}`),!this.isDefined(u)){"path"!==a&&delete h.d;const t=ty.create("marker",{refX:n,refY:r,id:u,overflow:"visible",orient:null!=s?s:"auto",markerUnits:i||"userSpaceOnUse"},l?l.map(t=>{var{tagName:e}=t,n=XA(t,["tagName"]);return ty.create(""+e||"path",c.kebablizeAttrs(Object.assign(Object.assign({},h),n)))}):[ty.create(a||"path",c.kebablizeAttrs(h))]);this.defs.appendChild(t.node)}return u}remove(t){const e=this.svg.getElementById(t);e&&e.parentNode&&e.parentNode.removeChild(e)}}class ZA extends EA{getClientMatrix(){return c.createSVGMatrix(this.view.stage.getScreenCTM())}getClientOffset(){const t=this.view.svg.getBoundingClientRect();return new xb["Point"](t.left,t.top)}getPageOffset(){return this.getClientOffset().translate(window.scrollX,window.scrollY)}snapToGrid(t,e){const n="number"===typeof t?this.clientToLocalPoint(t,e):this.clientToLocalPoint(t.x,t.y);return n.snapToGrid(this.graph.getGridSize())}localToGraphPoint(t,e){const n=xb["Point"].create(t,e);return jw.transformPoint(n,this.graph.matrix())}localToClientPoint(t,e){const n=xb["Point"].create(t,e);return jw.transformPoint(n,this.getClientMatrix())}localToPagePoint(t,e){const n="number"===typeof t?this.localToGraphPoint(t,e):this.localToGraphPoint(t);return n.translate(this.getPageOffset())}localToGraphRect(t,e,n,r){const i=xb["Rectangle"].create(t,e,n,r);return jw.transformRectangle(i,this.graph.matrix())}localToClientRect(t,e,n,r){const i=xb["Rectangle"].create(t,e,n,r);return jw.transformRectangle(i,this.getClientMatrix())}localToPageRect(t,e,n,r){const i="number"===typeof t?this.localToGraphRect(t,e,n,r):this.localToGraphRect(t);return i.translate(this.getPageOffset())}graphToLocalPoint(t,e){const n=xb["Point"].create(t,e);return jw.transformPoint(n,this.graph.matrix().inverse())}clientToLocalPoint(t,e){const n=xb["Point"].create(t,e);return jw.transformPoint(n,this.getClientMatrix().inverse())}clientToGraphPoint(t,e){const n=xb["Point"].create(t,e);return jw.transformPoint(n,this.graph.matrix().multiply(this.getClientMatrix().inverse()))}pageToLocalPoint(t,e){const n=xb["Point"].create(t,e),r=n.diff(this.getPageOffset());return this.graphToLocalPoint(r)}graphToLocalRect(t,e,n,r){const i=xb["Rectangle"].create(t,e,n,r);return jw.transformRectangle(i,this.graph.matrix().inverse())}clientToLocalRect(t,e,n,r){const i=xb["Rectangle"].create(t,e,n,r);return jw.transformRectangle(i,this.getClientMatrix().inverse())}clientToGraphRect(t,e,n,r){const i=xb["Rectangle"].create(t,e,n,r);return jw.transformRectangle(i,this.graph.matrix().multiply(this.getClientMatrix().inverse()))}pageToLocalRect(t,e,n,r){const i=xb["Rectangle"].create(t,e,n,r),s=this.getPageOffset();return i.x-=s.x,i.y-=s.y,this.graphToLocalRect(i)}}var KA=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class QA extends EA{constructor(){super(...arguments),this.highlights={}}init(){this.startListening()}startListening(){this.graph.on("cell:highlight",this.onCellHighlight,this),this.graph.on("cell:unhighlight",this.onCellUnhighlight,this)}stopListening(){this.graph.off("cell:highlight",this.onCellHighlight,this),this.graph.off("cell:unhighlight",this.onCellUnhighlight,this)}onCellHighlight({view:t,magnet:e,options:n={}}){const r=this.resolveHighlighter(n);if(!r)return;const i=this.getHighlighterId(e,r);if(!this.highlights[i]){const n=r.highlighter;n.highlight(t,e,Object.assign({},r.args)),this.highlights[i]={cellView:t,magnet:e,highlighter:n,args:r.args}}}onCellUnhighlight({magnet:t,options:e={}}){const n=this.resolveHighlighter(e);if(!n)return;const r=this.getHighlighterId(t,n);this.unhighlight(r)}resolveHighlighter(t){const e=this.options;let n=t.highlighter;if(null==n){const r=t.type;n=r&&e.highlighting[r]||e.highlighting.default}if(null==n)return null;const r="string"===typeof n?{name:n}:n,i=r.name,s=Rw.registry.get(i);return null==s?Rw.registry.onNotFound(i):(Rw.check(i,s),{name:i,highlighter:s,args:r.args||{}})}getHighlighterId(t,e){return c.ensureId(t),e.name+t.id+JSON.stringify(e.args)}unhighlight(t){const e=this.highlights[t];e&&(e.highlighter.unhighlight(e.cellView,e.magnet,e.args),delete this.highlights[t])}dispose(){Object.keys(this.highlights).forEach(t=>this.unhighlight(t)),this.stopListening()}}KA([QA.dispose()],QA.prototype,"dispose",null);var tO=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class eO extends EA{getScroller(){const t=this.graph.getPlugin("scroller");return t&&t.options.enabled?t:null}getContainer(){const t=this.getScroller();return t?t.container.parentElement:this.graph.container.parentElement}getSensorTarget(){const t=this.options.autoResize;if(t)return"boolean"===typeof t?this.getContainer():t}init(){const t=this.options.autoResize;if(t){const t=this.getSensorTarget();t&&hb.bind(t,()=>{const e=t.offsetWidth,n=t.offsetHeight;this.resize(e,n)})}}resize(t,e){const n=this.getScroller();n?n.resize(t,e):this.graph.transform.resize(t,e)}dispose(){hb.clear(this.graph.container)}}tO([EA.dispose()],eO.prototype,"dispose",null);var nO=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o};class rO extends Oh{get container(){return this.options.container}get[Symbol.toStringTag](){return rO.toStringTag}constructor(t){super(),this.installedPlugins=new Set,this.options=TA.get(t),this.css=new MA(this),this.view=new AA(this),this.defs=new YA(this),this.coord=new ZA(this),this.transform=new RA(this),this.highlight=new QA(this),this.grid=new LA(this),this.background=new DA(this),this.options.model?this.model=this.options.model:(this.model=new tA,this.model.graph=this),this.renderer=new JA(this),this.panning=new VA(this),this.mousewheel=new FA(this),this.virtualRender=new qA(this),this.size=new eO(this)}isNode(t){return t.isNode()}isEdge(t){return t.isEdge()}resetCells(t,e={}){return this.model.resetCells(t,e),this}clearCells(t={}){return this.model.clear(t),this}toJSON(t={}){return this.model.toJSON(t)}parseJSON(t){return this.model.parseJSON(t)}fromJSON(t,e={}){return this.model.fromJSON(t,e),this}getCellById(t){return this.model.getCell(t)}addNode(t,e={}){return this.model.addNode(t,e)}addNodes(t,e={}){return this.addCell(t.map(t=>JC.isNode(t)?t:this.createNode(t)),e)}createNode(t){return this.model.createNode(t)}removeNode(t,e={}){return this.model.removeCell(t,e)}addEdge(t,e={}){return this.model.addEdge(t,e)}addEdges(t,e={}){return this.addCell(t.map(t=>YC.isEdge(t)?t:this.createEdge(t)),e)}removeEdge(t,e={}){return this.model.removeCell(t,e)}createEdge(t){return this.model.createEdge(t)}addCell(t,e={}){return this.model.addCell(t,e),this}removeCell(t,e={}){return this.model.removeCell(t,e)}removeCells(t,e={}){return this.model.removeCells(t,e)}removeConnectedEdges(t,e={}){return this.model.removeConnectedEdges(t,e)}disconnectConnectedEdges(t,e={}){return this.model.disconnectConnectedEdges(t,e),this}hasCell(t){return this.model.has(t)}getCells(){return this.model.getCells()}getCellCount(){return this.model.total()}getNodes(){return this.model.getNodes()}getEdges(){return this.model.getEdges()}getOutgoingEdges(t){return this.model.getOutgoingEdges(t)}getIncomingEdges(t){return this.model.getIncomingEdges(t)}getConnectedEdges(t,e={}){return this.model.getConnectedEdges(t,e)}getRootNodes(){return this.model.getRoots()}getLeafNodes(){return this.model.getLeafs()}isRootNode(t){return this.model.isRoot(t)}isLeafNode(t){return this.model.isLeaf(t)}getNeighbors(t,e={}){return this.model.getNeighbors(t,e)}isNeighbor(t,e,n={}){return this.model.isNeighbor(t,e,n)}getSuccessors(t,e={}){return this.model.getSuccessors(t,e)}isSuccessor(t,e,n={}){return this.model.isSuccessor(t,e,n)}getPredecessors(t,e={}){return this.model.getPredecessors(t,e)}isPredecessor(t,e,n={}){return this.model.isPredecessor(t,e,n)}getCommonAncestor(...t){return this.model.getCommonAncestor(...t)}getSubGraph(t,e={}){return this.model.getSubGraph(t,e)}cloneSubGraph(t,e={}){return this.model.cloneSubGraph(t,e)}cloneCells(t){return this.model.cloneCells(t)}getNodesFromPoint(t,e){return this.model.getNodesFromPoint(t,e)}getNodesInArea(t,e,n,r,i){return this.model.getNodesInArea(t,e,n,r,i)}getNodesUnderNode(t,e={}){return this.model.getNodesUnderNode(t,e)}searchCell(t,e,n={}){return this.model.search(t,e,n),this}getShortestPath(t,e,n={}){return this.model.getShortestPath(t,e,n)}getAllCellsBBox(){return this.model.getAllCellsBBox()}getCellsBBox(t,e={}){return this.model.getCellsBBox(t,e)}startBatch(t,e={}){this.model.startBatch(t,e)}stopBatch(t,e={}){this.model.stopBatch(t,e)}batchUpdate(t,e,n){const r="string"===typeof t?t:"update",i="string"===typeof t?e:t,s="function"===typeof e?n:e;this.startBatch(r,s);const o=i();return this.stopBatch(r,s),o}updateCellId(t,e){return this.model.updateCellId(t,e)}findView(t){return HC.isCell(t)?this.findViewByCell(t):this.findViewByElem(t)}findViews(t){return xb["Rectangle"].isRectangleLike(t)?this.findViewsInArea(t):xb["Point"].isPointLike(t)?this.findViewsFromPoint(t):[]}findViewByCell(t){return this.renderer.findViewByCell(t)}findViewByElem(t){return this.renderer.findViewByElem(t)}findViewsFromPoint(t,e){const n="number"===typeof t?{x:t,y:e}:t;return this.renderer.findViewsFromPoint(n)}findViewsInArea(t,e,n,r,i){const s="number"===typeof t?{x:t,y:e,width:n,height:r}:t,o="number"===typeof t?i:e;return this.renderer.findViewsInArea(s,o)}matrix(t){return"undefined"===typeof t?this.transform.getMatrix():(this.transform.setMatrix(t),this)}resize(t,e){const n=this.getPlugin("scroller");return n?n.resize(t,e):this.transform.resize(t,e),this}scale(t,e=t,n=0,r=0){return"undefined"===typeof t?this.transform.getScale():(this.transform.scale(t,e,n,r),this)}zoom(t,e){const n=this.getPlugin("scroller");if(n){if("undefined"===typeof t)return n.zoom();n.zoom(t,e)}else{if("undefined"===typeof t)return this.transform.getZoom();this.transform.zoom(t,e)}return this}zoomTo(t,e={}){const n=this.getPlugin("scroller");return n?n.zoom(t,Object.assign(Object.assign({},e),{absolute:!0})):this.transform.zoom(t,Object.assign(Object.assign({},e),{absolute:!0})),this}zoomToRect(t,e={}){const n=this.getPlugin("scroller");return n?n.zoomToRect(t,e):this.transform.zoomToRect(t,e),this}zoomToFit(t={}){const e=this.getPlugin("scroller");return e?e.zoomToFit(t):this.transform.zoomToFit(t),this}rotate(t,e,n){return"undefined"===typeof t?this.transform.getRotation():(this.transform.rotate(t,e,n),this)}translate(t,e){return"undefined"===typeof t?this.transform.getTranslation():(this.transform.translate(t,e),this)}translateBy(t,e){const n=this.translate(),r=n.tx+t,i=n.ty+e;return this.translate(r,i)}getGraphArea(){return this.transform.getGraphArea()}getContentArea(t={}){return this.transform.getContentArea(t)}getContentBBox(t={}){return this.transform.getContentBBox(t)}fitToContent(t,e,n,r){return this.transform.fitToContent(t,e,n,r)}scaleContentToFit(t={}){return this.transform.scaleContentToFit(t),this}center(t){return this.centerPoint(t)}centerPoint(t,e,n){const r=this.getPlugin("scroller");return r?r.centerPoint(t,e,n):this.transform.centerPoint(t,e),this}centerContent(t){const e=this.getPlugin("scroller");return e?e.centerContent(t):this.transform.centerContent(t),this}centerCell(t,e){const n=this.getPlugin("scroller");return n?n.centerCell(t,e):this.transform.centerCell(t),this}positionPoint(t,e,n,r={}){const i=this.getPlugin("scroller");return i?i.positionPoint(t,e,n,r):this.transform.positionPoint(t,e,n),this}positionRect(t,e,n){const r=this.getPlugin("scroller");return r?r.positionRect(t,e,n):this.transform.positionRect(t,e),this}positionCell(t,e,n){const r=this.getPlugin("scroller");return r?r.positionCell(t,e,n):this.transform.positionCell(t,e),this}positionContent(t,e){const n=this.getPlugin("scroller");return n?n.positionContent(t,e):this.transform.positionContent(t,e),this}snapToGrid(t,e){return this.coord.snapToGrid(t,e)}pageToLocal(t,e,n,r){return xb["Rectangle"].isRectangleLike(t)?this.coord.pageToLocalRect(t):"number"===typeof t&&"number"===typeof e&&"number"===typeof n&&"number"===typeof r?this.coord.pageToLocalRect(t,e,n,r):this.coord.pageToLocalPoint(t,e)}localToPage(t,e,n,r){return xb["Rectangle"].isRectangleLike(t)?this.coord.localToPageRect(t):"number"===typeof t&&"number"===typeof e&&"number"===typeof n&&"number"===typeof r?this.coord.localToPageRect(t,e,n,r):this.coord.localToPagePoint(t,e)}clientToLocal(t,e,n,r){return xb["Rectangle"].isRectangleLike(t)?this.coord.clientToLocalRect(t):"number"===typeof t&&"number"===typeof e&&"number"===typeof n&&"number"===typeof r?this.coord.clientToLocalRect(t,e,n,r):this.coord.clientToLocalPoint(t,e)}localToClient(t,e,n,r){return xb["Rectangle"].isRectangleLike(t)?this.coord.localToClientRect(t):"number"===typeof t&&"number"===typeof e&&"number"===typeof n&&"number"===typeof r?this.coord.localToClientRect(t,e,n,r):this.coord.localToClientPoint(t,e)}localToGraph(t,e,n,r){return xb["Rectangle"].isRectangleLike(t)?this.coord.localToGraphRect(t):"number"===typeof t&&"number"===typeof e&&"number"===typeof n&&"number"===typeof r?this.coord.localToGraphRect(t,e,n,r):this.coord.localToGraphPoint(t,e)}graphToLocal(t,e,n,r){return xb["Rectangle"].isRectangleLike(t)?this.coord.graphToLocalRect(t):"number"===typeof t&&"number"===typeof e&&"number"===typeof n&&"number"===typeof r?this.coord.graphToLocalRect(t,e,n,r):this.coord.graphToLocalPoint(t,e)}clientToGraph(t,e,n,r){return xb["Rectangle"].isRectangleLike(t)?this.coord.clientToGraphRect(t):"number"===typeof t&&"number"===typeof e&&"number"===typeof n&&"number"===typeof r?this.coord.clientToGraphRect(t,e,n,r):this.coord.clientToGraphPoint(t,e)}defineFilter(t){return this.defs.filter(t)}defineGradient(t){return this.defs.gradient(t)}defineMarker(t){return this.defs.marker(t)}getGridSize(){return this.grid.getGridSize()}setGridSize(t){return this.grid.setGridSize(t),this}showGrid(){return this.grid.show(),this}hideGrid(){return this.grid.hide(),this}clearGrid(){return this.grid.clear(),this}drawGrid(t){return this.grid.draw(t),this}updateBackground(){return this.background.update(),this}drawBackground(t,e){const n=this.getPlugin("scroller");return null==n||null!=this.options.background&&e?this.background.draw(t):n.drawBackground(t,e),this}clearBackground(t){const e=this.getPlugin("scroller");return null==e||null!=this.options.background&&t?this.background.clear():e.clearBackground(t),this}enableVirtualRender(){return this.virtualRender.enableVirtualRender(),this}disableVirtualRender(){return this.virtualRender.disableVirtualRender(),this}isMouseWheelEnabled(){return!this.mousewheel.disabled}enableMouseWheel(){return this.mousewheel.enable(),this}disableMouseWheel(){return this.mousewheel.disable(),this}toggleMouseWheel(t){return null==t?this.isMouseWheelEnabled()?this.disableMouseWheel():this.enableMouseWheel():t?this.enableMouseWheel():this.disableMouseWheel(),this}isPannable(){const t=this.getPlugin("scroller");return t?t.isPannable():this.panning.pannable}enablePanning(){const t=this.getPlugin("scroller");return t?t.enablePanning():this.panning.enablePanning(),this}disablePanning(){const t=this.getPlugin("scroller");return t?t.disablePanning():this.panning.disablePanning(),this}togglePanning(t){return null==t?this.isPannable()?this.disablePanning():this.enablePanning():t!==this.isPannable()&&(t?this.enablePanning():this.disablePanning()),this}use(t,...e){return this.installedPlugins.has(t)||(this.installedPlugins.add(t),t.init(this,...e)),this}getPlugin(t){return Array.from(this.installedPlugins).find(e=>e.name===t)}getPlugins(t){return Array.from(this.installedPlugins).filter(e=>t.includes(e.name))}enablePlugins(t){let e=t;Array.isArray(e)||(e=[e]);const n=this.getPlugins(e);return null===n||void 0===n||n.forEach(t=>{var e;null===(e=null===t||void 0===t?void 0:t.enable)||void 0===e||e.call(t)}),this}disablePlugins(t){let e=t;Array.isArray(e)||(e=[e]);const n=this.getPlugins(e);return null===n||void 0===n||n.forEach(t=>{var e;null===(e=null===t||void 0===t?void 0:t.disable)||void 0===e||e.call(t)}),this}isPluginEnabled(t){var e;const n=this.getPlugin(t);return null===(e=null===n||void 0===n?void 0:n.isEnabled)||void 0===e?void 0:e.call(n)}disposePlugins(t){let e=t;Array.isArray(e)||(e=[e]);const n=this.getPlugins(e);return null===n||void 0===n||n.forEach(t=>{t.dispose(),this.installedPlugins.delete(t)}),this}dispose(t=!0){t&&this.model.dispose(),this.css.dispose(),this.defs.dispose(),this.grid.dispose(),this.coord.dispose(),this.transform.dispose(),this.highlight.dispose(),this.background.dispose(),this.mousewheel.dispose(),this.panning.dispose(),this.view.dispose(),this.renderer.dispose(),this.installedPlugins.forEach(t=>{t.dispose()})}}nO([Oh.dispose()],rO.prototype,"dispose",null),function(t){t.View=AA,t.Renderer=JA,t.MouseWheel=FA,t.DefsManager=YA,t.GridManager=LA,t.CoordManager=ZA,t.TransformManager=RA,t.HighlightManager=QA,t.BackgroundManager=DA,t.PanningManager=VA}(rO||(rO={})),function(t){function e(e){if(null==e)return!1;if(e instanceof t)return!0;const n=e[Symbol.toStringTag];return null==n||n===t.toStringTag}t.toStringTag="X6."+t.name,t.isGraph=e}(rO||(rO={})),function(t){function e(e,n){const r=e instanceof HTMLElement?new t({container:e}):new t(e);return null!=n&&r.fromJSON(n),r}t.render=e}(rO||(rO={})),function(t){t.registerNode=JC.registry.register,t.registerEdge=YC.registry.register,t.registerView=Px.registry.register,t.registerAttr=Aw.registry.register,t.registerGrid=Sb.registry.register,t.registerFilter=Lb.registry.register,t.registerNodeTool=Wx.registry.register,t.registerEdgeTool=Jx.registry.register,t.registerBackground=Nb.registry.register,t.registerHighlighter=Rw.registry.register,t.registerPortLayout=Jw.registry.register,t.registerPortLabelLayout=dx.registry.register,t.registerMarker=aw.registry.register,t.registerRouter=wC.registry.register,t.registerConnector=VC.registry.register,t.registerAnchor=gP.registry.register,t.registerEdgeAnchor=wP.registry.register,t.registerConnectionPoint=TP.registry.register}(rO||(rO={})),function(t){t.unregisterNode=JC.registry.unregister,t.unregisterEdge=YC.registry.unregister,t.unregisterView=Px.registry.unregister,t.unregisterAttr=Aw.registry.unregister,t.unregisterGrid=Sb.registry.unregister,t.unregisterFilter=Lb.registry.unregister,t.unregisterNodeTool=Wx.registry.unregister,t.unregisterEdgeTool=Jx.registry.unregister,t.unregisterBackground=Nb.registry.unregister,t.unregisterHighlighter=Rw.registry.unregister,t.unregisterPortLayout=Jw.registry.unregister,t.unregisterPortLabelLayout=dx.registry.unregister,t.unregisterMarker=aw.registry.unregister,t.unregisterRouter=wC.registry.unregister,t.unregisterConnector=VC.registry.unregister,t.unregisterAnchor=gP.registry.unregister,t.unregisterEdgeAnchor=wP.registry.unregister,t.unregisterConnectionPoint=TP.registry.unregister}(rO||(rO={}));var iO=function(t,e,n,r){var i,s=arguments.length,o=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(o=(s<3?i(o):s>3?i(e,n,o):i(e,n))||o);return s>3&&o&&Object.defineProperty(e,n,o),o},sO=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n};class oO extends JC{}(function(t){class e extends wA{init(){super.init(),this.cell.on("change:*",this.onCellChangeAny,this)}onCellChangeAny({key:e}){const n=t.shapeMaps[this.cell.shape];if(n){const{effect:t}=n;t&&!t.includes(e)||this.renderHTMLComponent()}}confirmUpdate(t){const n=super.confirmUpdate(t);return this.handleAction(n,e.action,()=>this.renderHTMLComponent())}renderHTMLComponent(){const e=this.selectors&&this.selectors.foContent;if(e){c.empty(e);const n=t.shapeMaps[this.cell.shape];if(!n)return;let{html:r}=n;"function"===typeof r&&(r=r(this.cell)),r&&("string"===typeof r?e.innerHTML=r:c.append(e,r))}}dispose(){this.cell.off("change:*",this.onCellChangeAny,this)}}iO([e.dispose()],e.prototype,"dispose",null),t.View=e,function(t){t.action="html",t.config({bootstrap:[t.action],actions:{html:t.action}}),wA.registry.register("html-view",t,!0)}(e=t.View||(t.View={}))})(oO||(oO={})),function(t){t.config({view:"html-view",markup:[{tagName:"rect",selector:"body"},Object.assign({},fx.getForeignObjectMarkup()),{tagName:"text",selector:"label"}],attrs:{body:{fill:"none",stroke:"none",refWidth:"100%",refHeight:"100%"},fo:{refWidth:"100%",refHeight:"100%"}}}),JC.registry.register("html",t,!0)}(oO||(oO={})),function(t){function e(e){const{shape:n,html:r,effect:i,inherit:s}=e,o=sO(e,["shape","html","effect","inherit"]);if(!n)throw new Error("should specify shape in config");t.shapeMaps[n]={html:r,effect:i},rO.registerNode(n,Object.assign({inherit:s||"html"},o),!0)}t.shapeMaps={},t.register=e}(oO||(oO={}))},"58e0":function(t,e,n){"use strict";(function(t){var r=n("26ee"),i=n("2f74"),s="object"==typeof exports&&exports&&!exports.nodeType&&exports,o=s&&"object"==typeof t&&t&&!t.nodeType&&t,a=o&&o.exports===s,l=a?r["a"].Buffer:void 0,c=l?l.isBuffer:void 0,h=c||i["a"];e["a"]=h}).call(this,n("dd40")(t))},"5ea3":function(t,e,n){"use strict";(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e["a"]=n}).call(this,n("c8ba"))},6362:function(t,e){"object"===typeof window&&window.NodeList&&!NodeList.prototype.forEach&&(NodeList.prototype.forEach=Array.prototype.forEach),"undefined"!==typeof window&&function(t){t.forEach(t=>{Object.prototype.hasOwnProperty.call(t,"append")||Object.defineProperty(t,"append",{configurable:!0,enumerable:!0,writable:!0,value(...t){const e=document.createDocumentFragment();t.forEach(t=>{const n=t instanceof Node;e.appendChild(n?t:document.createTextNode(String(t)))}),this.appendChild(e)}})})}([Element.prototype,Document.prototype,DocumentFragment.prototype])},7514:function(t,e,n){"use strict";var r,i;n.r(e),n.d(e,"Angle",(function(){return r})),n.d(e,"Point",(function(){return o})),n.d(e,"Line",(function(){return l})),n.d(e,"Ellipse",(function(){return c})),n.d(e,"Rectangle",(function(){return a})),n.d(e,"Path",(function(){return k})),n.d(e,"Segment",(function(){return v})),n.d(e,"normalizePathData",(function(){return j})),n.d(e,"Curve",(function(){return b})),n.d(e,"Polyline",(function(){return y})),n.d(e,"GeometryUtil",(function(){return i})),function(t){function e(t){return 180*t/Math.PI%360}function n(t){return t%360+(t<0?360:0)}t.toDeg=e,t.toRad=function(t,e=!1){const n=e?t:t%360;return n*Math.PI/180},t.normalize=n}(r||(r={})),function(t){function e(t,e=0){return Number.isInteger(t)?t:+t.toFixed(e)}function n(t,e){let n,r;if(null==e?(r=null==t?1:t,n=0):(r=e,n=null==t?0:t),r<n){const t=n;n=r,r=t}return Math.floor(Math.random()*(r-n+1)+n)}function r(t,e,n){return Number.isNaN(t)?NaN:Number.isNaN(e)||Number.isNaN(n)?0:e<n?t<e?e:t>n?n:t:t<n?n:t>e?e:t}function i(t,e){return e*Math.round(t/e)}function s(t,e){return null!=e&&null!=t&&e.x>=t.x&&e.x<=t.x+t.width&&e.y>=t.y&&e.y<=t.y+t.height}function o(t,e){const n=t.x-e.x,r=t.y-e.y;return n*n+r*r}t.round=e,t.random=n,t.clamp=r,t.snapToGrid=i,t.containsPoint=s,t.squaredLength=o}(i||(i={}));class s{valueOf(){return this.toJSON()}toString(){return JSON.stringify(this.toJSON())}}class o extends s{constructor(t,e){super(),this.x=null==t?0:t,this.y=null==e?0:e}round(t=0){return this.x=i.round(this.x,t),this.y=i.round(this.y,t),this}add(t,e){const n=o.create(t,e);return this.x+=n.x,this.y+=n.y,this}update(t,e){const n=o.create(t,e);return this.x=n.x,this.y=n.y,this}translate(t,e){const n=o.create(t,e);return this.x+=n.x,this.y+=n.y,this}rotate(t,e){const n=o.rotate(this,t,e);return this.x=n.x,this.y=n.y,this}scale(t,e,n=new o){const r=o.create(n);return this.x=r.x+t*(this.x-r.x),this.y=r.y+e*(this.y-r.y),this}closest(t){if(1===t.length)return o.create(t[0]);let e=null,n=1/0;return t.forEach(t=>{const r=this.squaredDistance(t);r<n&&(e=t,n=r)}),e?o.create(e):null}distance(t){return Math.sqrt(this.squaredDistance(t))}squaredDistance(t){const e=o.create(t),n=this.x-e.x,r=this.y-e.y;return n*n+r*r}manhattanDistance(t){const e=o.create(t);return Math.abs(e.x-this.x)+Math.abs(e.y-this.y)}magnitude(){return Math.sqrt(this.x*this.x+this.y*this.y)||.01}theta(t=new o){const e=o.create(t),n=-(e.y-this.y),r=e.x-this.x;let i=Math.atan2(n,r);return i<0&&(i=2*Math.PI+i),180*i/Math.PI}angleBetween(t,e){if(this.equals(t)||this.equals(e))return NaN;let n=this.theta(e)-this.theta(t);return n<0&&(n+=360),n}vectorAngle(t){const e=new o(0,0);return e.angleBetween(this,t)}toPolar(t){return this.update(o.toPolar(this,t)),this}changeInAngle(t,e,n=new o){return this.clone().translate(-t,-e).theta(n)-this.theta(n)}adhereToRect(t){return i.containsPoint(t,this)||(this.x=Math.min(Math.max(this.x,t.x),t.x+t.width),this.y=Math.min(Math.max(this.y,t.y),t.y+t.height)),this}bearing(t){const e=o.create(t),n=r.toRad(this.y),i=r.toRad(e.y),s=this.x,a=e.x,l=r.toRad(a-s),c=Math.sin(l)*Math.cos(i),h=Math.cos(n)*Math.sin(i)-Math.sin(n)*Math.cos(i)*Math.cos(l),u=r.toDeg(Math.atan2(c,h)),d=["NE","E","SE","S","SW","W","NW","N"];let g=u-22.5;return g<0&&(g+=360),g=parseInt(g/45,10),d[g]}cross(t,e){if(null!=t&&null!=e){const n=o.create(t),r=o.create(e);return(r.x-this.x)*(n.y-this.y)-(r.y-this.y)*(n.x-this.x)}return NaN}dot(t){const e=o.create(t);return this.x*e.x+this.y*e.y}diff(t,e){if("number"===typeof t)return new o(this.x-t,this.y-e);const n=o.create(t);return new o(this.x-n.x,this.y-n.y)}lerp(t,e){const n=o.create(t);return new o((1-e)*this.x+e*n.x,(1-e)*this.y+e*n.y)}normalize(t=1){const e=t/this.magnitude();return this.scale(e,e)}move(t,e){const n=o.create(t),i=r.toRad(n.theta(this));return this.translate(Math.cos(i)*e,-Math.sin(i)*e)}reflection(t){return o.create(t).move(this,this.distance(t))}snapToGrid(t,e){return this.x=i.snapToGrid(this.x,t),this.y=i.snapToGrid(this.y,null==e?t:e),this}equals(t){const e=o.create(t);return null!=e&&e.x===this.x&&e.y===this.y}clone(){return o.clone(this)}toJSON(){return o.toJSON(this)}serialize(){return`${this.x} ${this.y}`}}(function(t){function e(e){return null!=e&&e instanceof t}t.isPoint=e})(o||(o={})),function(t){function e(t){return null!=t&&"object"===typeof t&&"number"===typeof t.x&&"number"===typeof t.y}function n(t){return null!=t&&Array.isArray(t)&&2===t.length&&"number"===typeof t[0]&&"number"===typeof t[1]}t.isPointLike=e,t.isPointData=n}(o||(o={})),function(t){function e(e,r){return null==e||"number"===typeof e?new t(e,r):n(e)}function n(e){return t.isPoint(e)?new t(e.x,e.y):Array.isArray(e)?new t(e[0],e[1]):new t(e.x,e.y)}function s(e){return t.isPoint(e)?{x:e.x,y:e.y}:Array.isArray(e)?{x:e[0],y:e[1]}:{x:e.x,y:e.y}}function o(e,i,s=new t){let o=Math.abs(e*Math.cos(i)),a=Math.abs(e*Math.sin(i));const l=n(s),c=r.normalize(r.toDeg(i));return c<90?a=-a:c<180?(o=-o,a=-a):c<270&&(o=-o),new t(l.x+o,l.y+a)}function a(e,i=new t){const s=n(e),o=n(i),a=s.x-o.x,l=s.y-o.y;return new t(Math.sqrt(a*a+l*l),r.toRad(o.theta(s)))}function l(t,e){return t===e||null!=t&&null!=e&&(t.x===e.x&&t.y===e.y)}function c(t,e){if(null==t&&null!=e||null!=t&&null==e||null!=t&&null!=e&&t.length!==e.length)return!1;if(null!=t&&null!=e)for(let n=0,r=t.length;n<r;n+=1)if(!l(t[n],e[n]))return!1;return!0}function h(e,n,r,s){return new t(i.random(e,n),i.random(r,s))}function u(t,e,n){const i=r.toRad(r.normalize(-e)),s=Math.sin(i),o=Math.cos(i);return d(t,o,s,n)}function d(e,r,i,s=new t){const o=n(e),a=n(s),l=o.x-a.x,c=o.y-a.y,h=l*r-c*i,u=c*r+l*i;return new t(h+a.x,u+a.y)}t.create=e,t.clone=n,t.toJSON=s,t.fromPolar=o,t.toPolar=a,t.equals=l,t.equalPoints=c,t.random=h,t.rotate=u,t.rotateEx=d}(o||(o={}));class a extends s{get left(){return this.x}get top(){return this.y}get right(){return this.x+this.width}get bottom(){return this.y+this.height}get origin(){return new o(this.x,this.y)}get topLeft(){return new o(this.x,this.y)}get topCenter(){return new o(this.x+this.width/2,this.y)}get topRight(){return new o(this.x+this.width,this.y)}get center(){return new o(this.x+this.width/2,this.y+this.height/2)}get bottomLeft(){return new o(this.x,this.y+this.height)}get bottomCenter(){return new o(this.x+this.width/2,this.y+this.height)}get bottomRight(){return new o(this.x+this.width,this.y+this.height)}get corner(){return new o(this.x+this.width,this.y+this.height)}get rightMiddle(){return new o(this.x+this.width,this.y+this.height/2)}get leftMiddle(){return new o(this.x,this.y+this.height/2)}get topLine(){return new l(this.topLeft,this.topRight)}get rightLine(){return new l(this.topRight,this.bottomRight)}get bottomLine(){return new l(this.bottomLeft,this.bottomRight)}get leftLine(){return new l(this.topLeft,this.bottomLeft)}constructor(t,e,n,r){super(),this.x=null==t?0:t,this.y=null==e?0:e,this.width=null==n?0:n,this.height=null==r?0:r}getOrigin(){return this.origin}getTopLeft(){return this.topLeft}getTopCenter(){return this.topCenter}getTopRight(){return this.topRight}getCenter(){return this.center}getCenterX(){return this.x+this.width/2}getCenterY(){return this.y+this.height/2}getBottomLeft(){return this.bottomLeft}getBottomCenter(){return this.bottomCenter}getBottomRight(){return this.bottomRight}getCorner(){return this.corner}getRightMiddle(){return this.rightMiddle}getLeftMiddle(){return this.leftMiddle}getTopLine(){return this.topLine}getRightLine(){return this.rightLine}getBottomLine(){return this.bottomLine}getLeftLine(){return this.leftLine}bbox(t){if(!t)return this.clone();const e=r.toRad(t),n=Math.abs(Math.sin(e)),i=Math.abs(Math.cos(e)),s=this.width*i+this.height*n,o=this.width*n+this.height*i;return new a(this.x+(this.width-s)/2,this.y+(this.height-o)/2,s,o)}round(t=0){return this.x=i.round(this.x,t),this.y=i.round(this.y,t),this.width=i.round(this.width,t),this.height=i.round(this.height,t),this}add(t,e,n,r){const i=a.create(t,e,n,r),s=Math.min(this.x,i.x),o=Math.min(this.y,i.y),l=Math.max(this.x+this.width,i.x+i.width),c=Math.max(this.y+this.height,i.y+i.height);return this.x=s,this.y=o,this.width=l-s,this.height=c-o,this}update(t,e,n,r){const i=a.create(t,e,n,r);return this.x=i.x,this.y=i.y,this.width=i.width,this.height=i.height,this}inflate(t,e){const n=t,r=null!=e?e:t;return this.x-=n,this.y-=r,this.width+=2*n,this.height+=2*r,this}snapToGrid(t,e){const n=this.origin.snapToGrid(t,e),r=this.corner.snapToGrid(t,e);return this.x=n.x,this.y=n.y,this.width=r.x-n.x,this.height=r.y-n.y,this}translate(t,e){const n=o.create(t,e);return this.x+=n.x,this.y+=n.y,this}scale(t,e,n=new o){const r=this.origin.scale(t,e,n);return this.x=r.x,this.y=r.y,this.width*=t,this.height*=e,this}rotate(t,e=this.getCenter()){if(0!==t){const n=r.toRad(t),i=Math.cos(n),s=Math.sin(n);let l=this.getOrigin(),c=this.getTopRight(),h=this.getBottomRight(),u=this.getBottomLeft();l=o.rotateEx(l,i,s,e),c=o.rotateEx(c,i,s,e),h=o.rotateEx(h,i,s,e),u=o.rotateEx(u,i,s,e);const d=new a(l.x,l.y,0,0);d.add(c.x,c.y,0,0),d.add(h.x,h.y,0,0),d.add(u.x,u.y,0,0),this.update(d)}return this}rotate90(){const t=(this.width-this.height)/2;this.x+=t,this.y-=t;const e=this.width;return this.width=this.height,this.height=e,this}moveAndExpand(t){const e=a.clone(t);return this.x+=e.x||0,this.y+=e.y||0,this.width+=e.width||0,this.height+=e.height||0,this}getMaxScaleToFit(t,e=this.center){const n=a.clone(t),r=e.x,i=e.y;let s=1/0,o=1/0,l=1/0,c=1/0,h=1/0,u=1/0,d=1/0,g=1/0;const f=n.topLeft;f.x<r&&(s=(this.x-r)/(f.x-r)),f.y<i&&(h=(this.y-i)/(f.y-i));const p=n.bottomRight;p.x>r&&(o=(this.x+this.width-r)/(p.x-r)),p.y>i&&(u=(this.y+this.height-i)/(p.y-i));const m=n.topRight;m.x>r&&(l=(this.x+this.width-r)/(m.x-r)),m.y<i&&(d=(this.y-i)/(m.y-i));const y=n.bottomLeft;return y.x<r&&(c=(this.x-r)/(y.x-r)),y.y>i&&(g=(this.y+this.height-i)/(y.y-i)),{sx:Math.min(s,o,l,c),sy:Math.min(h,u,d,g)}}getMaxUniformScaleToFit(t,e=this.center){const n=this.getMaxScaleToFit(t,e);return Math.min(n.sx,n.sy)}containsPoint(t,e){return i.containsPoint(this,o.create(t,e))}containsRect(t,e,n,r){const i=a.create(t,e,n,r),s=this.x,o=this.y,l=this.width,c=this.height,h=i.x,u=i.y,d=i.width,g=i.height;return 0!==l&&0!==c&&0!==d&&0!==g&&(h>=s&&u>=o&&h+d<=s+l&&u+g<=o+c)}intersectsWithLine(t){const e=[this.topLine,this.rightLine,this.bottomLine,this.leftLine],n=[],r=[];return e.forEach(e=>{const i=t.intersectsWithLine(e);null!==i&&r.indexOf(i.toString())<0&&(n.push(i),r.push(i.toString()))}),n.length>0?n:null}intersectsWithLineFromCenterToPoint(t,e){const n=o.clone(t),r=this.center;let i=null;null!=e&&0!==e&&n.rotate(e,r);const s=[this.topLine,this.rightLine,this.bottomLine,this.leftLine],a=new l(r,n);for(let o=s.length-1;o>=0;o-=1){const t=s[o].intersectsWithLine(a);if(null!==t){i=t;break}}return i&&null!=e&&0!==e&&i.rotate(-e,r),i}intersectsWithRect(t,e,n,r){const i=a.create(t,e,n,r);if(!this.isIntersectWithRect(i))return null;const s=this.origin,o=this.corner,l=i.origin,c=i.corner,h=Math.max(s.x,l.x),u=Math.max(s.y,l.y);return new a(h,u,Math.min(o.x,c.x)-h,Math.min(o.y,c.y)-u)}isIntersectWithRect(t,e,n,r){const i=a.create(t,e,n,r),s=this.origin,o=this.corner,l=i.origin,c=i.corner;return!(c.x<=s.x||c.y<=s.y||l.x>=o.x||l.y>=o.y)}normalize(){let t=this.x,e=this.y,n=this.width,r=this.height;return this.width<0&&(t=this.x+this.width,n=-this.width),this.height<0&&(e=this.y+this.height,r=-this.height),this.x=t,this.y=e,this.width=n,this.height=r,this}union(t){const e=a.clone(t),n=this.origin,r=this.corner,i=e.origin,s=e.corner,o=Math.min(n.x,i.x),l=Math.min(n.y,i.y),c=Math.max(r.x,s.x),h=Math.max(r.y,s.y);return new a(o,l,c-o,h-l)}getNearestSideToPoint(t){const e=o.clone(t),n=e.x-this.x,r=this.x+this.width-e.x,i=e.y-this.y,s=this.y+this.height-e.y;let a=n,l="left";return r<a&&(a=r,l="right"),i<a&&(a=i,l="top"),s<a&&(l="bottom"),l}getNearestPointToPoint(t){const e=o.clone(t);if(this.containsPoint(e)){const t=this.getNearestSideToPoint(e);if("left"===t)return new o(this.x,e.y);if("top"===t)return new o(e.x,this.y);if("right"===t)return new o(this.x+this.width,e.y);if("bottom"===t)return new o(e.x,this.y+this.height)}return e.adhereToRect(this)}equals(t){return null!=t&&t.x===this.x&&t.y===this.y&&t.width===this.width&&t.height===this.height}clone(){return new a(this.x,this.y,this.width,this.height)}toJSON(){return{x:this.x,y:this.y,width:this.width,height:this.height}}serialize(){return`${this.x} ${this.y} ${this.width} ${this.height}`}}(function(t){function e(e){return null!=e&&e instanceof t}t.isRectangle=e})(a||(a={})),function(t){function e(t){return null!=t&&"object"===typeof t&&"number"===typeof t.x&&"number"===typeof t.y&&"number"===typeof t.width&&"number"===typeof t.height}t.isRectangleLike=e}(a||(a={})),function(t){function e(e,r,i,s){return null==e||"number"===typeof e?new t(e,r,i,s):n(e)}function n(e){return t.isRectangle(e)?e.clone():Array.isArray(e)?new t(e[0],e[1],e[2],e[3]):new t(e.x,e.y,e.width,e.height)}function r(e){return new t(e.x-e.a,e.y-e.b,2*e.a,2*e.b)}function i(e){return new t(0,0,e.width,e.height)}function s(e,n){return new t(e.x,e.y,n.width,n.height)}t.create=e,t.clone=n,t.fromEllipse=r,t.fromSize=i,t.fromPositionAndSize=s}(a||(a={}));class l extends s{get center(){return new o((this.start.x+this.end.x)/2,(this.start.y+this.end.y)/2)}constructor(t,e,n,r){super(),"number"===typeof t&&"number"===typeof e?(this.start=new o(t,e),this.end=new o(n,r)):(this.start=o.create(t),this.end=o.create(e))}getCenter(){return this.center}round(t=0){return this.start.round(t),this.end.round(t),this}translate(t,e){return"number"===typeof t?(this.start.translate(t,e),this.end.translate(t,e)):(this.start.translate(t),this.end.translate(t)),this}rotate(t,e){return this.start.rotate(t,e),this.end.rotate(t,e),this}scale(t,e,n){return this.start.scale(t,e,n),this.end.scale(t,e,n),this}length(){return Math.sqrt(this.squaredLength())}squaredLength(){const t=this.start.x-this.end.x,e=this.start.y-this.end.y;return t*t+e*e}setLength(t){const e=this.length();if(!e)return this;const n=t/e;return this.scale(n,n,this.start)}parallel(t){const e=this.clone();if(!e.isDifferentiable())return e;const{start:n,end:r}=e,i=n.clone().rotate(270,r),s=r.clone().rotate(90,n);return n.move(s,t),r.move(i,t),e}vector(){return new o(this.end.x-this.start.x,this.end.y-this.start.y)}angle(){const t=new o(this.start.x+1,this.start.y);return this.start.angleBetween(this.end,t)}bbox(){const t=Math.min(this.start.x,this.end.x),e=Math.min(this.start.y,this.end.y),n=Math.max(this.start.x,this.end.x),r=Math.max(this.start.y,this.end.y);return new a(t,e,n-t,r-e)}bearing(){return this.start.bearing(this.end)}closestPoint(t){return this.pointAt(this.closestPointNormalizedLength(t))}closestPointLength(t){return this.closestPointNormalizedLength(t)*this.length()}closestPointTangent(t){return this.tangentAt(this.closestPointNormalizedLength(t))}closestPointNormalizedLength(t){const e=this.vector().dot(new l(this.start,t).vector()),n=Math.min(1,Math.max(0,e/this.squaredLength()));return Number.isNaN(n)?0:n}pointAt(t){const e=this.start,n=this.end;return t<=0?e.clone():t>=1?n.clone():e.lerp(n,t)}pointAtLength(t){const e=this.start,n=this.end;let r=!0;t<0&&(r=!1,t=-t);const i=this.length();if(t>=i)return r?n.clone():e.clone();const s=(r?t:i-t)/i;return this.pointAt(s)}divideAt(t){const e=this.pointAt(t);return[new l(this.start,e),new l(e,this.end)]}divideAtLength(t){const e=this.pointAtLength(t);return[new l(this.start,e),new l(e,this.end)]}containsPoint(t){const e=this.start,n=this.end;if(0!==e.cross(t,n))return!1;const r=this.length();return!(new l(e,t).length()>r)&&!(new l(t,n).length()>r)}intersect(t,e){const n=t.intersectsWithLine(this,e);return n?Array.isArray(n)?n:[n]:null}intersectsWithLine(t){const e=new o(this.end.x-this.start.x,this.end.y-this.start.y),n=new o(t.end.x-t.start.x,t.end.y-t.start.y),r=e.x*n.y-e.y*n.x,i=new o(t.start.x-this.start.x,t.start.y-this.start.y),s=i.x*n.y-i.y*n.x,a=i.x*e.y-i.y*e.x;if(0===r||s*r<0||a*r<0)return null;if(r>0){if(s>r||a>r)return null}else if(s<r||a<r)return null;return new o(this.start.x+s*e.x/r,this.start.y+s*e.y/r)}isDifferentiable(){return!this.start.equals(this.end)}pointOffset(t){const e=o.clone(t),n=this.start,r=this.end,i=(r.x-n.x)*(e.y-n.y)-(r.y-n.y)*(e.x-n.x);return i/this.length()}pointSquaredDistance(t,e){const n=o.create(t,e);return this.closestPoint(n).squaredDistance(n)}pointDistance(t,e){const n=o.create(t,e);return this.closestPoint(n).distance(n)}tangentAt(t){if(!this.isDifferentiable())return null;const e=this.start,n=this.end,r=this.pointAt(t),i=new l(e,n);return i.translate(r.x-e.x,r.y-e.y),i}tangentAtLength(t){if(!this.isDifferentiable())return null;const e=this.start,n=this.end,r=this.pointAtLength(t),i=new l(e,n);return i.translate(r.x-e.x,r.y-e.y),i}relativeCcw(t,e){const n=o.create(t,e);let r=n.x-this.start.x,i=n.y-this.start.y;const s=this.end.x-this.start.x,a=this.end.y-this.start.y;let l=r*a-i*s;return 0===l&&(l=r*s+i*a,l>0&&(r-=s,i-=a,l=r*s+i*a,l<0&&(l=0))),l<0?-1:l>0?1:0}equals(t){return null!=t&&this.start.x===t.start.x&&this.start.y===t.start.y&&this.end.x===t.end.x&&this.end.y===t.end.y}clone(){return new l(this.start,this.end)}toJSON(){return{start:this.start.toJSON(),end:this.end.toJSON()}}serialize(){return[this.start.serialize(),this.end.serialize()].join(" ")}}(function(t){function e(e){return null!=e&&e instanceof t}t.isLine=e})(l||(l={}));class c extends s{get center(){return new o(this.x,this.y)}constructor(t,e,n,r){super(),this.x=null==t?0:t,this.y=null==e?0:e,this.a=null==n?0:n,this.b=null==r?0:r}bbox(){return a.fromEllipse(this)}getCenter(){return this.center}inflate(t,e){const n=t,r=null!=e?e:t;return this.a+=2*n,this.b+=2*r,this}normalizedDistance(t,e){const n=o.create(t,e),r=n.x-this.x,i=n.y-this.y,s=this.a,a=this.b;return r*r/(s*s)+i*i/(a*a)}containsPoint(t,e){return this.normalizedDistance(t,e)<=1}intersectsWithLine(t){const e=[],n=this.a,r=this.b,i=t.start,s=t.end,a=t.vector(),l=i.diff(new o(this.x,this.y)),c=new o(a.x/(n*n),a.y/(r*r)),h=new o(l.x/(n*n),l.y/(r*r)),u=a.dot(c),d=a.dot(h),g=l.dot(h)-1,f=d*d-u*g;if(f<0)return null;if(f>0){const t=Math.sqrt(f),n=(-d-t)/u,r=(-d+t)/u;if((n<0||n>1)&&(r<0||r>1))return null;n>=0&&n<=1&&e.push(i.lerp(s,n)),r>=0&&r<=1&&e.push(i.lerp(s,r))}else{const t=-d/u;if(!(t>=0&&t<=1))return null;e.push(i.lerp(s,t))}return e}intersectsWithLineFromCenterToPoint(t,e=0){const n=o.clone(t);e&&n.rotate(e,this.getCenter());const r=n.x-this.x,i=n.y-this.y;let s;if(0===r)return s=this.bbox().getNearestPointToPoint(n),e?s.rotate(-e,this.getCenter()):s;const a=i/r,l=a*a,c=this.a*this.a,h=this.b*this.b;let u=Math.sqrt(1/(1/c+l/h));u=r<0?-u:u;const d=a*u;return s=new o(this.x+u,this.y+d),e?s.rotate(-e,this.getCenter()):s}tangentTheta(t){const e=o.clone(t),n=e.x,r=e.y,i=this.a,s=this.b,a=this.bbox().center,l=a.x,c=a.y,h=30,u=n>a.x+i/2,d=n<a.x-i/2;let g,f;return u||d?(f=n>a.x?r-h:r+h,g=i*i/(n-l)-i*i*(r-c)*(f-c)/(s*s*(n-l))+l):(g=r>a.y?n+h:n-h,f=s*s/(r-c)-s*s*(n-l)*(g-l)/(i*i*(r-c))+c),new o(g,f).theta(e)}scale(t,e){return this.a*=t,this.b*=e,this}rotate(t,e){const n=a.fromEllipse(this);n.rotate(t,e);const r=c.fromRect(n);return this.a=r.a,this.b=r.b,this.x=r.x,this.y=r.y,this}translate(t,e){const n=o.create(t,e);return this.x+=n.x,this.y+=n.y,this}equals(t){return null!=t&&t.x===this.x&&t.y===this.y&&t.a===this.a&&t.b===this.b}clone(){return new c(this.x,this.y,this.a,this.b)}toJSON(){return{x:this.x,y:this.y,a:this.a,b:this.b}}serialize(){return`${this.x} ${this.y} ${this.a} ${this.b}`}}(function(t){function e(e){return null!=e&&e instanceof t}t.isEllipse=e})(c||(c={})),function(t){function e(e,r,i,s){return null==e||"number"===typeof e?new t(e,r,i,s):n(e)}function n(e){return t.isEllipse(e)?e.clone():Array.isArray(e)?new t(e[0],e[1],e[2],e[3]):new t(e.x,e.y,e.a,e.b)}function r(e){const n=e.center;return new t(n.x,n.y,e.width/2,e.height/2)}t.create=e,t.parse=n,t.fromRect=r}(c||(c={}));const h=new RegExp("^[\\s\\dLMCZz,.]*$");function u(t){return"string"===typeof t&&h.test(t)}function d(t,e){return(t%e+e)%e}function g(t,e,n,r,i){const s=[],a=t[t.length-1],l=null!=e&&e>0,c=e||0;if(r&&l){t=t.slice();const e=t[0],n=new o(a.x+(e.x-a.x)/2,a.y+(e.y-a.y)/2);t.splice(0,0,n)}let h=t[0],u=1;n?s.push("M",h.x,h.y):s.push("L",h.x,h.y);while(u<(r?t.length:t.length-1)){let e=t[d(u,t.length)],n=h.x-e.x,r=h.y-e.y;if(l&&(0!==n||0!==r)&&(null==i||i.indexOf(u-1)<0)){let i=Math.sqrt(n*n+r*r);const a=n*Math.min(c,i/2)/i,l=r*Math.min(c,i/2)/i,h=e.x+a,g=e.y+l;s.push("L",h,g);let f=t[d(u+1,t.length)];while(u<t.length-2&&0===Math.round(f.x-e.x)&&0===Math.round(f.y-e.y))f=t[d(u+2,t.length)],u+=1;n=f.x-e.x,r=f.y-e.y,i=Math.max(1,Math.sqrt(n*n+r*r));const p=n*Math.min(c,i/2)/i,m=r*Math.min(c,i/2)/i,y=e.x+p,b=e.y+m;s.push("Q",e.x,e.y,y,b),e=new o(y,b)}else s.push("L",e.x,e.y);h=e,u+=1}return r?s.push("Z"):s.push("L",a.x,a.y),s.map(t=>"string"===typeof t?t:+t.toFixed(3)).join(" ")}function f(t,e={}){const n=[];return t&&t.length&&t.forEach(t=>{Array.isArray(t)?n.push({x:t[0],y:t[1]}):n.push({x:t.x,y:t.y})}),g(n,e.round,null==e.initialMove||e.initialMove,e.close,e.exclude)}function p(t,e,n,r,i=0,s=0,o=0,a,l){if(0===n||0===r)return[];a-=t,l-=e,n=Math.abs(n),r=Math.abs(r);const c=-a/2,h=-l/2,u=Math.cos(i*Math.PI/180),d=Math.sin(i*Math.PI/180),g=u*c+d*h,f=-1*d*c+u*h,p=g*g,m=f*f,y=n*n,b=r*r,v=p/y+m/b;let w;if(v>1)n=Math.sqrt(v)*n,r=Math.sqrt(v)*r,w=0;else{let t=1;s===o&&(t=-1),w=t*Math.sqrt((y*b-y*m-b*p)/(y*m+b*p))}const x=w*n*f/r,P=-1*w*r*g/n,C=u*x-d*P+a/2,A=d*x+u*P+l/2;let O=Math.atan2((f-P)/r,(g-x)/n)-Math.atan2(0,1),E=O>=0?O:2*Math.PI+O;O=Math.atan2((-f-P)/r,(-g-x)/n)-Math.atan2((f-P)/r,(g-x)/n);let S=O>=0?O:2*Math.PI+O;0===o&&S>0?S-=2*Math.PI:0!==o&&S<0&&(S+=2*Math.PI);const M=2*S/Math.PI,T=Math.ceil(M<0?-1*M:M),j=S/T,k=8/3*Math.sin(j/4)*Math.sin(j/4)/Math.sin(j/2),N=u*n,L=u*r,R=d*n,B=d*r;let D=Math.cos(E),I=Math.sin(E),V=-k*(N*I+B*D),z=-k*(R*I-L*D),F=0,$=0;const G=[];for(let q=0;q<T;q+=1){E+=j,D=Math.cos(E),I=Math.sin(E),F=N*D-B*I+C,$=R*D+L*I+A;const n=-k*(N*I+B*D),r=-k*(R*I-L*D),i=6*q;G[i]=Number(V+t),G[i+1]=Number(z+e),G[i+2]=Number(F-n+t),G[i+3]=Number($-r+e),G[i+4]=Number(F+t),G[i+5]=Number($+e),V=F+n,z=$+r}return G.map(t=>+t.toFixed(2))}function m(t,e,n,r,i=0,s=0,o=0,a,l){const c=[],h=p(t,e,n,r,i,s,o,a,l);if(null!=h)for(let u=0,d=h.length;u<d;u+=6)c.push("C",h[u],h[u+1],h[u+2],h[u+3],h[u+4],h[u+5]);return c.join(" ")}class y extends s{get start(){return this.points[0]||null}get end(){return this.points[this.points.length-1]||null}constructor(t){if(super(),null!=t){if("string"===typeof t)return y.parse(t);this.points=t.map(t=>o.create(t))}else this.points=[]}scale(t,e,n=new o){return this.points.forEach(r=>r.scale(t,e,n)),this}rotate(t,e){return this.points.forEach(n=>n.rotate(t,e)),this}translate(t,e){const n=o.create(t,e);return this.points.forEach(t=>t.translate(n.x,n.y)),this}round(t=0){return this.points.forEach(e=>e.round(t)),this}bbox(){if(0===this.points.length)return new a;let t=1/0,e=-1/0,n=1/0,r=-1/0;const i=this.points;for(let s=0,o=i.length;s<o;s+=1){const o=i[s],a=o.x,l=o.y;a<t&&(t=a),a>e&&(e=a),l<n&&(n=l),l>r&&(r=l)}return new a(t,n,e-t,r-n)}closestPoint(t){const e=this.closestPointLength(t);return this.pointAtLength(e)}closestPointLength(t){const e=this.points,n=e.length;if(0===n||1===n)return 0;let r=0,i=0,s=1/0;for(let o=0,a=n-1;o<a;o+=1){const n=new l(e[o],e[o+1]),a=n.length(),c=n.closestPointNormalizedLength(t),h=n.pointAt(c),u=h.squaredDistance(t);u<s&&(s=u,i=r+c*a),r+=a}return i}closestPointNormalizedLength(t){const e=this.length();if(0===e)return 0;const n=this.closestPointLength(t);return n/e}closestPointTangent(t){const e=this.closestPointLength(t);return this.tangentAtLength(e)}containsPoint(t){if(0===this.points.length)return!1;const e=o.clone(t),n=e.x,r=e.y,i=this.points,s=i.length;let a=s-1,c=0;for(let h=0;h<s;h+=1){const s=i[a],u=i[h];if(e.equals(s))return!0;const d=new l(s,u);if(d.containsPoint(t))return!0;if(r<=s.y&&r>u.y||r>s.y&&r<=u.y){const e=s.x-n>u.x-n?s.x-n:u.x-n;if(e>=0){const i=new o(n+e,r),s=new l(t,i);d.intersectsWithLine(s)&&(c+=1)}}a=h}return c%2===1}intersectsWithLine(t){const e=[];for(let n=0,r=this.points.length-1;n<r;n+=1){const r=this.points[n],i=this.points[n+1],s=t.intersectsWithLine(new l(r,i));s&&e.push(s)}return e.length>0?e:null}isDifferentiable(){for(let t=0,e=this.points.length-1;t<e;t+=1){const e=this.points[t],n=this.points[t+1],r=new l(e,n);if(r.isDifferentiable())return!0}return!1}length(){let t=0;for(let e=0,n=this.points.length-1;e<n;e+=1){const n=this.points[e],r=this.points[e+1];t+=n.distance(r)}return t}pointAt(t){const e=this.points,n=e.length;if(0===n)return null;if(1===n)return e[0].clone();if(t<=0)return e[0].clone();if(t>=1)return e[n-1].clone();const r=this.length(),i=r*t;return this.pointAtLength(i)}pointAtLength(t){const e=this.points,n=e.length;if(0===n)return null;if(1===n)return e[0].clone();let r=!0;t<0&&(r=!1,t=-t);let i=0;for(let o=0,a=n-1;o<a;o+=1){const n=r?o:a-1-o,s=e[n],c=e[n+1],h=new l(s,c),u=s.distance(c);if(t<=i+u)return h.pointAtLength((r?1:-1)*(t-i));i+=u}const s=r?e[n-1]:e[0];return s.clone()}tangentAt(t){const e=this.points,n=e.length;if(0===n||1===n)return null;t<0&&(t=0),t>1&&(t=1);const r=this.length(),i=r*t;return this.tangentAtLength(i)}tangentAtLength(t){const e=this.points,n=e.length;if(0===n||1===n)return null;let r,i=!0;t<0&&(i=!1,t=-t);let s=0;for(let o=0,a=n-1;o<a;o+=1){const n=i?o:a-1-o,c=e[n],h=e[n+1],u=new l(c,h),d=c.distance(h);if(u.isDifferentiable()){if(t<=s+d)return u.tangentAtLength((i?1:-1)*(t-s));r=u}s+=d}if(r){const t=i?1:0;return r.tangentAt(t)}return null}simplify(t={}){const e=this.points;if(e.length<3)return this;const n=t.threshold||0;let r=0;while(e[r+2]){const t=r,i=r+1,s=r+2,o=e[t],a=e[i],c=e[s],h=new l(o,c),u=h.closestPoint(a),d=u.distance(a);d<=n?e.splice(i,1):r+=1}return this}toHull(){const t=this.points,e=t.length;if(0===e)return new y;let n=t[0];for(let u=1;u<e;u+=1)(t[u].y<n.y||t[u].y===n.y&&t[u].x>n.x)&&(n=t[u]);const r=[];for(let u=0;u<e;u+=1){let e=n.theta(t[u]);0===e&&(e=360),r.push([t[u],u,e])}if(r.sort((t,e)=>{let n=t[2]-e[2];return 0===n&&(n=e[1]-t[1]),n}),r.length>2){const t=r[r.length-1];r.unshift(t)}const i={},s=[],o=t=>`${t[0].toString()}@${t[1]}`;while(0!==r.length){const t=r.pop(),e=t[0];if(i[o(t)])continue;let n=!1;while(!n)if(s.length<2)s.push(t),n=!0;else{const a=s.pop(),l=a[0],c=s.pop(),h=c[0],u=h.cross(l,e);if(u<0)s.push(c),s.push(a),s.push(t),n=!0;else if(0===u){const t=1e-10,n=l.angleBetween(h,e);Math.abs(n-180)<t||l.equals(e)||h.equals(l)?(i[o(a)]=l,s.push(c)):Math.abs((n+1)%360-1)<t&&(s.push(c),r.push(a))}else i[o(a)]=l,s.push(c)}}let a;s.length>2&&s.pop();let l=-1;for(let u=0,d=s.length;u<d;u+=1){const t=s[u][1];(void 0===a||t<a)&&(a=t,l=u)}let c=[];if(l>0){const t=s.slice(l),e=s.slice(0,l);c=t.concat(e)}else c=s;const h=[];for(let u=0,d=c.length;u<d;u+=1)h.push(c[u][0]);return new y(h)}equals(t){return null!=t&&(t.points.length===this.points.length&&t.points.every((t,e)=>t.equals(this.points[e])))}clone(){return new y(this.points.map(t=>t.clone()))}toJSON(){return this.points.map(t=>t.toJSON())}serialize(){return this.points.map(t=>""+t.serialize()).join(" ")}}(function(t){function e(e){return null!=e&&e instanceof t}t.isPolyline=e})(y||(y={})),function(t){function e(e){const n=e.trim();if(""===n)return new t;const r=[],i=n.split(/\s*,\s*|\s+/);for(let t=0,s=i.length;t<s;t+=2)r.push({x:+i[t],y:+i[t+1]});return new t(r)}t.parse=e}(y||(y={}));class b extends s{constructor(t,e,n,r){super(),this.PRECISION=3,this.start=o.create(t),this.controlPoint1=o.create(e),this.controlPoint2=o.create(n),this.end=o.create(r)}bbox(){const t=this.start,e=this.controlPoint1,n=this.controlPoint2,r=this.end,i=t.x,s=t.y,o=e.x,l=e.y,c=n.x,h=n.y,u=r.x,d=r.y,g=[],f=[],p=[[],[]];let m,y,b,v,w,x,P,C,A,O,E;for(let a=0;a<2;a+=1)if(0===a?(y=6*i-12*o+6*c,m=-3*i+9*o-9*c+3*u,b=3*o-3*i):(y=6*s-12*l+6*h,m=-3*s+9*l-9*h+3*d,b=3*l-3*s),Math.abs(m)<1e-12){if(Math.abs(y)<1e-12)continue;v=-b/y,v>0&&v<1&&f.push(v)}else P=y*y-4*b*m,C=Math.sqrt(P),P<0||(w=(-y+C)/(2*m),w>0&&w<1&&f.push(w),x=(-y-C)/(2*m),x>0&&x<1&&f.push(x));let S=f.length;const M=S;while(S)S-=1,v=f[S],E=1-v,A=E*E*E*i+3*E*E*v*o+3*E*v*v*c+v*v*v*u,p[0][S]=A,O=E*E*E*s+3*E*E*v*l+3*E*v*v*h+v*v*v*d,p[1][S]=O,g[S]={X:A,Y:O};f[M]=0,f[M+1]=1,g[M]={X:i,Y:s},g[M+1]={X:u,Y:d},p[0][M]=i,p[1][M]=s,p[0][M+1]=u,p[1][M+1]=d,f.length=M+2,p[0].length=M+2,p[1].length=M+2,g.length=M+2;const T=Math.min.apply(null,p[0]),j=Math.min.apply(null,p[1]),k=Math.max.apply(null,p[0]),N=Math.max.apply(null,p[1]);return new a(T,j,k-T,N-j)}closestPoint(t,e={}){return this.pointAtT(this.closestPointT(t,e))}closestPointLength(t,e={}){const n=this.getOptions(e);return this.lengthAtT(this.closestPointT(t,n),n)}closestPointNormalizedLength(t,e={}){const n=this.getOptions(e),r=this.closestPointLength(t,n);if(!r)return 0;const i=this.length(n);return 0===i?0:r/i}closestPointT(t,e={}){const n=this.getPrecision(e),r=this.getDivisions(e),i=Math.pow(10,-n);let s=null,o=0,a=0,l=0,c=0,h=0,u=null;const d=r.length;let g=d>0?1/d:0;r.forEach((e,n)=>{const r=e.start.distance(t),i=e.end.distance(t),d=r+i;(null==u||d<u)&&(s=e,o=n*g,a=(n+1)*g,l=r,c=i,u=d,h=e.endpointDistance())});while(1){const e=l?Math.abs(l-c)/l:0,n=null!=c?Math.abs(l-c)/c:0,r=e<i||n<i,u=!l||l<h*i,d=!c||c<h*i,f=u||d;if(r||f)return l<=c?o:a;const p=s.divide(.5);g/=2;const m=p[0].start.distance(t),y=p[0].end.distance(t),b=m+y,v=p[1].start.distance(t),w=p[1].end.distance(t),x=v+w;b<=x?(s=p[0],a-=g,l=m,c=y):(s=p[1],o+=g,l=v,c=w)}}closestPointTangent(t,e={}){return this.tangentAtT(this.closestPointT(t,e))}containsPoint(t,e={}){const n=this.toPolyline(e);return n.containsPoint(t)}divideAt(t,e={}){if(t<=0)return this.divideAtT(0);if(t>=1)return this.divideAtT(1);const n=this.tAt(t,e);return this.divideAtT(n)}divideAtLength(t,e={}){const n=this.tAtLength(t,e);return this.divideAtT(n)}divide(t){return this.divideAtT(t)}divideAtT(t){const e=this.start,n=this.controlPoint1,r=this.controlPoint2,i=this.end;if(t<=0)return[new b(e,e,e,e),new b(e,n,r,i)];if(t>=1)return[new b(e,n,r,i),new b(i,i,i,i)];const s=this.getSkeletonPoints(t),o=s.startControlPoint1,a=s.startControlPoint2,l=s.divider,c=s.dividerControlPoint1,h=s.dividerControlPoint2;return[new b(e,o,a,l),new b(l,c,h,i)]}endpointDistance(){return this.start.distance(this.end)}getSkeletonPoints(t){const e=this.start,n=this.controlPoint1,r=this.controlPoint2,i=this.end;if(t<=0)return{startControlPoint1:e.clone(),startControlPoint2:e.clone(),divider:e.clone(),dividerControlPoint1:n.clone(),dividerControlPoint2:r.clone()};if(t>=1)return{startControlPoint1:n.clone(),startControlPoint2:r.clone(),divider:i.clone(),dividerControlPoint1:i.clone(),dividerControlPoint2:i.clone()};const s=new l(e,n).pointAt(t),o=new l(n,r).pointAt(t),a=new l(r,i).pointAt(t),c=new l(s,o).pointAt(t),h=new l(o,a).pointAt(t),u=new l(c,h).pointAt(t);return{startControlPoint1:s,startControlPoint2:c,divider:u,dividerControlPoint1:h,dividerControlPoint2:a}}getSubdivisions(t={}){const e=this.getPrecision(t);let n=[new b(this.start,this.controlPoint1,this.controlPoint2,this.end)];if(0===e)return n;let r=this.endpointDistance();const i=Math.pow(10,-e);let s=0;while(1){s+=1;const t=[];n.forEach(e=>{const n=e.divide(.5);t.push(n[0],n[1])});const e=t.reduce((t,e)=>t+e.endpointDistance(),0),o=0!==e?(e-r)/e:0;if(s>1&&o<i)return t;n=t,r=e}}length(t={}){const e=this.getDivisions(t);return e.reduce((t,e)=>t+e.endpointDistance(),0)}lengthAtT(t,e={}){if(t<=0)return 0;const n=void 0===e.precision?this.PRECISION:e.precision,r=this.divide(t)[0];return r.length({precision:n})}pointAt(t,e={}){if(t<=0)return this.start.clone();if(t>=1)return this.end.clone();const n=this.tAt(t,e);return this.pointAtT(n)}pointAtLength(t,e={}){const n=this.tAtLength(t,e);return this.pointAtT(n)}pointAtT(t){return t<=0?this.start.clone():t>=1?this.end.clone():this.getSkeletonPoints(t).divider}isDifferentiable(){const t=this.start,e=this.controlPoint1,n=this.controlPoint2,r=this.end;return!(t.equals(e)&&e.equals(n)&&n.equals(r))}tangentAt(t,e={}){if(!this.isDifferentiable())return null;t<0?t=0:t>1&&(t=1);const n=this.tAt(t,e);return this.tangentAtT(n)}tangentAtLength(t,e={}){if(!this.isDifferentiable())return null;const n=this.tAtLength(t,e);return this.tangentAtT(n)}tangentAtT(t){if(!this.isDifferentiable())return null;t<0&&(t=0),t>1&&(t=1);const e=this.getSkeletonPoints(t),n=e.startControlPoint2,r=e.dividerControlPoint1,i=e.divider,s=new l(n,r);return s.translate(i.x-n.x,i.y-n.y),s}getPrecision(t={}){return null==t.precision?this.PRECISION:t.precision}getDivisions(t={}){if(null!=t.subdivisions)return t.subdivisions;const e=this.getPrecision(t);return this.getSubdivisions({precision:e})}getOptions(t={}){const e=this.getPrecision(t),n=this.getDivisions(t);return{precision:e,subdivisions:n}}tAt(t,e={}){if(t<=0)return 0;if(t>=1)return 1;const n=this.getOptions(e),r=this.length(n),i=r*t;return this.tAtLength(i,n)}tAtLength(t,e={}){let n=!0;t<0&&(n=!1,t=-t);const r=this.getPrecision(e),i=this.getDivisions(e),s={precision:r,subdivisions:i};let o,a,l=null,c=0,h=0,u=0;const d=i.length;let g=d>0?1/d:0;for(let m=0;m<d;m+=1){const e=n?m:d-1-m,r=i[m],s=r.endpointDistance();if(t<=u+s){l=r,o=e*g,a=(e+1)*g,c=n?t-u:s+u-t,h=n?s+u-t:t-u;break}u+=s}if(null==l)return n?1:0;const f=this.length(s),p=Math.pow(10,-r);while(1){let t,e,n;if(t=0!==f?c/f:0,t<p)return o;if(t=0!==f?h/f:0,t<p)return a;const r=l.divide(.5);g/=2;const i=r[0].endpointDistance(),s=r[1].endpointDistance();c<=i?(l=r[0],a-=g,e=c,n=i-e):(l=r[1],o+=g,e=c-i,n=s-e),c=e,h=n}}toPoints(t={}){const e=this.getDivisions(t),n=[e[0].start.clone()];return e.forEach(t=>n.push(t.end.clone())),n}toPolyline(t={}){return new y(this.toPoints(t))}scale(t,e,n){return this.start.scale(t,e,n),this.controlPoint1.scale(t,e,n),this.controlPoint2.scale(t,e,n),this.end.scale(t,e,n),this}rotate(t,e){return this.start.rotate(t,e),this.controlPoint1.rotate(t,e),this.controlPoint2.rotate(t,e),this.end.rotate(t,e),this}translate(t,e){return"number"===typeof t?(this.start.translate(t,e),this.controlPoint1.translate(t,e),this.controlPoint2.translate(t,e),this.end.translate(t,e)):(this.start.translate(t),this.controlPoint1.translate(t),this.controlPoint2.translate(t),this.end.translate(t)),this}equals(t){return null!=t&&this.start.equals(t.start)&&this.controlPoint1.equals(t.controlPoint1)&&this.controlPoint2.equals(t.controlPoint2)&&this.end.equals(t.end)}clone(){return new b(this.start,this.controlPoint1,this.controlPoint2,this.end)}toJSON(){return{start:this.start.toJSON(),controlPoint1:this.controlPoint1.toJSON(),controlPoint2:this.controlPoint2.toJSON(),end:this.end.toJSON()}}serialize(){return[this.start.serialize(),this.controlPoint1.serialize(),this.controlPoint2.serialize(),this.end.serialize()].join(" ")}}(function(t){function e(e){return null!=e&&e instanceof t}t.isCurve=e})(b||(b={})),function(t){function e(t){const e=t.length,n=[],r=[];let i=2;n[0]=t[0]/i;for(let s=1;s<e;s+=1)r[s]=1/i,i=(s<e-1?4:3.5)-r[s],n[s]=(t[s]-n[s-1])/i;for(let s=1;s<e;s+=1)n[e-s-1]-=r[e-s]*n[e-s];return n}function n(t){const n=t.map(t=>o.clone(t)),r=[],i=[],s=n.length-1;if(1===s)return r[0]=new o((2*n[0].x+n[1].x)/3,(2*n[0].y+n[1].y)/3),i[0]=new o(2*r[0].x-n[0].x,2*r[0].y-n[0].y),[r,i];const a=[];for(let e=1;e<s-1;e+=1)a[e]=4*n[e].x+2*n[e+1].x;a[0]=n[0].x+2*n[1].x,a[s-1]=(8*n[s-1].x+n[s].x)/2;const l=e(a);for(let e=1;e<s-1;e+=1)a[e]=4*n[e].y+2*n[e+1].y;a[0]=n[0].y+2*n[1].y,a[s-1]=(8*n[s-1].y+n[s].y)/2;const c=e(a);for(let e=0;e<s;e+=1)r.push(new o(l[e],c[e])),e<s-1?i.push(new o(2*n[e+1].x-l[e+1],2*n[e+1].y-c[e+1])):i.push(new o((n[s].x+l[s-1])/2,(n[s].y+c[s-1])/2));return[r,i]}function r(e){if(null==e||Array.isArray(e)&&e.length<2)throw new Error("At least 2 points are required");const r=n(e),i=[];for(let n=0,s=r[0].length;n<s;n+=1){const s=new o(r[0][n].x,r[0][n].y),a=new o(r[1][n].x,r[1][n].y);i.push(new t(e[n],s,a,e[n+1]))}return i}t.throughPoints=r}(b||(b={}));class v extends s{constructor(){super(...arguments),this.isVisible=!0,this.isSegment=!0,this.isSubpathStart=!1}get end(){return this.endPoint}get start(){if(null==this.previousSegment)throw new Error("Missing previous segment. (This segment cannot be the first segment of a path, or segment has not yet been added to a path.)");return this.previousSegment.end}closestPointT(t,e){if(this.closestPointNormalizedLength)return this.closestPointNormalizedLength(t);throw new Error("Neither `closestPointT` nor `closestPointNormalizedLength` method is implemented.")}lengthAtT(t,e){if(t<=0)return 0;const n=this.length();return t>=1?n:n*t}divideAtT(t){if(this.divideAt)return this.divideAt(t);throw new Error("Neither `divideAtT` nor `divideAt` method is implemented.")}pointAtT(t){if(this.pointAt)return this.pointAt(t);throw new Error("Neither `pointAtT` nor `pointAt` method is implemented.")}tangentAtT(t){if(this.tangentAt)return this.tangentAt(t);throw new Error("Neither `tangentAtT` nor `tangentAt` method is implemented.")}}class w extends v{constructor(t,e){super(),l.isLine(t)?this.endPoint=t.end.clone().round(2):this.endPoint=o.create(t,e).round(2)}get type(){return"L"}get line(){return new l(this.start,this.end)}bbox(){return this.line.bbox()}closestPoint(t){return this.line.closestPoint(t)}closestPointLength(t){return this.line.closestPointLength(t)}closestPointNormalizedLength(t){return this.line.closestPointNormalizedLength(t)}closestPointTangent(t){return this.line.closestPointTangent(t)}length(){return this.line.length()}divideAt(t){const e=this.line.divideAt(t);return[new w(e[0]),new w(e[1])]}divideAtLength(t){const e=this.line.divideAtLength(t);return[new w(e[0]),new w(e[1])]}getSubdivisions(){return[]}pointAt(t){return this.line.pointAt(t)}pointAtLength(t){return this.line.pointAtLength(t)}tangentAt(t){return this.line.tangentAt(t)}tangentAtLength(t){return this.line.tangentAtLength(t)}isDifferentiable(){return null!=this.previousSegment&&!this.start.equals(this.end)}clone(){return new w(this.end)}scale(t,e,n){return this.end.scale(t,e,n),this}rotate(t,e){return this.end.rotate(t,e),this}translate(t,e){return"number"===typeof t?this.end.translate(t,e):this.end.translate(t),this}equals(t){return this.type===t.type&&this.start.equals(t.start)&&this.end.equals(t.end)}toJSON(){return{type:this.type,start:this.start.toJSON(),end:this.end.toJSON()}}serialize(){const t=this.end;return`${this.type} ${t.x} ${t.y}`}}(function(t){function e(...e){const n=e.length,r=e[0];if(l.isLine(r))return new t(r);if(o.isPointLike(r))return 1===n?new t(r):e.map(e=>new t(e));if(2===n)return new t(+e[0],+e[1]);const i=[];for(let s=0;s<n;s+=2){const n=+e[s],r=+e[s+1];i.push(new t(n,r))}return i}t.create=e})(w||(w={}));class x extends v{get end(){if(!this.subpathStartSegment)throw new Error("Missing subpath start segment. (This segment needs a subpath start segment (e.g. MoveTo), or segment has not yet been added to a path.)");return this.subpathStartSegment.end}get type(){return"Z"}get line(){return new l(this.start,this.end)}bbox(){return this.line.bbox()}closestPoint(t){return this.line.closestPoint(t)}closestPointLength(t){return this.line.closestPointLength(t)}closestPointNormalizedLength(t){return this.line.closestPointNormalizedLength(t)}closestPointTangent(t){return this.line.closestPointTangent(t)}length(){return this.line.length()}divideAt(t){const e=this.line.divideAt(t);return[e[1].isDifferentiable()?new w(e[0]):this.clone(),new w(e[1])]}divideAtLength(t){const e=this.line.divideAtLength(t);return[e[1].isDifferentiable()?new w(e[0]):this.clone(),new w(e[1])]}getSubdivisions(){return[]}pointAt(t){return this.line.pointAt(t)}pointAtLength(t){return this.line.pointAtLength(t)}tangentAt(t){return this.line.tangentAt(t)}tangentAtLength(t){return this.line.tangentAtLength(t)}isDifferentiable(){return!(!this.previousSegment||!this.subpathStartSegment)&&!this.start.equals(this.end)}scale(){return this}rotate(){return this}translate(){return this}equals(t){return this.type===t.type&&this.start.equals(t.start)&&this.end.equals(t.end)}clone(){return new x}toJSON(){return{type:this.type,start:this.start.toJSON(),end:this.end.toJSON()}}serialize(){return this.type}}(function(t){function e(){return new t}t.create=e})(x||(x={}));class P extends v{constructor(t,e){super(),this.isVisible=!1,this.isSubpathStart=!0,l.isLine(t)||b.isCurve(t)?this.endPoint=t.end.clone().round(2):this.endPoint=o.create(t,e).round(2)}get start(){throw new Error("Illegal access. Moveto segments should not need a start property.")}get type(){return"M"}bbox(){return null}closestPoint(){return this.end.clone()}closestPointLength(){return 0}closestPointNormalizedLength(){return 0}closestPointT(){return 1}closestPointTangent(){return null}length(){return 0}lengthAtT(){return 0}divideAt(){return[this.clone(),this.clone()]}divideAtLength(){return[this.clone(),this.clone()]}getSubdivisions(){return[]}pointAt(){return this.end.clone()}pointAtLength(){return this.end.clone()}pointAtT(){return this.end.clone()}tangentAt(){return null}tangentAtLength(){return null}tangentAtT(){return null}isDifferentiable(){return!1}scale(t,e,n){return this.end.scale(t,e,n),this}rotate(t,e){return this.end.rotate(t,e),this}translate(t,e){return"number"===typeof t?this.end.translate(t,e):this.end.translate(t),this}clone(){return new P(this.end)}equals(t){return this.type===t.type&&this.end.equals(t.end)}toJSON(){return{type:this.type,end:this.end.toJSON()}}serialize(){const t=this.end;return`${this.type} ${t.x} ${t.y}`}}(function(t){function e(...e){const n=e.length,r=e[0];if(l.isLine(r))return new t(r);if(b.isCurve(r))return new t(r);if(o.isPointLike(r)){if(1===n)return new t(r);const i=[];for(let r=0;r<n;r+=1)0===r?i.push(new t(e[r])):i.push(new w(e[r]));return i}if(2===n)return new t(+e[0],+e[1]);const i=[];for(let s=0;s<n;s+=2){const n=+e[s],r=+e[s+1];0===s?i.push(new t(n,r)):i.push(new w(n,r))}return i}t.create=e})(P||(P={}));class C extends v{constructor(t,e,n,r,i,s){super(),b.isCurve(t)?(this.controlPoint1=t.controlPoint1.clone().round(2),this.controlPoint2=t.controlPoint2.clone().round(2),this.endPoint=t.end.clone().round(2)):"number"===typeof t?(this.controlPoint1=new o(t,e).round(2),this.controlPoint2=new o(n,r).round(2),this.endPoint=new o(i,s).round(2)):(this.controlPoint1=o.create(t).round(2),this.controlPoint2=o.create(e).round(2),this.endPoint=o.create(n).round(2))}get type(){return"C"}get curve(){return new b(this.start,this.controlPoint1,this.controlPoint2,this.end)}bbox(){return this.curve.bbox()}closestPoint(t){return this.curve.closestPoint(t)}closestPointLength(t){return this.curve.closestPointLength(t)}closestPointNormalizedLength(t){return this.curve.closestPointNormalizedLength(t)}closestPointTangent(t){return this.curve.closestPointTangent(t)}length(){return this.curve.length()}divideAt(t,e={}){const n=this.curve.divideAt(t,e);return[new C(n[0]),new C(n[1])]}divideAtLength(t,e={}){const n=this.curve.divideAtLength(t,e);return[new C(n[0]),new C(n[1])]}divideAtT(t){const e=this.curve.divideAtT(t);return[new C(e[0]),new C(e[1])]}getSubdivisions(){return[]}pointAt(t){return this.curve.pointAt(t)}pointAtLength(t){return this.curve.pointAtLength(t)}tangentAt(t){return this.curve.tangentAt(t)}tangentAtLength(t){return this.curve.tangentAtLength(t)}isDifferentiable(){if(!this.previousSegment)return!1;const t=this.start,e=this.controlPoint1,n=this.controlPoint2,r=this.end;return!(t.equals(e)&&e.equals(n)&&n.equals(r))}scale(t,e,n){return this.controlPoint1.scale(t,e,n),this.controlPoint2.scale(t,e,n),this.end.scale(t,e,n),this}rotate(t,e){return this.controlPoint1.rotate(t,e),this.controlPoint2.rotate(t,e),this.end.rotate(t,e),this}translate(t,e){return"number"===typeof t?(this.controlPoint1.translate(t,e),this.controlPoint2.translate(t,e),this.end.translate(t,e)):(this.controlPoint1.translate(t),this.controlPoint2.translate(t),this.end.translate(t)),this}equals(t){return this.start.equals(t.start)&&this.end.equals(t.end)&&this.controlPoint1.equals(t.controlPoint1)&&this.controlPoint2.equals(t.controlPoint2)}clone(){return new C(this.controlPoint1,this.controlPoint2,this.end)}toJSON(){return{type:this.type,start:this.start.toJSON(),controlPoint1:this.controlPoint1.toJSON(),controlPoint2:this.controlPoint2.toJSON(),end:this.end.toJSON()}}serialize(){const t=this.controlPoint1,e=this.controlPoint2,n=this.end;return[this.type,t.x,t.y,e.x,e.y,n.x,n.y].join(" ")}}function A(t,e,n){return{x:t*Math.cos(n)-e*Math.sin(n),y:t*Math.sin(n)+e*Math.cos(n)}}function O(t,e,n,r,i,s){const o=1/3,a=2/3;return[o*t+a*n,o*e+a*r,o*i+a*n,o*s+a*r,i,s]}function E(t,e,n,r,i,s,o,a,l,c){const h=120*Math.PI/180,u=Math.PI/180*(+i||0);let d,g,f,p,m,y=[];if(c)g=c[0],f=c[1],p=c[2],m=c[3];else{d=A(t,e,-u),t=d.x,e=d.y,d=A(a,l,-u),a=d.x,l=d.y;const i=(t-a)/2,c=(e-l)/2;let h=i*i/(n*n)+c*c/(r*r);h>1&&(h=Math.sqrt(h),n*=h,r*=h);const y=n*n,b=r*r,v=(s===o?-1:1)*Math.sqrt(Math.abs((y*b-y*c*c-b*i*i)/(y*c*c+b*i*i)));p=v*n*c/r+(t+a)/2,m=v*-r*i/n+(e+l)/2,g=Math.asin((e-m)/r),f=Math.asin((l-m)/r),g=t<p?Math.PI-g:g,f=a<p?Math.PI-f:f,g<0&&(g=2*Math.PI+g),f<0&&(f=2*Math.PI+f),o&&g>f&&(g-=2*Math.PI),!o&&f>g&&(f-=2*Math.PI)}let b=f-g;if(Math.abs(b)>h){const t=f,e=a,s=l;f=g+h*(o&&f>g?1:-1),a=p+n*Math.cos(f),l=m+r*Math.sin(f),y=E(a,l,n,r,i,0,o,e,s,[f,t,p,m])}b=f-g;const v=Math.cos(g),w=Math.sin(g),x=Math.cos(f),P=Math.sin(f),C=Math.tan(b/4),O=4/3*(n*C),S=4/3*(r*C),M=[t,e],T=[t+O*w,e-S*v],j=[a+O*P,l-S*x],k=[a,l];if(T[0]=2*M[0]-T[0],T[1]=2*M[1]-T[1],c)return[T,j,k].concat(y);{y=[T,j,k].concat(y).join().split(",");const t=[],e=y.length;for(let n=0;n<e;n+=1)t[n]=n%2?A(+y[n-1],+y[n],u).y:A(+y[n],+y[n+1],u).x;return t}}function S(t){if(!t)return null;const e="\t\n\v\f\r   ᠎             　\u2028\u2029",n=new RegExp(`([a-z])[${e},]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?[${e}]*,?[${e}]*)+)`,"ig"),r=new RegExp(`(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[${e}]*,?[${e}]*`,"ig"),i={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},s=[];return t.replace(n,(t,e,n)=>{const o=[];let a=e.toLowerCase();n.replace(r,(t,e)=>(e&&o.push(+e),t)),"m"===a&&o.length>2&&(s.push([e,...o.splice(0,2)]),a="l",e="m"===e?"l":"L");const l=i[a];while(o.length>=l)if(s.push([e,...o.splice(0,l)]),!l)break;return t}),s}function M(t){const e=S(t);if(!e||!e.length)return[["M",0,0]];let n=0,r=0,i=0,s=0;const o=[];for(let a=0,l=e.length;a<l;a+=1){const t=[];o.push(t);const l=e[a],c=l[0];if(c!==c.toUpperCase())switch(t[0]=c.toUpperCase(),t[0]){case"A":t[1]=l[1],t[2]=l[2],t[3]=l[3],t[4]=l[4],t[5]=l[5],t[6]=+l[6]+n,t[7]=+l[7]+r;break;case"V":t[1]=+l[1]+r;break;case"H":t[1]=+l[1]+n;break;case"M":i=+l[1]+n,s=+l[2]+r;for(let e=1,i=l.length;e<i;e+=1)t[e]=+l[e]+(e%2?n:r);break;default:for(let e=1,i=l.length;e<i;e+=1)t[e]=+l[e]+(e%2?n:r);break}else for(let e=0,n=l.length;e<n;e+=1)t[e]=l[e];switch(t[0]){case"Z":n=+i,r=+s;break;case"H":n=t[1];break;case"V":r=t[1];break;case"M":i=t[t.length-2],s=t[t.length-1],n=t[t.length-2],r=t[t.length-1];break;default:n=t[t.length-2],r=t[t.length-1];break}}return o}function T(t){const e=M(t),n={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null};function r(t,e,n){let r,i;if(!t)return["C",e.x,e.y,e.x,e.y,e.x,e.y];switch(t[0]in{T:1,Q:1}||(e.qx=null,e.qy=null),t[0]){case"M":e.X=t[1],e.Y=t[2];break;case"A":return 0===parseFloat(t[1])||0===parseFloat(t[2])?["L",t[6],t[7]]:["C"].concat(E.apply(0,[e.x,e.y].concat(t.slice(1))));case"S":return"C"===n||"S"===n?(r=2*e.x-e.bx,i=2*e.y-e.by):(r=e.x,i=e.y),["C",r,i].concat(t.slice(1));case"T":return"Q"===n||"T"===n?(e.qx=2*e.x-e.qx,e.qy=2*e.y-e.qy):(e.qx=e.x,e.qy=e.y),["C"].concat(O(e.x,e.y,e.qx,e.qy,t[1],t[2]));case"Q":return e.qx=t[1],e.qy=t[2],["C"].concat(O(e.x,e.y,t[1],t[2],t[3],t[4]));case"H":return["L"].concat(t[1],e.y);case"V":return["L"].concat(e.x,t[1]);case"L":break;case"Z":break;default:break}return t}function i(t,n){if(t[n].length>7){t[n].shift();const r=t[n];while(r.length)s[n]="A",n+=1,t.splice(n,0,["C"].concat(r.splice(0,6)));t.splice(n,1),a=e.length}}const s=[];let o="",a=e.length;for(let l=0;l<a;l+=1){let t="";e[l]&&(t=e[l][0]),"C"!==t&&(s[l]=t,l>0&&(o=s[l-1])),e[l]=r(e[l],n,o),"A"!==s[l]&&"C"===t&&(s[l]="C"),i(e,l);const a=e[l],c=a.length;n.x=a[c-2],n.y=a[c-1],n.bx=parseFloat(a[c-4])||n.x,n.by=parseFloat(a[c-3])||n.y}return e[0][0]&&"M"===e[0][0]||e.unshift(["M",0,0]),e}function j(t){return T(t).map(t=>t.map(t=>"string"===typeof t?t:i.round(t,2))).join(",").split(",").join(" ")}(function(t){function e(...e){const n=e.length,r=e[0];if(b.isCurve(r))return new t(r);if(o.isPointLike(r)){if(3===n)return new t(e[0],e[1],e[2]);const r=[];for(let i=0;i<n;i+=3)r.push(new t(e[i],e[i+1],e[i+2]));return r}if(6===n)return new t(e[0],e[1],e[2],e[3],e[4],e[5]);const i=[];for(let s=0;s<n;s+=6)i.push(new t(e[s],e[s+1],e[s+2],e[s+3],e[s+4],e[s+5]));return i}t.create=e})(C||(C={}));class k extends s{constructor(t){if(super(),this.PRECISION=3,this.segments=[],Array.isArray(t))if(l.isLine(t[0])||b.isCurve(t[0])){let e=null;const n=t;n.forEach((t,n)=>{0===n&&this.appendSegment(k.createSegment("M",t.start)),null==e||e.end.equals(t.start)||this.appendSegment(k.createSegment("M",t.start)),l.isLine(t)?this.appendSegment(k.createSegment("L",t.end)):b.isCurve(t)&&this.appendSegment(k.createSegment("C",t.controlPoint1,t.controlPoint2,t.end)),e=t})}else{const e=t;e.forEach(t=>{t.isSegment&&this.appendSegment(t)})}else null!=t&&(l.isLine(t)?(this.appendSegment(k.createSegment("M",t.start)),this.appendSegment(k.createSegment("L",t.end))):b.isCurve(t)?(this.appendSegment(k.createSegment("M",t.start)),this.appendSegment(k.createSegment("C",t.controlPoint1,t.controlPoint2,t.end))):y.isPolyline(t)?t.points&&t.points.length&&t.points.forEach((t,e)=>{const n=0===e?k.createSegment("M",t):k.createSegment("L",t);this.appendSegment(n)}):t.isSegment&&this.appendSegment(t))}get start(){const t=this.segments,e=t.length;if(0===e)return null;for(let n=0;n<e;n+=1){const e=t[n];if(e.isVisible)return e.start}return t[e-1].end}get end(){const t=this.segments,e=t.length;if(0===e)return null;for(let n=e-1;n>=0;n-=1){const e=t[n];if(e.isVisible)return e.end}return t[e-1].end}moveTo(...t){return this.appendSegment(P.create.call(null,...t))}lineTo(...t){return this.appendSegment(w.create.call(null,...t))}curveTo(...t){return this.appendSegment(C.create.call(null,...t))}arcTo(t,e,n,r,i,s,a){const l=this.end||new o,c="number"===typeof s?p(l.x,l.y,t,e,n,r,i,s,a):p(l.x,l.y,t,e,n,r,i,s.x,s.y);if(null!=c)for(let o=0,h=c.length;o<h;o+=6)this.curveTo(c[o],c[o+1],c[o+2],c[o+3],c[o+4],c[o+5]);return this}quadTo(t,e,n,r){const i=this.end||new o,s=["M",i.x,i.y];if("number"===typeof t)s.push("Q",t,e,n,r);else{const n=e;s.push("Q",t.x,t.y,n.x,n.y)}const a=k.parse(s.join(" "));return this.appendSegment(a.segments.slice(1)),this}close(){return this.appendSegment(x.create())}drawPoints(t,e={}){const n=f(t,e),r=k.parse(n);r&&r.segments&&this.appendSegment(r.segments)}bbox(){const t=this.segments,e=t.length;if(0===e)return null;let n;for(let i=0;i<e;i+=1){const e=t[i];if(e.isVisible){const t=e.bbox();null!=t&&(n=n?n.union(t):t)}}if(null!=n)return n;const r=t[e-1];return new a(r.end.x,r.end.y,0,0)}appendSegment(t){const e=this.segments.length;let n,r=0!==e?this.segments[e-1]:null;const i=null;if(Array.isArray(t))for(let s=0,o=t.length;s<o;s+=1){const e=t[s];n=this.prepareSegment(e,r,i),this.segments.push(n),r=n}else null!=t&&t.isSegment&&(n=this.prepareSegment(t,r,i),this.segments.push(n));return this}insertSegment(t,e){const n=this.segments.length;if(t<0&&(t=n+t+1),t>n||t<0)throw new Error("Index out of range.");let r,i=null,s=null;if(0!==n&&(t>=1?(i=this.segments[t-1],s=i.nextSegment):(i=null,s=this.segments[0])),Array.isArray(e))for(let o=0,a=e.length;o<a;o+=1){const n=e[o];r=this.prepareSegment(n,i,s),this.segments.splice(t+o,0,r),i=r}else r=this.prepareSegment(e,i,s),this.segments.splice(t,0,r);return this}removeSegment(t){const e=this.fixIndex(t),n=this.segments.splice(e,1)[0],r=n.previousSegment,i=n.nextSegment;return r&&(r.nextSegment=i),i&&(i.previousSegment=r),n.isSubpathStart&&i&&this.updateSubpathStartSegment(i),n}replaceSegment(t,e){const n=this.fixIndex(t);let r;const i=this.segments[n];let s=i.previousSegment;const o=i.nextSegment;let a=i.isSubpathStart;if(Array.isArray(e)){this.segments.splice(t,1);for(let n=0,i=e.length;n<i;n+=1){const i=e[n];r=this.prepareSegment(i,s,o),this.segments.splice(t+n,0,r),s=r,a&&r.isSubpathStart&&(a=!1)}}else r=this.prepareSegment(e,s,o),this.segments.splice(n,1,r),a&&r.isSubpathStart&&(a=!1);a&&o&&this.updateSubpathStartSegment(o)}getSegment(t){const e=this.fixIndex(t);return this.segments[e]}fixIndex(t){const e=this.segments.length;if(0===e)throw new Error("Path has no segments.");let n=t;while(n<0)n=e+n;if(n>=e||n<0)throw new Error("Index out of range.");return n}segmentAt(t,e={}){const n=this.segmentIndexAt(t,e);return n?this.getSegment(n):null}segmentAtLength(t,e={}){const n=this.segmentIndexAtLength(t,e);return n?this.getSegment(n):null}segmentIndexAt(t,e={}){if(0===this.segments.length)return null;const n=i.clamp(t,0,1),r=this.getOptions(e),s=this.length(r),o=s*n;return this.segmentIndexAtLength(o,r)}segmentIndexAtLength(t,e={}){const n=this.segments.length;if(0===n)return null;let r=!0;t<0&&(r=!1,t=-t);const i=this.getPrecision(e),s=this.getSubdivisions(e);let o=0,a=null;for(let l=0;l<n;l+=1){const e=r?l:n-1-l,c=this.segments[e],h=s[e],u=c.length({precision:i,subdivisions:h});if(c.isVisible){if(t<=o+u)return e;a=e}o+=u}return a}getSegmentSubdivisions(t={}){const e=this.getPrecision(t),n=[];for(let r=0,i=this.segments.length;r<i;r+=1){const t=this.segments[r],i=t.getSubdivisions({precision:e});n.push(i)}return n}updateSubpathStartSegment(t){let e=t.previousSegment,n=t;while(n&&!n.isSubpathStart)n.subpathStartSegment=null!=e?e.subpathStartSegment:null,e=n,n=n.nextSegment}prepareSegment(t,e,n){t.previousSegment=e,t.nextSegment=n,null!=e&&(e.nextSegment=t),null!=n&&(n.previousSegment=t);let r=t;return t.isSubpathStart&&(t.subpathStartSegment=t,r=n),null!=r&&this.updateSubpathStartSegment(r),t}closestPoint(t,e={}){const n=this.closestPointT(t,e);return n?this.pointAtT(n):null}closestPointLength(t,e={}){const n=this.getOptions(e),r=this.closestPointT(t,n);return r?this.lengthAtT(r,n):0}closestPointNormalizedLength(t,e={}){const n=this.getOptions(e),r=this.closestPointLength(t,n);if(0===r)return 0;const i=this.length(n);return 0===i?0:r/i}closestPointT(t,e={}){if(0===this.segments.length)return null;const n=this.getPrecision(e),r=this.getSubdivisions(e);let s,o=1/0;for(let a=0,l=this.segments.length;a<l;a+=1){const e=this.segments[a],l=r[a];if(e.isVisible){const r=e.closestPointT(t,{precision:n,subdivisions:l}),c=e.pointAtT(r),h=i.squaredLength(c,t);h<o&&(s={segmentIndex:a,value:r},o=h)}}return s||{segmentIndex:this.segments.length-1,value:1}}closestPointTangent(t,e={}){if(0===this.segments.length)return null;const n=this.getPrecision(e),r=this.getSubdivisions(e);let s,o=1/0;for(let a=0,l=this.segments.length;a<l;a+=1){const e=this.segments[a],l=r[a];if(e.isDifferentiable()){const r=e.closestPointT(t,{precision:n,subdivisions:l}),a=e.pointAtT(r),c=i.squaredLength(a,t);c<o&&(s=e.tangentAtT(r),o=c)}}return s||null}containsPoint(t,e={}){const n=this.toPolylines(e);if(!n)return!1;let r=0;for(let i=0,s=n.length;i<s;i+=1){const e=n[i];e.containsPoint(t)&&(r+=1)}return r%2===1}pointAt(t,e={}){if(0===this.segments.length)return null;if(t<=0)return this.start.clone();if(t>=1)return this.end.clone();const n=this.getOptions(e),r=this.length(n),i=r*t;return this.pointAtLength(i,n)}pointAtLength(t,e={}){if(0===this.segments.length)return null;if(0===t)return this.start.clone();let n=!0;t<0&&(n=!1,t=-t);const r=this.getPrecision(e),i=this.getSubdivisions(e);let s,o=0;for(let l=0,c=this.segments.length;l<c;l+=1){const e=n?l:c-1-l,a=this.segments[e],h=i[e],u=a.length({precision:r,subdivisions:h});if(a.isVisible){if(t<=o+u)return a.pointAtLength((n?1:-1)*(t-o),{precision:r,subdivisions:h});s=a}o+=u}if(s)return n?s.end:s.start;const a=this.segments[this.segments.length-1];return a.end.clone()}pointAtT(t){const e=this.segments,n=e.length;if(0===n)return null;const r=t.segmentIndex;if(r<0)return e[0].pointAtT(0);if(r>=n)return e[n-1].pointAtT(1);const s=i.clamp(t.value,0,1);return e[r].pointAtT(s)}divideAt(t,e={}){if(0===this.segments.length)return null;const n=i.clamp(t,0,1),r=this.getOptions(e),s=this.length(r),o=s*n;return this.divideAtLength(o,r)}divideAtLength(t,e={}){if(0===this.segments.length)return null;let n=!0;t<0&&(n=!1,t=-t);const r=this.getPrecision(e),i=this.getSubdivisions(e);let s,o,a,l,c,h=0;for(let w=0,x=this.segments.length;w<x;w+=1){const e=n?w:x-1-w,c=this.getSegment(e),u=i[e],d={precision:r,subdivisions:u},g=c.length(d);if(c.isDifferentiable()&&(a=c,l=e,t<=h+g)){o=e,s=c.divideAtLength((n?1:-1)*(t-h),d);break}h+=g}if(!a)return null;s||(o=l,c=n?1:0,s=a.divideAtT(c));const u=this.clone(),d=o;u.replaceSegment(d,s);const g=d;let f=d+1,p=d+2;s[0].isDifferentiable()||(u.removeSegment(g),f-=1,p-=1);const m=u.getSegment(f).start;u.insertSegment(f,k.createSegment("M",m)),p+=1,s[1].isDifferentiable()||(u.removeSegment(p-1),p-=1);const y=p-g-1;for(let w=p,x=u.segments.length;w<x;w+=1){const t=this.getSegment(w-y),e=u.getSegment(w);if("Z"===e.type&&!t.subpathStartSegment.end.equals(e.subpathStartSegment.end)){const e=k.createSegment("L",t.end);u.replaceSegment(w,e)}}const b=new k(u.segments.slice(0,f)),v=new k(u.segments.slice(f));return[b,v]}intersectsWithLine(t,e={}){const n=this.toPolylines(e);if(null==n)return null;let r=null;for(let i=0,s=n.length;i<s;i+=1){const e=n[i],s=t.intersect(e);s&&(null==r&&(r=[]),Array.isArray(s)?r.push(...s):r.push(s))}return r}isDifferentiable(){for(let t=0,e=this.segments.length;t<e;t+=1){const e=this.segments[t];if(e.isDifferentiable())return!0}return!1}isValid(){const t=this.segments,e=0===t.length||"M"===t[0].type;return e}length(t={}){if(0===this.segments.length)return 0;const e=this.getSubdivisions(t);let n=0;for(let r=0,i=this.segments.length;r<i;r+=1){const t=this.segments[r],i=e[r];n+=t.length({subdivisions:i})}return n}lengthAtT(t,e={}){const n=this.segments.length;if(0===n)return 0;let r=t.segmentIndex;if(r<0)return 0;let s=i.clamp(t.value,0,1);r>=n&&(r=n-1,s=1);const o=this.getPrecision(e),a=this.getSubdivisions(e);let l=0;for(let i=0;i<r;i+=1){const t=this.segments[i],e=a[i];l+=t.length({precision:o,subdivisions:e})}const c=this.segments[r],h=a[r];return l+=c.lengthAtT(s,{precision:o,subdivisions:h}),l}tangentAt(t,e={}){if(0===this.segments.length)return null;const n=i.clamp(t,0,1),r=this.getOptions(e),s=this.length(r),o=s*n;return this.tangentAtLength(o,r)}tangentAtLength(t,e={}){if(0===this.segments.length)return null;let n=!0;t<0&&(n=!1,t=-t);const r=this.getPrecision(e),i=this.getSubdivisions(e);let s,o=0;for(let a=0,l=this.segments.length;a<l;a+=1){const e=n?a:l-1-a,c=this.segments[e],h=i[e],u=c.length({precision:r,subdivisions:h});if(c.isDifferentiable()){if(t<=o+u)return c.tangentAtLength((n?1:-1)*(t-o),{precision:r,subdivisions:h});s=c}o+=u}if(s){const t=n?1:0;return s.tangentAtT(t)}return null}tangentAtT(t){const e=this.segments.length;if(0===e)return null;const n=t.segmentIndex;if(n<0)return this.segments[0].tangentAtT(0);if(n>=e)return this.segments[e-1].tangentAtT(1);const r=i.clamp(t.value,0,1);return this.segments[n].tangentAtT(r)}getPrecision(t={}){return null==t.precision?this.PRECISION:t.precision}getSubdivisions(t={}){if(null==t.segmentSubdivisions){const e=this.getPrecision(t);return this.getSegmentSubdivisions({precision:e})}return t.segmentSubdivisions}getOptions(t={}){const e=this.getPrecision(t),n=this.getSubdivisions(t);return{precision:e,segmentSubdivisions:n}}toPoints(t={}){const e=this.segments,n=e.length;if(0===n)return null;const r=this.getSubdivisions(t),i=[];let s=[];for(let o=0;o<n;o+=1){const t=e[o];if(t.isVisible){const e=r[o];e.length>0?e.forEach(t=>s.push(t.start)):s.push(t.start)}else s.length>0&&(s.push(e[o-1].end),i.push(s),s=[])}return s.length>0&&(s.push(this.end),i.push(s)),i}toPolylines(t={}){const e=this.toPoints(t);return e?e.map(t=>new y(t)):null}scale(t,e,n){return this.segments.forEach(r=>r.scale(t,e,n)),this}rotate(t,e){return this.segments.forEach(n=>n.rotate(t,e)),this}translate(t,e){return"number"===typeof t?this.segments.forEach(n=>n.translate(t,e)):this.segments.forEach(e=>e.translate(t)),this}clone(){const t=new k;return this.segments.forEach(e=>t.appendSegment(e.clone())),t}equals(t){if(null==t)return!1;const e=this.segments,n=t.segments,r=e.length;if(n.length!==r)return!1;for(let i=0;i<r;i+=1){const t=e[i],r=n[i];if(t.type!==r.type||!t.equals(r))return!1}return!0}toJSON(){return this.segments.map(t=>t.toJSON())}serialize(){if(!this.isValid())throw new Error("Invalid path segments.");return this.segments.map(t=>t.serialize()).join(" ")}toString(){return this.serialize()}}(function(t){function e(e){return null!=e&&e instanceof t}t.isPath=e})(k||(k={})),function(t){function e(e){if(!e)return new t;const r=new t,i=/(?:[a-zA-Z] *)(?:(?:-?\d+(?:\.\d+)?(?:e[-+]?\d+)? *,? *)|(?:-?\.\d+ *,? *))+|(?:[a-zA-Z] *)(?! |\d|-|\.)/g,s=t.normalize(e).match(i);if(null!=s)for(let t=0,o=s.length;t<o;t+=1){const e=s[t],i=/(?:[a-zA-Z])|(?:(?:-?\d+(?:\.\d+)?(?:e[-+]?\d+)?))|(?:(?:-?\.\d+))/g,o=e.match(i);if(null!=o){const t=o[0],e=o.slice(1).map(t=>+t),i=n.call(null,t,...e);r.appendSegment(i)}}return r}function n(t,...e){if("M"===t)return P.create.call(null,...e);if("L"===t)return w.create.call(null,...e);if("C"===t)return C.create.call(null,...e);if("z"===t||"Z"===t)return x.create();throw new Error(`Invalid path segment type "${t}"`)}t.parse=e,t.createSegment=n}(k||(k={})),function(t){t.normalize=j,t.isValid=u,t.drawArc=m,t.drawPoints=f,t.arcToCurves=p}(k||(k={}))},c6eb:function(t,e,n){"use strict";(function(t){var r=n("5ea3"),i="object"==typeof exports&&exports&&!exports.nodeType&&exports,s=i&&"object"==typeof t&&t&&!t.nodeType&&t,o=s&&s.exports===i,a=o&&r["a"].process,l=function(){try{var t=s&&s.require&&s.require("util").types;return t||a&&a.binding&&a.binding("util")}catch(e){}}();e["a"]=l}).call(this,n("dd40")(t))},dd40:function(t,e){t.exports=function(t){if(!t.webpackPolyfill){var e=Object.create(t);e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),Object.defineProperty(e,"exports",{enumerable:!0}),e.webpackPolyfill=1}return e}},dff1:function(t,e,n){"use strict";(function(t){var r=n("26ee"),i="object"==typeof exports&&exports&&!exports.nodeType&&exports,s=i&&"object"==typeof t&&t&&!t.nodeType&&t,o=s&&s.exports===i,a=o?r["a"].Buffer:void 0,l=a?a.allocUnsafe:void 0;function c(t,e){if(e)return t.slice();var n=t.length,r=l?l(n):new t.constructor(n);return t.copy(r),r}e["a"]=c}).call(this,n("dd40")(t))}}]);