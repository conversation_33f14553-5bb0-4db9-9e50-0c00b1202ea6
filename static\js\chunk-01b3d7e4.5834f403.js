(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-01b3d7e4"],{6347:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"菜单名称"},model:{value:e.listQuery.menuName,callback:function(t){e.$set(e.listQuery,"menuName",t)},expression:"listQuery.menuName"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"英文名称"},model:{value:e.listQuery.remark,callback:function(t){e.$set(e.listQuery,"remark",t)},expression:"listQuery.remark"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"请求地址"},model:{value:e.listQuery.url,callback:function(t){e.$set(e.listQuery,"url",t)},expression:"listQuery.url"}}),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.handleCreate}},[e._v(" 新增 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"data-table",attrs:{height:"100%","element-loading-text":"Loading",data:e.dataList,"tree-props":{children:"children"},size:"","row-key":"id","header-cell-style":{textAlign:"center"},"cell-style":{textAlign:"left"},"header-cell-class-name":"table-header-gray"}},[a("el-table-column",{attrs:{label:"菜单名称",align:"left",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.children?e._e():a("div",{staticClass:"el-table__expand-icon",staticStyle:{visibility:"hidden"}},[a("i",{staticClass:"el-icon-arrow-right"})]),a("svg-icon",{staticStyle:{"margin-right":"10px"},attrs:{"icon-class":t.row.icon}}),a("span",{class:{showName:!t.row._level&&!(t.row.children&&t.row.children.length)},staticStyle:{display:"inline-block","white-space":"wrap",overflow:"hidden","text-overflow":"ellipsis"},domProps:{innerHTML:e._s(t.row.menuName)}})]}}])}),a("el-table-column",{attrs:{label:"英文名称",align:"left",width:"240"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.remark))]}}])}),a("el-table-column",{attrs:{label:"类型",align:"left",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:"目录"===t.row.menuType?"danger":"success"}},[e._v(e._s(t.row.menuType))])]}}])}),a("el-table-column",{attrs:{label:"请求地址",align:"left",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.url))]}}])}),a("el-table-column",{attrs:{label:"排序",align:"left",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.orderNum))]}}])}),a("el-table-column",{attrs:{label:"是否可见",align:"left",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"0","inactive-value":"1","active-text":"显示","inactive-text":"隐藏"},on:{change:function(a){return e.statusChange(t.row)}},model:{value:t.row.visible,callback:function(a){e.$set(t.row,"visible",a)},expression:"scope.row.visible"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-edit"},on:{click:function(t){return e.handleUpdate(i)}}},[e._v(" 编辑 ")]),"deleted"!==i.status?a("el-button",{attrs:{size:"small",icon:"el-icon-delete",type:"danger"},on:{click:function(t){return e.warn(i)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),a("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"800px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[a("el-form-item",{attrs:{label:"菜单类型",prop:"menuType"}},[a("el-radio-group",{attrs:{disabled:"update"===e.dialogStatus},model:{value:e.temp.menuType,callback:function(t){e.$set(e.temp,"menuType",t)},expression:"temp.menuType"}},[a("el-radio",{attrs:{label:"目录"}},[e._v("目录")]),a("el-radio",{attrs:{label:"菜单"}},[e._v("菜单")])],1)],1),"菜单"===e.temp.menuType?a("el-form-item",{attrs:{label:"父菜单",prop:"temp.parentId"}},[a("el-select",{attrs:{placeholder:"请选择活动区域"},model:{value:e.temp.parentId,callback:function(t){e.$set(e.temp,"parentId",t)},expression:"temp.parentId"}},e._l(e.parentMenu,(function(e,t){return a("el-option",{key:"menu-"+t,attrs:{label:e.menuName,value:e.id}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"菜单名称",prop:"menuName"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"菜单名称"},model:{value:e.temp.menuName,callback:function(t){e.$set(e.temp,"menuName",t)},expression:"temp.menuName"}})],1),a("el-form-item",{attrs:{label:"英文名称",prop:"remark"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"英文名称"},model:{value:e.temp.remark,callback:function(t){e.$set(e.temp,"remark",t)},expression:"temp.remark"}})],1),a("el-form-item",{attrs:{label:"图标",prop:"icon"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"图标"},model:{value:e.temp.icon,callback:function(t){e.$set(e.temp,"icon",t)},expression:"temp.icon"}})],1),a("el-form-item",{attrs:{label:"请求地址",prop:"url"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"请求地址"},model:{value:e.temp.url,callback:function(t){e.$set(e.temp,"url",t)},expression:"temp.url"}})],1),a("el-form-item",{attrs:{label:"显示排序",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"140px"},attrs:{placeholder:"显示排序"},model:{value:e.temp.orderNum,callback:function(t){e.$set(e.temp,"orderNum",t)},expression:"temp.orderNum"}})],1),a("el-form-item",{attrs:{label:"菜单状态",prop:"visible"}},[a("el-radio-group",{model:{value:e.temp.visible,callback:function(t){e.$set(e.temp,"visible",t)},expression:"temp.visible"}},[a("el-radio",{attrs:{label:"0"}},[e._v("显示")]),a("el-radio",{attrs:{label:"1"}},[e._v("隐藏")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:e.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(t){e.dialogPluginVisible=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogPvVisible=!1}}},[e._v("Confirm")])],1)],1)],1)},l=[],n=(a("14d9"),a("b775"));function r(e){return Object(n["a"])({url:"/api/menu/remove?id="+e,method:"post"})}function s(e){return Object(n["a"])({url:"/api/menu/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/api/menu/add",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/api/menu/update",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/api/menu/updateStatus",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/api/menu/listParent",method:"get",params:e})}var p=a("67248"),m=a("98aa"),f={name:"DevEnvSetting",directives:{waves:p["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{dataList:[],listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:10,url:"",menuName:"",remark:""},pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑",create:"创建"},rules:{menuType:[{required:!0,message:"菜单类型不能为空",trigger:"blur"}],menuName:[{required:!0,message:"菜单名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"显示排序不能为空",trigger:"blur"}],icon:[{required:!0,message:"图标不能为空",trigger:"blur"}],visible:[{required:!0,message:"菜单状态不能为空",trigger:"blur"}]},parentMenu:[],temp:{id:void 0,menuName:"",parentId:"0",orderNum:"",menuType:"",visible:"",icon:""},visible:!0}},created:function(){this.fetchData(),this.listParent()},methods:{listParent:function(){var e=this;d({}).then((function(t){e.parentMenu=t.content.data}))},statusChange:function(e){var t=this;u({id:e.id,status:e.visible}).then((function(){t.fetchData(),t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))},fetchData:function(){var e=this;this.listLoading=!0,s(this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.dataList=m["a"](a.data,"parentId","children"),e.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,menuName:"",parentId:"",orderNum:"",menuType:"",visible:"",icon:""}},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&("目录"===e.temp.menuType&&(e.temp.parentId="0"),o(e.temp).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})})))}))},handleUpdate:function(e){var t=this;this.temp=Object.assign({},e),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a=Object.assign({},e.temp);c(a).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},warn:function(e){var t=this;this.$confirm(" 删除目录会同时级联删除所关联的菜单，是否确定删除目录或菜单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.handleDelete(e)})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},handleDelete:function(e){var t=this,a=[];a.push(e.id),r(e.id).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))}}},h=f,b=(a("9cc2"),a("2877")),v=Object(b["a"])(h,i,l,!1,null,"17579bbf",null);t["default"]=v.exports},67248:function(e,t,a){"use strict";a("8d41");var i="@@wavesContext";function l(e,t){function a(a){var i=Object.assign({},t.value),l=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),n=l.ele;if(n){n.style.position="relative",n.style.overflow="hidden";var r=n.getBoundingClientRect(),s=n.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":(s=document.createElement("span"),s.className="waves-ripple",s.style.height=s.style.width=Math.max(r.width,r.height)+"px",n.appendChild(s)),l.type){case"center":s.style.top=r.height/2-s.offsetHeight/2+"px",s.style.left=r.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(a.pageY-r.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(a.pageX-r.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=l.color,s.className="waves-ripple z-active",!1}}return e[i]?e[i].removeHandle=a:e[i]={removeHandle:a},a}var n={bind:function(e,t){e.addEventListener("click",l(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[i].removeHandle,!1),e.addEventListener("click",l(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[i].removeHandle,!1),e[i]=null,delete e[i]}},r=function(e){e.directive("waves",n)};window.Vue&&(window.waves=n,Vue.use(r)),n.install=r;t["a"]=n},"8d41":function(e,t,a){},"98aa":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("4de4"),a("d81d"),a("14d9"),a("fb6a"),a("e9c4"),a("b64b"),a("d3b7");function i(e,t,a){var i=e.filter((function(e){return 0===e[t]})),l=e.filter((function(e){return 0!==e[t]}));return n(i,l),i;function n(e,i){e.map((function(e){i.map((function(l,r){if(l[t]===e.id){var s=JSON.parse(JSON.stringify(i));s.slice(r,1),n([l],s),e[a]?e[a].push(l):e[a]=[l]}}))}))}}},"9cc2":function(e,t,a){"use strict";a("bdd61")},bdd61:function(e,t,a){}}]);