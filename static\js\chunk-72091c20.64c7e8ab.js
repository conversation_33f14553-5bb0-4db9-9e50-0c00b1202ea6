(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-72091c20","chunk-17a231b2"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(t,e,a){var n=o(),s=t-n,l=20,u=0;e="undefined"===typeof e?500:e;var c=function t(){u+=l;var o=Math.easeInOutQuad(u,n,s,e);r(o),u<e?i(t):a&&"function"===typeof a&&a()};c()}},"0f7c":function(t,e,a){},"2d839":function(t,e,a){"use strict";a("359e")},"359e":function(t,e,a){},"508e":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("div",{staticClass:"search-header"},[a("el-form",{attrs:{"label-suffix":"：","label-width":"138px",inline:""}},[a("el-form-item",{attrs:{label:"API名称","label-width":"auto"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"API名称"},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}})],1),a("el-form-item",{attrs:{label:"请求路径"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"请求路径"},model:{value:t.listQuery.path,callback:function(e){t.$set(t.listQuery,"path",e)},expression:"listQuery.path"}})],1)],1),a("div",{staticClass:"search-opt"},[a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item search",attrs:{type:"primary round"},on:{click:t.fetchData}},[t._v(" 搜索 ")]),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item reset-btn",attrs:{type:"primary round"},on:{click:t.handleReset}},[t._v(" 重置 ")])],1)],1),a("el-divider"),a("el-button",{staticClass:"filter-item opt",staticStyle:{"margin-left":"10px"},attrs:{type:"success"},on:{click:t.handleAdd}},[t._v(" 新增 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:t.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{label:"API名称",align:"left",width:"185"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name))]}}])}),a("el-table-column",{attrs:{label:"请求路径",align:"left",width:"500"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.path))]}}])}),a("el-table-column",{attrs:{label:"返回值类型",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.group_id))]}}])}),a("el-table-column",{attrs:{label:"描述",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.describe))]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"table-btn",on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v(" 编辑 ")]),a("span",{staticClass:"table-btn",on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(" 删除 ")])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.current,limit:t.listQuery.size},on:{"update:page":function(e){return t.$set(t.listQuery,"current",e)},"update:limit":function(e){return t.$set(t.listQuery,"size",e)},pagination:t.fetchData}}),a("el-dialog",{attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:t.rules,model:t.temp,"label-position":"right","label-width":"100px"}},[a("el-form-item",{attrs:{label:"资源名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"资源名称"},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}})],1),a("el-form-item",{attrs:{label:"资源地址",prop:"resourcePath"}},[a("el-input",{attrs:{placeholder:"资源地址"},model:{value:t.temp.resource_address,callback:function(e){t.$set(t.temp,"resource_address",e)},expression:"temp.resource_address"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){"create"===t.dialogStatus?t.createData():t.updateData()}}},[t._v("确定")])],1)],1),a("InfoDialog",{ref:"InfoDialog"})],1)},r=[],o=(a("b0c0"),a("8d6b")),n=a("67248"),s=a("333d"),l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{ref:"InfoDialog",attrs:{visible:t.dialogShow,title:"edit"==t.mode?"修改API":"新增API","destroy-on-close":!0,width:"800px","append-to-body":""},on:{"update:visible":function(e){t.dialogShow=e},close:t.close},scopedSlots:t._u([{key:"footer",fn:function(){return[a("div",{staticClass:"dialog-footer"},[a("el-button",{attrs:{type:"info"},on:{click:t.handleCancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleSubmit}},[t._v("确认")])],1)]},proxy:!0}])},[t.formData?a("el-form",{ref:"infoDialogForm",attrs:{"label-suffix":"：",model:t.formData,rules:t.rules,size:"medium","label-width":"140px"}},[a("el-form-item",{attrs:{label:"API名称",prop:"name"}},[a("el-input",{attrs:{autocomplete:"off"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),a("el-form-item",{attrs:{label:"请求路径",prop:"pathAfter"}},[a("div",{staticStyle:{display:"flex"}},[a("div",{staticStyle:{"flex-shrink":"0",color:"#c2d4e9",padding:"0 5px","border-radius":"4px","margin-right":"10px"}},[t._v(" /dynamic/ ")]),a("el-input",{attrs:{autocomplete:"off"},on:{input:function(){t.formData.path="/dynamic/"+t.formData.pathAfter}},model:{value:t.formData.pathAfter,callback:function(e){t.$set(t.formData,"pathAfter",e)},expression:"formData.pathAfter"}})],1)]),a("el-form-item",{attrs:{label:"返回值类型",prop:"group_id"}},[a("el-input",{attrs:{disabled:""},model:{value:t.formData.group_id,callback:function(e){t.$set(t.formData,"group_id",e)},expression:"formData.group_id"}})],1),a("el-form-item",{attrs:{label:"数据源",prop:"datasource_id"}},[t.sourceList?a("el-select",{staticStyle:{width:"310px"},attrs:{placeholder:"请选择"},model:{value:t.formData.datasource_id,callback:function(e){t.$set(t.formData,"datasource_id",e)},expression:"formData.datasource_id"}},t._l(t.sourceList,(function(t){return a("el-option",{key:"datasource_id_"+t.id,attrs:{label:t.datasourceName,value:t.id}})})),1):t._e()],1),a("el-form-item",{attrs:{label:"SQL语句",prop:"sql_text"}},[a("SqlEditor",{model:{value:t.formData.sql_text,callback:function(e){t.$set(t.formData,"sql_text",e)},expression:"formData.sql_text"}})],1),a("el-form-item",{attrs:{label:"请求参数",prop:"params"}},[a("el-input",{staticStyle:{"white-space":"pre"},attrs:{autosize:{minRows:2,maxRows:12},type:"textarea",placeholder:"{'name':'zhangsan'}"},model:{value:t.formData.params,callback:function(e){t.$set(t.formData,"params",e)},expression:"formData.params"}})],1),a("el-form-item",{attrs:{label:"描述",prop:"describe"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:4},placeholder:"请输入"},model:{value:t.formData.describe,callback:function(e){t.$set(t.formData,"describe",e)},expression:"formData.describe"}})],1)],1):t._e()],1)},u=[],c=(a("ac1f"),a("5319"),a("1bf5")),d=a("b252"),f=a("f656"),m=a("2ef0"),p=a.n(m),h={name:"ChangePassword",components:{SqlEditor:c["a"]},data:function(){return{rules:{name:[{required:!0,message:"不能为空",trigger:"blur"},Object(f["b"])()],pathAfter:[{required:!0,message:"不能为空",trigger:"blur"},Object(f["b"])()],group_id:[{required:!0,message:"不能为空",trigger:"blur"}],describe:[{required:!0,message:"不能为空",trigger:"blur"},Object(f["a"])()],datasource_id:[{required:!0,message:"不能为空",trigger:"blur"}],params:[{required:!0,message:"不能为空",trigger:"blur"},Object(f["a"])()],sql_text:[{required:!0,message:"不能为空",trigger:"blur"}]},dialogShow:!1,onClose:null,onSubmit:null,mode:"edit",infoTitle:"详细信息",defaultFormData:{id:"",name:"",path:"",pathAfter:"",group_id:"json",describe:"",datasource_id:"",params:"",sql_text:""},formData:null,sourceList:null}},created:function(){this.formData=Object.assign({},this.defaultFormData)},methods:{init:function(){var t=this;Object(d["c"])().then((function(e){t.sourceList=e.content.data}))},openDialog:function(t){var e=this,a=t.formData,i=void 0===a?{}:a,r=t.infoTitle,o=void 0===r?"":r,n=t.mode,s=void 0===n?"edit":n,l=t.onSubmit,u=void 0===l?null:l,c=t.onClose,d=void 0===c?null:c;this.mode=s,this.onSubmit=u,this.onClose=d,this.infoTitle=o,this.formData=null,setTimeout((function(){e.formData=Object.assign({},e.defaultFormData,i),e.formData.path?e.formData.pathAfter=e.formData.path.replace(/\/dynamic\//,""):(e.formData.pathAfter="",e.formData.path="/dynamic/"),console.log(e.formData,"this.formData")}),200),this.dialogShow=!0,this.init()},handleSubmit:function(){var t=this;this.$refs["infoDialogForm"].validate((function(e){if(e){var a,i=p.a.cloneDeep(t.formData);return delete i.pathAfter,null===(a=t.onSubmit)||void 0===a||a.call(t,i),t.dialogShow=!1,!0}return t.$message.error("填写异常，请仔细检查"),!1}))},handleCancel:function(){this.dialogShow=!1},close:function(){var t;null===(t=this.onClose)||void 0===t||t.call(this,this.formData)}}},b=h,g=(a("2d839"),a("2877")),v=Object(g["a"])(b,l,u,!1,null,"bed32be6",null),w=v.exports,y={name:"User",components:{Pagination:s["a"],InfoDialog:w},directives:{waves:n["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{current:1,size:10,name:"",path:""},roles:["ROLE_USER","ROLE_ADMIN"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"修改API",create:"新增API"},rules:{role:[{required:!0,message:"role is required",trigger:"change"}],name:[{required:!0,message:"name is required",trigger:"blur"}],password:[{required:!1,message:"password is required",trigger:"blur"}]},temp:{id:void 0,role:"",name:"",password:"",permission:"",resource_address:""},resetTemp:function(){this.temp=this.$options.data().temp}}},created:function(){this.fetchData()},methods:{handleReset:function(){this.listQuery.name="",this.listQuery.path="",this.fetchData()},fetchData:function(){var t=this;this.listLoading=!0,o["a"](this.listQuery).then((function(e){var a=e.content;t.total=a.recordsTotal,t.list=a.data,t.listLoading=!1}))},handleAdd:function(){var t=this;this.$refs["InfoDialog"].openDialog({onSubmit:function(e){Object(d["a"])(e).then((function(){t.$message.success("新增成功"),t.fetchData()}))},mode:"add"})},handleUpdate:function(t){var e=this;this.$refs["InfoDialog"].openDialog({formData:t,onSubmit:function(t){Object(d["d"])(t).then((function(){e.$message.success("更新成功"),e.fetchData()}))}})},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a={name:t.temp.name,resource_address:t.temp.resource_address};o["b"](a).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}}))},handleVisit:function(t){window.open(t.resource_address)},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a={id:t.temp.id,name:t.temp.name,resource_address:t.temp.resource_address},i=Object.assign({},a);o["d"](i).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;this.$confirm("确定删除这条数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){o["c"](t.id).then((function(t){e.fetchData(),e.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))})).catch((function(){}))}}},D=y,_=Object(g["a"])(D,i,r,!1,null,null,null);e["default"]=_.exports},67248:function(t,e,a){"use strict";a("8d41");var i="@@wavesContext";function r(t,e){function a(a){var i=Object.assign({},e.value),r=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),o=r.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var n=o.getBoundingClientRect(),s=o.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":(s=document.createElement("span"),s.className="waves-ripple",s.style.height=s.style.width=Math.max(n.width,n.height)+"px",o.appendChild(s)),r.type){case"center":s.style.top=n.height/2-s.offsetHeight/2+"px",s.style.left=n.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(a.pageY-n.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(a.pageX-n.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=r.color,s.className="waves-ripple z-active",!1}}return t[i]?t[i].removeHandle=a:t[i]={removeHandle:a},a}var o={bind:function(t,e){t.addEventListener("click",r(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[i].removeHandle,!1),t.addEventListener("click",r(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[i].removeHandle,!1),t[i]=null,delete t[i]}},n=function(t){t.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(n)),o.install=n;e["a"]=o},"8d41":function(t,e,a){},"8d6b":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return s}));var i=a("b775");function r(t){return Object(i["a"])({url:"/api/base/resource/add",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/api/base/resource/update",method:"post",data:t})}function n(t){return Object(i["a"])({url:"/api/apiConfig/remove?id="+t,method:"post"})}function s(t){return Object(i["a"])({url:"/api/apiConfig/list",method:"get",params:t})}},a7be:function(t,e,a){},b252:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"d",(function(){return o})),a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s}));var i=a("b775");function r(t){return Object(i["a"])({url:"/api/apiConfig/add",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/api/apiConfig/update",method:"post",data:t})}function n(t){return Object(i["a"])({url:"/api/apiConfig/execute",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/api/apiConfig/listDataSourceName",method:"get",params:t})}},f656:function(t,e,a){"use strict";function i(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function r(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return r}))}}]);