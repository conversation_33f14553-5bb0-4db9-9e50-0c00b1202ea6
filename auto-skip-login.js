// 自动跳过登录脚本 - 直接进入主页面
// 这个脚本会在页面加载时立即执行，跳过任何登录验证

(function() {
    'use strict';
    
    console.log('🚀 启动自动跳过登录模式');
    
    // 立即设置登录状态
    function setLoginState() {
        const mockUser = {
            username: 'admin',
            token: 'mock-token-admin-123456',
            roles: ['admin'],
            name: '管理员',
            avatar: '/user.png'
        };
        
        // 设置各种可能的存储方式
        localStorage.setItem('token', mockUser.token);
        localStorage.setItem('access_token', mockUser.token);
        localStorage.setItem('authToken', mockUser.token);
        localStorage.setItem('user_token', mockUser.token);
        localStorage.setItem('roles', JSON.stringify(mockUser.roles));
        localStorage.setItem('username', mockUser.username);
        localStorage.setItem('user', JSON.stringify(mockUser));
        localStorage.setItem('userInfo', JSON.stringify(mockUser));
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('loginStatus', 'true');
        localStorage.setItem('menuIsChecked', JSON.stringify([]));
        
        // 也设置到sessionStorage
        sessionStorage.setItem('token', mockUser.token);
        sessionStorage.setItem('user', JSON.stringify(mockUser));
        sessionStorage.setItem('isLoggedIn', 'true');
        
        console.log('✅ 已设置登录状态:', mockUser.username);
    }
    
    // 立即设置登录状态
    setLoginState();
    
    // 拦截所有可能的登录检查
    function interceptLoginChecks() {
        // 拦截fetch请求
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                // 如果是登录相关的请求，直接返回成功
                if (typeof url === 'string' && (url.includes('/login') || url.includes('/auth'))) {
                    console.log('🔄 拦截登录请求:', url);
                    return Promise.resolve(new Response(JSON.stringify({
                        code: 200,
                        message: '登录成功',
                        data: {
                            token: 'mock-token-admin-123456',
                            user: {
                                username: 'admin',
                                name: '管理员',
                                roles: ['admin']
                            }
                        }
                    }), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json' }
                    }));
                }
                return originalFetch.apply(this, arguments);
            };
        }
        
        // 拦截XMLHttpRequest
        if (window.XMLHttpRequest) {
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                let requestUrl = '';
                
                xhr.open = function(method, url, ...args) {
                    requestUrl = url;
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                xhr.send = function(data) {
                    if (requestUrl.includes('/login') || requestUrl.includes('/auth')) {
                        console.log('🔄 拦截XHR登录请求:', requestUrl);
                        setTimeout(() => {
                            Object.defineProperty(xhr, 'status', { value: 200 });
                            Object.defineProperty(xhr, 'responseText', { value: JSON.stringify({
                                code: 200,
                                message: '登录成功',
                                data: {
                                    token: 'mock-token-admin-123456',
                                    user: {
                                        username: 'admin',
                                        name: '管理员',
                                        roles: ['admin']
                                    }
                                }
                            })});
                            Object.defineProperty(xhr, 'readyState', { value: 4 });
                            
                            if (xhr.onreadystatechange) {
                                xhr.onreadystatechange();
                            }
                        }, 100);
                        return;
                    }
                    return originalSend.apply(this, arguments);
                };
                
                return xhr;
            };
        }
    }
    
    // 立即拦截登录检查
    interceptLoginChecks();
    
    // 处理路由跳转
    function handleRouting() {
        // 如果当前在登录页面，立即跳转
        function redirectFromLogin() {
            const hash = window.location.hash;
            const pathname = window.location.pathname;
            
            if (hash.includes('login') || pathname.includes('login')) {
                console.log('🔄 检测到登录页面，立即跳转到主页');
                if (hash) {
                    window.location.hash = '#/';
                } else {
                    window.location.href = '/';
                }
            }
        }
        
        // 立即检查
        redirectFromLogin();
        
        // 监听路由变化
        if (window.addEventListener) {
            window.addEventListener('hashchange', redirectFromLogin);
            window.addEventListener('popstate', redirectFromLogin);
        }
        
        // 拦截Vue Router（如果存在）
        setTimeout(() => {
            if (window.Vue && window.VueRouter) {
                const router = window.$router || (window.Vue.prototype.$router);
                if (router) {
                    const originalPush = router.push;
                    router.push = function(location) {
                        if (typeof location === 'string' && location.includes('login')) {
                            console.log('🔄 拦截Vue Router登录路由');
                            return originalPush.call(this, '/');
                        }
                        if (typeof location === 'object' && (location.path?.includes('login') || location.name?.includes('login'))) {
                            console.log('🔄 拦截Vue Router登录路由');
                            return originalPush.call(this, '/');
                        }
                        return originalPush.call(this, location);
                    };
                }
            }
        }, 1000);
    }
    
    // 处理路由
    handleRouting();
    
    // 页面加载完成后再次确保登录状态
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                setLoginState();
                console.log('🎉 自动跳过登录完成，已直接进入系统');
            }, 500);
        });
    } else {
        setTimeout(() => {
            setLoginState();
            console.log('🎉 自动跳过登录完成，已直接进入系统');
        }, 500);
    }
    
})();
