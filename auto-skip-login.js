// 自动跳过登录脚本 - 直接进入主页面
// 这个脚本会在页面加载时立即执行，跳过任何登录验证

(function() {
    'use strict';
    
    console.log('🚀 启动自动跳过登录模式');
    
    // 立即设置登录状态
    function setLoginState() {
        const mockUser = {
            username: 'admin',
            token: 'mock-token-admin-123456',
            roles: ['admin'],
            name: '管理员',
            avatar: '/user.png'
        };
        
        // 设置各种可能的存储方式
        localStorage.setItem('token', mockUser.token);
        localStorage.setItem('access_token', mockUser.token);
        localStorage.setItem('authToken', mockUser.token);
        localStorage.setItem('user_token', mockUser.token);
        localStorage.setItem('roles', JSON.stringify(mockUser.roles));
        localStorage.setItem('username', mockUser.username);
        localStorage.setItem('user', JSON.stringify(mockUser));
        localStorage.setItem('userInfo', JSON.stringify(mockUser));
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('loginStatus', 'true');
        localStorage.setItem('menuIsChecked', JSON.stringify([]));
        
        // 也设置到sessionStorage
        sessionStorage.setItem('token', mockUser.token);
        sessionStorage.setItem('user', JSON.stringify(mockUser));
        sessionStorage.setItem('isLoggedIn', 'true');
        
        console.log('✅ 已设置登录状态:', mockUser.username);
    }
    
    // 立即设置登录状态
    setLoginState();
    
    // 拦截所有可能的登录检查
    function interceptLoginChecks() {
        // 拦截fetch请求
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                // 如果是登录相关的请求，直接返回成功
                if (typeof url === 'string' && (url.includes('/login') || url.includes('/auth'))) {
                    console.log('🔄 拦截登录请求:', url);
                    return Promise.resolve(new Response(JSON.stringify({
                        code: 200,
                        message: '登录成功',
                        data: {
                            token: 'mock-token-admin-123456',
                            user: {
                                username: 'admin',
                                name: '管理员',
                                roles: ['admin']
                            }
                        }
                    }), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json' }
                    }));
                }
                return originalFetch.apply(this, arguments);
            };
        }
        
        // 拦截XMLHttpRequest
        if (window.XMLHttpRequest) {
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                let requestUrl = '';
                
                xhr.open = function(method, url, ...args) {
                    requestUrl = url;
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                xhr.send = function(data) {
                    if (requestUrl.includes('/login') || requestUrl.includes('/auth')) {
                        console.log('🔄 拦截XHR登录请求:', requestUrl);
                        setTimeout(() => {
                            Object.defineProperty(xhr, 'status', { value: 200 });
                            Object.defineProperty(xhr, 'responseText', { value: JSON.stringify({
                                code: 200,
                                message: '登录成功',
                                content: {
                                    data: 'mock-token-admin-123456',  // 应用期望 content.data 是token
                                    roles: ['admin', 'user']  // 应用期望 content.roles 是角色数组
                                }
                            })});
                            Object.defineProperty(xhr, 'readyState', { value: 4 });

                            if (xhr.onreadystatechange) {
                                xhr.onreadystatechange();
                            }
                        }, 100);
                        return;
                    }
                    return originalSend.apply(this, arguments);
                };
                
                return xhr;
            };
        }
    }
    
    // 立即拦截登录检查
    interceptLoginChecks();
    
    // 处理路由跳转
    function handleRouting() {
        // 如果当前在登录页面，立即跳转
        function redirectFromLogin() {
            const hash = window.location.hash;
            const pathname = window.location.pathname;
            
            if (hash.includes('login') || pathname.includes('login')) {
                console.log('🔄 检测到登录页面，立即跳转到主页');
                if (hash) {
                    window.location.hash = '#/';
                } else {
                    window.location.href = '/';
                }
            }
        }
        
        // 立即检查
        redirectFromLogin();
        
        // 监听路由变化
        if (window.addEventListener) {
            window.addEventListener('hashchange', redirectFromLogin);
            window.addEventListener('popstate', redirectFromLogin);
        }
        
        // 拦截Vue Router（如果存在）
        setTimeout(() => {
            if (window.Vue && window.VueRouter) {
                const router = window.$router || (window.Vue.prototype.$router);
                if (router) {
                    const originalPush = router.push;
                    router.push = function(location) {
                        if (typeof location === 'string' && location.includes('login')) {
                            console.log('🔄 拦截Vue Router登录路由');
                            return originalPush.call(this, '/');
                        }
                        if (typeof location === 'object' && (location.path?.includes('login') || location.name?.includes('login'))) {
                            console.log('🔄 拦截Vue Router登录路由');
                            return originalPush.call(this, '/');
                        }
                        return originalPush.call(this, location);
                    };
                }
            }
        }, 1000);
    }
    
    // 处理路由
    handleRouting();
    
    // 自动填充并提交登录表单
    function autoSubmitLogin() {
        // 查找登录表单元素
        const usernameInputs = document.querySelectorAll('input[type="text"], input[name*="user"], input[placeholder*="用户"], input[placeholder*="账号"]');
        const passwordInputs = document.querySelectorAll('input[type="password"], input[name*="pass"], input[placeholder*="密码"]');
        const submitButtons = document.querySelectorAll('button[type="submit"], button:contains("登录"), button:contains("确定"), .login-btn, .submit-btn');

        console.log('🔍 查找登录表单元素...');
        console.log('用户名输入框:', usernameInputs.length);
        console.log('密码输入框:', passwordInputs.length);
        console.log('提交按钮:', submitButtons.length);

        if (usernameInputs.length > 0 && passwordInputs.length > 0) {
            console.log('📝 找到登录表单，开始自动填充...');

            // 填充用户名
            const usernameInput = usernameInputs[0];
            usernameInput.value = 'admin';
            usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('change', { bubbles: true }));

            // 填充密码
            const passwordInput = passwordInputs[0];
            passwordInput.value = '123456';
            passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('change', { bubbles: true }));

            console.log('✅ 已填充登录信息: admin / 123456');

            // 查找并点击提交按钮
            setTimeout(() => {
                let submitButton = null;

                // 尝试多种方式查找提交按钮
                const buttonSelectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    '.el-button--primary',
                    '.login-btn',
                    '.submit-btn',
                    'button:contains("登录")',
                    'button:contains("确定")',
                    'button:contains("提交")'
                ];

                for (const selector of buttonSelectors) {
                    const buttons = document.querySelectorAll(selector);
                    if (buttons.length > 0) {
                        submitButton = buttons[0];
                        break;
                    }
                }

                // 如果没找到特定按钮，查找所有按钮
                if (!submitButton) {
                    const allButtons = document.querySelectorAll('button');
                    for (const btn of allButtons) {
                        const text = btn.textContent || btn.innerText || '';
                        if (text.includes('登录') || text.includes('确定') || text.includes('提交')) {
                            submitButton = btn;
                            break;
                        }
                    }
                }

                if (submitButton) {
                    console.log('🚀 找到提交按钮，自动提交登录...');
                    submitButton.click();
                } else {
                    console.log('⚠️ 未找到提交按钮，尝试表单提交...');
                    // 尝试直接提交表单
                    const forms = document.querySelectorAll('form');
                    if (forms.length > 0) {
                        forms[0].submit();
                    }
                }
            }, 200);
        } else {
            console.log('⚠️ 未找到登录表单元素');
        }
    }

    // 强制跳转到主页面
    function forceRedirectToMain() {
        console.log('🔄 强制跳转到主页面...');

        // 尝试多种跳转方式
        const redirectMethods = [
            () => { window.location.hash = '#/dashboard'; },
            () => { window.location.hash = '#/home'; },
            () => { window.location.hash = '#/main'; },
            () => { window.location.hash = '#/'; },
            () => { window.location.href = '/dashboard'; },
            () => { window.location.href = '/home'; },
            () => { window.location.href = '/main'; },
            () => { window.location.href = '/'; }
        ];

        let redirectIndex = 0;
        const tryRedirect = () => {
            if (redirectIndex < redirectMethods.length) {
                try {
                    redirectMethods[redirectIndex]();
                    console.log(`✅ 尝试跳转方法 ${redirectIndex + 1}`);
                } catch (e) {
                    console.log(`❌ 跳转方法 ${redirectIndex + 1} 失败:`, e);
                }
                redirectIndex++;
                setTimeout(tryRedirect, 1000);
            }
        };

        tryRedirect();
    }

    // 页面加载完成后的处理
    function onPageReady() {
        setLoginState();

        // 等待一下让页面渲染完成
        setTimeout(() => {
            // 检查是否在登录页面
            const isLoginPage = document.body.innerHTML.includes('登录') ||
                               document.body.innerHTML.includes('密码') ||
                               document.querySelector('input[type="password"]') !== null;

            if (isLoginPage) {
                console.log('🔍 检测到登录页面，开始自动登录流程...');
                autoSubmitLogin();

                // 如果5秒后还在登录页面，强制跳转
                setTimeout(() => {
                    const stillLoginPage = document.querySelector('input[type="password"]') !== null;
                    if (stillLoginPage) {
                        console.log('⚠️ 自动登录可能失败，强制跳转...');
                        forceRedirectToMain();
                    }
                }, 5000);
            } else {
                console.log('✅ 已在主页面，无需登录');
            }

            console.log('🎉 自动跳过登录完成，已直接进入系统');
        }, 1000);
    }

    // 页面加载完成后再次确保登录状态
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', onPageReady);
    } else {
        onPageReady();
    }
    
})();
