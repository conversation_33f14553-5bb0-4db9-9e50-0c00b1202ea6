(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-31859dd0"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function s(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(e,t,a){var r=o(),l=e-r,i=20,c=0;t="undefined"===typeof t?500:t;var u=function e(){c+=i;var o=Math.easeInOutQuad(c,r,l,t);s(o),c<t?n(e):a&&"function"===typeof a&&a()};u()}},67248:function(e,t,a){"use strict";a("8d41");var n="@@wavesContext";function s(e,t){function a(a){var n=Object.assign({},t.value),s=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},n),o=s.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var r=o.getBoundingClientRect(),l=o.querySelector(".waves-ripple");switch(l?l.className="waves-ripple":(l=document.createElement("span"),l.className="waves-ripple",l.style.height=l.style.width=Math.max(r.width,r.height)+"px",o.appendChild(l)),s.type){case"center":l.style.top=r.height/2-l.offsetHeight/2+"px",l.style.left=r.width/2-l.offsetWidth/2+"px";break;default:l.style.top=(a.pageY-r.top-l.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",l.style.left=(a.pageX-r.left-l.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return l.style.backgroundColor=s.color,l.className="waves-ripple z-active",!1}}return e[n]?e[n].removeHandle=a:e[n]={removeHandle:a},a}var o={bind:function(e,t){e.addEventListener("click",s(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[n].removeHandle,!1),e.addEventListener("click",s(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[n].removeHandle,!1),e[n]=null,delete e[n]}},r=function(e){e.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(r)),o.install=r;t["a"]=o},"731f":function(e,t,a){},"8d41":function(e,t,a){},e30f:function(e,t,a){"use strict";a("731f")},f6d3:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"登录账号"},model:{value:e.listQuery.username,callback:function(t){e.$set(e.listQuery,"username",t)},expression:"listQuery.username"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"用户名称"},model:{value:e.listQuery.aliasname,callback:function(t){e.$set(e.listQuery,"aliasname",t)},expression:"listQuery.aliasname"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"角色"},model:{value:e.listQuery.roleName,callback:function(t){e.$set(e.listQuery,"roleName",t)},expression:"listQuery.roleName"}}),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v("搜索")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v(" 新增 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"left",label:"序号",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),a("el-table-column",{attrs:{label:"登录账号",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.username))]}}])}),a("el-table-column",{attrs:{label:"用户名称",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.aliasname))]}}])}),a("el-table-column",{attrs:{label:"用户角色",align:"left",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.roleName))]}}])}),a("el-table-column",{attrs:{label:"电子邮箱",align:"left",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.phone))]}}])}),a("el-table-column",{attrs:{label:"用户状态",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"0","inactive-value":"1","active-text":"启用","inactive-text":"停用"},on:{change:function(a){return e.statusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.createtime))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-edit"},on:{click:function(t){return e.handleUpdate(n)}}},[e._v("编辑")]),"deleted"!==n.status?a("el-button",{attrs:{size:"small",icon:"el-icon-delete",type:"danger"},on:{click:function(t){return e.handleDelete(n)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}})],1)},s=[],o=a("c5fb"),r=a("67248"),l=a("333d"),i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"base-widget-demo"},[e.options.observer?a("BaseWidget",{ref:"baseWidget",attrs:{"data-example":e.dataExample,"default-data":e.defaultData,options:e.options,"on-submit":e.onSubmit,mode:e.mode},scopedSlots:e._u([{key:"slot-role",fn:function(t){return[e.roleList?a("el-select",{attrs:{placeholder:"请选择"},on:{change:function(){var a=e.roleList.find((function(e){return e.role_key===t.slotScope.formData.role}));a&&(t.slotScope.formData.roleName=a.role_name)}},model:{value:t.slotScope.formData.role,callback:function(a){e.$set(t.slotScope.formData,"role",a)},expression:"scope.slotScope.formData.role"}},e._l(e.roleList,(function(e){return a("el-option",{key:e.role_key,attrs:{label:e.role_name,value:e.role_key}})})),1):e._e()]}},{key:"slot-password",fn:function(t){return[a("span",{staticStyle:{display:"flex"}},[a("el-input",{key:e.passwordType,ref:"password",attrs:{type:e.passwordType,placeholder:"请输入密码",name:"password"},model:{value:t.slotScope.formData.password,callback:function(a){e.$set(t.slotScope.formData,"password",a)},expression:"scope.slotScope.formData.password"}}),a("span",{staticStyle:{margin:"0 5px 0 10px"},on:{click:function(t){e.passwordType="password"===e.passwordType?"":"password"}}},[a("svg-icon",{attrs:{"icon-class":"password"===e.passwordType?"eye":"eye-open"}})],1)],1)]}},{key:"slot-repeatpassword",fn:function(t){return[a("span",{staticStyle:{display:"flex"}},[a("el-input",{key:e.passwordType,ref:"password",attrs:{type:e.passwordType,placeholder:"请重新输入密码",name:"password"},model:{value:t.slotScope.formData.repeatPassword,callback:function(a){e.$set(t.slotScope.formData,"repeatPassword",a)},expression:"scope.slotScope.formData.repeatPassword"}}),a("span",{staticStyle:{margin:"0 5px 0 10px"},on:{click:function(t){e.passwordType="password"===e.passwordType?"":"password"}}},[a("svg-icon",{attrs:{"icon-class":"password"===e.passwordType?"eye":"eye-open"}})],1)],1)]}},{key:"slot-status",fn:function(t){return["edit"===e.mode?a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"0","inactive-value":"1","active-text":"启用","inactive-text":"停用"},model:{value:t.slotScope.formData.status,callback:function(a){e.$set(t.slotScope.formData,"status",a)},expression:"scope.slotScope.formData.status"}}):a("span",[e._v(" "+e._s(0===t.slotScope.formData.status?"启用":"停用")+" ")])]}}],null,!1,2766869592)}):e._e()],1)},c=[],u=a("c7eb"),d=a("1da1"),p=(a("d9e2"),a("eeb0")),f=a("57e7"),m=a("1de4"),w={components:{BaseWidget:p["a"]},mixins:[f["a"]],props:{defaultData:{type:Object,default:null},defaultOptions:{type:Object,default:null},mode:{type:String,default:"edit"}},data:function(){var e=this;return{passwordType:"password",roleList:null,dataExample:{id:0,username:"",password:"",repeatPassword:"",aliasname:"",status:"0",phone:"",role:"",roleName:""},options:{observer:null,afterInit:null,rules:{username:[{required:!0,validator:function(){var e=Object(d["a"])(Object(u["a"])().mark((function e(t,a,n){return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a.length>=4?n():n(new Error("登录名称需要大于等于4位"));case 1:case"end":return e.stop()}}),e)})));function t(t,a,n){return e.apply(this,arguments)}return t}(),trigger:"blur"}],aliasname:[{required:!0,message:"不能为空",trigger:"blur"}],status:[{required:!0,message:"不能为空",trigger:"blur"}],role:[{required:!0,message:"必须选择",trigger:"blur"}],password:[{required:!0,validator:function(){var e=Object(d["a"])(Object(u["a"])().mark((function e(t,a,n){return Object(u["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a.length>=6?n():n(new Error("密码需要大于等于6位"));case 1:case"end":return e.stop()}}),e)})));function t(t,a,n){return e.apply(this,arguments)}return t}(),trigger:"blur"}],repeatPassword:[{required:!0,validator:function(){var t=Object(d["a"])(Object(u["a"])().mark((function t(a,n,s){return Object(u["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n.length>=6?n===e.$refs["baseWidget"].formData.password?s():s(new Error("两次输入不一致")):s(new Error("密码需要大于等于6位"));case 1:case"end":return t.stop()}}),t)})));function a(e,a,n){return t.apply(this,arguments)}return a}(),trigger:"blur"}],phone:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}]},formItems:[{label:"登录名称",prop:"username",type:"input",options:{disabled:function(e){return!!e.id}}},{label:"账号状态",prop:"status",type:"slot",options:{typeFun:function(){return"slot"}}},{label:"密码",prop:"password",type:"slot",options:{ifFun:function(t){return"edit"===e.mode}}},{label:"重复密码",prop:"repeatPassword",type:"slot",options:{ifFun:function(t){return"edit"===e.mode}}},{label:"用户名称",prop:"aliasname",type:"input",options:{}},{label:"角色",prop:"role",type:"slot",options:{}},{label:"电子邮箱",prop:"phone",type:"input",options:{}}]}}},mounted:function(){this.init()},methods:{init:function(){var e=this;m["e"]({}).then((function(t){e.roleList=t.content.data,console.log("res = ",t)}))},onSubmit:function(e){}}},h=w,y=(a("e30f"),a("2877")),v=Object(y["a"])(h,i,c,!1,null,"86402d54",null),g=v.exports,b={name:"User",components:{Pagination:l["a"]},directives:{waves:r["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,listQuery:{current:1,size:10,total:0,username:"",aliasname:"",roleName:""}}},created:function(){this.fetchData()},methods:{statusChange:function(e){var t=this;o["e"]({userid:e.id,status:e.status}).then((function(){t.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})}))},fetchData:function(){var e=this;this.listLoading=!0,o["c"](this.listQuery).then((function(t){var a=t.content;e.listQuery.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},handleView:function(e){this.$dialog.show("详情",g,{area:"600px"},{defaultData:e,mode:"preview"})},handleAdd:function(){var e=this;this.$dialog.show("新增用户",g,{area:"600px"},{mode:"edit"}).then((function(t){o["a"](t).then((function(){e.fetchData(),e.$notify({title:"成功",message:"新增成功",type:"success",duration:2e3})}))}))},handleUpdate:function(e){var t=this;this.$dialog.show("编辑用户",g,{area:"600px"},{defaultData:e,mode:"edit"}).then((function(e){o["f"](e).then((function(){t.fetchData(),t.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})}))}))},handleDelete:function(e){var t=this;this.$confirm("是否删除该数据？","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){o["b"](e.id).then((function(e){t.fetchData(),t.$notify({title:"成功",message:"删除成功",type:"success",duration:2e3})}))}))}}},x=b,k=Object(y["a"])(x,n,s,!1,null,null,null);t["default"]=k.exports}}]);