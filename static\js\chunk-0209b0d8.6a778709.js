(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0209b0d8"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,a){var s=l(),o=t-s,r=20,c=0;e="undefined"===typeof e?500:e;var u=function t(){c+=r;var l=Math.easeInOutQuad(c,s,o,e);n(l),c<e?i(t):a&&"function"===typeof a&&a()};u()}},"4c95":function(t,e,a){"use strict";a.r(e);var i={};a.r(i),a.d(i,"list",(function(){return o})),a.d(i,"updated",(function(){return r})),a.d(i,"test",(function(){return c})),a.d(i,"created",(function(){return u})),a.d(i,"deleted",(function(){return d})),a.d(i,"getDevEnvSettingList",(function(){return p}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("div",{staticClass:"search-header"},[a("el-form",{attrs:{"label-suffix":"：","label-width":"138px",inline:""}},[a("el-form-item",{attrs:{label:"环境名称","label-width":"auto"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"环境名称"},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}})],1),a("el-form-item",{attrs:{label:"访问地址"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"访问地址"},model:{value:t.listQuery.propValue,callback:function(e){t.$set(t.listQuery,"propValue",e)},expression:"listQuery.propValue"}})],1)],1),a("div",{staticClass:"search-opt"},[a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item search",attrs:{type:"primary round"},on:{click:t.fetchData}},[t._v(" 搜索 ")]),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item reset-btn",attrs:{type:"primary round"},on:{click:t.handleReset}},[t._v(" 重置 ")])],1)],1),a("el-divider"),a("el-button",{staticClass:"filter-item opt",staticStyle:{"margin-left":"10px"},attrs:{type:"success"},on:{click:t.handleCreate}},[t._v(" 新增 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:t.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{label:"环境名称",align:"left",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name))]}}])}),a("el-table-column",{attrs:{label:"访问地址",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.propValue)+" ")]}}])}),a("el-table-column",{attrs:{label:"上传路径",align:"left",width:"280"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.description))]}}])}),a("el-table-column",{attrs:{label:"连接状态",width:"100",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[!0===e.row.flag?a("el-tag",{attrs:{type:"success"}},[t._v("正常")]):a("el-tag",{attrs:{type:"danger"}},[t._v("异常")])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("span",{staticClass:"table-btn",on:{click:function(e){return t.handleUpdate(i)}}},[t._v(" 编辑 ")]),a("span",{staticClass:"table-btn",on:{click:function(e){return t.testDataSource2(i)}}},[t._v(" 测试连接 ")]),"deleted"!==i.status?a("span",{staticClass:"table-btn",on:{click:function(e){return t.handleDelete(i)}}},[t._v(" 删除 ")]):t._e()]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.pageNo,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageNo",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.fetchData}}),a("el-dialog",{attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,width:"600px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[a("el-form-item",{attrs:{label:"开发引擎",prop:"name"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"Flink{version}"},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}})],1),a("el-form-item",{attrs:{label:"访问地址",prop:"propValue"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"http://{ip}:{port}"},model:{value:t.temp.propValue,callback:function(e){t.$set(t.temp,"propValue",e)},expression:"temp.propValue"}})],1),a("el-form-item",{attrs:{label:"上传路径",prop:"description"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{type:"textarea",rows:"2",placeholder:"上传路径"},model:{value:t.temp.description,callback:function(e){t.$set(t.temp,"description",e)},expression:"temp.description"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){"create"===t.dialogStatus?t.createData():t.updateData()}}},[t._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:t.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(e){t.dialogPluginVisible=e}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogPvVisible=!1}}},[t._v("Confirm")])],1)],1)],1)},l=[],s=(a("14d9"),a("b0c0"),a("b775"));function o(t){return Object(s["a"])({url:"/api/devEnvSetting",method:"get",params:t})}function r(t){return Object(s["a"])({url:"/api/devEnvSetting",method:"put",data:t})}function c(t){return Object(s["a"])({url:"/api/devEnvSetting/test",method:"post",data:t})}function u(t){return Object(s["a"])({url:"/api/devEnvSetting",method:"post",data:t})}function d(t){return Object(s["a"])({url:"/api/devEnvSetting",method:"delete",params:t})}function p(t){return Object(s["a"])({url:"api/devEnvSetting/list",method:"get",params:t})}var f=a("67248"),m=a("333d"),g=a("f656"),h={name:"DevEnvSetting",components:{Pagination:m["a"]},directives:{waves:f["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:10,name:"",propValue:""},pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"修改开发环境",create:"新增开发环境"},rules:{name:[{required:!0,message:"开发引擎必填",trigger:"blur"},Object(g["b"])()],propValue:[{required:!0,message:"访问地址必填",trigger:"blur"},Object(g["b"])()],description:[{required:!0,message:"上传路径必填",trigger:"blur"},Object(g["b"])()]},temp:{id:void 0,name:"",description:""},visible:!0}},created:function(){this.fetchData()},methods:{handleReset:function(){this.listQuery.name="",this.listQuery.propValue="",this.fetchData()},fetchData:function(){var t=this;console.log(1),this.listLoading=!0,console.log(i),o(this.listQuery).then((function(e){var a=e.records,i=e.total;t.total=i,t.list=a,t.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&u(t.temp).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"添加操作",message:"添加环境配置成功！",type:"success",duration:2e3})}))}))},handleUpdate:function(t){var e=this;this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},testDataSource2:function(t){var e=this;this.temp=Object.assign({},t),c(this.temp).then((function(t){e.fetchData(),!1===t?e.$notify({title:"测试连接",message:"环境地址连接失败!",type:"error",duration:2e3}):e.$notify({title:"测试连接",message:"环境地址连接成功!",type:"success",duration:2e3})}))},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a=Object.assign({},t.temp);r(a).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"修改操作",message:"修改环境配置成功！",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;console.log("删除");var a=[];a.push(t.id),d({idList:t.id}).then((function(t){e.fetchData(),e.$notify({title:"删除操作",message:"删除环境配置成功！",type:"success",duration:2e3})}))}}},v=h,b=a("2877"),y=Object(b["a"])(v,n,l,!1,null,null,null);e["default"]=y.exports},67248:function(t,e,a){"use strict";a("8d41");var i="@@wavesContext";function n(t,e){function a(a){var i=Object.assign({},e.value),n=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),l=n.ele;if(l){l.style.position="relative",l.style.overflow="hidden";var s=l.getBoundingClientRect(),o=l.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":(o=document.createElement("span"),o.className="waves-ripple",o.style.height=o.style.width=Math.max(s.width,s.height)+"px",l.appendChild(o)),n.type){case"center":o.style.top=s.height/2-o.offsetHeight/2+"px",o.style.left=s.width/2-o.offsetWidth/2+"px";break;default:o.style.top=(a.pageY-s.top-o.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",o.style.left=(a.pageX-s.left-o.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return o.style.backgroundColor=n.color,o.className="waves-ripple z-active",!1}}return t[i]?t[i].removeHandle=a:t[i]={removeHandle:a},a}var l={bind:function(t,e){t.addEventListener("click",n(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[i].removeHandle,!1),t.addEventListener("click",n(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[i].removeHandle,!1),t[i]=null,delete t[i]}},s=function(t){t.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(s)),l.install=s;e["a"]=l},"8d41":function(t,e,a){},f656:function(t,e,a){"use strict";function i(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function n(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return n}))}}]);