(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d70bef62"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function l(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=n(),r=t-o,s=20,u=0;e="undefined"===typeof e?500:e;var c=function t(){u+=s;var n=Math.easeInOutQuad(u,o,r,e);l(n),u<e?i(t):a&&"function"===typeof a&&a()};c()}},"32e8":function(t,e,a){"use strict";a.d(e,"b",(function(){return l})),a.d(e,"a",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"e",(function(){return r})),a.d(e,"d",(function(){return s}));var i=a("b775");function l(t){return Object(i["a"])({url:"api/log/pageList",method:"get",params:t})}function n(t,e,a){return Object(i["a"])({url:"/api/log/clearLog?jobGroup="+t+"&jobId="+e+"&type="+a,method:"post"})}function o(t){return Object(i["a"])({url:"/api/log/killJob",method:"post",data:t})}function r(t,e,a,l){return Object(i["a"])({url:"/api/log/logDetailCat?executorAddress="+t+"&triggerTime="+e+"&logId="+a+"&fromLineNum="+l,method:"get"})}function s(t,e,a,l,n){return Object(i["a"])({url:"/api/schedulerlog/logDetailCat?executorAddress="+t+"&tasktype="+e+"&triggerTime="+a+"&logId="+l+"&fromLineNum="+n,method:"get"})}},3760:function(t,e,a){"use strict";a("4345")},4345:function(t,e,a){},67248:function(t,e,a){"use strict";a("8d41");var i="@@wavesContext";function l(t,e){function a(a){var i=Object.assign({},e.value),l=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),n=l.ele;if(n){n.style.position="relative",n.style.overflow="hidden";var o=n.getBoundingClientRect(),r=n.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",n.appendChild(r)),l.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(a.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(a.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=l.color,r.className="waves-ripple z-active",!1}}return t[i]?t[i].removeHandle=a:t[i]={removeHandle:a},a}var n={bind:function(t,e){t.addEventListener("click",l(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[i].removeHandle,!1),t.addEventListener("click",l(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[i].removeHandle,!1),t[i]=null,delete t[i]}},o=function(t){t.directive("waves",n)};window.Vue&&(window.waves=n,Vue.use(o)),n.install=o;e["a"]=n},"8d41":function(t,e,a){},bbd1:function(t,e,a){"use strict";a.r(e);var i={};a.r(i),a.d(i,"list",(function(){return s})),a.d(i,"readShell",(function(){return u})),a.d(i,"writeShell",(function(){return c})),a.d(i,"updated",(function(){return d})),a.d(i,"created",(function(){return p})),a.d(i,"upload",(function(){return f})),a.d(i,"deleted",(function(){return m}));var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:t.uploadData.uploading,expression:"uploadData.uploading",modifiers:{fullscreen:!0,lock:!0}}],staticClass:"app-container",attrs:{"element-loading-text":t.uploadData.process}},[a("div",{staticClass:"filter-container"},[a("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"文件类型"},model:{value:t.listQuery.filetype,callback:function(e){t.$set(t.listQuery,"filetype",e)},expression:"listQuery.filetype"}},[a("el-option",{attrs:{label:"",value:""}}),a("el-option",{attrs:{label:"SHELL",value:"SHELL"}}),a("el-option",{attrs:{label:"PYTHON",value:"PYTHON"}})],1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{clearable:"",placeholder:"文件名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.filename,callback:function(e){t.$set(t.listQuery,"filename",e)},expression:"listQuery.filename"}}),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:t.fetchData}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v(" 新增 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:t.list,"element-loading-text":"Loading",width:"640",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"left",label:"序号",width:"65"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.$index+1))]}}])}),a("el-table-column",{attrs:{label:"文件类型",width:"80",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.filetype)+" ")]}}])}),a("el-table-column",{attrs:{label:"文件路径",align:"left",width:"220"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.filepath))]}}])}),a("el-table-column",{attrs:{label:"文件名称",width:"160",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.filename)+" ")]}}])}),a("el-table-column",{attrs:{label:"日志名称",width:"160",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.logname)+" ")]}}])}),a("el-table-column",{attrs:{label:"创建用户",width:"90",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.muser)+" ")]}}])}),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.createtime)+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-edit"},on:{click:function(e){return t.updatefile(i)}}},[t._v(" 编辑文件")]),a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-edit"},on:{click:function(e){return t.handleUpdate(i)}}},[t._v(" 编辑 ")]),"deleted"!==i.status?a("el-button",{attrs:{size:"small",icon:"el-icon-delete",type:"danger"},on:{click:function(e){return t.handleDelete(i)}}},[t._v(" 删除 ")]):t._e()]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.pageNo,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageNo",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.fetchData}}),a("el-dialog",{attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,width:"600px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[a("el-form-item",{attrs:{label:"文件类型",prop:"filetype"}},[a("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"文件类型"},model:{value:t.temp.filetype,callback:function(e){t.$set(t.temp,"filetype",e)},expression:"temp.filetype"}},[a("el-option",{attrs:{label:"SHELL",value:"SHELL"}}),a("el-option",{attrs:{label:"PYTHON",value:"PYTHON"}})],1)],1),a("el-form-item",{attrs:{label:"文件路径",prop:"filepath"}},[a("el-input",{staticStyle:{width:"80%","margin-right":"5px"},attrs:{placeholder:"/home/<USER>/"},model:{value:t.temp.filepath,callback:function(e){t.$set(t.temp,"filepath",e)},expression:"temp.filepath"}})],1),a("el-form-item",{attrs:{label:"日志路径",prop:"logpath"}},[a("el-input",{staticStyle:{width:"80%","margin-right":"5px"},attrs:{placeholder:"/home/<USER>/"},model:{value:t.temp.logpath,callback:function(e){t.$set(t.temp,"logpath",e)},expression:"temp.logpath"}})],1),a("el-form-item",{attrs:{label:"文件名称",prop:"filename"}},[a("el-input",{staticStyle:{width:"80%","margin-right":"5px"},attrs:{placeholder:"test.sh"},model:{value:t.temp.filename,callback:function(e){t.$set(t.temp,"filename",e)},expression:"temp.filename"}})],1),a("el-form-item",{attrs:{label:"日志名称",prop:"logname"}},[a("el-input",{staticStyle:{width:"80%","margin-right":"5px"},attrs:{placeholder:"test.log"},model:{value:t.temp.logname,callback:function(e){t.$set(t.temp,"logname",e)},expression:"temp.logname"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){"create"===t.dialogStatus?t.createData():t.updateData()}}},[t._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:t.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(e){t.dialogPluginVisible=e}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogPvVisible=!1}}},[t._v("Confirm")])],1)],1),a("el-dialog",{attrs:{title:"日志查看",visible:t.dialogVisible,width:"65%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",{staticClass:"log-container"},[a("pre",{attrs:{loading:t.logLoading},domProps:{textContent:t._s(t.logContent)}})]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v(" 关闭 ")]),a("el-button",{attrs:{type:"primary"},on:{click:t.loadLog}},[t._v(" 刷新日志 ")])],1)]),a("el-dialog",{attrs:{visible:t.fileVisible,fullscreen:""},on:{"update:visible":function(e){t.fileVisible=e}},scopedSlots:t._u([{key:"title",fn:function(){return[a("el-row",[a("el-col",{attrs:{span:21}},[a("div",{staticClass:"title"},[t._v("文件路径："+t._s(t.path))])]),a("el-col",{attrs:{span:3}},[a("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:t.submit}},[t._v("保存")]),a("el-button",{attrs:{loading:t.loading},on:{click:t.close}},[t._v("关闭")])],1)],1)]},proxy:!0}])},[a("div",[a("el-input",{staticClass:"area",attrs:{type:"textarea",rows:35,placeholder:"请输入内容"},model:{value:t.content,callback:function(e){t.content=e},expression:"content"}})],1)])],1)},n=[],o=(a("99af"),a("14d9"),a("b0c0"),a("b64b"),a("d3b7"),a("159b"),a("32e8")),r=a("b775");function s(t){return Object(r["a"])({url:"/api/uploadFileList/list",method:"get",params:t})}function u(t){return Object(r["a"])({url:"/api/uploadFileList/readShell",method:"get",params:t})}function c(t){return Object(r["a"])({url:"/api/uploadFileList/writeShell",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/api/uploadFileList/update",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/api/uploadFileList/add",method:"post",data:t})}function f(t,e){return Object(r["a"])({url:"/api/uploadFileList/upload",method:"post",data:t,timeout:0,onUploadProgress:function(t){var a=t.loaded/t.total*100|0;console.log("上传进度：".concat(a,"%")),null===e||void 0===e||e(a)}})}function m(t){return Object(r["a"])({url:"/api/uploadFileList/remove?id="+t,method:"post"})}var g=a("f26b"),h=a("67248"),b=a("333d"),v={name:"DevEnvSetting",components:{Pagination:b["a"]},directives:{waves:h["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return{list:null,listLoading:!0,dialogVisible:!1,fileVisible:!1,total:0,listQuery:{pageNo:1,pageSize:10,filetype:"",filename:""},showCronBox:!1,pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑文件",create:"新增文件"},rules:{filetype:[{required:!0,message:"文件类型不为空",trigger:"blur"}],filepath:[{required:!0,message:"文件路径不为空",trigger:"blur"}],logpath:[{required:!0,message:"日志路径不为空",trigger:"blur"}],filename:[{required:!0,message:"文件名称不为空",trigger:"blur"}],logname:[{required:!0,message:"日志名称不为空",trigger:"blur"}]},warnpersonList:[],temp:{id:"",filename:"",filetype:"",filepath:"",logname:""},visible:!0,jobLogQuery:{executorAddress:""},logContent:"",logShow:!1,logLoading:!1,uploadData:{uploading:!1,process:""},path:null,content:null,loading:!1}},created:function(){this.fetchData(),this.listWarnPerson()},methods:{selectUpload:function(t){var e=this;return this.$selectFile({}).then((function(a){console.log("upload files: ",a);var i=a[0],l=new FormData;return l.append("file",i),l.append("enctype","multipart/form-data"),e.uploadData.uploading=!0,e.uploadData.process="0%",f(l,(function(t){e.uploadData.process=t+"%"})).then((function(a){e.uploadData.uploading=!1,e.uploadData.process="",console.log("upload res: ",a),t.filename=i.name,t.filepath=i.webkitRelativePath||i.name}))}))},listWarnPerson:function(){var t=this;g["listWarnPerson"]({}).then((function(e){t.warnpersonList=e.content.data}))},fetchData:function(){var t=this;this.listLoading=!0,console.log(i),s(this.listQuery).then((function(e){var a=e.content;t.total=a.recordsTotal,t.list=a.data,t.listLoading=!1}))},resetTemp:function(){var t=this;Object.keys(this.temp).forEach((function(e){t.temp[e]=""}))},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&p(t.temp).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}))},handleUpdate:function(t){var e=this;this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},updatefile:function(t){var e=this,a=t.filepath,i=t.filename;this.path="".concat(a,"/").concat(i),u({spath:this.path}).then((function(t){var a=t.content,i=a.data;e.content=i})),this.fileVisible=!0},close:function(){this.fileVisible=!1},submit:function(){var t=this;this.loading=!0,c({spath:this.path,content:this.content}).then((function(e){t.$message.success(e.msg),t.content=null,t.fileVisible=!1})).finally((function(){t.loading=!1}))},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a=Object.assign({},t.temp);d(a).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;console.log("删除");var a=[];a.push(t.id),m(t.id).then((function(t){e.fetchData(),e.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},handleViewJobLog:function(t){this.dialogVisible=!0,this.jobLogQuery.executorAddress=t.logpath||"",!1===this.logShow&&(this.logShow=!0),this.loadLog()},loadLog:function(){var t=this;this.logLoading=!0,o["e"](this.jobLogQuery.executorAddress).then((function(e){"\n"===e.content.logContent||(t.logContent=e.content.logContent),t.logLoading=!1}))}}},y=v,w=(a("3760"),a("2877")),k=Object(w["a"])(y,l,n,!1,null,"0c0cff6c",null);e["default"]=k.exports},f26b:function(t,e,a){"use strict";a.r(e),a.d(e,"list",(function(){return l})),a.d(e,"listWarnPerson",(function(){return n})),a.d(e,"updated",(function(){return o})),a.d(e,"created",(function(){return r})),a.d(e,"deleted",(function(){return s})),a.d(e,"updateStatus",(function(){return u})),a.d(e,"triggerJob",(function(){return c}));var i=a("b775");function l(t){return Object(i["a"])({url:"/api/warnrule/list",method:"get",params:t})}function n(){return Object(i["a"])({url:"/api/warnrule/listWarnPerson",method:"get"})}function o(t){return Object(i["a"])({url:"/api/warnrule/update",method:"post",data:t})}function r(t){return Object(i["a"])({url:"/api/warnrule/add",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/api/warnrule/remove?id="+t,method:"post"})}function u(t){return Object(i["a"])({url:"/api/warnrule/updateStatus",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/api/warnrule/trigger",method:"post",data:t})}}}]);