(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-26886c31"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),Math.easeInOutQuad=function(e,t,a,s){return e/=s/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var s=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(e,t,a){var r=n(),l=e-r,o=20,c=0;t="undefined"===typeof t?500:t;var u=function e(){c+=o;var n=Math.easeInOutQuad(c,r,l,t);i(n),c<t?s(e):a&&"function"===typeof a&&a()};u()}},"52c8":function(e,t,a){"use strict";a("7be4")},67248:function(e,t,a){"use strict";a("8d41");var s="@@wavesContext";function i(e,t){function a(a){var s=Object.assign({},t.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},s),n=i.ele;if(n){n.style.position="relative",n.style.overflow="hidden";var r=n.getBoundingClientRect(),l=n.querySelector(".waves-ripple");switch(l?l.className="waves-ripple":(l=document.createElement("span"),l.className="waves-ripple",l.style.height=l.style.width=Math.max(r.width,r.height)+"px",n.appendChild(l)),i.type){case"center":l.style.top=r.height/2-l.offsetHeight/2+"px",l.style.left=r.width/2-l.offsetWidth/2+"px";break;default:l.style.top=(a.pageY-r.top-l.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",l.style.left=(a.pageX-r.left-l.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return l.style.backgroundColor=i.color,l.className="waves-ripple z-active",!1}}return e[s]?e[s].removeHandle=a:e[s]={removeHandle:a},a}var n={bind:function(e,t){e.addEventListener("click",i(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[s].removeHandle,!1),e.addEventListener("click",i(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[s].removeHandle,!1),e[s]=null,delete e[s]}},r=function(e){e.directive("waves",n)};window.Vue&&(window.waves=n,Vue.use(r)),n.install=r;t["a"]=n},"7be4":function(e,t,a){},"8d41":function(e,t,a){},9213:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.showIfr,expression:"showIfr"}],staticClass:"run-task-list-container"},[a("div",{staticClass:"filter-container"},[a("div",{staticClass:"search-header"},[a("el-form",{attrs:{"label-suffix":"：","label-width":"138px",inline:""}},[a("el-form-item",{attrs:{label:"环境名称","label-width":"auto"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"环境名称"},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}})],1),a("el-form-item",{attrs:{label:"作业名称"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"作业名称"},model:{value:e.listQuery.jobname,callback:function(t){e.$set(e.listQuery,"jobname",t)},expression:"listQuery.jobname"}})],1)],1),a("div",{staticClass:"search-opt"},[a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item search",attrs:{type:"primary round"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item reset-btn",attrs:{type:"primary round"},on:{click:e.handleReset}},[e._v(" 重置 ")])],1)],1),a("el-divider")],1),a("div",{staticClass:"table-box"},[a("el-table",{attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"left",label:"环境名称",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.dname))]}}])}),e._e(),a("el-table-column",{attrs:{label:"作业名称",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),a("el-table-column",{attrs:{label:"开始时间",align:"left",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.begintime))]}}])}),a("el-table-column",{attrs:{label:"持续时间",align:"left",width:"150px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.duration))]}}])}),a("el-table-column",{attrs:{label:"结束时间",align:"left",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.endtime))]}}])}),a("el-table-column",{attrs:{label:"任务数",align:"left",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.tasknumber))]}}])}),a("el-table-column",{attrs:{label:"状态",align:"left",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return["RUNNING"===t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("RUNNING")]):e._e(),"FINISHED"===t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("FINISHED")]):"FAILED"===t.row.status?a("el-tag",{attrs:{type:"danger"}},[e._v("FAILED")]):"CANCELED"===t.row.status?a("el-tag",{attrs:{type:"warning"}},[e._v("CANCELED")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("span",{staticClass:"table-btn",on:{click:function(t){return e.handleVisit(s)}}},[e._v("任务详情")]),a("span",{class:["table-btn","FINISHED"===s.status?"disabled":""],on:{click:function(t){return e.handleCancel(s)}}},[e._v("任务下线")])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}}),a("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"right","label-width":"100px"}},[a("el-form-item",{attrs:{label:"资源名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"资源名称"},model:{value:e.temp.name,callback:function(t){e.$set(e.temp,"name",t)},expression:"temp.name"}})],1),a("el-form-item",{attrs:{label:"资源地址",prop:"resourcePath"}},[a("el-input",{attrs:{placeholder:"资源地址"},model:{value:e.temp.resource_address,callback:function(t){e.$set(e.temp,"resource_address",t)},expression:"temp.resource_address"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v("确定")])],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.showIfr,expression:"!showIfr"}],staticClass:"iframe-container"},[a("iframe",{staticClass:"iframe",attrs:{src:e.ifrSrc,frameborder:"0"}}),a("el-button",{staticClass:"btn",attrs:{type:"primary",round:""},on:{click:e.handleClose}},[e._v("返回")])],1)])},i=[],n=(a("b0c0"),a("b775"));function r(e){return Object(n["a"])({url:"/api/base/resource/add",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/api/base/resource/update",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/api/devTask/remove?jid="+e.jid+"&url="+e.url,method:"get"})}function c(e){return Object(n["a"])({url:"/api/deployTask/list",method:"get",params:e})}var u=a("67248"),d=a("333d"),f={name:"User",components:{Pagination:d["a"]},directives:{waves:u["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{showIfr:!0,ifrSrc:null,list:null,listLoading:!0,total:0,listQuery:{current:1,size:10,jobname:"",name:""},roles:["ROLE_USER","ROLE_ADMIN"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"Edit",create:"Create"},rules:{role:[{required:!0,message:"role is required",trigger:"change"}],name:[{required:!0,message:"name is required",trigger:"blur"}],password:[{required:!1,message:"password is required",trigger:"blur"}]},temp:{id:void 0,role:"",name:"",password:"",permission:"",resource_address:""},resetTemp:function(){this.temp=this.$options.data().temp}}},created:function(){this.fetchData()},methods:{handleReset:function(){this.listQuery.name="",this.listQuery.jobname="",this.fetchData()},fetchData:function(){var e=this;this.listLoading=!0,c(this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={name:e.temp.name,resource_address:e.temp.resource_address};r(a).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}}))},handleVisit:function(e){this.ifrSrc=e.url+"#/job/"+e.jid+"/overview",this.showIfr=!1},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={id:e.temp.id,name:e.temp.name,resource_address:e.temp.resource_address},s=Object.assign({},a);l(s).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleCancel:function(e){var t=this;"FINISHED"!==e.status&&o(e).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},handleClose:function(){this.showIfr=!0,this.fetchData()}}},m=f,p=(a("52c8"),a("2877")),h=Object(p["a"])(m,s,i,!1,null,"56a15346",null);t["default"]=h.exports}}]);