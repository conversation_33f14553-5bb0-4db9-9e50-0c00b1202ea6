// 强制自动登录脚本 - 立即执行，无条件跳过登录
// 这个脚本会立即执行，不等待页面加载完成

(function() {
    'use strict';
    
    console.log('⚡ 启动强制自动登录模式');
    
    // 立即设置登录状态
    const mockUser = {
        username: 'admin',
        password: '123456',
        token: 'mock-token-admin-123456',
        roles: ['admin'],
        name: '管理员',
        avatar: '/user.png'
    };
    
    // 设置所有可能的存储方式
    function setAllStorageData() {
        const storageData = {
            'token': mockUser.token,
            'access_token': mockUser.token,
            'authToken': mockUser.token,
            'user_token': mockUser.token,
            'jwt_token': mockUser.token,
            'authorization': 'Bearer ' + mockUser.token,
            'roles': JSON.stringify(mockUser.roles),
            'username': mockUser.username,
            'user': JSON.stringify(mockUser),
            'userInfo': JSON.stringify(mockUser),
            'currentUser': JSON.stringify(mockUser),
            'isLoggedIn': 'true',
            'loginStatus': 'true',
            'authenticated': 'true',
            'auth': 'true',
            'menuIsChecked': JSON.stringify([]),
            'permissions': JSON.stringify(['*']),
            'loginTime': new Date().toISOString()
        };
        
        // 设置到localStorage
        Object.keys(storageData).forEach(key => {
            localStorage.setItem(key, storageData[key]);
        });
        
        // 设置到sessionStorage
        Object.keys(storageData).forEach(key => {
            sessionStorage.setItem(key, storageData[key]);
        });
        
        console.log('✅ 已设置所有登录状态数据');
    }
    
    // 立即设置存储数据
    setAllStorageData();
    
    // 拦截所有可能的登录验证
    function interceptAllAuth() {
        // 拦截fetch
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                const urlStr = typeof url === 'string' ? url : url.toString();
                
                if (urlStr.includes('/login') || urlStr.includes('/auth') || urlStr.includes('/signin')) {
                    console.log('🔄 拦截登录API请求:', urlStr);
                    return Promise.resolve(new Response(JSON.stringify({
                        code: 200,
                        status: 'success',
                        message: '登录成功',
                        data: mockUser,
                        token: mockUser.token,
                        user: mockUser
                    }), {
                        status: 200,
                        headers: { 'Content-Type': 'application/json' }
                    }));
                }
                return originalFetch.apply(this, arguments);
            };
        }
        
        // 拦截XMLHttpRequest
        if (window.XMLHttpRequest) {
            const originalXHR = window.XMLHttpRequest;
            window.XMLHttpRequest = function() {
                const xhr = new originalXHR();
                const originalOpen = xhr.open;
                const originalSend = xhr.send;
                
                let requestUrl = '';
                
                xhr.open = function(method, url, ...args) {
                    requestUrl = url;
                    return originalOpen.apply(this, [method, url, ...args]);
                };
                
                xhr.send = function(data) {
                    if (requestUrl.includes('/login') || requestUrl.includes('/auth') || requestUrl.includes('/signin')) {
                        console.log('🔄 拦截XHR登录请求:', requestUrl);
                        setTimeout(() => {
                            Object.defineProperty(xhr, 'status', { value: 200 });
                            Object.defineProperty(xhr, 'responseText', { value: JSON.stringify({
                                code: 200,
                                status: 'success',
                                message: '登录成功',
                                data: mockUser,
                                token: mockUser.token,
                                user: mockUser
                            })});
                            Object.defineProperty(xhr, 'readyState', { value: 4 });
                            
                            if (xhr.onreadystatechange) {
                                xhr.onreadystatechange();
                            }
                            if (xhr.onload) {
                                xhr.onload();
                            }
                        }, 50);
                        return;
                    }
                    return originalSend.apply(this, arguments);
                };
                
                return xhr;
            };
        }
    }
    
    // 立即拦截
    interceptAllAuth();
    
    // 自动填充登录表单的函数
    function autoFillAndSubmitLogin() {
        console.log('🔍 开始查找并填充登录表单...');
        
        // 查找用户名输入框
        const usernameSelectors = [
            'input[name="username"]',
            'input[name="user"]',
            'input[name="account"]',
            'input[placeholder*="用户"]',
            'input[placeholder*="账号"]',
            'input[type="text"]',
            '.username input',
            '.user input',
            '.account input'
        ];
        
        // 查找密码输入框
        const passwordSelectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input[name="pass"]',
            'input[placeholder*="密码"]',
            '.password input',
            '.pass input'
        ];
        
        let usernameInput = null;
        let passwordInput = null;
        
        // 查找用户名输入框
        for (const selector of usernameSelectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                usernameInput = elements[0];
                break;
            }
        }
        
        // 查找密码输入框
        for (const selector of passwordSelectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                passwordInput = elements[0];
                break;
            }
        }
        
        if (usernameInput && passwordInput) {
            console.log('📝 找到登录表单，开始自动填充...');
            
            // 填充用户名
            usernameInput.value = 'admin';
            usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('blur', { bubbles: true }));
            
            // 填充密码
            passwordInput.value = '123456';
            passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('blur', { bubbles: true }));
            
            console.log('✅ 已填充登录信息: admin / 123456');
            
            // 查找并点击登录按钮
            setTimeout(() => {
                const buttonSelectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:contains("登录")',
                    'button:contains("确定")',
                    'button:contains("提交")',
                    '.login-btn',
                    '.submit-btn',
                    '.el-button--primary',
                    '.ant-btn-primary',
                    'button'
                ];
                
                let submitButton = null;
                
                for (const selector of buttonSelectors) {
                    const buttons = document.querySelectorAll(selector);
                    for (const btn of buttons) {
                        const text = (btn.textContent || btn.innerText || '').trim();
                        if (text.includes('登录') || text.includes('确定') || text.includes('提交') || selector === 'button[type="submit"]' || selector === 'input[type="submit"]') {
                            submitButton = btn;
                            break;
                        }
                    }
                    if (submitButton) break;
                }
                
                if (submitButton) {
                    console.log('🚀 找到登录按钮，自动点击提交...');
                    submitButton.click();
                    
                    // 如果点击后3秒还在登录页面，尝试强制跳转
                    setTimeout(() => {
                        if (document.querySelector('input[type="password"]')) {
                            console.log('⚠️ 登录可能失败，尝试强制跳转...');
                            forceRedirect();
                        }
                    }, 3000);
                } else {
                    console.log('⚠️ 未找到登录按钮，尝试表单提交...');
                    const form = usernameInput.closest('form');
                    if (form) {
                        form.submit();
                    } else {
                        // 直接尝试跳转
                        setTimeout(forceRedirect, 1000);
                    }
                }
            }, 300);
        } else {
            console.log('⚠️ 未找到登录表单，尝试直接跳转...');
            setTimeout(forceRedirect, 1000);
        }
    }
    
    // 强制跳转函数
    function forceRedirect() {
        console.log('🔄 执行强制跳转...');
        
        const redirectUrls = [
            '#/dashboard',
            '#/home',
            '#/main',
            '#/',
            '/dashboard',
            '/home',
            '/main',
            '/'
        ];
        
        for (const url of redirectUrls) {
            try {
                if (url.startsWith('#')) {
                    window.location.hash = url;
                } else {
                    window.location.href = url;
                }
                console.log(`✅ 尝试跳转到: ${url}`);
                break;
            } catch (e) {
                console.log(`❌ 跳转失败: ${url}`, e);
            }
        }
    }
    
    // 监控页面变化
    function startMonitoring() {
        let checkCount = 0;
        const maxChecks = 20; // 最多检查20次
        
        const checkAndAct = () => {
            checkCount++;
            console.log(`🔍 第${checkCount}次检查页面状态...`);
            
            // 重新设置存储数据
            setAllStorageData();
            
            // 检查是否在登录页面
            const hasPasswordInput = document.querySelector('input[type="password"]') !== null;
            const hasLoginText = document.body && (document.body.innerHTML.includes('登录') || document.body.innerHTML.includes('密码'));
            
            if (hasPasswordInput || hasLoginText) {
                console.log('🔍 检测到登录页面，执行自动登录...');
                autoFillAndSubmitLogin();
            } else {
                console.log('✅ 已在主页面或登录页面未加载完成');
            }
            
            // 继续监控
            if (checkCount < maxChecks) {
                setTimeout(checkAndAct, 1000);
            } else {
                console.log('⚠️ 达到最大检查次数，停止监控');
            }
        };
        
        // 立即开始第一次检查
        checkAndAct();
    }
    
    // 立即开始监控
    startMonitoring();
    
    console.log('⚡ 强制自动登录脚本已启动');
    
})();
