(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-345868cf"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,a,l){return t/=l/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var l=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=i(),r=t-o,s=20,u=0;e="undefined"===typeof e?500:e;var c=function t(){u+=s;var i=Math.easeInOutQuad(u,o,r,e);n(i),u<e?l(t):a&&"function"===typeof a&&a()};c()}},67248:function(t,e,a){"use strict";a("8d41");var l="@@wavesContext";function n(t,e){function a(a){var l=Object.assign({},e.value),n=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),i=n.ele;if(i){i.style.position="relative",i.style.overflow="hidden";var o=i.getBoundingClientRect(),r=i.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",i.appendChild(r)),n.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(a.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(a.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}return t[l]?t[l].removeHandle=a:t[l]={removeHandle:a},a}var i={bind:function(t,e){t.addEventListener("click",n(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[l].removeHandle,!1),t.addEventListener("click",n(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[l].removeHandle,!1),t[l]=null,delete t[l]}},o=function(t){t.directive("waves",i)};window.Vue&&(window.waves=i,Vue.use(o)),i.install=o;e["a"]=i},"8d41":function(t,e,a){},"990b":function(t,e,a){"use strict";a.d(e,"g",(function(){return n})),a.d(e,"e",(function(){return i})),a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"f",(function(){return s})),a.d(e,"i",(function(){return u})),a.d(e,"h",(function(){return c})),a.d(e,"a",(function(){return d})),a.d(e,"b",(function(){return p}));var l=a("b775");function n(t){return Object(l["a"])({url:"/api/dashboardConfig/listDashBoardAPI",method:"get",params:t})}function i(){return Object(l["a"])({url:"/api/dashboardConfig/getDashboardNameList",method:"get"})}function o(t){return Object(l["a"])({url:"/api/dashboradData/findINameField",method:"get",params:t})}function r(t){return Object(l["a"])({url:"/api/dashboradData/findFieldsByInterface",method:"get",params:t})}function s(t){return Object(l["a"])({url:"/api/dashboardConfig/list",method:"get",params:t})}function u(t){return Object(l["a"])({url:"/api/dashboardConfig/update",method:"post",data:t})}function c(t){return Object(l["a"])({url:"/api/dashboardConfig/getValueById?id="+t,method:"get"})}function d(t){return Object(l["a"])({url:"/api/dashboardConfig/add",method:"post",data:t})}function p(t){return Object(l["a"])({url:"/api/dashboardConfig/remove",method:"post",params:t})}},dee7:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"240px","margin-right":"10px"},attrs:{placeholder:"看板名称"},model:{value:t.listQuery.tname,callback:function(e){t.$set(t.listQuery,"tname",e)},expression:"listQuery.tname"}},t._l(t.getDashboardNameList,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"字段名称"},model:{value:t.listQuery.tvalue,callback:function(e){t.$set(t.listQuery,"tvalue",e)},expression:"listQuery.tvalue"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"取值类型"},model:{value:t.listQuery.groupId,callback:function(e){t.$set(t.listQuery,"groupId",e)},expression:"listQuery.groupId"}},t._l(t.groupIds,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:t.fetchData}},[t._v(" 搜索 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:t.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"left",label:"序号",width:"75"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.$index+1))]}}])}),a("el-table-column",{attrs:{label:"看板名称",align:"left",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.tname))]}}])}),a("el-table-column",{attrs:{label:"字段名称",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.tvalue))]}}])}),a("el-table-column",{attrs:{label:"字段分组",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.fgroup))]}}])}),a("el-table-column",{attrs:{label:"值配置",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.vkey))]}}])}),a("el-table-column",{attrs:{label:"接口名称",align:"left",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name))]}}])}),a("el-table-column",{attrs:{label:"接口字段",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.ikey))]}}])}),a("el-table-column",{attrs:{label:"取值类型",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return["扇形"===e.row.groupId?a("el-tag",{attrs:{type:"warning"}},[t._v("扇形")]):t._e(),"列表"===e.row.groupId?a("el-tag",{attrs:{type:"warning"}},[t._v("列表")]):"无"===e.row.groupId?a("el-tag",{attrs:{type:"info"}},[t._v("无")]):"表头"===e.row.groupId?a("el-tag",{attrs:{type:"info"}},[t._v("表头")]):"标题"===e.row.groupId?a("el-tag",{attrs:{type:"info"}},[t._v("标题")]):"数值"===e.row.groupId?a("el-tag",{attrs:{type:"success"}},[t._v("数值")]):t._e()]}}])}),a("el-table-column",{attrs:{label:"显示值",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return["列表"===e.row.groupId||"扇形"===e.row.groupId?a("div",[a("el-popover",{attrs:{placement:"bottom",width:"160"},on:{show:function(a){return t.showVValue(e.row)}}},[a("h5",{domProps:{innerHTML:t._s(t.vValueText)}}),a("el-button",{attrs:{slot:"reference",size:"small"},slot:"reference"},[t._v("查看")])],1)],1):"数值"===e.row.groupId||"无"===e.row.groupId||"表头"===e.row.groupId||"标题"===e.row.groupId?a("div",[t._v(" "+t._s(e.row.vvalue)+" ")]):t._e()]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var l=e.row;return[a("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-edit"},on:{click:function(e){return t.handleUpdate(l)}}},[t._v(" 编辑 ")])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.pageNo,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageNo",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.fetchData}}),a("el-dialog",{attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,width:"800px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"看板名称",prop:"tname"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{readonly:"true",placeholder:"看板名称"},model:{value:t.temp.tname,callback:function(e){t.$set(t.temp,"tname",e)},expression:"temp.tname"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"字段名称",prop:"tvalue"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{readonly:"true",placeholder:"字段名称"},model:{value:t.temp.tvalue,callback:function(e){t.$set(t.temp,"tvalue",e)},expression:"temp.tvalue"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"字段分组",prop:"fgroup"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{readonly:"true",placeholder:"字段分组"},model:{value:t.temp.fgroup,callback:function(e){t.$set(t.temp,"fgroup",e)},expression:"temp.fgroup"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"值配置",prop:"vkey"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{readonly:"true",placeholder:"值配置"},model:{value:t.temp.vkey,callback:function(e){t.$set(t.temp,"vkey",e)},expression:"temp.vkey"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"取值类型",prop:"groupId"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{readonly:"true",placeholder:"取值类型"},model:{value:t.temp.groupId,callback:function(e){t.$set(t.temp,"groupId",e)},expression:"temp.groupId"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"接口名称",prop:"name"}},[a("el-select",{attrs:{placeholder:"接口名称"},on:{change:t.changeInterface},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}},t._l(t.interfaceList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"接口字段",prop:"ikey"}},[a("el-select",{attrs:{placeholder:"接口字段"},model:{value:t.temp.ikey,callback:function(e){t.$set(t.temp,"ikey",e)},expression:"temp.ikey"}},t._l(t.interfaceFieldList,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){"create"===t.dialogStatus?t.createData():t.updateData()}}},[t._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:t.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(e){t.dialogPluginVisible=e}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogPvVisible=!1}}},[t._v("Confirm")])],1)],1)],1)},n=[],i=(a("b0c0"),a("990b")),o=a("67248"),r=a("333d"),s={name:"DevEnvSetting",components:{Pagination:r["a"]},directives:{waves:o["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:10,tname:"",tvalue:""},interfaceList:"",interfaceFieldList:"",getDashboardNameList:"",pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,vValueText:"",dialogStatus:"",textMap:{update:"看板配置修改",create:"看板配置新增"},rules:{name:[{required:!0,message:"this is required",trigger:"blur"}],description:[{required:!0,message:"this is required",trigger:"blur"}]},groupIds:[{value:"",label:""},{value:"无",label:"无"},{value:"表头",label:"表头"},{value:"标题",label:"标题"},{value:"数值",label:"数值"},{value:"扇形",label:"扇形"},{value:"列表",label:"列表"}],temp:{id:void 0,tname:"",key:"",tvalue:""},visible:!0}},created:function(){this.fetchData(),this.getDashboardName()},methods:{fetchData:function(){var t=this;this.listLoading=!0,i["g"](this.listQuery).then((function(e){var a=e.content;t.total=a.recordsTotal,t.list=a.data,t.listLoading=!1}))},getInterfaceData:function(t){var e=this;i["d"](t).then((function(t){e.interfaceList=t.content.data}))},changeInterface:function(t){var e=this;this.interfaceFieldList="",this.temp.ikey="",i["c"]({id:t}).then((function(t){e.interfaceFieldList=t.content.data}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},showVValue:function(t){var e=this;i["h"](t.id).then((function(t){e.vValueText=t.content}))},getDashboardName:function(){var t=this;i["e"]().then((function(e){t.getDashboardNameList=e.content}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&i["a"](t.temp).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"添加操作",message:"添加成功",type:"success",duration:2e3})}))}))},handleUpdate:function(t){var e=this;this.getInterfaceData({groupID:t.groupId}),this.temp=Object.assign({},t),this.temp.name=parseInt(t.apiID),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a=Object.assign({},t.temp);i["i"](a).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;i["b"]({id:t.id}).then((function(t){e.fetchData(),e.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))}}},u=s,c=a("2877"),d=Object(c["a"])(u,l,n,!1,null,null,null);e["default"]=d.exports}}]);