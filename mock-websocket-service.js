// Mock WebSocket服务 - 模拟实时数据推送
// 为数据中台提供实时数据模拟支持

(function() {
    'use strict';
    
    console.log('📡 启动Mock WebSocket服务');
    
    // Mock WebSocket类
    class MockWebSocket {
        constructor(url, protocols) {
            this.url = url;
            this.protocols = protocols;
            this.readyState = MockWebSocket.CONNECTING;
            this.onopen = null;
            this.onclose = null;
            this.onmessage = null;
            this.onerror = null;
            
            // 模拟连接延迟
            setTimeout(() => {
                this.readyState = MockWebSocket.OPEN;
                if (this.onopen) {
                    this.onopen({ type: 'open', target: this });
                }
                this.startMockData();
            }, 100);
        }
        
        // 开始发送模拟数据
        startMockData() {
            // 根据URL确定数据类型
            if (this.url.includes('realtime')) {
                this.sendRealtimeData();
            } else if (this.url.includes('notification')) {
                this.sendNotifications();
            } else if (this.url.includes('monitor')) {
                this.sendMonitorData();
            } else {
                this.sendGeneralData();
            }
        }
        
        // 发送实时数据
        sendRealtimeData() {
            const sendData = () => {
                if (this.readyState === MockWebSocket.OPEN) {
                    const data = {
                        type: 'realtime_data',
                        timestamp: new Date().toISOString(),
                        data: {
                            cpu: Math.random() * 100,
                            memory: Math.random() * 100,
                            disk: Math.random() * 100,
                            network: Math.random() * 1000,
                            activeUsers: Math.floor(Math.random() * 1000) + 500,
                            requests: Math.floor(Math.random() * 100) + 50
                        }
                    };
                    
                    if (this.onmessage) {
                        this.onmessage({
                            type: 'message',
                            data: JSON.stringify(data),
                            target: this
                        });
                    }
                }
            };
            
            // 每2秒发送一次数据
            this.dataInterval = setInterval(sendData, 2000);
        }
        
        // 发送通知数据
        sendNotifications() {
            const notifications = [
                { type: 'info', title: '系统信息', message: '数据同步完成' },
                { type: 'warning', title: '系统警告', message: 'CPU使用率较高' },
                { type: 'error', title: '系统错误', message: '数据库连接失败' },
                { type: 'success', title: '操作成功', message: '备份任务完成' }
            ];
            
            const sendNotification = () => {
                if (this.readyState === MockWebSocket.OPEN) {
                    const notification = notifications[Math.floor(Math.random() * notifications.length)];
                    const data = {
                        type: 'notification',
                        timestamp: new Date().toISOString(),
                        data: {
                            id: Date.now(),
                            ...notification
                        }
                    };
                    
                    if (this.onmessage) {
                        this.onmessage({
                            type: 'message',
                            data: JSON.stringify(data),
                            target: this
                        });
                    }
                }
            };
            
            // 每10秒发送一次通知
            this.notificationInterval = setInterval(sendNotification, 10000);
        }
        
        // 发送监控数据
        sendMonitorData() {
            const sendMonitorData = () => {
                if (this.readyState === MockWebSocket.OPEN) {
                    const data = {
                        type: 'monitor_data',
                        timestamp: new Date().toISOString(),
                        data: {
                            services: [
                                { name: '数据库服务', status: 'running', uptime: '99.9%' },
                                { name: 'API服务', status: 'running', uptime: '99.8%' },
                                { name: '缓存服务', status: 'running', uptime: '99.7%' },
                                { name: '消息队列', status: Math.random() > 0.9 ? 'error' : 'running', uptime: '98.5%' }
                            ],
                            metrics: {
                                responseTime: Math.random() * 100 + 50,
                                throughput: Math.random() * 1000 + 500,
                                errorRate: Math.random() * 5
                            }
                        }
                    };
                    
                    if (this.onmessage) {
                        this.onmessage({
                            type: 'message',
                            data: JSON.stringify(data),
                            target: this
                        });
                    }
                }
            };
            
            // 每5秒发送一次监控数据
            this.monitorInterval = setInterval(sendMonitorData, 5000);
        }
        
        // 发送通用数据
        sendGeneralData() {
            const sendData = () => {
                if (this.readyState === MockWebSocket.OPEN) {
                    const data = {
                        type: 'general_data',
                        timestamp: new Date().toISOString(),
                        data: {
                            value: Math.random() * 100,
                            status: Math.random() > 0.8 ? 'warning' : 'normal',
                            message: '数据更新'
                        }
                    };
                    
                    if (this.onmessage) {
                        this.onmessage({
                            type: 'message',
                            data: JSON.stringify(data),
                            target: this
                        });
                    }
                }
            };
            
            // 每3秒发送一次数据
            this.generalInterval = setInterval(sendData, 3000);
        }
        
        // 发送消息
        send(data) {
            console.log('📤 WebSocket发送消息:', data);
            
            // 模拟服务器响应
            setTimeout(() => {
                if (this.onmessage) {
                    const response = {
                        type: 'response',
                        timestamp: new Date().toISOString(),
                        data: {
                            status: 'success',
                            message: '消息已收到',
                            echo: data
                        }
                    };
                    
                    this.onmessage({
                        type: 'message',
                        data: JSON.stringify(response),
                        target: this
                    });
                }
            }, 100);
        }
        
        // 关闭连接
        close(code, reason) {
            this.readyState = MockWebSocket.CLOSING;
            
            // 清理定时器
            if (this.dataInterval) clearInterval(this.dataInterval);
            if (this.notificationInterval) clearInterval(this.notificationInterval);
            if (this.monitorInterval) clearInterval(this.monitorInterval);
            if (this.generalInterval) clearInterval(this.generalInterval);
            
            setTimeout(() => {
                this.readyState = MockWebSocket.CLOSED;
                if (this.onclose) {
                    this.onclose({
                        type: 'close',
                        code: code || 1000,
                        reason: reason || 'Normal closure',
                        target: this
                    });
                }
            }, 100);
        }
        
        // 添加事件监听器
        addEventListener(type, listener) {
            if (type === 'open') this.onopen = listener;
            else if (type === 'close') this.onclose = listener;
            else if (type === 'message') this.onmessage = listener;
            else if (type === 'error') this.onerror = listener;
        }
        
        // 移除事件监听器
        removeEventListener(type, listener) {
            if (type === 'open' && this.onopen === listener) this.onopen = null;
            else if (type === 'close' && this.onclose === listener) this.onclose = null;
            else if (type === 'message' && this.onmessage === listener) this.onmessage = null;
            else if (type === 'error' && this.onerror === listener) this.onerror = null;
        }
    }
    
    // WebSocket状态常量
    MockWebSocket.CONNECTING = 0;
    MockWebSocket.OPEN = 1;
    MockWebSocket.CLOSING = 2;
    MockWebSocket.CLOSED = 3;
    
    // 替换原生WebSocket
    if (window.WebSocket) {
        window.OriginalWebSocket = window.WebSocket;
    }
    window.WebSocket = MockWebSocket;
    
    // 创建一个全局的WebSocket管理器
    window.mockWebSocketManager = {
        connections: [],
        
        // 创建连接
        createConnection(url, protocols) {
            const ws = new MockWebSocket(url, protocols);
            this.connections.push(ws);
            console.log(`📡 创建WebSocket连接: ${url}`);
            return ws;
        },
        
        // 关闭所有连接
        closeAllConnections() {
            this.connections.forEach(ws => {
                if (ws.readyState === MockWebSocket.OPEN) {
                    ws.close();
                }
            });
            this.connections = [];
            console.log('📡 已关闭所有WebSocket连接');
        },
        
        // 广播消息到所有连接
        broadcast(message) {
            this.connections.forEach(ws => {
                if (ws.readyState === MockWebSocket.OPEN && ws.onmessage) {
                    ws.onmessage({
                        type: 'message',
                        data: JSON.stringify({
                            type: 'broadcast',
                            timestamp: new Date().toISOString(),
                            data: message
                        }),
                        target: ws
                    });
                }
            });
            console.log('📡 广播消息到所有WebSocket连接:', message);
        }
    };
    
    console.log('✅ Mock WebSocket服务初始化完成');
    
})();
