// Mock API服务 - 拦截所有API请求并返回模拟数据
// 为数据中台提供完整的API模拟支持

(function() {
    'use strict';
    
    console.log('🌐 启动Mock API服务');
    
    // API路由映射
    const apiRoutes = {
        // 用户相关
        'GET /api/user/info': () => ({
            code: 200,
            message: '获取用户信息成功',
            data: window.mockDataGenerator.users[0]
        }),
        
        'GET /api/user/list': (params) => {
            const { page = 1, size = 10, keyword = '' } = params;
            let users = window.mockDataGenerator.users;
            
            if (keyword) {
                users = users.filter(user => 
                    user.name.includes(keyword) || user.username.includes(keyword)
                );
            }
            
            const start = (page - 1) * size;
            const end = start + size;
            
            return {
                code: 200,
                message: '获取用户列表成功',
                data: {
                    list: users.slice(start, end),
                    total: users.length,
                    page: parseInt(page),
                    size: parseInt(size)
                }
            };
        },
        
        'POST /api/user/create': (data) => ({
            code: 200,
            message: '创建用户成功',
            data: { id: Date.now(), ...data }
        }),
        
        'PUT /api/user/update': (data) => ({
            code: 200,
            message: '更新用户成功',
            data: data
        }),
        
        'DELETE /api/user/delete': () => ({
            code: 200,
            message: '删除用户成功'
        }),
        
        // 仪表板相关
        'GET /api/dashboard/overview': () => ({
            code: 200,
            message: '获取概览数据成功',
            data: window.mockDataGenerator.dashboardData.overview
        }),
        
        'GET /api/dashboard/activities': () => ({
            code: 200,
            message: '获取活动数据成功',
            data: window.mockDataGenerator.dashboardData.recentActivities
        }),
        
        'GET /api/dashboard/notifications': () => ({
            code: 200,
            message: '获取通知数据成功',
            data: window.mockDataGenerator.dashboardData.notifications
        }),
        
        // 数据管理相关
        'GET /api/data/list': (params) => {
            const { page = 1, size = 10, type = '', status = '' } = params;
            let data = window.mockDataGenerator.tableData;
            
            if (type) {
                data = data.filter(item => item.type === type);
            }
            if (status) {
                data = data.filter(item => item.status === status);
            }
            
            const start = (page - 1) * size;
            const end = start + size;
            
            return {
                code: 200,
                message: '获取数据列表成功',
                data: {
                    list: data.slice(start, end),
                    total: data.length,
                    page: parseInt(page),
                    size: parseInt(size)
                }
            };
        },
        
        'GET /api/data/detail': (params) => {
            const { id } = params;
            const item = window.mockDataGenerator.tableData.find(d => d.id == id);
            return {
                code: 200,
                message: '获取数据详情成功',
                data: item || null
            };
        },
        
        'POST /api/data/create': (data) => ({
            code: 200,
            message: '创建数据成功',
            data: { id: Date.now(), ...data }
        }),
        
        'PUT /api/data/update': (data) => ({
            code: 200,
            message: '更新数据成功',
            data: data
        }),
        
        'DELETE /api/data/delete': () => ({
            code: 200,
            message: '删除数据成功'
        }),
        
        // 图表数据相关
        'GET /api/chart/line': () => ({
            code: 200,
            message: '获取折线图数据成功',
            data: window.mockDataGenerator.chartData.lineChart
        }),
        
        'GET /api/chart/bar': () => ({
            code: 200,
            message: '获取柱状图数据成功',
            data: window.mockDataGenerator.chartData.barChart
        }),
        
        'GET /api/chart/pie': () => ({
            code: 200,
            message: '获取饼图数据成功',
            data: window.mockDataGenerator.chartData.pieChart
        }),
        
        'GET /api/chart/realtime': () => ({
            code: 200,
            message: '获取实时数据成功',
            data: window.mockDataGenerator.chartData.realtimeData
        }),
        
        // 菜单相关
        'GET /api/menu/list': () => ({
            code: 200,
            message: '获取菜单列表成功',
            data: window.mockDataGenerator.menuData
        }),
        
        // 系统配置相关
        'GET /api/system/config': () => ({
            code: 200,
            message: '获取系统配置成功',
            data: window.mockDataGenerator.systemConfig
        }),
        
        'POST /api/system/config': (data) => ({
            code: 200,
            message: '更新系统配置成功',
            data: data
        }),
        
        // 报表相关
        'GET /api/report/daily': () => ({
            code: 200,
            message: '获取日报数据成功',
            data: window.mockDataGenerator.reportData.dailyReport
        }),
        
        'GET /api/report/weekly': () => ({
            code: 200,
            message: '获取周报数据成功',
            data: window.mockDataGenerator.reportData.weeklyReport
        }),
        
        'GET /api/report/monthly': () => ({
            code: 200,
            message: '获取月报数据成功',
            data: window.mockDataGenerator.reportData.monthlyReport
        }),
        
        'POST /api/report/export': () => ({
            code: 200,
            message: '导出报表成功',
            data: { downloadUrl: '/download/report_' + Date.now() + '.xlsx' }
        }),
        
        // 文件上传
        'POST /api/upload': () => ({
            code: 200,
            message: '文件上传成功',
            data: { 
                url: '/uploads/file_' + Date.now() + '.jpg',
                filename: 'uploaded_file.jpg',
                size: Math.floor(Math.random() * 1000000)
            }
        }),
        
        // 通用搜索
        'GET /api/search': (params) => {
            const { keyword = '', type = 'all' } = params;
            return {
                code: 200,
                message: '搜索成功',
                data: window.mockDataGenerator.generateRandomData('search', 5).map(item => ({
                    ...item,
                    title: `搜索结果 ${keyword} ${item.id}`,
                    type: type,
                    highlight: keyword
                }))
            };
        }
    };
    
    // 解析URL参数
    function parseUrlParams(url) {
        const params = {};
        const urlObj = new URL(url, window.location.origin);
        urlObj.searchParams.forEach((value, key) => {
            params[key] = value;
        });
        return params;
    }
    
    // 匹配API路由
    function matchRoute(method, url) {
        const cleanUrl = url.split('?')[0]; // 移除查询参数
        const routeKey = `${method.toUpperCase()} ${cleanUrl}`;
        
        // 精确匹配
        if (apiRoutes[routeKey]) {
            return apiRoutes[routeKey];
        }
        
        // 模糊匹配（支持路径参数）
        for (const route in apiRoutes) {
            const [routeMethod, routePath] = route.split(' ');
            if (routeMethod === method.toUpperCase()) {
                // 简单的路径匹配，支持 /api/data/:id 这样的路径
                const routeRegex = routePath.replace(/:\w+/g, '\\w+');
                if (new RegExp(`^${routeRegex}$`).test(cleanUrl)) {
                    return apiRoutes[route];
                }
            }
        }
        
        return null;
    }
    
    // 拦截fetch请求
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            const method = options.method || 'GET';
            const handler = matchRoute(method, url);
            
            if (handler) {
                console.log(`🔄 拦截API请求: ${method} ${url}`);
                
                const params = parseUrlParams(url);
                let requestData = null;
                
                if (options.body) {
                    try {
                        requestData = JSON.parse(options.body);
                    } catch (e) {
                        requestData = options.body;
                    }
                }
                
                const responseData = handler(requestData || params);
                
                return Promise.resolve(new Response(JSON.stringify(responseData), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }));
            }
            
            return originalFetch.apply(this, arguments);
        };
    }
    
    // 拦截XMLHttpRequest
    if (window.XMLHttpRequest) {
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            const originalSend = xhr.send;
            
            let requestMethod = '';
            let requestUrl = '';
            let requestData = null;
            
            xhr.open = function(method, url, ...args) {
                requestMethod = method;
                requestUrl = url;
                return originalOpen.apply(this, [method, url, ...args]);
            };
            
            xhr.send = function(data) {
                requestData = data;
                const handler = matchRoute(requestMethod, requestUrl);
                
                if (handler) {
                    console.log(`🔄 拦截XHR请求: ${requestMethod} ${requestUrl}`);
                    
                    const params = parseUrlParams(requestUrl);
                    let parsedData = null;
                    
                    if (data) {
                        try {
                            parsedData = JSON.parse(data);
                        } catch (e) {
                            parsedData = data;
                        }
                    }
                    
                    const responseData = handler(parsedData || params);
                    
                    setTimeout(() => {
                        Object.defineProperty(xhr, 'status', { value: 200 });
                        Object.defineProperty(xhr, 'responseText', { value: JSON.stringify(responseData) });
                        Object.defineProperty(xhr, 'readyState', { value: 4 });
                        
                        if (xhr.onreadystatechange) {
                            xhr.onreadystatechange();
                        }
                    }, Math.random() * 500 + 100); // 模拟网络延迟
                    
                    return;
                }
                
                return originalSend.apply(this, arguments);
            };
            
            return xhr;
        };
    }
    
    console.log('✅ Mock API服务初始化完成，已拦截所有API请求');
    
})();
