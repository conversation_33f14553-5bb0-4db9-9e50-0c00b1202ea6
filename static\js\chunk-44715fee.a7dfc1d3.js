(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-44715fee"],{"09f4":function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,n,a){return e/=a/2,e<1?n/2*e*e+t:(e--,-n/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,n){var o=s(),r=e-o,l=20,c=0;t="undefined"===typeof t?500:t;var u=function e(){c+=l;var s=Math.easeInOutQuad(c,o,r,t);i(s),c<t?a(e):n&&"function"===typeof n&&n()};u()}},"10e4":function(e,t,n){"use strict";n("fd1f")},67248:function(e,t,n){"use strict";n("8d41");var a="@@wavesContext";function i(e,t){function n(n){var a=Object.assign({},t.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),s=i.ele;if(s){s.style.position="relative",s.style.overflow="hidden";var o=s.getBoundingClientRect(),r=s.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",s.appendChild(r)),i.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(n.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(n.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=i.color,r.className="waves-ripple z-active",!1}}return e[a]?e[a].removeHandle=n:e[a]={removeHandle:n},n}var s={bind:function(e,t){e.addEventListener("click",i(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[a].removeHandle,!1),e.addEventListener("click",i(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[a].removeHandle,!1),e[a]=null,delete e[a]}},o=function(e){e.directive("waves",s)};window.Vue&&(window.waves=s,Vue.use(o)),s.install=o;t["a"]=s},"8d41":function(e,t,n){},"98aa":function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));n("4de4"),n("d81d"),n("14d9"),n("fb6a"),n("e9c4"),n("b64b"),n("d3b7");function a(e,t,n){var a=e.filter((function(e){return 0===e[t]})),i=e.filter((function(e){return 0!==e[t]}));return s(a,i),a;function s(e,a){e.map((function(e){a.map((function(i,o){if(i[t]===e.id){var r=JSON.parse(JSON.stringify(a));r.slice(o,1),s([i],r),e[n]?e[n].push(i):e[n]=[i]}}))}))}}},e869:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-dialog",{attrs:{title:"权限配置",visible:e.roleDialog,width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.roleDialog=t}}},[n("el-scrollbar",{staticStyle:{height:"400px"}},[n("el-tree",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingTree,expression:"loadingTree"}],ref:"roletree",attrs:{props:e.menuCheckedProps,data:e.menuTreeCheckData,"node-key":"id","default-expanded-keys":e.expandedKeys,"default-checked-keys":e.checkedKeys,"show-checkbox":""},on:{"check-change":e.handleCheckChange},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node;return n("span",{staticClass:"custom-tree-node"},[n("i",{staticClass:"el-icon-folder",staticStyle:{"margin-right":"10px"}}),n("span",[e._v(e._s(a.label))])])}}])})],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.roleDialog=!1}}},[e._v("取 消")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitRole()}}},[e._v("确 定")])],1)],1),n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"角色名称"},model:{value:e.listQuery.role_name,callback:function(t){e.$set(e.listQuery,"role_name",t)},expression:"listQuery.role_name"}}),n("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"权限字符"},model:{value:e.listQuery.role_key,callback:function(t){e.$set(e.listQuery,"role_key",t)},expression:"listQuery.role_key"}}),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),n("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v(" 新增 ")])],1),n("div",{staticClass:"table-box"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"left",label:"序号",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),n("el-table-column",{attrs:{label:"角色名称",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.role_name))]}}])}),n("el-table-column",{attrs:{label:"权限字符",align:"left",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.role_key))]}}])}),n("el-table-column",{attrs:{label:"显示顺序",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.role_sort))]}}])}),n("el-table-column",{attrs:{label:"角色状态",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"0","inactive-value":"1","active-text":"启用","inactive-text":"停用"},on:{change:function(n){return e.statusChange(t.row)}},model:{value:t.row.status,callback:function(n){e.$set(t.row,"status",n)},expression:"scope.row.status"}})]}}])}),n("el-table-column",{attrs:{label:"创建时间",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.create_time))]}}])}),n("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[n("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-edit"},on:{click:function(t){return e.handleUpdate(a)}}},[e._v(" 编辑 ")]),n("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.handleRole(a)}}},[e._v(" 权限 ")]),"deleted"!==a.status?n("el-button",{attrs:{size:"small",icon:"el-icon-delete",type:"danger"},on:{click:function(t){return e.handleDelete(a)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.listQuery.total>0,expression:"listQuery.total > 0"}],attrs:{total:e.listQuery.total,page:e.listQuery.pageNo,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageNo",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.fetchData}})],1)},i=[],s=n("c7eb"),o=n("1da1"),r=(n("d81d"),n("14d9"),n("1de4")),l=n("67248"),c=n("333d"),u=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"base-widget-demo"},[e.options.observer?n("BaseWidget",{ref:"baseWidget",attrs:{"data-example":e.dataExample,"default-data":e.defaultData,options:e.options,"on-submit":e.onSubmit,mode:e.mode},scopedSlots:e._u([{key:"slot-status",fn:function(t){return["edit"===e.mode?n("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"0","inactive-value":"1","active-text":"启用","inactive-text":"停用"},model:{value:t.slotScope.formData.status,callback:function(n){e.$set(t.slotScope.formData,"status",n)},expression:"scope.slotScope.formData.status"}}):n("span",[e._v(" "+e._s("0"===t.slotScope.formData.status?"启用":"停用")+" ")])]}}],null,!1,2403185781)}):e._e()],1)},d=[],f=n("eeb0"),p=n("57e7"),h={components:{BaseWidget:f["a"]},mixins:[p["a"]],props:{defaultData:{type:Object,default:null},defaultOptions:{type:Object,default:null},mode:{type:String,default:"edit"}},data:function(){return{passwordType:"password",dataExample:{role_name:"",role_key:"",role_sort:1,status:"0"},options:{observer:null,afterInit:null,rules:{role_name:[{required:!0,message:"不能为空",trigger:"blur"}],role_key:[{required:!0,message:"不能为空",trigger:"blur"}],status:[{required:!0,message:"不能为空",trigger:"blur"}]},formItems:[{label:"角色名称",prop:"role_name",type:"input",options:{}},{label:"账号状态",prop:"status",type:"slot",options:{typeFun:function(){return"slot"}}},{label:"权限字符",prop:"role_key",type:"input",options:{}},{label:"显示顺序",prop:"role_sort",type:"input-number",options:{}}]}}},mounted:function(){this.init()},methods:{init:function(){},onSubmit:function(e){}}},m=h,v=(n("10e4"),n("2877")),y=Object(v["a"])(m,u,d,!1,null,"fee3ac70",null),b=y.exports,g=n("98aa"),w={name:"DevEnvSetting",components:{Pagination:c["a"]},directives:{waves:l["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,roleDialog:!1,loadingTree:!1,roleid:1,listQuery:{total:0,pageNo:1,pageSize:10,role_name:"",role_key:""},menuCheckData:[],menuTreeCheckData:[],expandedKeys:[],checkedKeys:[],menuCheckedProps:{children:"children",label:"menuName"}}},created:function(){this.fetchData()},methods:{submitRole:function(){var e=this,t=this.$refs.roletree.getCheckedNodes(!1,!0),n="";t.map((function(e){n+=e.id+","})),n=n.substring(0,n.length-1);var a={};a.role_id=this.roleid,a.menuids=n;var i=Object.assign({},a);r["a"](i).then((function(t){e.roleDialog=!1,e.$notify({title:"添加操作",message:"添加成功",type:"success",duration:2e3}),e.$store.dispatch("user/menuIsCheckedByUser")}))},handleClose:function(){this.expandedKeys=[],this.checkedKeys=[],this.roleDialog=!1},handleCheckChange:function(e){},menuIsChecked:function(e){var t=this;this.expandedKeys=[],this.checkedKeys=[];var n={};this.roleid=e,n.roleid=e,r["f"](n).then((function(e){t.menuCheckData=e.content.data,t.menuTreeCheckData=[],t.menuTreeCheckData=g["a"](t.menuCheckData,"parentId","children"),t.setCheckedKeys(),t.loadingTree=!1}))},setCheckedKeys:function(){var e=this;this.menuCheckData.map((function(t){0===t.parentId&&1===t.checked&&e.expandedKeys.push(t.id),0!==t.parentId&&1===t.checked&&e.checkedKeys.push(t.id)}))},fetchData:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var n,a;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.listLoading=!0,t.next=3,r["d"](e.listQuery);case 3:n=t.sent,a=n.content,e.listQuery.total=a.recordsTotal,e.list=a.data,e.listLoading=!1;case 8:case"end":return t.stop()}}),t)})))()},statusChange:function(e){var t=this;r["h"]({roleid:e.id,status:e.status}).then((function(){t.fetchData(),t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))},handleRole:function(e){this.loadingTree=!0,this.menuIsChecked(e.id),this.roleDialog=!0},handleView:function(e){this.$dialog.show("详情",b,{area:"600px"},{defaultData:e,mode:"preview"})},handleAdd:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var n;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$dialog.show("新增",b,{area:"600px"},{mode:"edit"});case 2:return n=t.sent,t.next=5,r["b"](n).then(Object(o["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.fetchData();case 2:return t.next=4,e.$notify({title:"成功",message:"新增成功",type:"success",duration:2e3});case 4:case"end":return t.stop()}}),t)}))));case 5:case"end":return t.stop()}}),t)})))()},handleUpdate:function(e){var t=this;return Object(o["a"])(Object(s["a"])().mark((function n(){var a;return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.$dialog.show("编辑",b,{area:"600px"},{defaultData:e,mode:"edit"});case 2:return a=n.sent,n.next=5,r["i"](a).then(Object(o["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.fetchData();case 2:return e.next=4,t.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3});case 4:case"end":return e.stop()}}),e)}))));case 5:case"end":return n.stop()}}),n)})))()},handleDelete:function(e){var t=this;return Object(o["a"])(Object(s["a"])().mark((function n(){return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.$confirm("是否删除该数据？","删除提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 2:return n.next=4,r["c"](e.id).then(function(){var e=Object(o["a"])(Object(s["a"])().mark((function e(n){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.fetchData();case 2:return e.next=4,t.$notify({title:"成功",message:"删除成功",type:"success",duration:2e3});case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 4:case"end":return n.stop()}}),n)})))()}}},k=w,x=Object(v["a"])(k,a,i,!1,null,null,null);t["default"]=x.exports},fd1f:function(e,t,n){}}]);