(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c9b7e52e","chunk-17a231b2"],{"0f7c":function(e,t,r){},"1b55":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"wrapper-visual-api"},[r("div",{staticClass:"form-container"},[e.ruleForm?r("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,"status-icon":"",rules:e.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"数据源名称",prop:"datasource_id"}},[e.sourceList?r("el-select",{staticStyle:{width:"310px"},attrs:{placeholder:"请选择数据源"},model:{value:e.ruleForm.datasource_id,callback:function(t){e.$set(e.ruleForm,"datasource_id",t)},expression:"ruleForm.datasource_id"}},e._l(e.sourceList,(function(e){return r("el-option",{key:"datasource_id_"+e.id,attrs:{label:e.datasourceName,value:e.id}})})),1):e._e()],1),r("el-form-item",{attrs:{label:"请求的SQL",prop:"sql_text"}},[r("SqlEditor",{attrs:{height:"300px"},model:{value:e.ruleForm.sql_text,callback:function(t){e.$set(e.ruleForm,"sql_text",t)},expression:"ruleForm.sql_text"}})],1),r("el-form-item",{attrs:{label:"请求的参数",prop:"params"}},[r("el-input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],staticStyle:{"white-space":"pre"},attrs:{autosize:{minRows:2,maxRows:12},type:"textarea",placeholder:"{'name':'zhangsan'}"},model:{value:e.ruleForm.params,callback:function(t){e.$set(e.ruleForm,"params",t)},expression:"ruleForm.params"}}),r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,border:""}},[r("el-table-column",{attrs:{prop:"key",label:"key",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input",{attrs:{clearable:"",placeholder:"请输入参数名","validate-event":"false"},on:{input:e.setRuleParams},model:{value:t.row.key,callback:function(r){e.$set(t.row,"key",r)},expression:"scope.row.key"}})]}}],null,!1,2887340365)}),r("el-table-column",{attrs:{prop:"value",label:"value",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input",{attrs:{clearable:"",placeholder:"请输入参数值","validate-event":"false"},on:{input:e.setRuleParams},model:{value:t.row.value,callback:function(r){e.$set(t.row,"value",r)},expression:"scope.row.value"}})]}}],null,!1,3518541664)}),r("el-table-column",{attrs:{prop:"op",label:"操作",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{circle:"",icon:"el-icon-plus"},on:{click:function(t){return e.handleAdd()}}}),r("el-button",{attrs:{circle:"",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row,t.$index)}}})]}}],null,!1,3394260243)})],1)],1),r("el-form-item",[r("el-button",{staticStyle:{float:"right","margin-left":"30px"},attrs:{type:"primary"},on:{click:function(t){return e.testConnect("ruleForm")}}},[e._v("发送请求")]),r("el-button",{staticStyle:{float:"right","margin-left":"30px"},attrs:{type:"primary"},on:{click:e.formartSql}},[e._v("格式化SQL")])],1)],1):e._e()],1),r("div",{staticClass:"res-form",domProps:{innerHTML:e._s(e.resStr)}})])},l=[],s=(r("14d9"),r("fb6a"),r("a434"),r("b0c0"),r("e9c4"),r("ac1f"),r("5319"),r("1bf5")),o=r("db05"),n=r("b252"),i={components:{SqlEditor:s["a"]},data:function(){return{ruleForm:{name:"",path:"",group_id:"",describe:"",datasource_id:"",params:"",sql_text:""},tableData:[{check:!0,key:"",value:""}],paramsArr:[],rules:{name:[{required:!0,message:"不能为空",trigger:"blur"}],path:[{required:!0,message:"不能为空",trigger:"blur"}],group_id:[{required:!0,message:"不能为空",trigger:"blur"}],describe:[{required:!0,message:"不能为空",trigger:"blur"}],datasource_id:[{required:!0,message:"不能为空",trigger:"blur"}],sql_text:[{required:!0,message:"不能为空",trigger:"blur"}]},resStr:"",sourceList:null}},mounted:function(){var e=this;Object(n["c"])().then((function(t){e.sourceList=t.content.data}))},methods:{handleDelete:function(e,t){0!==t&&(this.tableData.splice(t,1),this.setRuleParams())},setRuleParams:function(){this.ruleForm.params="",this.ruleForm.params+="{";for(var e=!1,t=0;t<this.tableData.length;t++){var r=this.tableData[t];r.check&&(e=!0,this.ruleForm.params+="'"+r.key+"':'"+r.value+"',")}this.ruleForm.params=this.ruleForm.params.slice(0,this.ruleForm.params.length-1),e&&(this.ruleForm.params+="}")},handleAdd:function(){var e={check:!0,key:"",value:""};this.tableData.push(e),this.setRuleParams()},formartSql:function(){var e=this,t=this.ruleForm;this.ruleForm=null,t.sql_text=Object(o["format"])(t.sql_text).replace(/# /g,"#").replace(/{ /g,"{").replace(/ }/g,"}").replace(/< foreach/g,"\n<foreach\n").replace(/< \/ foreach >/g,"\n</foreach>\n").replace(/< if/g,"\n<if").replace(/< \/ if >/g,"\n</if>\n").replace(/<\nwhere\n {2}>/g,"\n<where>\n").replace(/< \/\nwhere\n {2}>/g,"\n</where>\n").replace(/< trim/g,"\n<trim").replace(/< \/ trim >/g,"\n</trim>\n").toLowerCase(),setTimeout((function(){e.ruleForm=t}),200)},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;Object(n["a"])({name:t.ruleForm.name,path:t.ruleForm.path,group_id:t.ruleForm.group_id,describe:t.ruleForm.describe,datasource_id:t.ruleForm.datasource_id,params:t.ruleForm.params,sql_text:t.ruleForm.sql_text}).then((function(){t.$message.success("新增成功"),t.ruleForm={name:"",path:"",group_id:"",describe:"",datasource_id:"",params:"",sql_text:""}}))}))},testConnect:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;Object(n["b"])({datasource_id:t.ruleForm.datasource_id,params:t.ruleForm.params,sql_text:t.ruleForm.sql_text}).then((function(e){t.resStr=JSON.stringify(e,null,2),t.$message.success("请求成功")}),(function(e){t.resStr=JSON.stringify(e,null,2)}))}))},resetForm:function(e){this.$refs[e].resetFields()}}},u=i,c=(r("95d0"),r("2877")),m=Object(c["a"])(u,a,l,!1,null,"738e42c3",null);t["default"]=m.exports},"3aa3":function(e,t,r){},"95d0":function(e,t,r){"use strict";r("3aa3")},a7be:function(e,t,r){},b252:function(e,t,r){"use strict";r.d(t,"a",(function(){return l})),r.d(t,"d",(function(){return s})),r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return n}));var a=r("b775");function l(e){return Object(a["a"])({url:"/api/apiConfig/add",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/api/apiConfig/update",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/api/apiConfig/execute",method:"post",data:e})}function n(e){return Object(a["a"])({url:"/api/apiConfig/listDataSourceName",method:"get",params:e})}}}]);