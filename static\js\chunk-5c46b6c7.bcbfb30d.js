(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c46b6c7"],{"00cd":function(t,a,s){},"05f7":function(t,a,s){"use strict";s("00cd")},a1b8:function(t,a,s){"use strict";s.r(a);var i=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"app-container"},[s("el-row",[s("el-col",{attrs:{span:16}},[s("el-card",{staticClass:"lCard"},[s("div",{staticClass:"co-header"},[s("p",[s("strong",[t._v("欢迎来到圈子")])]),s("div",[s("i",{staticClass:"el-icon-sort"}),s("span",{on:{click:function(a){return t.handleNew("addtime")}}},[t._v("最新")]),s("i",{staticClass:"el-icon-sort",staticStyle:{"margin-left":"10px"}}),s("span",{on:{click:function(a){return t.handleNew("likenumber")}}},[t._v("最热")])])]),s("div",{staticClass:"co-body"},[s("div",{staticClass:"left"},t._l(t.dataList,(function(a){return s("div",{key:a.id,staticClass:"co-block"},[s("el-image",{staticClass:"img",attrs:{src:t.url,fit:"fill"}}),s("el-card",{staticClass:"box-card"},[s("div",{attrs:{slot:"header"},slot:"header"},[s("el-row",[s("el-col",{attrs:{span:23}},[s("span",[t._v(t._s(a.addperson))]),s("i",{staticClass:"el-icon-time time"}),s("span",[t._v(t._s(a.addtime))])]),s("el-col",{attrs:{span:1}},[s("i",{staticClass:"el-icon-top",on:{click:t.topClick}})])],1)],1),s("div",{staticClass:"card-body"},[s("div",{staticClass:"text"},[t._v(" "+t._s(a.content)+" ")]),s("div",{staticClass:"bottom"},[s("el-row",[s("el-col",{attrs:{span:20}},[s("el-row",[s("el-col",{attrs:{span:4}},[s("i",{staticClass:"el-icon-chat-round",on:{click:t.chatClick}})]),s("el-col",{attrs:{span:4}},[s("i",{staticClass:"el-icon-link",on:{click:t.linkClick}})]),s("el-col",{attrs:{span:6}},[s("i",{staticClass:"el-icon-star-off",on:{click:t.likeClick}}),t._v(" "+t._s(a.likenumber)+" ")])],1)],1),s("el-col",{attrs:{span:3}},[s("span",[t._v(t._s(a.ttyple))])])],1)],1)])])],1)})),0)])])],1),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"rCard"},[s("el-card",{staticClass:"user"},[s("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[s("el-row",[s("el-col",{attrs:{span:21}},[s("div",{staticClass:"info"},[s("el-image",{staticClass:"img",staticStyle:{width:"50px",height:"50px"},attrs:{src:t.url,fit:"fill"}}),s("div",{staticClass:"tip"},[s("span",{staticClass:"name"},[t._v("Lark")]),s("span",[t._v("第99位会员")])])],1)]),s("el-col",{attrs:{span:3}},[s("el-button",{attrs:{type:"primary",icon:"el-icon-edit",circle:""}})],1)],1)],1),s("div",{staticClass:"body"},[s("div",{staticClass:"one"},[s("el-row",[s("el-col",{attrs:{span:8}},[s("div",[s("p",{staticClass:"num"},[t._v("0")]),s("p",{staticClass:"tip"},[t._v("提醒")])])]),s("el-col",{attrs:{span:8}},[s("div",{staticClass:"cText"},[s("p",{staticClass:"num"},[t._v("0")]),s("p",{staticClass:"tip"},[t._v("收藏")])])]),s("el-col",{attrs:{span:8}},[s("div",[s("p",{staticClass:"num"},[t._v("0")]),s("p",{staticClass:"tip"},[t._v("社区币")])])])],1)],1),s("div",{staticClass:"two"},[s("el-row",[s("el-col",{attrs:{span:16}},[s("div",{staticClass:"txt"},[t._v(" 我的特别关注 ")])]),s("el-col",{attrs:{span:6}},[s("div",[s("el-button",{attrs:{round:""}},[t._v("1000"),s("i",{staticClass:"el-icon-ice-cream"})])],1)])],1)],1),s("div",{staticClass:"three"},[s("el-row",[s("el-col",{attrs:{span:12}},[s("div",{staticClass:"lc",on:{click:t.addClick}},[s("el-icon",{staticClass:"el-icon-edit-outline"}),t._v(" 创作新主题 ")],1)]),s("el-col",{attrs:{span:12}},[s("div",{staticClass:"rc"},[s("el-icon",{staticClass:"el-icon-present"}),t._v(" 领取登录奖励 ")],1)])],1)],1)])]),s("el-card",{staticClass:"type"},[s("div",{staticClass:"btn"},t._l(t.type,(function(a){return s("div",{key:a.ttyple},[s("el-button",{attrs:{plain:"",round:"",size:"medium"},on:{click:function(s){return t.handleType(a)}}},[t._v(" "+t._s(a.ttyple)+" ")])],1)})),0)])],1)])],1),s("el-dialog",{attrs:{title:t.title,visible:t.dialogVisible},on:{"update:visible":function(a){t.dialogVisible=a}}},[s("el-form",{attrs:{model:t.form}},[s("el-form-item",{attrs:{label:"板块","label-width":t.labelWidth}},[s("el-select",{attrs:{placeholder:"请选择板块"},model:{value:t.form.ttyple,callback:function(a){t.$set(t.form,"ttyple",a)},expression:"form.ttyple"}},t._l(t.type,(function(t){return s("el-option",{attrs:{label:t.ttyple,value:t.ttyple}})})),1)],1),s("el-form-item",{attrs:{label:"内容","label-width":t.labelWidth}},[s("el-input",{attrs:{type:"textarea",rows:"20",autocomplete:"off"},model:{value:t.form.content,callback:function(a){t.$set(t.form,"content",a)},expression:"form.content"}})],1)],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(a){t.dialogVisible=!1}}},[t._v("取 消")]),s("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")])],1)],1)],1)},l=[],e=(s("d3b7"),s("da69")),n={name:"Community",data:function(){return{url:"https://www.tianya.im/avatar/21/46/34741/100",dataList:[],total:null,type:null,title:"新增",dialogVisible:!1,form:{ttyple:null,content:null},labelWidth:"120px"}},created:function(){var t=this;this.list(),e["a"]().then((function(a){var s=a.content.data;t.type=s}))},methods:{list:function(){var t=this,a=this.$loading({lock:!0,text:"Loading",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e["c"](this.listQuery).then((function(a){var s=a.content;t.total=s.recordsTotal,t.dataList=s.data})).finally((function(){a.close()}))},topClick:function(){this.$message.success("topClick")},likeClick:function(){this.$message.success("likeClick")},linkClick:function(){this.$message.success("linkClick")},chatClick:function(){this.$message.success("chatClick")},addClick:function(){this.dialogVisible=!0},submitForm:function(){var t=this;e["b"](this.form).then((function(){t.list(),t.dialogVisible=!1,t.$notify({title:"新增操作",message:"新增操作 成功",type:"success",duration:2e3})}))},handleNew:function(t){var a=this,s=this.$loading({lock:!0,text:"Loading",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e["c"]({ofiled:t}).then((function(t){var s=t.content;a.total=s.recordsTotal,a.dataList=s.data})).finally((function(){s.close()}))},handleType:function(t){var a=this,s=this.$loading({lock:!0,text:"Loading",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e["c"]({ttype:t.ttyple}).then((function(t){var s=t.content;a.total=s.recordsTotal,a.dataList=s.data})).finally((function(){s.close()}))}}},c=n,o=(s("05f7"),s("2877")),r=Object(o["a"])(c,i,l,!1,null,"28a73ef6",null);a["default"]=r.exports},da69:function(t,a,s){"use strict";s.d(a,"c",(function(){return l})),s.d(a,"d",(function(){return e})),s.d(a,"b",(function(){return n})),s.d(a,"a",(function(){return c}));var i=s("b775");function l(t){return Object(i["a"])({url:"/api/circle/list",method:"get",params:t})}function e(t){return Object(i["a"])({url:"/api/goods/update",method:"post",data:t})}function n(t){return Object(i["a"])({url:"/api/circle/add",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/api/circle/findAllType",method:"get",params:t})}}}]);