(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9ff42110"],{"3041f":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"wrap"},[t._m(0),i("div",{staticClass:"canvas-card"},[i("div",[i("el-button",{attrs:{type:"primary",plain:""},on:{click:t.verifyTask}},[t._v("校验任务")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.issueTaskFlag,expression:"issueTaskFlag"}],attrs:{type:"primary",plain:""},on:{click:t.beforeIssue}},[t._v("发布任务")]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.editTask,expression:"editTask"}],attrs:{type:"primary",plain:""},on:{click:t.onEditTask}},[t._v("修改任务")]),i("el-button",{attrs:{type:"primary",plain:""},on:{click:t.exportPNG}},[t._v("下载DAG任务图")]),i("el-button",{attrs:{type:"primary",plain:""},on:{click:t.resetSvg}},[t._v("重置画布")])],1),i("div",{attrs:{id:"container"}})]),i("el-drawer",{attrs:{direction:"rtl",size:"20%",visible:t.dialog,"custom-class":"demo-drawer","close-on-press-escape":""},on:{"update:visible":function(e){t.dialog=e}}},[i("div",{staticClass:"demo-drawer__content"},[i("el-form",{staticClass:"formStyle",attrs:{model:t.form,"label-width":"90px"}},[i("el-form-item",{attrs:{label:"层级名称:"}},[i("el-input",{attrs:{disabled:!0},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),i("el-form-item",{attrs:{label:"任务名称:"}},[i("el-input",{model:{value:t.form.taskName,callback:function(e){t.$set(t.form,"taskName",e)},expression:"form.taskName"}})],1),i("el-form-item",{attrs:{label:"脚本名称:"}},[i("el-select",{attrs:{placeholder:"请选择脚本名称"},model:{value:t.form.scriptName,callback:function(e){t.$set(t.form,"scriptName",e)},expression:"form.scriptName"}},t._l(t.scriptList,(function(t){return i("el-option",{key:t.id,attrs:{label:t.filename,value:t.filename}})})),1)],1)],1),i("div",{staticClass:"drawer__footer btnStyle"},[i("el-button",{on:{click:t.cancelForm}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:t.handleClick}},[t._v(t._s(t.loading?"提交中 ...":"确 定"))])],1)],1)]),i("el-dialog",{attrs:{title:"发布任务",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[i("el-form",{attrs:{model:t.issueForm,"label-width":"90px"}},[i("el-form-item",{attrs:{label:"任务名称:"}},[i("el-input",{model:{value:t.issueForm.taskName,callback:function(e){t.$set(t.issueForm,"taskName",e)},expression:"issueForm.taskName"}})],1),i("el-dialog",{attrs:{title:"配置调度表达式",visible:t.showCronBox,width:"60%","append-to-body":""},on:{"update:visible":function(e){t.showCronBox=e}}},[i("cron",{model:{value:t.issueForm.expression,callback:function(e){t.$set(t.issueForm,"expression",e)},expression:"issueForm.expression"}}),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.showCronBox=!1}}},[t._v("关闭")]),i("el-button",{attrs:{type:"primary"},on:{click:function(e){t.showCronBox=!1}}},[t._v("确 定")])],1)],1),i("el-form-item",{attrs:{label:"调度表达式",prop:"expression"}},[i("el-input",{attrs:{"auto-complete":"off",placeholder:"请输入Cron表达式"},model:{value:t.issueForm.expression,callback:function(e){t.$set(t.issueForm,"expression",e)},expression:"issueForm.expression"}},[t.showCronBox?i("el-button",{attrs:{slot:"append",icon:"el-icon-open",title:"关闭图形配置"},on:{click:function(e){t.showCronBox=!1}},slot:"append"}):i("el-button",{attrs:{slot:"append",icon:"el-icon-turn-off",title:"打开图形配置"},on:{click:function(e){t.showCronBox=!0}},slot:"append"})],1)],1)],1),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),i("el-button",{attrs:{type:"primary"},on:{click:t.issueTask}},[t._v("确 定")])],1)],1)],1)},r=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"menu-bar"},[i("div",{attrs:{id:"stencil"}})])}],a=i("2909"),o=i("5530"),n=(i("99af"),i("4de4"),i("a630"),i("caad"),i("a15b"),i("d81d"),i("14d9"),i("b0c0"),i("e9c4"),i("4ec9"),i("b64b"),i("d3b7"),i("07ac"),i("2532"),i("3ca3"),i("ddb0"),i("5728")),l=i("2b91"),c=i("4904"),h=i("54cf"),u=i("b458"),d=i("0806"),m=i("e619"),f=i("545c"),p=i("17df"),g=i("b775");function b(){return Object(g["a"])({url:"/api/warnrule/listScheduleFileName",method:"get"})}function v(t){return Object(g["a"])({url:"/api/dagSchedual/checkTask",method:"post",data:t})}function k(t){return Object(g["a"])({url:"/api/dagSchedual/runTask",method:"post",data:t})}function F(t){return Object(g["a"])({url:"/api/dagSchedual/updateDAGTask",method:"post",data:t})}var y=i("5ec8"),w={name:"TaskDag",components:{cron:y["a"]},data:function(){return{nodeList:[],scriptList:[],graph:null,json:null,node:null,width:null,height:null,originId:null,dialog:!1,loading:!1,dialogVisible:!1,form:{},issueForm:{},showCronBox:!1,editTask:!1,issueTaskFlag:!0,formData:[],originData:{}}},mounted:function(){var t,e,i;if(this.width=document.querySelector(".wrap").clientWidth-220,this.height=document.querySelector(".app-main").clientHeight-50,this.init(),null!==(t=this.$route.params)&&void 0!==t&&t.data){var s=this.$route.params.data,r=s.taskcode,a=s.describe,o=JSON.parse(a),n=o.rparams;this.issueTaskFlag=!1,this.originData=n,this.originId=r,this.graph.fromJSON(this.originData.taskdagstr)}"createTask"===(null===(e=this.$route.params)||void 0===e?void 0:e.origin)&&(this.editTask=!1),"updateTask"===(null===(i=this.$route.params)||void 0===i?void 0:i.origin)&&(this.editTask=!0)},created:function(){this.listScriptList()},methods:{init:function(){var t=new n["m"]({container:document.getElementById("container"),width:this.width,height:this.height,grid:!0,mousewheel:{enabled:!0,zoomAtMousePosition:!0,modifiers:"ctrl",minScale:.5,maxScale:3},connecting:{router:"manhattan",connector:{name:"rounded",args:{radius:8}},anchor:"center",connectionPoint:"anchor",allowBlank:!1,snap:{radius:20},createEdge:function(){return new n["u"].Edge({attrs:{line:{stroke:"#A2B1C3",strokeWidth:2,targetMarker:{name:"block",width:12,height:8}}},tools:[{name:"edge-editor",args:{attrs:{backgroundColor:"#fff"}}}],zIndex:0})},validateConnection:function(t){var e=t.targetMagnet;return!!e}},highlighting:{magnetAdsorbed:{name:"stroke",args:{attrs:{fill:"#5F95FF",stroke:"#5F95FF"}}}}});t.use(new c["a"]({resizing:!0,rotating:!0})).use(new h["a"]({rubberband:!0,showNodeSelectionBox:!0})).use(new u["a"]).use(new d["a"]).use(new m["a"]).use(new f["a"]).use(new p["a"]);var e={groups:{top:{position:"top",attrs:{circle:{r:4,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},right:{position:"right",attrs:{circle:{r:4,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},bottom:{position:"bottom",attrs:{circle:{r:4,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}},left:{position:"left",attrs:{circle:{r:4,magnet:!0,stroke:"#5F95FF",strokeWidth:1,fill:"#fff",style:{visibility:"hidden"}}}}},items:[{group:"top"},{group:"right"},{group:"bottom"},{group:"left"}]},i=new l["a"]({title:"数仓层级类型",target:t,stencilGraphWidth:150,stencilGraphHeight:500,collapsable:!1,nodeMovable:!0,stencilGraphPadding:100,groups:[{title:"数仓层级类型",name:"group1"}],layoutOptions:{columns:1,columnWidth:90,rowHeight:65,dx:50}});document.getElementById("stencil").appendChild(i.container),n["m"].registerNode("custom-rect",{inherit:"rect",width:100,height:40,attrs:{body:{strokeWidth:1,stroke:"#5F95FF",fill:"#EFF4FF"},text:{fontSize:14,fill:"#262626"}},ports:Object(o["a"])({},e),tools:[{name:"node-editor",args:{x:20,y:10,attrs:{backgroundColor:"#EFF4FF",fontSize:"16"}}}]},!0),n["m"].registerNode("custom-polygon",{inherit:"polygon",width:80,height:40,attrs:{body:{strokeWidth:1,stroke:"#5F95FF",fill:"#EFF4FF"},text:{fontSize:14,fill:"#262626"}},ports:Object(o["a"])({},e),tools:[{name:"node-editor",args:{x:20,y:10,attrs:{backgroundColor:"#EFF4FF",fontSize:"16"}}}]},!0),n["m"].registerNode("custom-circle",{inherit:"circle",width:60,height:60,attrs:{body:{strokeWidth:1,stroke:"#5F95FF",fill:"#EFF4FF"},text:{fontSize:14,fill:"#262626"}},ports:Object(o["a"])({},e),tools:[{name:"node-editor",args:{x:0,y:20,attrs:{backgroundColor:"#EFF4FF",fontSize:"16"}}}]},!0);var s=t.createNode({shape:"custom-rect",label:"开始",attrs:{name:"开始",body:{rx:20,ry:26}}}),r=t.createNode({shape:"custom-rect",attrs:{body:{rx:6,ry:6},name:"贴源层"},label:"贴源层"}),a=t.createNode({shape:"custom-rect",attrs:{body:{rx:6,ry:6},name:"数据细节层"},label:"数据细节层"}),g=t.createNode({shape:"custom-rect",attrs:{body:{rx:6,ry:6},name:"数据中间层"},label:"数据中间层"}),b=t.createNode({shape:"custom-rect",attrs:{body:{rx:6,ry:6},name:"数据服务层"},label:"数据服务层"}),v=t.createNode({shape:"custom-rect",attrs:{body:{rx:6,ry:6},name:"数据应用层"},label:"数据应用层"}),k=t.createNode({shape:"custom-rect",label:"结束",attrs:{name:"结束",body:{rx:20,ry:26}}});i.load([s,r,a,g,b,v,k],"group1"),this.graph=t,this.onMouse(),this.KeyDown(),this.clickEvent()},KeyDown:function(){var t=this;this.graph.bindKey(["meta+c","ctrl+c"],(function(){var e=t.graph.getSelectedCells();return e.length&&t.graph.copy(e),!1})),this.graph.bindKey(["delete"],(function(){var e=t.graph.getSelectedCells();return e.length&&t.graph.removeCells(e),!1})),this.graph.bindKey(["meta+x","ctrl+x"],(function(){var e=t.graph.getSelectedCells();return e.length&&t.graph.cut(e),!1})),this.graph.bindKey(["meta+v","ctrl+v"],(function(){if(!t.graph.isClipboardEmpty()){var e=t.graph.paste({offset:32});t.graph.cleanSelection(),t.graph.select(e)}return!1})),this.graph.bindKey(["meta+z","ctrl+z"],(function(){return t.graph.canUndo()&&t.graph.undo(),!1})),this.graph.bindKey(["meta+shift+z","ctrl+shift+z"],(function(){return t.graph.canRedo()&&t.graph.redo(),!1})),this.graph.bindKey(["meta+a","ctrl+a"],(function(){var e=t.graph.getNodes();e&&t.graph.select(e)})),this.graph.bindKey("backspace",(function(){var e=t.graph.getSelectedCells();e.length&&t.graph.removeCells(e)})),this.graph.bindKey(["ctrl+1","meta+1"],(function(){var e=t.graph.zoom();e<1.5&&t.graph.zoom(.1)})),this.graph.bindKey(["ctrl+2","meta+2"],(function(){var e=t.graph.zoom();e>.5&&t.graph.zoom(-.1)}))},showPorts:function(t,e){for(var i=0,s=t.length;i<s;i+=1)t[i].style.visibility=e?"visible":"hidden"},clickEvent:function(){var t=this;this.resetForm(),this.graph.on("node:dblclick",(function(e){var i=e.node;if(t.resetForm(),t.formData.length>0){var s=t.formData.filter((function(t){return t.id==i.id}));s.length>0?t.form=s[0]:t.setForm(i)}else t.setForm(i);t.node=i,t.dialog=!0}))},onMouse:function(){var t=this;this.graph.on("node:mouseenter",(function(){var e=document.getElementById("container"),i=e.querySelectorAll(".x6-port-body");t.showPorts(i,!0)})),this.graph.on("node:mouseleave",(function(){var e=document.getElementById("container"),i=e.querySelectorAll(".x6-port-body");t.showPorts(i,!1)}))},listScriptList:function(){var t=this;b().then((function(e){t.scriptList=e.content.data}))},exportPNG:function(){this.graph.exportPNG("larkmidtable",{quality:.92,backgroundColor:"white",padding:20})},exportJSON:function(){this.graph.toJSON(),this.json=this.graph.toJSON(),this.graph.clearCells()},importJSON:function(){this.graph.fromJSON(this.json)},verifyTask:function(){var t=this;this.json=this.graph.toJSON();var e=this.verifyFlow(this.json),i=this.getIds(this.json),s=[],r=[],o=[];if(s=this.formData.map((function(t){if(i.includes(t.id))return t.id})),"{}"!=JSON.stringify(this.originData)&&(r=this.originData.attrstr,s=this.originData.attrstr.map((function(t){if(i.includes(t.id))return t.id}))),i.length!==s.length){var n=i.filter((function(t){return-1===s.indexOf(t)}));o=n.map((function(t){return{id:t}}))}var l=[].concat(Object(a["a"])(o),Object(a["a"])(this.formData),Object(a["a"])(r)),c=Array.from(new Map(l.map((function(t){return[t.id,t]}))).values());if(e){var h={rparams:{taskdagstr:this.json,attrstr:c}};v(h).then((function(){t.$notify({title:"校验操作",message:"校验成功",type:"success",duration:2e3})}))}},createTask:function(){this.json=this.graph.toJSON()},beforeIssue:function(){this.resetIssueForm(),"{}"!=JSON.stringify(this.originData)&&(this.issueForm.expression=this.originData.expression,this.issueForm.taskName=this.originData.taskName),this.dialogVisible=!0},issueTask:function(){var t=this;this.json=this.graph.toJSON();var e=this.verifyFlow(this.json),i=this.getIds(this.json),s=this.formData.map((function(t){if(i.includes(t.id))return t.id})),r=[];if(i.length!==s.length){var o=i.filter((function(t){return-1===s.indexOf(t)}));r=o.map((function(t){return{id:t}}))}if(e)if(null!=this.issueForm.id){var n={rparams:{taskdagstr:this.json,attrstr:[].concat(Object(a["a"])(this.formData),Object(a["a"])(r)),taskName:this.issueForm.taskName,expression:this.issueForm.expression,id:this.issueForm.id}};F(n).then((function(){t.dialogVisible=!1,t.$notify({title:"发布操作",message:"修改成功",type:"success",duration:2e3})}))}else{var l={rparams:{taskdagstr:this.json,attrstr:[].concat(Object(a["a"])(this.formData),Object(a["a"])(r)),taskName:this.issueForm.taskName,expression:this.issueForm.expression}};k(l).then((function(){t.dialogVisible=!1,t.$notify({title:"发布操作",message:"发布成功",type:"success",duration:2e3})}))}},pauseTask:function(){this.json=this.graph.toJSON()},cancelForm:function(){this.dialog=!1},handleClick:function(){this.formData.push(this.form),this.node.attrs.label=this.form.taskName,this.node.label=this.form.taskName,this.dialog=!1,this.resetForm()},resetForm:function(){this.form={id:null,name:null,taskName:null,scriptName:null}},resetIssueForm:function(){this.issueForm={taskName:null,expression:null,id:null}},setForm:function(t){var e=t.id,i=t.attrs;if("{}"===JSON.stringify(this.originData))this.form.id=e,this.form.name=i.name,this.form.taskName=i.name;else{var s,r,a,o,n=Object.values(i.name).join(""),l=this.originData.attrstr,c=l.filter((function(t){return t.id===e}));this.form.id=(null===(s=c[0])||void 0===s?void 0:s.id)||e,this.form.name=(null===(r=c[0])||void 0===r?void 0:r.name)||n,this.form.taskName=(null===(a=c[0])||void 0===a?void 0:a.taskName)||n,this.form.scriptName=(null===(o=c[0])||void 0===o?void 0:o.scriptName)||null}},verifyFlow:function(t){var e=t.cells;if(e.length<3)this.$message.warning("dag图至少需要3个节点");else{var i=e.filter((function(t){return"edge"===t.shape})),s=e.filter((function(t){return"edge"!==t.shape}));if(s.length-i.length>1)this.$message.warning("存在未连接节点");else{var r=s.filter((function(t){return"开始"===t.attrs.text.text}));if(0!==r.length)if(r.length>1)this.$message.warning("存在多余开始节点");else{var a=s.filter((function(t){return"结束"===t.attrs.text.text}));if(0!==a.length){if(!(a.length>1))return!0;this.$message.warning("存在多余结束节点")}else this.$message.warning("缺少结束节点")}else this.$message.warning("缺少开始节点")}}},getIds:function(t){var e=t.cells;return e.filter((function(t){return"edge"!==t.shape})).map((function(t){return t.id}))},resetSvg:function(){this.editTask=!0,this.issueTaskFlag=!0,this.originData={},this.resetIssueForm(),this.graph.clearCells()},onEditTask:function(){this.resetIssueForm(),"{}"!=JSON.stringify(this.originData)&&(this.issueForm.expression=this.originData.expression,this.issueForm.taskName=this.originData.taskName,this.issueForm.id=this.originId),this.dialogVisible=!0}}},x=w,N=(i("8a91"),i("2877")),S=Object(N["a"])(x,s,r,!1,null,"af90c864",null);e["default"]=S.exports},"7901e":function(t,e,i){},"8a91":function(t,e,i){"use strict";i("7901e")}}]);