(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-89639478"],{"0fc3":function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E.createOperatorRegex=n,E.createLineCommentRegex=R,E.createReservedWordRegex=r,E.createWordRegex=N,E.createStringRegex=o,E.createStringPattern=I,E.createParenRegex=O,E.createPlaceholderRegex=S;var T=t("cd49");function n(e){return new RegExp("^(".concat((0,T.sortByLengthDesc)(e).map(T.escapeRegExp).join("|"),"|.)"),"u")}function R(e){return new RegExp("^((?:".concat(e.map((function(e){return(0,T.escapeRegExp)(e)})).join("|"),").*?)(?:\r\n|\r|\n|$)"),"u")}function r(e){if(0===e.length)return new RegExp("^\b$","u");var E=(0,T.sortByLengthDesc)(e).join("|").replace(/ /g,"\\s+");return new RegExp("^(".concat(E,")\\b"),"iu")}function N(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return new RegExp("^([\\p{Alphabetic}\\p{Mark}\\p{Decimal_Number}\\p{Connector_Punctuation}\\p{Join_Control}".concat(e.join(""),"]+)"),"u")}function o(e){return new RegExp("^("+I(e)+")","u")}function I(e){var E={"``":"((`[^`]*($|`))+)","{}":"((\\{[^\\}]*($|\\}))+)","[]":"((\\[[^\\]]*($|\\]))(\\][^\\]]*($|\\]))*)",'""':'(("[^"\\\\]*(?:\\\\.[^"\\\\]*)*("|$))+)',"''":"(('[^'\\\\]*(?:\\\\.[^'\\\\]*)*('|$))+)","N''":"((N'[^'\\\\]*(?:\\\\.[^'\\\\]*)*('|$))+)","U&''":"((U&'[^'\\\\]*(?:\\\\.[^'\\\\]*)*('|$))+)",'U&""':'((U&"[^"\\\\]*(?:\\\\.[^"\\\\]*)*("|$))+)',$$:"((?<tag>\\$\\w*\\$)[\\s\\S]*?(?:\\k<tag>|$))"};return e.map((function(e){return E[e]})).join("|")}function O(e){return new RegExp("^("+e.map(A).join("|")+")","iu")}function A(e){return 1===e.length?(0,T.escapeRegExp)(e):"\\b"+e+"\\b"}function S(e,E){if((0,T.isEmpty)(e))return!1;var t=e.map(T.escapeRegExp).join("|");return new RegExp("^((?:".concat(t,")(?:").concat(E,"))"),"u")}},"15e2":function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var T=t("cd49");function n(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function R(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function r(e,E,t){return E&&R(e.prototype,E),t&&R(e,t),e}var N="top-level",o="block-level",I=function(){function e(E){n(this,e),this.indent=E||"  ",this.indentTypes=[]}return r(e,[{key:"getIndent",value:function(){return this.indent.repeat(this.indentTypes.length)}},{key:"increaseTopLevel",value:function(){this.indentTypes.push(N)}},{key:"increaseBlockLevel",value:function(){this.indentTypes.push(o)}},{key:"decreaseTopLevel",value:function(){this.indentTypes.length>0&&(0,T.last)(this.indentTypes)===N&&this.indentTypes.pop()}},{key:"decreaseBlockLevel",value:function(){while(this.indentTypes.length>0){var e=this.indentTypes.pop();if(e!==N)break}}},{key:"resetIndentation",value:function(){this.indentTypes=[]}}]),e}();E["default"]=I,e.exports=E.default},1847:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["ABS","ALL","ALLOCATE","ALTER","AND","ANY","ARE","ARRAY","AS","ASENSITIVE","ASYMMETRIC","AT","ATOMIC","AUTHORIZATION","AVG","BEGIN","BETWEEN","BIGINT","BINARY","BLOB","BOOLEAN","BOTH","BY","CALL","CALLED","CARDINALITY","CASCADED","CASE","CAST","CEIL","CEILING","CHAR","CHAR_LENGTH","CHARACTER","CHARACTER_LENGTH","CHECK","CLOB","CLOSE","COALESCE","COLLATE","COLLECT","COLUMN","COMMIT","CONDITION","CONNECT","CONSTRAINT","CONVERT","CORR","CORRESPONDING","COUNT","COVAR_POP","COVAR_SAMP","CREATE","CROSS","CUBE","CUME_DIST","CURRENT","CURRENT_CATALOG","CURRENT_DATE","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_PATH","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CURRENT_USER","CURSOR","CYCLE","DATE","DAY","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DELETE","DENSE_RANK","DEREF","DESCRIBE","DETERMINISTIC","DISCONNECT","DISTINCT","DOUBLE","DROP","DYNAMIC","EACH","ELEMENT","ELSE","END","END-EXEC","ESCAPE","EVERY","EXCEPT","EXEC","EXECUTE","EXISTS","EXP","EXTERNAL","EXTRACT","FALSE","FETCH","FILTER","FLOAT","FLOOR","FOR","FOREIGN","FREE","FROM","FULL","FUNCTION","FUSION","GET","GLOBAL","GRANT","GROUP","GROUPING","HAVING","HOLD","HOUR","IDENTITY","IN","INDICATOR","INNER","INOUT","INSENSITIVE","INSERT","INT","INTEGER","INTERSECT","INTERSECTION","INTERVAL","INTO","IS","JOIN","LANGUAGE","LARGE","LATERAL","LEADING","LEFT","LIKE","LIKE_REGEX","LN","LOCAL","LOCALTIME","LOCALTIMESTAMP","LOWER","MATCH","MAX","MEMBER","MERGE","METHOD","MIN","MINUTE","MOD","MODIFIES","MODULE","MONTH","MULTISET","NATIONAL","NATURAL","NCHAR","NCLOB","NEW","NO","NONE","NORMALIZE","NOT","NULL","NULLIF","NUMERIC","OCTET_LENGTH","OCCURRENCES_REGEX","OF","OLD","ON","ONLY","OPEN","OR","ORDER","OUT","OUTER","OVER","OVERLAPS","OVERLAY","PARAMETER","PARTITION","PERCENT_RANK","PERCENTILE_CONT","PERCENTILE_DISC","POSITION","POSITION_REGEX","POWER","PRECISION","PREPARE","PRIMARY","PROCEDURE","RANGE","RANK","READS","REAL","RECURSIVE","REF","REFERENCES","REFERENCING","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","RELEASE","RESULT","RETURN","RETURNS","REVOKE","RIGHT","ROLLBACK","ROLLUP","ROW","ROW_NUMBER","ROWS","SAVEPOINT","SCOPE","SCROLL","SEARCH","SECOND","SELECT","SENSITIVE","SESSION_USER","SET","SIMILAR","SMALLINT","SOME","SPECIFIC","SPECIFICTYPE","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQRT","START","STATIC","STDDEV_POP","STDDEV_SAMP","SUBMULTISET","SUBSTRING","SUBSTRING_REGEX","SUM","SYMMETRIC","SYSTEM","SYSTEM_USER","TABLE","TABLESAMPLE","THEN","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TRAILING","TRANSLATE","TRANSLATE_REGEX","TRANSLATION","TREAT","TRIGGER","TRIM","TRUE","UESCAPE","UNION","UNIQUE","UNKNOWN","UNNEST","UPDATE","UPPER","USER","USING","VALUE","VALUES","VAR_POP","VAR_SAMP","VARBINARY","VARCHAR","VARYING","WHEN","WHENEVER","WHERE","WIDTH_BUCKET","WINDOW","WITH","WITHIN","WITHOUT","YEAR"],c=["ADD","ALTER COLUMN","ALTER TABLE","CASE","DELETE FROM","END","FETCH FIRST","FETCH NEXT","FETCH PRIOR","FETCH LAST","FETCH ABSOLUTE","FETCH RELATIVE","FROM","GROUP BY","HAVING","INSERT INTO","LIMIT","ORDER BY","SELECT","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],f=["INTERSECT","INTERSECT ALL","INTERSECT DISTINCT","UNION","UNION ALL","UNION DISTINCT","EXCEPT","EXCEPT ALL","EXCEPT DISTINCT"],l=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:['""',"''"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[],lineCommentTypes:["--"]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},"1d3a":function(e,E,t){"use strict";function T(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function n(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function R(e,E,t){return E&&n(e.prototype,E),t&&n(e,t),e}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var r=function(){function e(E){T(this,e),this.params=E,this.index=0}return R(e,[{key:"get",value:function(e){var E=e.key,t=e.value;return this.params?E?this.params[E]:this.params[this.index++]:t}}]),e}();E["default"]=r,e.exports=E.default},2673:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=o(t("a48e")),R=t("4fdb"),r=o(t("c606")),N=o(t("6b51"));function o(e){return e&&e.__esModule?e:{default:e}}function I(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function O(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function A(e,E,t){return E&&O(e.prototype,E),t&&O(e,t),e}function S(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&L(e,E)}function L(e,E){return L=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},L(e,E)}function u(e){var E=a();return function(){var t,T=c(e);if(E){var n=c(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return i(this,t)}}function i(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?C(e):E}function C(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function a(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}var f=["A","ACCESSIBLE","AGENT","AGGREGATE","ALL","ALTER","ANY","ARRAY","AS","ASC","AT","ATTRIBUTE","AUTHID","AVG","BETWEEN","BFILE_BASE","BINARY_INTEGER","BINARY","BLOB_BASE","BLOCK","BODY","BOOLEAN","BOTH","BOUND","BREADTH","BULK","BY","BYTE","C","CALL","CALLING","CASCADE","CASE","CHAR_BASE","CHAR","CHARACTER","CHARSET","CHARSETFORM","CHARSETID","CHECK","CLOB_BASE","CLONE","CLOSE","CLUSTER","CLUSTERS","COALESCE","COLAUTH","COLLECT","COLUMNS","COMMENT","COMMIT","COMMITTED","COMPILED","COMPRESS","CONNECT","CONSTANT","CONSTRUCTOR","CONTEXT","CONTINUE","CONVERT","COUNT","CRASH","CREATE","CREDENTIAL","CURRENT","CURRVAL","CURSOR","CUSTOMDATUM","DANGLING","DATA","DATE_BASE","DATE","DAY","DECIMAL","DEFAULT","DEFINE","DELETE","DEPTH","DESC","DETERMINISTIC","DIRECTORY","DISTINCT","DO","DOUBLE","DROP","DURATION","ELEMENT","ELSIF","EMPTY","END","ESCAPE","EXCEPTIONS","EXCLUSIVE","EXECUTE","EXISTS","EXIT","EXTENDS","EXTERNAL","EXTRACT","FALSE","FETCH","FINAL","FIRST","FIXED","FLOAT","FOR","FORALL","FORCE","FROM","FUNCTION","GENERAL","GOTO","GRANT","GROUP","HASH","HEAP","HIDDEN","HOUR","IDENTIFIED","IF","IMMEDIATE","IN","INCLUDING","INDEX","INDEXES","INDICATOR","INDICES","INFINITE","INSTANTIABLE","INT","INTEGER","INTERFACE","INTERVAL","INTO","INVALIDATE","IS","ISOLATION","JAVA","LANGUAGE","LARGE","LEADING","LENGTH","LEVEL","LIBRARY","LIKE","LIKE2","LIKE4","LIKEC","LIMITED","LOCAL","LOCK","LONG","MAP","MAX","MAXLEN","MEMBER","MERGE","MIN","MINUTE","MLSLABEL","MOD","MODE","MONTH","MULTISET","NAME","NAN","NATIONAL","NATIVE","NATURAL","NATURALN","NCHAR","NEW","NEXTVAL","NOCOMPRESS","NOCOPY","NOT","NOWAIT","NULL","NULLIF","NUMBER_BASE","NUMBER","OBJECT","OCICOLL","OCIDATE","OCIDATETIME","OCIDURATION","OCIINTERVAL","OCILOBLOCATOR","OCINUMBER","OCIRAW","OCIREF","OCIREFCURSOR","OCIROWID","OCISTRING","OCITYPE","OF","OLD","ON","ONLY","OPAQUE","OPEN","OPERATOR","OPTION","ORACLE","ORADATA","ORDER","ORGANIZATION","ORLANY","ORLVARY","OTHERS","OUT","OVERLAPS","OVERRIDING","PACKAGE","PARALLEL_ENABLE","PARAMETER","PARAMETERS","PARENT","PARTITION","PASCAL","PCTFREE","PIPE","PIPELINED","PLS_INTEGER","PLUGGABLE","POSITIVE","POSITIVEN","PRAGMA","PRECISION","PRIOR","PRIVATE","PROCEDURE","PUBLIC","RAISE","RANGE","RAW","READ","REAL","RECORD","REF","REFERENCE","RELEASE","RELIES_ON","REM","REMAINDER","RENAME","RESOURCE","RESULT_CACHE","RESULT","RETURN","RETURNING","REVERSE","REVOKE","ROLLBACK","ROW","ROWID","ROWNUM","ROWTYPE","SAMPLE","SAVE","SAVEPOINT","SB1","SB2","SB4","SEARCH","SECOND","SEGMENT","SELF","SEPARATE","SEQUENCE","SERIALIZABLE","SHARE","SHORT","SIZE_T","SIZE","SMALLINT","SOME","SPACE","SPARSE","SQL","SQLCODE","SQLDATA","SQLERRM","SQLNAME","SQLSTATE","STANDARD","START","STATIC","STDDEV","STORED","STRING","STRUCT","STYLE","SUBMULTISET","SUBPARTITION","SUBSTITUTABLE","SUBTYPE","SUCCESSFUL","SUM","SYNONYM","SYSDATE","TABAUTH","TABLE","TDO","THE","THEN","TIME","TIMESTAMP","TIMEZONE_ABBR","TIMEZONE_HOUR","TIMEZONE_MINUTE","TIMEZONE_REGION","TO","TRAILING","TRANSACTION","TRANSACTIONAL","TRIGGER","TRUE","TRUSTED","TYPE","UB1","UB2","UB4","UID","UNDER","UNIQUE","UNPLUG","UNSIGNED","UNTRUSTED","USE","USER","USING","VALIDATE","VALIST","VALUE","VARCHAR","VARCHAR2","VARIABLE","VARIANCE","VARRAY","VARYING","VIEW","VIEWS","VOID","WHENEVER","WHILE","WITH","WORK","WRAPPED","WRITE","YEAR","ZONE"],l=["ADD","ALTER COLUMN","ALTER TABLE","BEGIN","CONNECT BY","DECLARE","DELETE FROM","DELETE","END","EXCEPT","EXCEPTION","FETCH FIRST","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","LOOP","MODIFY","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","START WITH","UPDATE","VALUES","WHERE"],s=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],D=["AND","CROSS APPLY","ELSE","END","OR","OUTER APPLY","WHEN","XOR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],U=function(e){S(t,e);var E=u(t);function t(){return I(this,t),E.apply(this,arguments)}return A(t,[{key:"tokenizer",value:function(){return new r["default"]({reservedWords:f,reservedTopLevelWords:l,reservedNewlineWords:D,reservedTopLevelWordsNoIndent:s,stringTypes:['""',"N''","''","``"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[":"],lineCommentTypes:["--"],specialWordChars:["_","$","#",".","@"],operators:["||","**","!=",":="]})}},{key:"tokenOverride",value:function(e){return(0,R.isSet)(e)&&(0,R.isBy)(this.previousReservedToken)?{type:N["default"].RESERVED,value:e.value}:e}}]),t}(n["default"]);E["default"]=U,e.exports=E.default},"2e6f":function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["ALL","ALTER","ANALYZE","AND","ANY","ARRAY","AS","ASC","BEGIN","BETWEEN","BINARY","BOOLEAN","BREAK","BUCKET","BUILD","BY","CALL","CASE","CAST","CLUSTER","COLLATE","COLLECTION","COMMIT","CONNECT","CONTINUE","CORRELATE","COVER","CREATE","DATABASE","DATASET","DATASTORE","DECLARE","DECREMENT","DELETE","DERIVED","DESC","DESCRIBE","DISTINCT","DO","DROP","EACH","ELEMENT","ELSE","END","EVERY","EXCEPT","EXCLUDE","EXECUTE","EXISTS","EXPLAIN","FALSE","FETCH","FIRST","FLATTEN","FOR","FORCE","FROM","FUNCTION","GRANT","GROUP","GSI","HAVING","IF","IGNORE","ILIKE","IN","INCLUDE","INCREMENT","INDEX","INFER","INLINE","INNER","INSERT","INTERSECT","INTO","IS","JOIN","KEY","KEYS","KEYSPACE","KNOWN","LAST","LEFT","LET","LETTING","LIKE","LIMIT","LSM","MAP","MAPPING","MATCHED","MATERIALIZED","MERGE","MISSING","NAMESPACE","NEST","NOT","NULL","NUMBER","OBJECT","OFFSET","ON","OPTION","OR","ORDER","OUTER","OVER","PARSE","PARTITION","PASSWORD","PATH","POOL","PREPARE","PRIMARY","PRIVATE","PRIVILEGE","PROCEDURE","PUBLIC","RAW","REALM","REDUCE","RENAME","RETURN","RETURNING","REVOKE","RIGHT","ROLE","ROLLBACK","SATISFIES","SCHEMA","SELECT","SELF","SEMI","SET","SHOW","SOME","START","STATISTICS","STRING","SYSTEM","THEN","TO","TRANSACTION","TRIGGER","TRUE","TRUNCATE","UNDER","UNION","UNIQUE","UNKNOWN","UNNEST","UNSET","UPDATE","UPSERT","USE","USER","USING","VALIDATE","VALUE","VALUED","VALUES","VIA","VIEW","WHEN","WHERE","WHILE","WITH","WITHIN","WORK","XOR"],c=["DELETE FROM","EXCEPT ALL","EXCEPT","EXPLAIN DELETE FROM","EXPLAIN UPDATE","EXPLAIN UPSERT","FROM","GROUP BY","HAVING","INFER","INSERT INTO","LET","LIMIT","MERGE","NEST","ORDER BY","PREPARE","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UNNEST","UPDATE","UPSERT","USE KEYS","VALUES","WHERE"],f=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],l=["AND","OR","XOR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:['""',"''","``"],openParens:["(","[","{"],closeParens:[")","]","}"],namedPlaceholderTypes:["$"],lineCommentTypes:["#","--"],operators:["==","!="]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},"4fdb":function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E.isEnd=E.isWindow=E.isBy=E.isSet=E.isLimit=E.isBetween=E.isAnd=void 0;var T=n(t("6b51"));function n(e){return e&&e.__esModule?e:{default:e}}var R=function(e,E){return function(t){return(null===t||void 0===t?void 0:t.type)===e&&E.test(null===t||void 0===t?void 0:t.value)}},r=R(T["default"].RESERVED_NEWLINE,/^AND$/i);E.isAnd=r;var N=R(T["default"].RESERVED,/^BETWEEN$/i);E.isBetween=N;var o=R(T["default"].RESERVED_TOP_LEVEL,/^LIMIT$/i);E.isLimit=o;var I=R(T["default"].RESERVED_TOP_LEVEL,/^[S\u017F]ET$/i);E.isSet=I;var O=R(T["default"].RESERVED,/^BY$/i);E.isBy=O;var A=R(T["default"].RESERVED_TOP_LEVEL,/^WINDOW$/i);E.isWindow=A;var S=R(T["default"].CLOSE_PAREN,/^END$/i);E.isEnd=S},"6b51":function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var T={WORD:"word",STRING:"string",RESERVED:"reserved",RESERVED_TOP_LEVEL:"reserved-top-level",RESERVED_TOP_LEVEL_NO_INDENT:"reserved-top-level-no-indent",RESERVED_NEWLINE:"reserved-newline",OPERATOR:"operator",OPEN_PAREN:"open-paren",CLOSE_PAREN:"close-paren",LINE_COMMENT:"line-comment",BLOCK_COMMENT:"block-comment",NUMBER:"number",PLACEHOLDER:"placeholder"};E["default"]=T,e.exports=E.default},"78f6":function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["ACCESSIBLE","ADD","ALL","ALTER","ANALYZE","AND","AS","ASC","ASENSITIVE","BEFORE","BETWEEN","BIGINT","BINARY","BLOB","BOTH","BY","CALL","CASCADE","CASE","CHANGE","CHAR","CHARACTER","CHECK","COLLATE","COLUMN","CONDITION","CONSTRAINT","CONTINUE","CONVERT","CREATE","CROSS","CUBE","CUME_DIST","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","DATABASE","DATABASES","DAY_HOUR","DAY_MICROSECOND","DAY_MINUTE","DAY_SECOND","DEC","DECIMAL","DECLARE","DEFAULT","DELAYED","DELETE","DENSE_RANK","DESC","DESCRIBE","DETERMINISTIC","DISTINCT","DISTINCTROW","DIV","DOUBLE","DROP","DUAL","EACH","ELSE","ELSEIF","EMPTY","ENCLOSED","ESCAPED","EXCEPT","EXISTS","EXIT","EXPLAIN","FALSE","FETCH","FIRST_VALUE","FLOAT","FLOAT4","FLOAT8","FOR","FORCE","FOREIGN","FROM","FULLTEXT","FUNCTION","GENERATED","GET","GRANT","GROUP","GROUPING","GROUPS","HAVING","HIGH_PRIORITY","HOUR_MICROSECOND","HOUR_MINUTE","HOUR_SECOND","IF","IGNORE","IN","INDEX","INFILE","INNER","INOUT","INSENSITIVE","INSERT","INT","INT1","INT2","INT3","INT4","INT8","INTEGER","INTERVAL","INTO","IO_AFTER_GTIDS","IO_BEFORE_GTIDS","IS","ITERATE","JOIN","JSON_TABLE","KEY","KEYS","KILL","LAG","LAST_VALUE","LATERAL","LEAD","LEADING","LEAVE","LEFT","LIKE","LIMIT","LINEAR","LINES","LOAD","LOCALTIME","LOCALTIMESTAMP","LOCK","LONG","LONGBLOB","LONGTEXT","LOOP","LOW_PRIORITY","MASTER_BIND","MASTER_SSL_VERIFY_SERVER_CERT","MATCH","MAXVALUE","MEDIUMBLOB","MEDIUMINT","MEDIUMTEXT","MIDDLEINT","MINUTE_MICROSECOND","MINUTE_SECOND","MOD","MODIFIES","NATURAL","NOT","NO_WRITE_TO_BINLOG","NTH_VALUE","NTILE","NULL","NUMERIC","OF","ON","OPTIMIZE","OPTIMIZER_COSTS","OPTION","OPTIONALLY","OR","ORDER","OUT","OUTER","OUTFILE","OVER","PARTITION","PERCENT_RANK","PRECISION","PRIMARY","PROCEDURE","PURGE","RANGE","RANK","READ","READS","READ_WRITE","REAL","RECURSIVE","REFERENCES","REGEXP","RELEASE","RENAME","REPEAT","REPLACE","REQUIRE","RESIGNAL","RESTRICT","RETURN","REVOKE","RIGHT","RLIKE","ROW","ROWS","ROW_NUMBER","SCHEMA","SCHEMAS","SECOND_MICROSECOND","SELECT","SENSITIVE","SEPARATOR","SET","SHOW","SIGNAL","SMALLINT","SPATIAL","SPECIFIC","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQL_BIG_RESULT","SQL_CALC_FOUND_ROWS","SQL_SMALL_RESULT","SSL","STARTING","STORED","STRAIGHT_JOIN","SYSTEM","TABLE","TERMINATED","THEN","TINYBLOB","TINYINT","TINYTEXT","TO","TRAILING","TRIGGER","TRUE","UNDO","UNION","UNIQUE","UNLOCK","UNSIGNED","UPDATE","USAGE","USE","USING","UTC_DATE","UTC_TIME","UTC_TIMESTAMP","VALUES","VARBINARY","VARCHAR","VARCHARACTER","VARYING","VIRTUAL","WHEN","WHERE","WHILE","WINDOW","WITH","WRITE","XOR","YEAR_MONTH","ZEROFILL"],c=["ADD","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET","UPDATE","VALUES","WHERE"],f=["INTERSECT","INTERSECT ALL","UNION","UNION ALL"],l=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","CROSS JOIN","NATURAL JOIN","STRAIGHT_JOIN","NATURAL LEFT JOIN","NATURAL LEFT OUTER JOIN","NATURAL RIGHT JOIN","NATURAL RIGHT OUTER JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:["``","''",'""'],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[],lineCommentTypes:["--","#"],specialWordChars:["@"],operators:[":=","<<",">>","!=","<>","<=>","&&","||","->","->>"]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},"8f69":function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["ADD","EXTERNAL","PROCEDURE","ALL","FETCH","PUBLIC","ALTER","FILE","RAISERROR","AND","FILLFACTOR","READ","ANY","FOR","READTEXT","AS","FOREIGN","RECONFIGURE","ASC","FREETEXT","REFERENCES","AUTHORIZATION","FREETEXTTABLE","REPLICATION","BACKUP","FROM","RESTORE","BEGIN","FULL","RESTRICT","BETWEEN","FUNCTION","RETURN","BREAK","GOTO","REVERT","BROWSE","GRANT","REVOKE","BULK","GROUP","RIGHT","BY","HAVING","ROLLBACK","CASCADE","HOLDLOCK","ROWCOUNT","CASE","IDENTITY","ROWGUIDCOL","CHECK","IDENTITY_INSERT","RULE","CHECKPOINT","IDENTITYCOL","SAVE","CLOSE","IF","SCHEMA","CLUSTERED","IN","SECURITYAUDIT","COALESCE","INDEX","SELECT","COLLATE","INNER","SEMANTICKEYPHRASETABLE","COLUMN","INSERT","SEMANTICSIMILARITYDETAILSTABLE","COMMIT","INTERSECT","SEMANTICSIMILARITYTABLE","COMPUTE","INTO","SESSION_USER","CONSTRAINT","IS","SET","CONTAINS","JOIN","SETUSER","CONTAINSTABLE","KEY","SHUTDOWN","CONTINUE","KILL","SOME","CONVERT","LEFT","STATISTICS","CREATE","LIKE","SYSTEM_USER","CROSS","LINENO","TABLE","CURRENT","LOAD","TABLESAMPLE","CURRENT_DATE","MERGE","TEXTSIZE","CURRENT_TIME","NATIONAL","THEN","CURRENT_TIMESTAMP","NOCHECK","TO","CURRENT_USER","NONCLUSTERED","TOP","CURSOR","NOT","TRAN","DATABASE","NULL","TRANSACTION","DBCC","NULLIF","TRIGGER","DEALLOCATE","OF","TRUNCATE","DECLARE","OFF","TRY_CONVERT","DEFAULT","OFFSETS","TSEQUAL","DELETE","ON","UNION","DENY","OPEN","UNIQUE","DESC","OPENDATASOURCE","UNPIVOT","DISK","OPENQUERY","UPDATE","DISTINCT","OPENROWSET","UPDATETEXT","DISTRIBUTED","OPENXML","USE","DOUBLE","OPTION","USER","DROP","OR","VALUES","DUMP","ORDER","VARYING","ELSE","OUTER","VIEW","END","OVER","WAITFOR","ERRLVL","PERCENT","WHEN","ESCAPE","PIVOT","WHERE","EXCEPT","PLAN","WHILE","EXEC","PRECISION","WITH","EXECUTE","PRIMARY","WITHIN GROUP","EXISTS","PRINT","WRITETEXT","EXIT","PROC"],c=["ADD","ALTER COLUMN","ALTER TABLE","CASE","DELETE FROM","END","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],f=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],l=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:['""',"N''","''","[]"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:[],namedPlaceholderTypes:["@"],lineCommentTypes:["--"],specialWordChars:["#","@"],operators:[">=","<=","<>","!=","!<","!>","+=","-=","*=","/=","%=","|=","&=","^=","::"]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},9493:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["ABS","ACTIVATE","ALIAS","ALL","ALLOCATE","ALLOW","ALTER","ANY","ARE","ARRAY","AS","ASC","ASENSITIVE","ASSOCIATE","ASUTIME","ASYMMETRIC","AT","ATOMIC","ATTRIBUTES","AUDIT","AUTHORIZATION","AUX","AUXILIARY","AVG","BEFORE","BEGIN","BETWEEN","BIGINT","BINARY","BLOB","BOOLEAN","BOTH","BUFFERPOOL","BY","CACHE","CALL","CALLED","CAPTURE","CARDINALITY","CASCADED","CASE","CAST","CCSID","CEIL","CEILING","CHAR","CHARACTER","CHARACTER_LENGTH","CHAR_LENGTH","CHECK","CLOB","CLONE","CLOSE","CLUSTER","COALESCE","COLLATE","COLLECT","COLLECTION","COLLID","COLUMN","COMMENT","COMMIT","CONCAT","CONDITION","CONNECT","CONNECTION","CONSTRAINT","CONTAINS","CONTINUE","CONVERT","CORR","CORRESPONDING","COUNT","COUNT_BIG","COVAR_POP","COVAR_SAMP","CREATE","CROSS","CUBE","CUME_DIST","CURRENT","CURRENT_DATE","CURRENT_DEFAULT_TRANSFORM_GROUP","CURRENT_LC_CTYPE","CURRENT_PATH","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_SERVER","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_TIMEZONE","CURRENT_TRANSFORM_GROUP_FOR_TYPE","CURRENT_USER","CURSOR","CYCLE","DATA","DATABASE","DATAPARTITIONNAME","DATAPARTITIONNUM","DATE","DAY","DAYS","DB2GENERAL","DB2GENRL","DB2SQL","DBINFO","DBPARTITIONNAME","DBPARTITIONNUM","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DEFAULTS","DEFINITION","DELETE","DENSERANK","DENSE_RANK","DEREF","DESCRIBE","DESCRIPTOR","DETERMINISTIC","DIAGNOSTICS","DISABLE","DISALLOW","DISCONNECT","DISTINCT","DO","DOCUMENT","DOUBLE","DROP","DSSIZE","DYNAMIC","EACH","EDITPROC","ELEMENT","ELSE","ELSEIF","ENABLE","ENCODING","ENCRYPTION","END","END-EXEC","ENDING","ERASE","ESCAPE","EVERY","EXCEPTION","EXCLUDING","EXCLUSIVE","EXEC","EXECUTE","EXISTS","EXIT","EXP","EXPLAIN","EXTENDED","EXTERNAL","EXTRACT","FALSE","FENCED","FETCH","FIELDPROC","FILE","FILTER","FINAL","FIRST","FLOAT","FLOOR","FOR","FOREIGN","FREE","FULL","FUNCTION","FUSION","GENERAL","GENERATED","GET","GLOBAL","GOTO","GRANT","GRAPHIC","GROUP","GROUPING","HANDLER","HASH","HASHED_VALUE","HINT","HOLD","HOUR","HOURS","IDENTITY","IF","IMMEDIATE","IN","INCLUDING","INCLUSIVE","INCREMENT","INDEX","INDICATOR","INDICATORS","INF","INFINITY","INHERIT","INNER","INOUT","INSENSITIVE","INSERT","INT","INTEGER","INTEGRITY","INTERSECTION","INTERVAL","INTO","IS","ISOBID","ISOLATION","ITERATE","JAR","JAVA","KEEP","KEY","LABEL","LANGUAGE","LARGE","LATERAL","LC_CTYPE","LEADING","LEAVE","LEFT","LIKE","LINKTYPE","LN","LOCAL","LOCALDATE","LOCALE","LOCALTIME","LOCALTIMESTAMP","LOCATOR","LOCATORS","LOCK","LOCKMAX","LOCKSIZE","LONG","LOOP","LOWER","MAINTAINED","MATCH","MATERIALIZED","MAX","MAXVALUE","MEMBER","MERGE","METHOD","MICROSECOND","MICROSECONDS","MIN","MINUTE","MINUTES","MINVALUE","MOD","MODE","MODIFIES","MODULE","MONTH","MONTHS","MULTISET","NAN","NATIONAL","NATURAL","NCHAR","NCLOB","NEW","NEW_TABLE","NEXTVAL","NO","NOCACHE","NOCYCLE","NODENAME","NODENUMBER","NOMAXVALUE","NOMINVALUE","NONE","NOORDER","NORMALIZE","NORMALIZED","NOT","NULL","NULLIF","NULLS","NUMERIC","NUMPARTS","OBID","OCTET_LENGTH","OF","OFFSET","OLD","OLD_TABLE","ON","ONLY","OPEN","OPTIMIZATION","OPTIMIZE","OPTION","ORDER","OUT","OUTER","OVER","OVERLAPS","OVERLAY","OVERRIDING","PACKAGE","PADDED","PAGESIZE","PARAMETER","PART","PARTITION","PARTITIONED","PARTITIONING","PARTITIONS","PASSWORD","PATH","PERCENTILE_CONT","PERCENTILE_DISC","PERCENT_RANK","PIECESIZE","PLAN","POSITION","POWER","PRECISION","PREPARE","PREVVAL","PRIMARY","PRIQTY","PRIVILEGES","PROCEDURE","PROGRAM","PSID","PUBLIC","QUERY","QUERYNO","RANGE","RANK","READ","READS","REAL","RECOVERY","RECURSIVE","REF","REFERENCES","REFERENCING","REFRESH","REGR_AVGX","REGR_AVGY","REGR_COUNT","REGR_INTERCEPT","REGR_R2","REGR_SLOPE","REGR_SXX","REGR_SXY","REGR_SYY","RELEASE","RENAME","REPEAT","RESET","RESIGNAL","RESTART","RESTRICT","RESULT","RESULT_SET_LOCATOR","RETURN","RETURNS","REVOKE","RIGHT","ROLE","ROLLBACK","ROLLUP","ROUND_CEILING","ROUND_DOWN","ROUND_FLOOR","ROUND_HALF_DOWN","ROUND_HALF_EVEN","ROUND_HALF_UP","ROUND_UP","ROUTINE","ROW","ROWNUMBER","ROWS","ROWSET","ROW_NUMBER","RRN","RUN","SAVEPOINT","SCHEMA","SCOPE","SCRATCHPAD","SCROLL","SEARCH","SECOND","SECONDS","SECQTY","SECURITY","SENSITIVE","SEQUENCE","SESSION","SESSION_USER","SIGNAL","SIMILAR","SIMPLE","SMALLINT","SNAN","SOME","SOURCE","SPECIFIC","SPECIFICTYPE","SQL","SQLEXCEPTION","SQLID","SQLSTATE","SQLWARNING","SQRT","STACKED","STANDARD","START","STARTING","STATEMENT","STATIC","STATMENT","STAY","STDDEV_POP","STDDEV_SAMP","STOGROUP","STORES","STYLE","SUBMULTISET","SUBSTRING","SUM","SUMMARY","SYMMETRIC","SYNONYM","SYSFUN","SYSIBM","SYSPROC","SYSTEM","SYSTEM_USER","TABLE","TABLESAMPLE","TABLESPACE","THEN","TIME","TIMESTAMP","TIMEZONE_HOUR","TIMEZONE_MINUTE","TO","TRAILING","TRANSACTION","TRANSLATE","TRANSLATION","TREAT","TRIGGER","TRIM","TRUE","TRUNCATE","TYPE","UESCAPE","UNDO","UNIQUE","UNKNOWN","UNNEST","UNTIL","UPPER","USAGE","USER","USING","VALIDPROC","VALUE","VARCHAR","VARIABLE","VARIANT","VARYING","VAR_POP","VAR_SAMP","VCAT","VERSION","VIEW","VOLATILE","VOLUMES","WHEN","WHENEVER","WHILE","WIDTH_BUCKET","WINDOW","WITH","WITHIN","WITHOUT","WLM","WRITE","XMLELEMENT","XMLEXISTS","XMLNAMESPACES","YEAR","YEARS"],c=["ADD","AFTER","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FETCH FIRST","FROM","GROUP BY","GO","HAVING","INSERT INTO","INTERSECT","LIMIT","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],f=["INTERSECT","INTERSECT ALL","MINUS","UNION","UNION ALL"],l=["AND","OR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:['""',"''","``","[]"],openParens:["("],closeParens:[")"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[":"],lineCommentTypes:["--"],specialWordChars:["#","@"],operators:["**","!=","!>","!>","||"]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},a48e:function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var T=I(t("6b51")),n=I(t("15e2")),R=I(t("d09a")),r=I(t("1d3a")),N=t("cd49"),o=t("4fdb");function I(e){return e&&e.__esModule?e:{default:e}}function O(e,E,t){return E in e?Object.defineProperty(e,E,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[E]=t,e}function A(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function S(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function L(e,E,t){return E&&S(e.prototype,E),t&&S(e,t),e}var u=function(){function e(E){A(this,e),this.cfg=E,this.indentation=new n["default"](this.cfg.indent),this.inlineBlock=new R["default"],this.params=new r["default"](this.cfg.params),this.previousReservedToken={},this.tokens=[],this.index=0}return L(e,[{key:"tokenizer",value:function(){throw new Error("tokenizer() not implemented by subclass")}},{key:"tokenOverride",value:function(e){return e}},{key:"format",value:function(e){this.tokens=this.tokenizer().tokenize(e);var E=this.getFormattedQueryFromTokens();return E.trim()}},{key:"getFormattedQueryFromTokens",value:function(){var e=this,E="";return this.tokens.forEach((function(t,n){e.index=n,t=e.tokenOverride(t),t.type===T["default"].LINE_COMMENT?E=e.formatLineComment(t,E):t.type===T["default"].BLOCK_COMMENT?E=e.formatBlockComment(t,E):t.type===T["default"].RESERVED_TOP_LEVEL?(E=e.formatTopLevelReservedWord(t,E),e.previousReservedToken=t):t.type===T["default"].RESERVED_TOP_LEVEL_NO_INDENT?(E=e.formatTopLevelReservedWordNoIndent(t,E),e.previousReservedToken=t):t.type===T["default"].RESERVED_NEWLINE?(E=e.formatNewlineReservedWord(t,E),e.previousReservedToken=t):t.type===T["default"].RESERVED?(E=e.formatWithSpaces(t,E),e.previousReservedToken=t):E=t.type===T["default"].OPEN_PAREN?e.formatOpeningParentheses(t,E):t.type===T["default"].CLOSE_PAREN?e.formatClosingParentheses(t,E):t.type===T["default"].PLACEHOLDER?e.formatPlaceholder(t,E):","===t.value?e.formatComma(t,E):":"===t.value?e.formatWithSpaceAfter(t,E):"."===t.value?e.formatWithoutSpaces(t,E):";"===t.value?e.formatQuerySeparator(t,E):e.formatWithSpaces(t,E)})),E}},{key:"formatLineComment",value:function(e,E){return this.addNewline(E+this.show(e))}},{key:"formatBlockComment",value:function(e,E){return this.addNewline(this.addNewline(E)+this.indentComment(e.value))}},{key:"indentComment",value:function(e){return e.replace(/\n[\t ]*/g,"\n"+this.indentation.getIndent()+" ")}},{key:"formatTopLevelReservedWordNoIndent",value:function(e,E){return this.indentation.decreaseTopLevel(),E=this.addNewline(E)+this.equalizeWhitespace(this.show(e)),this.addNewline(E)}},{key:"formatTopLevelReservedWord",value:function(e,E){return this.indentation.decreaseTopLevel(),E=this.addNewline(E),this.indentation.increaseTopLevel(),E+=this.equalizeWhitespace(this.show(e)),this.addNewline(E)}},{key:"formatNewlineReservedWord",value:function(e,E){return(0,o.isAnd)(e)&&(0,o.isBetween)(this.tokenLookBehind(2))?this.formatWithSpaces(e,E):this.addNewline(E)+this.equalizeWhitespace(this.show(e))+" "}},{key:"equalizeWhitespace",value:function(e){return e.replace(/[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+/g," ")}},{key:"formatOpeningParentheses",value:function(e,E){var t,n,R=(t={},O(t,T["default"].OPEN_PAREN,!0),O(t,T["default"].LINE_COMMENT,!0),O(t,T["default"].OPERATOR,!0),t);return 0!==e.whitespaceBefore.length||R[null===(n=this.tokenLookBehind())||void 0===n?void 0:n.type]||(E=(0,N.trimSpacesEnd)(E)),E+=this.show(e),this.inlineBlock.beginIfPossible(this.tokens,this.index),this.inlineBlock.isActive()||(this.indentation.increaseBlockLevel(),E=this.addNewline(E)),E}},{key:"formatClosingParentheses",value:function(e,E){return this.inlineBlock.isActive()?(this.inlineBlock.end(),this.formatWithSpaceAfter(e,E)):(this.indentation.decreaseBlockLevel(),this.formatWithSpaces(e,this.addNewline(E)))}},{key:"formatPlaceholder",value:function(e,E){return E+this.params.get(e)+" "}},{key:"formatComma",value:function(e,E){return E=(0,N.trimSpacesEnd)(E)+this.show(e)+" ",this.inlineBlock.isActive()||(0,o.isLimit)(this.previousReservedToken)?E:this.addNewline(E)}},{key:"formatWithSpaceAfter",value:function(e,E){return(0,N.trimSpacesEnd)(E)+this.show(e)+" "}},{key:"formatWithoutSpaces",value:function(e,E){return(0,N.trimSpacesEnd)(E)+this.show(e)}},{key:"formatWithSpaces",value:function(e,E){return E+this.show(e)+" "}},{key:"formatQuerySeparator",value:function(e,E){return this.indentation.resetIndentation(),(0,N.trimSpacesEnd)(E)+this.show(e)+"\n".repeat(this.cfg.linesBetweenQueries||1)}},{key:"show",value:function(e){var E=e.type,t=e.value;return!this.cfg.uppercase||E!==T["default"].RESERVED&&E!==T["default"].RESERVED_TOP_LEVEL&&E!==T["default"].RESERVED_TOP_LEVEL_NO_INDENT&&E!==T["default"].RESERVED_NEWLINE&&E!==T["default"].OPEN_PAREN&&E!==T["default"].CLOSE_PAREN?t:t.toUpperCase()}},{key:"addNewline",value:function(e){return e=(0,N.trimSpacesEnd)(e),e.endsWith("\n")||(e+="\n"),e+this.indentation.getIndent()}},{key:"tokenLookBehind",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.tokens[this.index-e]}},{key:"tokenLookAhead",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return this.tokens[this.index+e]}}]),e}();E["default"]=u,e.exports=E.default},b539:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["ABORT","ABSOLUTE","ACCESS","ACTION","ADD","ADMIN","AFTER","AGGREGATE","ALL","ALSO","ALTER","ALWAYS","ANALYSE","ANALYZE","AND","ANY","ARRAY","AS","ASC","ASSERTION","ASSIGNMENT","ASYMMETRIC","AT","ATTACH","ATTRIBUTE","AUTHORIZATION","BACKWARD","BEFORE","BEGIN","BETWEEN","BIGINT","BINARY","BIT","BOOLEAN","BOTH","BY","CACHE","CALL","CALLED","CASCADE","CASCADED","CASE","CAST","CATALOG","CHAIN","CHAR","CHARACTER","CHARACTERISTICS","CHECK","CHECKPOINT","CLASS","CLOSE","CLUSTER","COALESCE","COLLATE","COLLATION","COLUMN","COLUMNS","COMMENT","COMMENTS","COMMIT","COMMITTED","CONCURRENTLY","CONFIGURATION","CONFLICT","CONNECTION","CONSTRAINT","CONSTRAINTS","CONTENT","CONTINUE","CONVERSION","COPY","COST","CREATE","CROSS","CSV","CUBE","CURRENT","CURRENT_CATALOG","CURRENT_DATE","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","CYCLE","DATA","DATABASE","DAY","DEALLOCATE","DEC","DECIMAL","DECLARE","DEFAULT","DEFAULTS","DEFERRABLE","DEFERRED","DEFINER","DELETE","DELIMITER","DELIMITERS","DEPENDS","DESC","DETACH","DICTIONARY","DISABLE","DISCARD","DISTINCT","DO","DOCUMENT","DOMAIN","DOUBLE","DROP","EACH","ELSE","ENABLE","ENCODING","ENCRYPTED","END","ENUM","ESCAPE","EVENT","EXCEPT","EXCLUDE","EXCLUDING","EXCLUSIVE","EXECUTE","EXISTS","EXPLAIN","EXPRESSION","EXTENSION","EXTERNAL","EXTRACT","FALSE","FAMILY","FETCH","FILTER","FIRST","FLOAT","FOLLOWING","FOR","FORCE","FOREIGN","FORWARD","FREEZE","FROM","FULL","FUNCTION","FUNCTIONS","GENERATED","GLOBAL","GRANT","GRANTED","GREATEST","GROUP","GROUPING","GROUPS","HANDLER","HAVING","HEADER","HOLD","HOUR","IDENTITY","IF","ILIKE","IMMEDIATE","IMMUTABLE","IMPLICIT","IMPORT","IN","INCLUDE","INCLUDING","INCREMENT","INDEX","INDEXES","INHERIT","INHERITS","INITIALLY","INLINE","INNER","INOUT","INPUT","INSENSITIVE","INSERT","INSTEAD","INT","INTEGER","INTERSECT","INTERVAL","INTO","INVOKER","IS","ISNULL","ISOLATION","JOIN","KEY","LABEL","LANGUAGE","LARGE","LAST","LATERAL","LEADING","LEAKPROOF","LEAST","LEFT","LEVEL","LIKE","LIMIT","LISTEN","LOAD","LOCAL","LOCALTIME","LOCALTIMESTAMP","LOCATION","LOCK","LOCKED","LOGGED","MAPPING","MATCH","MATERIALIZED","MAXVALUE","METHOD","MINUTE","MINVALUE","MODE","MONTH","MOVE","NAME","NAMES","NATIONAL","NATURAL","NCHAR","NEW","NEXT","NFC","NFD","NFKC","NFKD","NO","NONE","NORMALIZE","NORMALIZED","NOT","NOTHING","NOTIFY","NOTNULL","NOWAIT","NULL","NULLIF","NULLS","NUMERIC","OBJECT","OF","OFF","OFFSET","OIDS","OLD","ON","ONLY","OPERATOR","OPTION","OPTIONS","OR","ORDER","ORDINALITY","OTHERS","OUT","OUTER","OVER","OVERLAPS","OVERLAY","OVERRIDING","OWNED","OWNER","PARALLEL","PARSER","PARTIAL","PARTITION","PASSING","PASSWORD","PLACING","PLANS","POLICY","POSITION","PRECEDING","PRECISION","PREPARE","PREPARED","PRESERVE","PRIMARY","PRIOR","PRIVILEGES","PROCEDURAL","PROCEDURE","PROCEDURES","PROGRAM","PUBLICATION","QUOTE","RANGE","READ","REAL","REASSIGN","RECHECK","RECURSIVE","REF","REFERENCES","REFERENCING","REFRESH","REINDEX","RELATIVE","RELEASE","RENAME","REPEATABLE","REPLACE","REPLICA","RESET","RESTART","RESTRICT","RETURNING","RETURNS","REVOKE","RIGHT","ROLE","ROLLBACK","ROLLUP","ROUTINE","ROUTINES","ROW","ROWS","RULE","SAVEPOINT","SCHEMA","SCHEMAS","SCROLL","SEARCH","SECOND","SECURITY","SELECT","SEQUENCE","SEQUENCES","SERIALIZABLE","SERVER","SESSION","SESSION_USER","SET","SETOF","SETS","SHARE","SHOW","SIMILAR","SIMPLE","SKIP","SMALLINT","SNAPSHOT","SOME","SQL","STABLE","STANDALONE","START","STATEMENT","STATISTICS","STDIN","STDOUT","STORAGE","STORED","STRICT","STRIP","SUBSCRIPTION","SUBSTRING","SUPPORT","SYMMETRIC","SYSID","SYSTEM","TABLE","TABLES","TABLESAMPLE","TABLESPACE","TEMP","TEMPLATE","TEMPORARY","TEXT","THEN","TIES","TIME","TIMESTAMP","TO","TRAILING","TRANSACTION","TRANSFORM","TREAT","TRIGGER","TRIM","TRUE","TRUNCATE","TRUSTED","TYPE","TYPES","UESCAPE","UNBOUNDED","UNCOMMITTED","UNENCRYPTED","UNION","UNIQUE","UNKNOWN","UNLISTEN","UNLOGGED","UNTIL","UPDATE","USER","USING","VACUUM","VALID","VALIDATE","VALIDATOR","VALUE","VALUES","VARCHAR","VARIADIC","VARYING","VERBOSE","VERSION","VIEW","VIEWS","VOLATILE","WHEN","WHERE","WHITESPACE","WINDOW","WITH","WITHIN","WITHOUT","WORK","WRAPPER","WRITE","XML","XMLATTRIBUTES","XMLCONCAT","XMLELEMENT","XMLEXISTS","XMLFOREST","XMLNAMESPACES","XMLPARSE","XMLPI","XMLROOT","XMLSERIALIZE","XMLTABLE","YEAR","YES","ZONE"],c=["ADD","AFTER","ALTER COLUMN","ALTER TABLE","CASE","DELETE FROM","END","EXCEPT","FETCH FIRST","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UPDATE","VALUES","WHERE"],f=["INTERSECT","INTERSECT ALL","UNION","UNION ALL"],l=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:['""',"''","U&''",'U&""',"$$"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["$"],namedPlaceholderTypes:[":"],lineCommentTypes:["--"],operators:["!=","<<",">>","||/","|/","::","->>","->","~~*","~~","!~~*","!~~","~*","!~*","!~","!!"]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},b979:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["AES128","AES256","ALLOWOVERWRITE","ANALYSE","ARRAY","AS","ASC","AUTHORIZATION","BACKUP","BINARY","BLANKSASNULL","BOTH","BYTEDICT","BZIP2","CAST","CHECK","COLLATE","COLUMN","CONSTRAINT","CREATE","CREDENTIALS","CURRENT_DATE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURRENT_USER_ID","DEFAULT","DEFERRABLE","DEFLATE","DEFRAG","DELTA","DELTA32K","DESC","DISABLE","DISTINCT","DO","ELSE","EMPTYASNULL","ENABLE","ENCODE","ENCRYPT","ENCRYPTION","END","EXPLICIT","FALSE","FOR","FOREIGN","FREEZE","FULL","GLOBALDICT256","GLOBALDICT64K","GRANT","GZIP","IDENTITY","IGNORE","ILIKE","INITIALLY","INTO","LEADING","LOCALTIME","LOCALTIMESTAMP","LUN","LUNS","LZO","LZOP","MINUS","MOSTLY13","MOSTLY32","MOSTLY8","NATURAL","NEW","NULLS","OFF","OFFLINE","OFFSET","OLD","ON","ONLY","OPEN","ORDER","OVERLAPS","PARALLEL","PARTITION","PERCENT","PERMISSIONS","PLACING","PRIMARY","RAW","READRATIO","RECOVER","REFERENCES","REJECTLOG","RESORT","RESTORE","SESSION_USER","SIMILAR","SYSDATE","SYSTEM","TABLE","TAG","TDES","TEXT255","TEXT32K","THEN","TIMESTAMP","TO","TOP","TRAILING","TRUE","TRUNCATECOLUMNS","UNIQUE","USER","USING","VERBOSE","WALLET","WHEN","WITH","WITHOUT","PREDICATE","COLUMNS","COMPROWS","COMPRESSION","COPY","FORMAT","DELIMITER","FIXEDWIDTH","AVRO","JSON","ENCRYPTED","BZIP2","GZIP","LZOP","PARQUET","ORC","ACCEPTANYDATE","ACCEPTINVCHARS","BLANKSASNULL","DATEFORMAT","EMPTYASNULL","ENCODING","ESCAPE","EXPLICIT_IDS","FILLRECORD","IGNOREBLANKLINES","IGNOREHEADER","NULL AS","REMOVEQUOTES","ROUNDEC","TIMEFORMAT","TRIMBLANKS","TRUNCATECOLUMNS","COMPROWS","COMPUPDATE","MAXERROR","NOLOAD","STATUPDATE","MANIFEST","REGION","IAM_ROLE","MASTER_SYMMETRIC_KEY","SSH","ACCEPTANYDATE","ACCEPTINVCHARS","ACCESS_KEY_ID","SECRET_ACCESS_KEY","AVRO","BLANKSASNULL","BZIP2","COMPROWS","COMPUPDATE","CREDENTIALS","DATEFORMAT","DELIMITER","EMPTYASNULL","ENCODING","ENCRYPTED","ESCAPE","EXPLICIT_IDS","FILLRECORD","FIXEDWIDTH","FORMAT","IAM_ROLE","GZIP","IGNOREBLANKLINES","IGNOREHEADER","JSON","LZOP","MANIFEST","MASTER_SYMMETRIC_KEY","MAXERROR","NOLOAD","NULL AS","READRATIO","REGION","REMOVEQUOTES","ROUNDEC","SSH","STATUPDATE","TIMEFORMAT","SESSION_TOKEN","TRIMBLANKS","TRUNCATECOLUMNS","EXTERNAL","DATA CATALOG","HIVE METASTORE","CATALOG_ROLE","VACUUM","COPY","UNLOAD","EVEN","ALL"],c=["ADD","AFTER","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","INTERSECT","TOP","LIMIT","MODIFY","ORDER BY","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","UNION ALL","UNION","UPDATE","VALUES","WHERE","VACUUM","COPY","UNLOAD","ANALYZE","ANALYSE","DISTKEY","SORTKEY","COMPOUND","INTERLEAVED","FORMAT","DELIMITER","FIXEDWIDTH","AVRO","JSON","ENCRYPTED","BZIP2","GZIP","LZOP","PARQUET","ORC","ACCEPTANYDATE","ACCEPTINVCHARS","BLANKSASNULL","DATEFORMAT","EMPTYASNULL","ENCODING","ESCAPE","EXPLICIT_IDS","FILLRECORD","IGNOREBLANKLINES","IGNOREHEADER","NULL AS","REMOVEQUOTES","ROUNDEC","TIMEFORMAT","TRIMBLANKS","TRUNCATECOLUMNS","COMPROWS","COMPUPDATE","MAXERROR","NOLOAD","STATUPDATE","MANIFEST","REGION","IAM_ROLE","MASTER_SYMMETRIC_KEY","SSH","ACCEPTANYDATE","ACCEPTINVCHARS","ACCESS_KEY_ID","SECRET_ACCESS_KEY","AVRO","BLANKSASNULL","BZIP2","COMPROWS","COMPUPDATE","CREDENTIALS","DATEFORMAT","DELIMITER","EMPTYASNULL","ENCODING","ENCRYPTED","ESCAPE","EXPLICIT_IDS","FILLRECORD","FIXEDWIDTH","FORMAT","IAM_ROLE","GZIP","IGNOREBLANKLINES","IGNOREHEADER","JSON","LZOP","MANIFEST","MASTER_SYMMETRIC_KEY","MAXERROR","NOLOAD","NULL AS","READRATIO","REGION","REMOVEQUOTES","ROUNDEC","SSH","STATUPDATE","TIMEFORMAT","SESSION_TOKEN","TRIMBLANKS","TRUNCATECOLUMNS","EXTERNAL","DATA CATALOG","HIVE METASTORE","CATALOG_ROLE"],f=[],l=["AND","ELSE","OR","OUTER APPLY","WHEN","VACUUM","COPY","UNLOAD","ANALYZE","ANALYSE","DISTKEY","SORTKEY","COMPOUND","INTERLEAVED","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:['""',"''","``"],openParens:["("],closeParens:[")"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:["@","#","$"],lineCommentTypes:["--"],operators:["|/","||/","<<",">>","!=","||"]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},bcef:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=r(t("a48e")),R=r(t("c606"));function r(e){return e&&e.__esModule?e:{default:e}}function N(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function o(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function I(e,E,t){return E&&o(e.prototype,E),t&&o(e,t),e}function O(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&A(e,E)}function A(e,E){return A=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},A(e,E)}function S(e){var E=i();return function(){var t,T=C(e);if(E){var n=C(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return L(this,t)}}function L(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?u(e):E}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function C(e){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},C(e)}var a=["ACCESSIBLE","ADD","ALL","ALTER","ANALYZE","AND","AS","ASC","ASENSITIVE","BEFORE","BETWEEN","BIGINT","BINARY","BLOB","BOTH","BY","CALL","CASCADE","CASE","CHANGE","CHAR","CHARACTER","CHECK","COLLATE","COLUMN","CONDITION","CONSTRAINT","CONTINUE","CONVERT","CREATE","CROSS","CURRENT_DATE","CURRENT_ROLE","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","CURSOR","DATABASE","DATABASES","DAY_HOUR","DAY_MICROSECOND","DAY_MINUTE","DAY_SECOND","DEC","DECIMAL","DECLARE","DEFAULT","DELAYED","DELETE","DESC","DESCRIBE","DETERMINISTIC","DISTINCT","DISTINCTROW","DIV","DO_DOMAIN_IDS","DOUBLE","DROP","DUAL","EACH","ELSE","ELSEIF","ENCLOSED","ESCAPED","EXCEPT","EXISTS","EXIT","EXPLAIN","FALSE","FETCH","FLOAT","FLOAT4","FLOAT8","FOR","FORCE","FOREIGN","FROM","FULLTEXT","GENERAL","GRANT","GROUP","HAVING","HIGH_PRIORITY","HOUR_MICROSECOND","HOUR_MINUTE","HOUR_SECOND","IF","IGNORE","IGNORE_DOMAIN_IDS","IGNORE_SERVER_IDS","IN","INDEX","INFILE","INNER","INOUT","INSENSITIVE","INSERT","INT","INT1","INT2","INT3","INT4","INT8","INTEGER","INTERSECT","INTERVAL","INTO","IS","ITERATE","JOIN","KEY","KEYS","KILL","LEADING","LEAVE","LEFT","LIKE","LIMIT","LINEAR","LINES","LOAD","LOCALTIME","LOCALTIMESTAMP","LOCK","LONG","LONGBLOB","LONGTEXT","LOOP","LOW_PRIORITY","MASTER_HEARTBEAT_PERIOD","MASTER_SSL_VERIFY_SERVER_CERT","MATCH","MAXVALUE","MEDIUMBLOB","MEDIUMINT","MEDIUMTEXT","MIDDLEINT","MINUTE_MICROSECOND","MINUTE_SECOND","MOD","MODIFIES","NATURAL","NOT","NO_WRITE_TO_BINLOG","NULL","NUMERIC","ON","OPTIMIZE","OPTION","OPTIONALLY","OR","ORDER","OUT","OUTER","OUTFILE","OVER","PAGE_CHECKSUM","PARSE_VCOL_EXPR","PARTITION","POSITION","PRECISION","PRIMARY","PROCEDURE","PURGE","RANGE","READ","READS","READ_WRITE","REAL","RECURSIVE","REF_SYSTEM_ID","REFERENCES","REGEXP","RELEASE","RENAME","REPEAT","REPLACE","REQUIRE","RESIGNAL","RESTRICT","RETURN","RETURNING","REVOKE","RIGHT","RLIKE","ROWS","SCHEMA","SCHEMAS","SECOND_MICROSECOND","SELECT","SENSITIVE","SEPARATOR","SET","SHOW","SIGNAL","SLOW","SMALLINT","SPATIAL","SPECIFIC","SQL","SQLEXCEPTION","SQLSTATE","SQLWARNING","SQL_BIG_RESULT","SQL_CALC_FOUND_ROWS","SQL_SMALL_RESULT","SSL","STARTING","STATS_AUTO_RECALC","STATS_PERSISTENT","STATS_SAMPLE_PAGES","STRAIGHT_JOIN","TABLE","TERMINATED","THEN","TINYBLOB","TINYINT","TINYTEXT","TO","TRAILING","TRIGGER","TRUE","UNDO","UNION","UNIQUE","UNLOCK","UNSIGNED","UPDATE","USAGE","USE","USING","UTC_DATE","UTC_TIME","UTC_TIMESTAMP","VALUES","VARBINARY","VARCHAR","VARCHARACTER","VARYING","WHEN","WHERE","WHILE","WINDOW","WITH","WRITE","XOR","YEAR_MONTH","ZEROFILL"],c=["ADD","ALTER COLUMN","ALTER TABLE","DELETE FROM","EXCEPT","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","ORDER BY","SELECT","SET","UPDATE","VALUES","WHERE"],f=["INTERSECT","INTERSECT ALL","UNION","UNION ALL"],l=["AND","ELSE","OR","WHEN","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","CROSS JOIN","NATURAL JOIN","STRAIGHT_JOIN","NATURAL LEFT JOIN","NATURAL LEFT OUTER JOIN","NATURAL RIGHT JOIN","NATURAL RIGHT OUTER JOIN"],s=function(e){O(t,e);var E=S(t);function t(){return N(this,t),E.apply(this,arguments)}return I(t,[{key:"tokenizer",value:function(){return new R["default"]({reservedWords:a,reservedTopLevelWords:c,reservedNewlineWords:l,reservedTopLevelWordsNoIndent:f,stringTypes:["``","''",'""'],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:[],lineCommentTypes:["--","#"],specialWordChars:["@"],operators:[":=","<<",">>","!=","<>","<=>","&&","||"]})}}]),t}(n["default"]);E["default"]=s,e.exports=E.default},c606:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=I(t("6b51")),R=o(t("0fc3")),r=t("cd49");function N(){if("function"!==typeof WeakMap)return null;var e=new WeakMap;return N=function(){return e},e}function o(e){if(e&&e.__esModule)return e;if(null===e||"object"!==T(e)&&"function"!==typeof e)return{default:e};var E=N();if(E&&E.has(e))return E.get(e);var t={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var R in e)if(Object.prototype.hasOwnProperty.call(e,R)){var r=n?Object.getOwnPropertyDescriptor(e,R):null;r&&(r.get||r.set)?Object.defineProperty(t,R,r):t[R]=e[R]}return t["default"]=e,E&&E.set(e,t),t}function I(e){return e&&e.__esModule?e:{default:e}}function O(e,E){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(e);E&&(T=T.filter((function(E){return Object.getOwnPropertyDescriptor(e,E).enumerable}))),t.push.apply(t,T)}return t}function A(e){for(var E=1;E<arguments.length;E++){var t=null!=arguments[E]?arguments[E]:{};E%2?O(Object(t),!0).forEach((function(E){S(e,E,t[E])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):O(Object(t)).forEach((function(E){Object.defineProperty(e,E,Object.getOwnPropertyDescriptor(t,E))}))}return e}function S(e,E,t){return E in e?Object.defineProperty(e,E,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[E]=t,e}function L(e){return a(e)||C(e)||i(e)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(e,E){if(e){if("string"===typeof e)return c(e,E);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(e,E):void 0}}function C(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function a(e){if(Array.isArray(e))return c(e)}function c(e,E){(null==E||E>e.length)&&(E=e.length);for(var t=0,T=new Array(E);t<E;t++)T[t]=e[t];return T}function f(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function l(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function s(e,E,t){return E&&l(e.prototype,E),t&&l(e,t),e}var D=function(){function e(E){f(this,e),this.WHITESPACE_REGEX=/^([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)/,this.NUMBER_REGEX=/^((\x2D[\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]*)?[0-9]+(\.[0-9]+)?([Ee]\x2D?[0-9]+(\.[0-9]+)?)?|0x[0-9A-Fa-f]+|0b[01]+)\b/,this.OPERATOR_REGEX=R.createOperatorRegex(["<>","<=",">="].concat(L(E.operators||[]))),this.BLOCK_COMMENT_REGEX=/^(\/\*(?:(?![])[\s\S])*?(?:\*\/|$))/,this.LINE_COMMENT_REGEX=R.createLineCommentRegex(E.lineCommentTypes),this.RESERVED_TOP_LEVEL_REGEX=R.createReservedWordRegex(E.reservedTopLevelWords),this.RESERVED_TOP_LEVEL_NO_INDENT_REGEX=R.createReservedWordRegex(E.reservedTopLevelWordsNoIndent),this.RESERVED_NEWLINE_REGEX=R.createReservedWordRegex(E.reservedNewlineWords),this.RESERVED_PLAIN_REGEX=R.createReservedWordRegex(E.reservedWords),this.WORD_REGEX=R.createWordRegex(E.specialWordChars),this.STRING_REGEX=R.createStringRegex(E.stringTypes),this.OPEN_PAREN_REGEX=R.createParenRegex(E.openParens),this.CLOSE_PAREN_REGEX=R.createParenRegex(E.closeParens),this.INDEXED_PLACEHOLDER_REGEX=R.createPlaceholderRegex(E.indexedPlaceholderTypes,"[0-9]*"),this.IDENT_NAMED_PLACEHOLDER_REGEX=R.createPlaceholderRegex(E.namedPlaceholderTypes,"[a-zA-Z0-9._$]+"),this.STRING_NAMED_PLACEHOLDER_REGEX=R.createPlaceholderRegex(E.namedPlaceholderTypes,R.createStringPattern(E.stringTypes))}return s(e,[{key:"tokenize",value:function(e){var E,t=[];while(e.length){var T=this.getWhitespace(e);e=e.substring(T.length),e.length&&(E=this.getNextToken(e,E),e=e.substring(E.value.length),t.push(A(A({},E),{},{whitespaceBefore:T})))}return t}},{key:"getWhitespace",value:function(e){var E=e.match(this.WHITESPACE_REGEX);return E?E[1]:""}},{key:"getNextToken",value:function(e,E){return this.getCommentToken(e)||this.getStringToken(e)||this.getOpenParenToken(e)||this.getCloseParenToken(e)||this.getPlaceholderToken(e)||this.getNumberToken(e)||this.getReservedWordToken(e,E)||this.getWordToken(e)||this.getOperatorToken(e)}},{key:"getCommentToken",value:function(e){return this.getLineCommentToken(e)||this.getBlockCommentToken(e)}},{key:"getLineCommentToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].LINE_COMMENT,regex:this.LINE_COMMENT_REGEX})}},{key:"getBlockCommentToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].BLOCK_COMMENT,regex:this.BLOCK_COMMENT_REGEX})}},{key:"getStringToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].STRING,regex:this.STRING_REGEX})}},{key:"getOpenParenToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].OPEN_PAREN,regex:this.OPEN_PAREN_REGEX})}},{key:"getCloseParenToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].CLOSE_PAREN,regex:this.CLOSE_PAREN_REGEX})}},{key:"getPlaceholderToken",value:function(e){return this.getIdentNamedPlaceholderToken(e)||this.getStringNamedPlaceholderToken(e)||this.getIndexedPlaceholderToken(e)}},{key:"getIdentNamedPlaceholderToken",value:function(e){return this.getPlaceholderTokenWithKey({input:e,regex:this.IDENT_NAMED_PLACEHOLDER_REGEX,parseKey:function(e){return e.slice(1)}})}},{key:"getStringNamedPlaceholderToken",value:function(e){var E=this;return this.getPlaceholderTokenWithKey({input:e,regex:this.STRING_NAMED_PLACEHOLDER_REGEX,parseKey:function(e){return E.getEscapedPlaceholderKey({key:e.slice(2,-1),quoteChar:e.slice(-1)})}})}},{key:"getIndexedPlaceholderToken",value:function(e){return this.getPlaceholderTokenWithKey({input:e,regex:this.INDEXED_PLACEHOLDER_REGEX,parseKey:function(e){return e.slice(1)}})}},{key:"getPlaceholderTokenWithKey",value:function(e){var E=e.input,t=e.regex,T=e.parseKey,R=this.getTokenOnFirstMatch({input:E,regex:t,type:n["default"].PLACEHOLDER});return R&&(R.key=T(R.value)),R}},{key:"getEscapedPlaceholderKey",value:function(e){var E=e.key,t=e.quoteChar;return E.replace(new RegExp((0,r.escapeRegExp)("\\"+t),"gu"),t)}},{key:"getNumberToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].NUMBER,regex:this.NUMBER_REGEX})}},{key:"getOperatorToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].OPERATOR,regex:this.OPERATOR_REGEX})}},{key:"getReservedWordToken",value:function(e,E){if(!E||!E.value||"."!==E.value)return this.getTopLevelReservedToken(e)||this.getNewlineReservedToken(e)||this.getTopLevelReservedTokenNoIndent(e)||this.getPlainReservedToken(e)}},{key:"getTopLevelReservedToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].RESERVED_TOP_LEVEL,regex:this.RESERVED_TOP_LEVEL_REGEX})}},{key:"getNewlineReservedToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].RESERVED_NEWLINE,regex:this.RESERVED_NEWLINE_REGEX})}},{key:"getTopLevelReservedTokenNoIndent",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].RESERVED_TOP_LEVEL_NO_INDENT,regex:this.RESERVED_TOP_LEVEL_NO_INDENT_REGEX})}},{key:"getPlainReservedToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].RESERVED,regex:this.RESERVED_PLAIN_REGEX})}},{key:"getWordToken",value:function(e){return this.getTokenOnFirstMatch({input:e,type:n["default"].WORD,regex:this.WORD_REGEX})}},{key:"getTokenOnFirstMatch",value:function(e){var E=e.input,t=e.type,T=e.regex,n=E.match(T);return n?{type:t,value:n[1]}:void 0}}]),e}();E["default"]=D,e.exports=E.default},cd49:function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E.sortByLengthDesc=E.escapeRegExp=E.isEmpty=E.last=E.trimSpacesEnd=void 0;var T=function(e){return e.replace(/[\t ]+$/,"")};E.trimSpacesEnd=T;var n=function(e){return e[e.length-1]};E.last=n;var R=function(e){return!Array.isArray(e)||0===e.length};E.isEmpty=R;var r=function(e){return e.replace(/[\$\(-\+\.\?\[-\^\{-\}]/g,"\\$&")};E.escapeRegExp=r;var N=function(e){return e.sort((function(e,E){return E.length-e.length||e.localeCompare(E)}))};E.sortByLengthDesc=N},d09a:function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var T=n(t("6b51"));function n(e){return e&&e.__esModule?e:{default:e}}function R(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function r(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function N(e,E,t){return E&&r(e.prototype,E),t&&r(e,t),e}var o=50,I=function(){function e(){R(this,e),this.level=0}return N(e,[{key:"beginIfPossible",value:function(e,E){0===this.level&&this.isInlineBlock(e,E)?this.level=1:this.level>0?this.level++:this.level=0}},{key:"end",value:function(){this.level--}},{key:"isActive",value:function(){return this.level>0}},{key:"isInlineBlock",value:function(e,E){for(var t=0,n=0,R=E;R<e.length;R++){var r=e[R];if(t+=r.value.length,t>o)return!1;if(r.type===T["default"].OPEN_PAREN)n++;else if(r.type===T["default"].CLOSE_PAREN&&(n--,0===n))return!0;if(this.isForbiddenToken(r))return!1}return!1}},{key:"isForbiddenToken",value:function(e){var E=e.type,t=e.value;return E===T["default"].RESERVED_TOP_LEVEL||E===T["default"].RESERVED_NEWLINE||E===T["default"].COMMENT||E===T["default"].BLOCK_COMMENT||";"===t}}]),e}();E["default"]=I,e.exports=E.default},db05:function(e,E,t){"use strict";Object.defineProperty(E,"__esModule",{value:!0}),E.supportedDialects=E.format=void 0;var T=L(t("9493")),n=L(t("bcef")),R=L(t("78f6")),r=L(t("2e6f")),N=L(t("2673")),o=L(t("b539")),I=L(t("b979")),O=L(t("de5e")),A=L(t("1847")),S=L(t("8f69"));function L(e){return e&&e.__esModule?e:{default:e}}function u(e){return u="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}var i={db2:T["default"],mariadb:n["default"],mysql:R["default"],n1ql:r["default"],plsql:N["default"],postgresql:o["default"],redshift:I["default"],spark:O["default"],sql:A["default"],tsql:S["default"]},C=function(e){var E=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"!==typeof e)throw new Error("Invalid query argument. Extected string, instead got "+u(e));var t=A["default"];if(void 0!==E.language&&(t=i[E.language]),void 0===t)throw Error("Unsupported SQL dialect: ".concat(E.language));return new t(E).format(e)};E.format=C;var a=Object.keys(i);E.supportedDialects=a},de5e:function(e,E,t){"use strict";function T(e){return T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}Object.defineProperty(E,"__esModule",{value:!0}),E["default"]=void 0;var n=o(t("a48e")),R=t("4fdb"),r=o(t("c606")),N=o(t("6b51"));function o(e){return e&&e.__esModule?e:{default:e}}function I(e,E){if(!(e instanceof E))throw new TypeError("Cannot call a class as a function")}function O(e,E){for(var t=0;t<E.length;t++){var T=E[t];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(e,T.key,T)}}function A(e,E,t){return E&&O(e.prototype,E),t&&O(e,t),e}function S(e,E){if("function"!==typeof E&&null!==E)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(E&&E.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),E&&L(e,E)}function L(e,E){return L=Object.setPrototypeOf||function(e,E){return e.__proto__=E,e},L(e,E)}function u(e){var E=a();return function(){var t,T=c(e);if(E){var n=c(this).constructor;t=Reflect.construct(T,arguments,n)}else t=T.apply(this,arguments);return i(this,t)}}function i(e,E){return!E||"object"!==T(E)&&"function"!==typeof E?C(e):E}function C(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function a(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}var f=["ALL","ALTER","ANALYSE","ANALYZE","ARRAY_ZIP","ARRAY","AS","ASC","AVG","BETWEEN","CASCADE","CASE","CAST","COALESCE","COLLECT_LIST","COLLECT_SET","COLUMN","COLUMNS","COMMENT","CONSTRAINT","CONTAINS","CONVERT","COUNT","CUME_DIST","CURRENT ROW","CURRENT_DATE","CURRENT_TIMESTAMP","DATABASE","DATABASES","DATE_ADD","DATE_SUB","DATE_TRUNC","DAY_HOUR","DAY_MINUTE","DAY_SECOND","DAY","DAYS","DECODE","DEFAULT","DELETE","DENSE_RANK","DESC","DESCRIBE","DISTINCT","DISTINCTROW","DIV","DROP","ELSE","ENCODE","END","EXISTS","EXPLAIN","EXPLODE_OUTER","EXPLODE","FILTER","FIRST_VALUE","FIRST","FIXED","FLATTEN","FOLLOWING","FROM_UNIXTIME","FULL","GREATEST","GROUP_CONCAT","HOUR_MINUTE","HOUR_SECOND","HOUR","HOURS","IF","IFNULL","IN","INSERT","INTERVAL","INTO","IS","LAG","LAST_VALUE","LAST","LEAD","LEADING","LEAST","LEVEL","LIKE","MAX","MERGE","MIN","MINUTE_SECOND","MINUTE","MONTH","NATURAL","NOT","NOW()","NTILE","NULL","NULLIF","OFFSET","ON DELETE","ON UPDATE","ON","ONLY","OPTIMIZE","OVER","PERCENT_RANK","PRECEDING","RANGE","RANK","REGEXP","RENAME","RLIKE","ROW","ROWS","SECOND","SEPARATOR","SEQUENCE","SIZE","STRING","STRUCT","SUM","TABLE","TABLES","TEMPORARY","THEN","TO_DATE","TO_JSON","TO","TRAILING","TRANSFORM","TRUE","TRUNCATE","TYPE","TYPES","UNBOUNDED","UNIQUE","UNIX_TIMESTAMP","UNLOCK","UNSIGNED","USING","VARIABLES","VIEW","WHEN","WITH","YEAR_MONTH"],l=["ADD","AFTER","ALTER COLUMN","ALTER DATABASE","ALTER SCHEMA","ALTER TABLE","CLUSTER BY","CLUSTERED BY","DELETE FROM","DISTRIBUTE BY","FROM","GROUP BY","HAVING","INSERT INTO","INSERT","LIMIT","OPTIONS","ORDER BY","PARTITION BY","PARTITIONED BY","RANGE","ROWS","SELECT","SET CURRENT SCHEMA","SET SCHEMA","SET","TBLPROPERTIES","UPDATE","USING","VALUES","WHERE","WINDOW"],s=["EXCEPT ALL","EXCEPT","INTERSECT ALL","INTERSECT","UNION ALL","UNION"],D=["AND","CREATE OR","CREATE","ELSE","LATERAL VIEW","OR","OUTER APPLY","WHEN","XOR","JOIN","INNER JOIN","LEFT JOIN","LEFT OUTER JOIN","RIGHT JOIN","RIGHT OUTER JOIN","FULL JOIN","FULL OUTER JOIN","CROSS JOIN","NATURAL JOIN","ANTI JOIN","SEMI JOIN","LEFT ANTI JOIN","LEFT SEMI JOIN","RIGHT OUTER JOIN","RIGHT SEMI JOIN","NATURAL ANTI JOIN","NATURAL FULL OUTER JOIN","NATURAL INNER JOIN","NATURAL LEFT ANTI JOIN","NATURAL LEFT OUTER JOIN","NATURAL LEFT SEMI JOIN","NATURAL OUTER JOIN","NATURAL RIGHT OUTER JOIN","NATURAL RIGHT SEMI JOIN","NATURAL SEMI JOIN"],U=function(e){S(t,e);var E=u(t);function t(){return I(this,t),E.apply(this,arguments)}return A(t,[{key:"tokenizer",value:function(){return new r["default"]({reservedWords:f,reservedTopLevelWords:l,reservedNewlineWords:D,reservedTopLevelWordsNoIndent:s,stringTypes:['""',"''","``","{}"],openParens:["(","CASE"],closeParens:[")","END"],indexedPlaceholderTypes:["?"],namedPlaceholderTypes:["$"],lineCommentTypes:["--"],operators:["!=","<=>","&&","||","=="]})}},{key:"tokenOverride",value:function(e){if((0,R.isWindow)(e)){var E=this.tokenLookAhead();if(E&&E.type===N["default"].OPEN_PAREN)return{type:N["default"].RESERVED,value:e.value}}if((0,R.isEnd)(e)){var t=this.tokenLookBehind();if(t&&t.type===N["default"].OPERATOR&&"."===t.value)return{type:N["default"].WORD,value:e.value}}return e}}]),t}(n["default"]);E["default"]=U,e.exports=E.default}}]);