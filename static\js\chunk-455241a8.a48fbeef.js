(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-455241a8"],{"09f4":function(e,t,o){"use strict";o.d(t,"a",(function(){return a})),Math.easeInOutQuad=function(e,t,o,i){return e/=i/2,e<1?o/2*e*e+t:(e--,-o/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function l(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function a(e,t,o){var a=n(),r=e-a,s=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=s;var n=Math.easeInOutQuad(u,a,r,t);l(n),u<t?i(e):o&&"function"===typeof o&&o()};c()}},"32e8":function(e,t,o){"use strict";o.d(t,"b",(function(){return l})),o.d(t,"a",(function(){return n})),o.d(t,"c",(function(){return a})),o.d(t,"e",(function(){return r})),o.d(t,"d",(function(){return s}));var i=o("b775");function l(e){return Object(i["a"])({url:"api/log/pageList",method:"get",params:e})}function n(e,t,o){return Object(i["a"])({url:"/api/log/clearLog?jobGroup="+e+"&jobId="+t+"&type="+o,method:"post"})}function a(e){return Object(i["a"])({url:"/api/log/killJob",method:"post",data:e})}function r(e,t,o,l){return Object(i["a"])({url:"/api/log/logDetailCat?executorAddress="+e+"&triggerTime="+t+"&logId="+o+"&fromLineNum="+l,method:"get"})}function s(e,t,o,l,n){return Object(i["a"])({url:"/api/schedulerlog/logDetailCat?executorAddress="+e+"&tasktype="+t+"&triggerTime="+o+"&logId="+l+"&fromLineNum="+n,method:"get"})}},67248:function(e,t,o){"use strict";o("8d41");var i="@@wavesContext";function l(e,t){function o(o){var i=Object.assign({},t.value),l=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),n=l.ele;if(n){n.style.position="relative",n.style.overflow="hidden";var a=n.getBoundingClientRect(),r=n.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(a.width,a.height)+"px",n.appendChild(r)),l.type){case"center":r.style.top=a.height/2-r.offsetHeight/2+"px",r.style.left=a.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(o.pageY-a.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(o.pageX-a.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=l.color,r.className="waves-ripple z-active",!1}}return e[i]?e[i].removeHandle=o:e[i]={removeHandle:o},o}var n={bind:function(e,t){e.addEventListener("click",l(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[i].removeHandle,!1),e.addEventListener("click",l(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[i].removeHandle,!1),e[i]=null,delete e[i]}},a=function(e){e.directive("waves",n)};window.Vue&&(window.waves=n,Vue.use(a)),n.install=a;t["a"]=n},"8d41":function(e,t,o){},"9a37":function(e,t,o){},aa5c:function(e,t,o){"use strict";o("9a37")},c8ae:function(e,t,o){"use strict";o.r(t);var i=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("div",{staticClass:"filter-container"},[o("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"文件类型"},model:{value:e.listQuery.filetype,callback:function(t){e.$set(e.listQuery,"filetype",t)},expression:"listQuery.filetype"}},[o("el-option",{attrs:{label:"",value:""}}),o("el-option",{attrs:{label:"SHELL",value:"SHELL"}}),o("el-option",{attrs:{label:"PYTHON",value:"PYTHON"}})],1),o("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{clearable:"",placeholder:"任务编码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.taskcode,callback:function(t){e.$set(e.listQuery,"taskcode",t)},expression:"listQuery.taskcode"}}),o("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{clearable:"",placeholder:"任务名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.taskname,callback:function(t){e.$set(e.listQuery,"taskname",t)},expression:"listQuery.taskname"}}),o("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")])],1),o("div",{staticClass:"table-box"},[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[o("el-table-column",{attrs:{align:"left",label:"序号",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),o("el-table-column",{attrs:{label:"任务类型",align:"left",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.filetype))]}}])}),e._e(),o("el-table-column",{attrs:{label:"任务名称",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.taskname))]}}])}),o("el-table-column",{attrs:{label:"调度结果",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[500===t.row.triggercode?o("el-tag",{attrs:{type:"danger"}},[e._v("失败")]):200===t.row.triggercode?o("el-tag",{attrs:{type:"success"}},[e._v("成功")]):e._e()]}}])}),o("el-table-column",{attrs:{label:"日志路径",align:"left",width:"340"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.logpath))]}}])}),o("el-table-column",{attrs:{label:"执行结果",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[500===t.row.isExecuteSuccess?o("el-tag",{attrs:{type:"danger"}},[e._v("失败")]):200===t.row.isExecuteSuccess?o("el-tag",{attrs:{type:"success"}},[e._v("成功")]):e._e()]}}])}),o("el-table-column",{attrs:{label:"执行时间",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.createtime))]}}])}),o("el-table-column",{attrs:{label:"操作",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[o("el-button",{directives:[{name:"show",rawName:"v-show",value:i.id,expression:"row.id"}],attrs:{type:"warning",icon:"el-icon-tickets"},on:{click:function(t){return e.handleViewJobLog(i)}}},[e._v("日志查看")])]}}])})],1)],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.pageNo,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageNo",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.fetchData}}),o("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"600px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[o("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[o("el-form-item",{attrs:{label:"任务名称",prop:"job_name"}},[o("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"任务名称"},model:{value:e.temp.job_name,callback:function(t){e.$set(e.temp,"job_name",t)},expression:"temp.job_name"}})],1),o("el-dialog",{attrs:{title:"提示",visible:e.showCronBox,width:"60%","append-to-body":""},on:{"update:visible":function(t){e.showCronBox=t}}},[o("cron",{model:{value:e.temp.cron,callback:function(t){e.$set(e.temp,"cron",t)},expression:"temp.cron"}}),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.showCronBox=!1}}},[e._v("取消")]),o("el-button",{attrs:{type:"primary"},on:{click:function(t){e.showCronBox=!1}}},[e._v("确 定")])],1)],1),o("el-form-item",{attrs:{label:"调度表达式",prop:"cron"}},[o("el-input",{attrs:{"auto-complete":"off",placeholder:"请输入Cron表达式"},model:{value:e.temp.cron,callback:function(t){e.$set(e.temp,"cron",t)},expression:"temp.cron"}},[e.showCronBox?o("el-button",{attrs:{slot:"append",icon:"el-icon-open",title:"关闭图形配置"},on:{click:function(t){e.showCronBox=!1}},slot:"append"}):o("el-button",{attrs:{slot:"append",icon:"el-icon-turn-off",title:"打开图形配置"},on:{click:function(t){e.showCronBox=!0}},slot:"append"})],1)],1),o("el-form-item",{attrs:{label:"任务状态",prop:"status"}},[o("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"任务状态"},model:{value:e.temp.status,callback:function(t){e.$set(e.temp,"status",t)},expression:"temp.status"}},[o("el-option",{attrs:{label:"启用",value:"启用"}}),o("el-option",{attrs:{label:"停用",value:"停用"}})],1)],1),o("el-form-item",{attrs:{label:"调度文件",prop:"filename"}},[o("el-select",{attrs:{placeholder:"调度文件"},model:{value:e.temp.filename,callback:function(t){e.$set(e.temp,"filename",t)},expression:"temp.filename"}},e._l(e.scheduleFileNameList,(function(e,t){return o("el-option",{key:"person-"+t,attrs:{label:e.filename,value:e.filename}})})),1)],1),o("el-form-item",{attrs:{label:"负责人",prop:"warnperson"}},[o("el-select",{attrs:{placeholder:"负责人"},model:{value:e.temp.warnperson,callback:function(t){e.$set(e.temp,"warnperson",t)},expression:"temp.warnperson"}},e._l(e.warnpersonList,(function(e,t){return o("el-option",{key:"person-"+t,attrs:{label:e.username,value:e.username}})})),1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),o("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确认 ")])],1)],1),o("el-dialog",{attrs:{visible:e.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(t){e.dialogPluginVisible=t}}},[o("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pluginData,border:"",fit:"","highlight-current-row":""}},[o("el-table-column",{attrs:{prop:"key",label:"Channel"}}),o("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogPvVisible=!1}}},[e._v("Confirm")])],1)],1),o("el-dialog",{attrs:{title:"任务调度日志查看",visible:e.dialogVisible,width:"65%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[o("div",{staticClass:"log-container"},[o("pre",{attrs:{loading:e.logLoading},domProps:{textContent:e._s(e.logContent)}})]),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v(" 关闭 ")]),o("el-button",{attrs:{type:"primary"},on:{click:e.loadLog}},[e._v(" 刷新日志 ")])],1)])],1)},l=[],n=o("32e8"),a=o("b775");function r(e){return Object(a["a"])({url:"/api/schedulerlog/list",method:"get",params:e})}var s=o("5ec8"),u=o("67248"),c=o("333d"),d={name:"DevEnvSetting",components:{Pagination:c["a"],Cron:s["a"]},directives:{waves:u["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,dialogVisible:!1,total:0,listQuery:{pageNo:1,pageSize:10,filetype:"",taskcode:"",taskname:""},showCronBox:!1,pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑任务调度",create:"新增任务调度"},rules:{job_name:[{required:!0,message:"this is required",trigger:"blur"}],cron:[{required:!0,message:"this is required",trigger:"blur"}],warnperson:[{required:!0,message:"this is required",trigger:"blur"}],remark:[{required:!0,message:"this is required",trigger:"blur"}],status:[{required:!0,message:"this is required",trigger:"blur"}],filename:[{required:!0,message:"this is required",trigger:"blur"}]},warnpersonList:[],scheduleFileNameList:[],temp:{id:void 0,name:"",description:""},visible:!0,jobLogQuery:{executorAddress:"",filetype:"",triggerTime:"",id:"",fromLineNum:1},logContent:"",logShow:!1,logLoading:!1}},created:function(){this.fetchData()},methods:{fetchData:function(){var e=this;this.listLoading=!0,r(this.listQuery).then((function(t){var o=t.content;e.total=o.recordsTotal,e.list=o.data,e.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleViewJobLog:function(e){this.logContent="",this.dialogVisible=!0,this.jobLogQuery.executorAddress=e.logpath,this.jobLogQuery.filetype=e.filetype,this.jobLogQuery.triggerTime=Date.parse(e.createtime),this.jobLogQuery.id=e.id,!1===this.logShow&&(this.logShow=!0),this.loadLog()},loadLog:function(){var e=this;this.logLoading=!0,"DAG"==this.jobLogQuery.filetype?n["e"](this.jobLogQuery.executorAddress,this.jobLogQuery.triggerTime,this.jobLogQuery.id,this.jobLogQuery.fromLineNum).then((function(t){"\n"===t.content.logContent||(e.logContent=t.content.logContent),e.logLoading=!1})):n["d"](this.jobLogQuery.executorAddress,this.jobLogQuery.filetype,this.jobLogQuery.triggerTime,this.jobLogQuery.id,this.jobLogQuery.fromLineNum).then((function(t){"\n"===t.content.logContent||(e.logContent=t.content.logContent),e.logLoading=!1}))}}},p=d,f=(o("aa5c"),o("2877")),g=Object(f["a"])(p,i,l,!1,null,"1e5c5fec",null);t["default"]=g.exports}}]);