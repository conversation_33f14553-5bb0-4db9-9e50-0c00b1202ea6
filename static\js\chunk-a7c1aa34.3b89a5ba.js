(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a7c1aa34"],{"51cb":function(e,t,n){},"6b03":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{padding:"20px"}},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:18}},[e.form?n("el-form",{ref:"form",attrs:{model:e.form,"status-icon":""}},[n("el-form-item",[n("el-row",[n("el-col",{attrs:{span:4}},[n("vxe-select",{staticClass:"opt",staticStyle:{width:"100%"},attrs:{placeholder:"请选择",options:e.options,clearable:""},model:{value:e.queryMethod,callback:function(t){e.queryMethod=t},expression:"queryMethod"}})],1),n("el-col",{attrs:{span:20}},[n("el-input",{attrs:{placeholder:"请输入请求的URL地址",prop:"name"},on:{input:e.nameInput},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1)],1)],1):e._e()],1),n("el-col",{attrs:{span:6}},[n("el-button",{attrs:{loading:e.sendLoading,type:"primary"},on:{click:e.sendMethod}},[e._v("Send ")])],1)],1),n("div",{staticClass:"reqCss"},[n("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{attrs:{label:"Params",name:"first"}},[n("div",{staticStyle:{height:"25vh",overflow:"scroll"}},[e.firstShow?n("vxe-table",{ref:"first",staticClass:"table",attrs:{border:"",keepSource:"","edit-config":{trigger:"click",mode:"cell",showIcon:!1},data:e.firstTable,loading:!1,"checkbox-config":{checkStrictly:!0},"row-config":{useKey:!0}},on:{"checkbox-change":e.handleCheckbox}},[n("vxe-column",{attrs:{type:"checkbox",width:"50"}}),n("vxe-column",{attrs:{field:"key",title:"Key","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var o=t.row;return[n("vxe-input",{attrs:{type:"text",placeholder:"Key"},on:{input:function(t){return e.firstInput(o)}},model:{value:o.key,callback:function(t){e.$set(o,"key",t)},expression:"row.key"}})]}}],null,!1,1044639737)}),n("vxe-column",{attrs:{field:"value",title:"Value","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var o=t.row;return[n("vxe-input",{attrs:{type:"text",placeholder:"Value"},on:{input:function(t){return e.firstInput(o)}},model:{value:o.value,callback:function(t){e.$set(o,"value",t)},expression:"row.value"}})]}}],null,!1,2851095673)}),n("vxe-column",{attrs:{title:"操作",width:"100","show-overflow":"",visible:e.firstIconVisible},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("vxe-button",{staticClass:"drag-btn",attrs:{mode:"text",icon:"vxe-icon-drag-handle"},on:{click:function(t){return e.rowDrop(o)}}}),n("vxe-button",{attrs:{mode:"text",icon:"vxe-icon-delete"},on:{click:function(t){return e.removeRowFirst(o)}}})]}}],null,!1,2608510476)})],1):n("el-input",{attrs:{type:"textarea",rows:10,placeholder:"Rows are separated by new lines\nKeys and values are separated by :\nPrepend // to any row you want to add but keep disabled"},model:{value:e.firstArea,callback:function(t){e.firstArea=t},expression:"firstArea"}})],1)]),n("el-tab-pane",{attrs:{label:"Headers",name:"second"}},[n("div",{staticStyle:{height:"25vh",overflow:"scroll"}},[e.secondShow?n("vxe-table",{ref:"second",staticClass:"table",attrs:{border:"","edit-config":{trigger:"click",mode:"cell",showIcon:!1,beforeEditMethod:e.editActive},data:e.secondTable,loading:!1,"checkbox-config":{checkStrictly:!0,checkMethod:e.checkMethod},"row-config":{useKey:!0}},on:{"checkbox-change":e.handleCheckbox}},[n("vxe-column",{attrs:{type:"checkbox",width:"50"}}),n("vxe-column",{attrs:{field:"key",title:"Key","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var o=t.row;return[n("vxe-input",{attrs:{type:"text",placeholder:"Key"},on:{input:function(t){return e.secondInput(o)}},model:{value:o.key,callback:function(t){e.$set(o,"key",t)},expression:"row.key"}})]}}],null,!1,3036579027)}),n("vxe-column",{attrs:{field:"value",title:"Value","edit-render":{}},scopedSlots:e._u([{key:"edit",fn:function(t){var o=t.row;return[n("vxe-input",{attrs:{type:"text",placeholder:"Value"},on:{input:function(t){return e.secondInput(o)}},model:{value:o.value,callback:function(t){e.$set(o,"value",t)},expression:"row.value"}})]}}],null,!1,627056531)}),n("vxe-column",{attrs:{title:"操作",width:"100","show-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return[n("vxe-button",{staticClass:"drag-btn",attrs:{disabled:o.disabled,mode:"text",icon:"vxe-icon-drag-handle"},on:{click:function(t){return e.rowDrop(o)}}}),n("vxe-button",{attrs:{disabled:o.disabled,mode:"text",icon:"vxe-icon-delete"},on:{click:function(t){return e.removeRowSecond(o)}}})]}}],null,!1,1358798662)})],1):n("el-input",{attrs:{type:"textarea",rows:10,placeholder:"Rows are separated by new lines\nKeys and values are separated by :\nPrepend // to any row you want to add but keep disabled"},model:{value:e.secondArea,callback:function(t){e.secondArea=t},expression:"secondArea"}})],1)])],1)],1),n("div",{staticClass:"resCss",staticStyle:{border:"1px solid #ddd"},domProps:{innerHTML:e._s(e.resStr)}})],1)},a=[],r=(n("3835"),n("99af"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("4e82"),n("a434"),n("b0c0"),n("e9c4"),n("b64b"),n("d3b7"),n("ac1f"),n("2532"),n("5319"),n("498a"),n("159b"),n("aa47")),s=n("b775");function i(e){return Object(s["a"])({url:"/api/apiConfig/getOrPostRequest",method:"get",params:e})}var l={data:function(){return{form:{name:""},title:"Bulk Edit",firstArea:"",secondArea:"",firstShow:!0,secondShow:!0,firstIconVisible:!1,sendLoading:!1,firstTable:[{}],resStr:"Response",secondTable:[{key:"Accept",value:"*/*",disabled:!0,edit:!0},{key:"Connection",value:"keep-alive",disabled:!0,edit:!0},{key:"content-type",value:"application/json;charset=UTF-8",disabled:!0,edit:!0},{disabled:!0}],options:[{value:"GET",label:"GET",className:"green"},{value:"POST",label:"POST",className:"gold"}],queryMethod:"GET",activeName:"first",sortable:null,firstTableDom:null,secondTableDom:null}},mounted:function(){this.firstTableDom=this.$refs.first,this.secondTableDom=this.$refs.second,this.rowDrop(this.firstTableDom),this.rowDrop(this.secondTableDom),document.querySelector(".vxe-input--inner").classList.add("green"),this.secondTableDom.setCheckboxRow(this.secondTable[0],!0)},created:function(){},beforeDestroy:function(){this.sortable&&this.sortable.destroy()},methods:{handleClick:function(e,t){},removeRowFirst:function(e){var t=this.firstTableDom,n=t.getTableData().fullData.length;n>2?t.remove(e):(t.remove(e),this.firstIconVisible=!1)},removeRowSecond:function(e){this.secondTableDom.remove(e)},rowDrop:function(e){var t=this;this.$nextTick((function(){t.sortable=r["a"].create(e.$el.querySelector(".body--wrapper>.vxe-table--body tbody"),{handle:".drag-btn",onEnd:function(e){var n=e.newIndex,o=e.oldIndex,a=t.firstTable.splice(o,1)[0];t.firstTable.splice(n,0,a)}})}))},handleCheckbox:function(){var e,t=this.firstTableDom.getCheckboxRecords().sort((function(e,t){var n=parseInt(e._X_ROW_KEY.replace("row_",""),10),o=parseInt(t._X_ROW_KEY.replace("row_",""),10);return n-o})),n=this.form.name.split("?")[0];if(0!==t.length){var o=t.map((function(e){return"".concat(e.key?e.key:"","=").concat(e.value?e.value:"")})),a=o.join("&");null!==(e=this.form)&&void 0!==e&&e.name.includes("?")?this.form.name="".concat(n,"?").concat(a):this.form.name="".concat(this.form.name,"?").concat(a),this.$forceUpdate()}else this.form.name=n},firstInput:function(e){var t=this.firstTableDom;t.setCheckboxRow(e,!0),this.handleCheckbox();var n=t.getVTRowIndex(e)+1,o=t.getTableData().fullData;n===o.length&&(t.insertAt({},-1),this.firstTable=t.getTableData().fullData,this.firstIconVisible=!0)},secondInput:function(e){var t=this.secondTableDom;t.setCheckboxRow(e,!0);var n=t.getVTRowIndex(e)+1,o=t.getTableData().fullData;n===o.length&&(t.insertAt({},-1),this.secondTable=t.getTableData().fullData)},nameInput:function(){},firstBtn:function(){},secondBtn:function(){},checkMethod:function(e){var t=e.row;return"Token"!=t.key},editActive:function(e){var t=e.row;return!t.edit},sendMethod:function(){var e=this;console.log("Headers",JSON.stringify());var t=-1==this.form.name.indexOf("?")?this.form.name:this.form.name.substr(0,this.form.name.indexOf("?")),n=Object.assign({},this.firstTable),o=JSON.stringify(n),a=Object.assign({},this.secondTable),r=JSON.stringify(a);this.form.name.trim()?(this.sendLoading=!0,i({rtype:this.queryMethod,pathurl:t,rparam:o,rheader:r}).then((function(t){e.resStr=JSON.parse(JSON.stringify(t,null,2)).data,e.$message.success("请求成功")}),(function(t){e.resStr=JSON.stringify(t,null,2)})).finally((function(){e.sendLoading=!1}))):this.$message.error("请输入请求地址")}},watch:{queryMethod:function(e){"GET"===e?(document.querySelector(".vxe-input--inner").classList.add("green"),document.querySelector(".vxe-input--inner").classList.remove("gold")):(document.querySelector(".vxe-input--inner").classList.add("gold"),document.querySelector(".vxe-input--inner").classList.remove("green"))}}},c=l,d=(n("97b3"),n("2877")),u=Object(d["a"])(c,o,a,!1,null,"c40bd538",null);t["default"]=u.exports},"97b3":function(e,t,n){"use strict";n("51cb")}}]);