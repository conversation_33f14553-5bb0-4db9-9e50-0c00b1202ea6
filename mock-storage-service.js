// Mock存储服务 - 模拟数据持久化和缓存
// 为数据中台提供本地数据存储模拟支持

(function() {
    'use strict';
    
    console.log('💾 启动Mock存储服务');
    
    // Mock存储管理器
    class MockStorageManager {
        constructor() {
            this.prefix = 'datacenter_';
            this.initDefaultData();
        }
        
        // 初始化默认数据
        initDefaultData() {
            // 如果没有数据，初始化一些默认数据
            if (!this.get('initialized')) {
                this.initUserPreferences();
                this.initSystemSettings();
                this.initCacheData();
                this.set('initialized', true);
                console.log('💾 初始化默认存储数据完成');
            }
        }
        
        // 初始化用户偏好设置
        initUserPreferences() {
            const preferences = {
                theme: 'default',
                language: 'zh-CN',
                pageSize: 10,
                autoRefresh: true,
                refreshInterval: 30000,
                notifications: {
                    email: true,
                    browser: true,
                    sound: false
                },
                dashboard: {
                    layout: 'grid',
                    widgets: ['overview', 'chart', 'table', 'notifications']
                }
            };
            this.set('user_preferences', preferences);
        }
        
        // 初始化系统设置
        initSystemSettings() {
            const settings = {
                version: '1.0.0',
                lastUpdate: new Date().toISOString(),
                features: {
                    darkMode: true,
                    realTimeData: true,
                    exportData: true,
                    advancedSearch: true
                },
                limits: {
                    maxFileSize: 10485760, // 10MB
                    maxRecords: 10000,
                    sessionTimeout: 7200 // 2小时
                }
            };
            this.set('system_settings', settings);
        }
        
        // 初始化缓存数据
        initCacheData() {
            const cacheData = {
                recentSearches: [
                    '用户数据',
                    '销售报表',
                    '系统日志',
                    '性能监控'
                ],
                favoriteReports: [
                    { id: 1, name: '日销售报表', path: '/report/daily-sales' },
                    { id: 2, name: '用户活跃度', path: '/report/user-activity' },
                    { id: 3, name: '系统性能', path: '/report/system-performance' }
                ],
                recentlyViewed: [
                    { id: 1, type: 'report', name: '月度总结', time: new Date().toISOString() },
                    { id: 2, type: 'data', name: '用户列表', time: new Date(Date.now() - 3600000).toISOString() },
                    { id: 3, type: 'chart', name: '销售趋势', time: new Date(Date.now() - 7200000).toISOString() }
                ]
            };
            this.set('cache_data', cacheData);
        }
        
        // 存储数据
        set(key, value, expiry = null) {
            const fullKey = this.prefix + key;
            const data = {
                value: value,
                timestamp: Date.now(),
                expiry: expiry
            };
            
            try {
                localStorage.setItem(fullKey, JSON.stringify(data));
                console.log(`💾 存储数据: ${key}`);
                return true;
            } catch (error) {
                console.error('💾 存储数据失败:', error);
                return false;
            }
        }
        
        // 获取数据
        get(key, defaultValue = null) {
            const fullKey = this.prefix + key;
            
            try {
                const item = localStorage.getItem(fullKey);
                if (!item) return defaultValue;
                
                const data = JSON.parse(item);
                
                // 检查是否过期
                if (data.expiry && Date.now() > data.expiry) {
                    this.remove(key);
                    return defaultValue;
                }
                
                return data.value;
            } catch (error) {
                console.error('💾 获取数据失败:', error);
                return defaultValue;
            }
        }
        
        // 删除数据
        remove(key) {
            const fullKey = this.prefix + key;
            localStorage.removeItem(fullKey);
            console.log(`💾 删除数据: ${key}`);
        }
        
        // 清空所有数据
        clear() {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            console.log('💾 清空所有存储数据');
        }
        
        // 获取所有键
        getAllKeys() {
            const keys = Object.keys(localStorage);
            return keys
                .filter(key => key.startsWith(this.prefix))
                .map(key => key.replace(this.prefix, ''));
        }
        
        // 获取存储大小
        getStorageSize() {
            let size = 0;
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    size += localStorage.getItem(key).length;
                }
            });
            return size;
        }
        
        // 缓存API响应
        cacheApiResponse(url, method, data, ttl = 300000) { // 默认5分钟
            const cacheKey = `api_cache_${method}_${btoa(url)}`;
            const expiry = Date.now() + ttl;
            this.set(cacheKey, data, expiry);
        }
        
        // 获取缓存的API响应
        getCachedApiResponse(url, method) {
            const cacheKey = `api_cache_${method}_${btoa(url)}`;
            return this.get(cacheKey);
        }
        
        // 添加到最近搜索
        addRecentSearch(query) {
            const searches = this.get('cache_data', {}).recentSearches || [];
            
            // 移除重复项
            const index = searches.indexOf(query);
            if (index > -1) {
                searches.splice(index, 1);
            }
            
            // 添加到开头
            searches.unshift(query);
            
            // 限制数量
            if (searches.length > 10) {
                searches.splice(10);
            }
            
            const cacheData = this.get('cache_data', {});
            cacheData.recentSearches = searches;
            this.set('cache_data', cacheData);
        }
        
        // 添加到最近查看
        addRecentlyViewed(item) {
            const viewed = this.get('cache_data', {}).recentlyViewed || [];
            
            // 移除重复项
            const index = viewed.findIndex(v => v.id === item.id && v.type === item.type);
            if (index > -1) {
                viewed.splice(index, 1);
            }
            
            // 添加到开头
            viewed.unshift({
                ...item,
                time: new Date().toISOString()
            });
            
            // 限制数量
            if (viewed.length > 20) {
                viewed.splice(20);
            }
            
            const cacheData = this.get('cache_data', {});
            cacheData.recentlyViewed = viewed;
            this.set('cache_data', cacheData);
        }
        
        // 更新用户偏好
        updateUserPreference(key, value) {
            const preferences = this.get('user_preferences', {});
            
            // 支持嵌套键，如 'notifications.email'
            const keys = key.split('.');
            let current = preferences;
            
            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) {
                    current[keys[i]] = {};
                }
                current = current[keys[i]];
            }
            
            current[keys[keys.length - 1]] = value;
            this.set('user_preferences', preferences);
            
            console.log(`💾 更新用户偏好: ${key} = ${value}`);
        }
        
        // 获取用户偏好
        getUserPreference(key, defaultValue = null) {
            const preferences = this.get('user_preferences', {});
            
            const keys = key.split('.');
            let current = preferences;
            
            for (const k of keys) {
                if (current && typeof current === 'object' && k in current) {
                    current = current[k];
                } else {
                    return defaultValue;
                }
            }
            
            return current;
        }
        
        // 导出数据
        exportData() {
            const data = {};
            const keys = this.getAllKeys();
            
            keys.forEach(key => {
                data[key] = this.get(key);
            });
            
            return data;
        }
        
        // 导入数据
        importData(data) {
            Object.keys(data).forEach(key => {
                this.set(key, data[key]);
            });
            console.log('💾 导入数据完成');
        }
    }
    
    // 创建全局存储管理器实例
    window.mockStorageManager = new MockStorageManager();
    
    // 扩展window对象，提供便捷方法
    window.mockStorage = {
        // 快捷方法
        setUserPref: (key, value) => window.mockStorageManager.updateUserPreference(key, value),
        getUserPref: (key, defaultValue) => window.mockStorageManager.getUserPreference(key, defaultValue),
        addSearch: (query) => window.mockStorageManager.addRecentSearch(query),
        addViewed: (item) => window.mockStorageManager.addRecentlyViewed(item),
        
        // 数据管理
        save: (key, value, expiry) => window.mockStorageManager.set(key, value, expiry),
        load: (key, defaultValue) => window.mockStorageManager.get(key, defaultValue),
        delete: (key) => window.mockStorageManager.remove(key),
        clear: () => window.mockStorageManager.clear(),
        
        // 缓存管理
        cacheApi: (url, method, data, ttl) => window.mockStorageManager.cacheApiResponse(url, method, data, ttl),
        getCachedApi: (url, method) => window.mockStorageManager.getCachedApiResponse(url, method),
        
        // 工具方法
        getSize: () => window.mockStorageManager.getStorageSize(),
        export: () => window.mockStorageManager.exportData(),
        import: (data) => window.mockStorageManager.importData(data)
    };
    
    console.log('✅ Mock存储服务初始化完成');
    
})();
