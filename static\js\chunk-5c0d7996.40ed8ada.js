(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c0d7996"],{"16d2":function(e,r,t){},"34e3":function(e,r,t){"use strict";t.r(r);var a=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("div",{staticClass:"build-container"},[t("el-steps",{attrs:{active:e.active,"finish-status":"success"}},[t("el-step",{attrs:{title:"1.选择来源",description:""}},[e._v("1")]),t("el-step",{attrs:{title:"2.选择去向",description:""}},[e._v("2")]),t("el-step",{attrs:{title:"3.字段映射",description:""}},[e._v("3")]),t("el-step",{attrs:{title:"4.构建任务",description:""}},[e._v("4")])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1===e.active,expression:"active === 1"}],staticClass:"step1"},[t("Reader",{ref:"reader"})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:2===e.active,expression:"active === 2"}],staticClass:"step2"},[t("Writer",{ref:"writer"})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:3===e.active,expression:"active === 3"}],staticClass:"step3"},[t("Mapper",{ref:"mapper"})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:4===e.active,expression:"active === 4"}],staticClass:"step4"},[t("el-button",{attrs:{type:"primary"},on:{click:e.handleJobTemplateSelectDrawer}},[e._v(e._s(e.jobTemplate?e.jobTemplate:"调度模板"))]),t("el-button",{attrs:{type:"info"},on:{click:function(r){return e.handleCopy(e.inputData,r)}}},[e._v("复制json")]),e._v(" (步骤：选择调度模板->下一步) "),t("el-drawer",{ref:"jobTemplateSelectDrawer",attrs:{title:"调度模板",visible:e.jobTemplateSelectDrawer,direction:"rtl",size:"50%"},on:{"update:visible":function(r){e.jobTemplateSelectDrawer=r}}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","destroy-on-close":"true"},on:{"current-change":e.handleCurrentChange}},[t("el-table-column",{attrs:{align:"left",label:"任务ID",width:"80"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(e._s(r.row.id))]}}])}),t("el-table-column",{attrs:{label:"所属项目",align:"left",width:"120"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(e._s(r.row.projectName))]}}])}),t("el-table-column",{attrs:{label:"调度名称",align:"left"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(e._s(r.row.jobDesc))]}}])}),t("el-table-column",{attrs:{label:"任务类型",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return["datax"===r.row.glueType?t("el-tag",{attrs:{type:""}},[e._v("datax")]):"flinkx"===r.row.glueType?t("el-tag",{attrs:{type:"success"}},[e._v("flinkx")]):e._e()]}}])}),t("el-table-column",{attrs:{label:"调度表达式",align:"left"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(r.row.jobCron))])]}}])}),t("el-table-column",{attrs:{label:"路由策略",align:"left"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(" "+e._s(e.routeStrategies.find((function(e){return e.value===r.row.executorRouteStrategy})).label)+" ")]}}])}),t("el-table-column",{attrs:{label:"路由策略",align:"left"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(" "+e._s(e.routeStrategies.find((function(e){return e.value===r.row.executorRouteStrategy})).label)+" ")]}}])}),t("el-table-column",{attrs:{label:"线程数",align:"left"},scopedSlots:e._u([{key:"default",fn:function(r){return[e._v(e._s(r.row.cores))]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(r){return e.$set(e.listQuery,"current",r)},"update:limit":function(r){return e.$set(e.listQuery,"size",r)},pagination:e.fetchData}})],1),t("div",{staticStyle:{"margin-bottom":"20px"}}),t("json-editor",{directives:[{name:"show",rawName:"v-show",value:4===e.active,expression:"active === 4"}],ref:"jsonEditor",model:{value:e.configJson,callback:function(r){e.configJson=r},expression:"configJson"}})],1),t("el-button",{staticStyle:{"margin-top":"12px"},attrs:{disabled:1===e.active},on:{click:e.last}},[e._v("上一步")]),t("el-button",{staticStyle:{"margin-top":"12px","margin-bottom":"12px"},attrs:{type:"primary"},on:{click:e.next}},[e._v("下一步")])],1)])},o=[],i=(t("14d9"),t("b64b"),t("b775"));function s(e){return Object(i["a"])({url:"/api/flinkxJson/buildJson",method:"post",data:e})}var l=t("3a8d"),n=t("2b10"),c=t("333d"),d=t("fa7e"),u=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("RDBMSReader",{directives:[{name:"show",rawName:"v-show",value:"hive"!==e.dataSource&&"hbase"!==e.dataSource&&"mongodb"!==e.dataSource,expression:"dataSource!=='hive' && dataSource!=='hbase' && dataSource!=='mongodb'"}],ref:"rdbmsreader",on:{selectDataSource:e.showDataSource}}),t("HiveReader",{directives:[{name:"show",rawName:"v-show",value:"hive"===e.dataSource,expression:"dataSource==='hive'"}],ref:"hivereader",on:{selectDataSource:e.showDataSource}}),t("HBaseReader",{directives:[{name:"show",rawName:"v-show",value:"hbase"===e.dataSource,expression:"dataSource==='hbase'"}],ref:"hbasereader",on:{selectDataSource:e.showDataSource}}),t("MongoDBReader",{directives:[{name:"show",rawName:"v-show",value:"mongodb"===e.dataSource,expression:"dataSource==='mongodb'"}],ref:"mongodbreader",on:{selectDataSource:e.showDataSource}})],1)},m=[],h=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-form",{attrs:{"label-position":"right","label-width":"120px",model:e.readerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据库源：",prop:"datasourceId"}},[t("el-select",{staticStyle:{width:"300px"},attrs:{filterable:""},on:{change:e.rDsChange},model:{value:e.readerForm.datasourceId,callback:function(r){e.$set(e.readerForm,"datasourceId",r)},expression:"readerForm.datasourceId"}},e._l(e.rDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"postgresql"===e.dataSource||"oracle"===e.dataSource||"sqlserver"===e.dataSource||"hana"===e.dataSource,expression:"dataSource==='postgresql' || dataSource==='oracle' || dataSource==='sqlserver' || dataSource==='hana'"}],attrs:{label:"Schema：",prop:"tableSchema"}},[t("el-select",{staticStyle:{width:"300px"},attrs:{"allow-create":"","default-first-option":"",filterable:""},on:{change:e.schemaChange},model:{value:e.readerForm.tableSchema,callback:function(r){e.$set(e.readerForm,"tableSchema",r)},expression:"readerForm.tableSchema"}},e._l(e.schemaList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),t("el-form-item",{attrs:{label:"数据库表名：",prop:"tableName"}},[t("el-select",{staticStyle:{width:"300px"},attrs:{"allow-create":"","default-first-option":"",filterable:""},on:{change:e.rTbChange},model:{value:e.readerForm.tableName,callback:function(r){e.$set(e.readerForm,"tableName",r)},expression:"readerForm.tableName"}},e._l(e.rTbList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),t("el-form-item",{attrs:{label:"SQL语句："}},[t("el-input",{staticStyle:{width:"42%"},attrs:{autosize:{minRows:3,maxRows:20},type:"textarea",placeholder:"自定义筛选SQL,例如多表关联查询Join后同步数据,使用select a,b from table_a join table_b on table_a.id = table_b.id"},model:{value:e.readerForm.querySql,callback:function(r){e.$set(e.readerForm,"querySql",r)},expression:"readerForm.querySql"}}),t("el-button",{attrs:{type:"primary"},on:{click:function(r){return r.preventDefault(),e.getColumns("reader")}}},[e._v("解析字段")])],1),t("el-form-item",{attrs:{label:"切分字段："}},[t("el-input",{staticStyle:{width:"13%"},attrs:{placeholder:"切分主键"},model:{value:e.readerForm.splitPk,callback:function(r){e.$set(e.readerForm,"splitPk",r)},expression:"readerForm.splitPk"}})],1),t("el-form-item",{attrs:{label:"表所有字段："}},[t("el-checkbox",{attrs:{indeterminate:e.readerForm.isIndeterminate},on:{change:e.rHandleCheckAllChange},model:{value:e.readerForm.checkAll,callback:function(r){e.$set(e.readerForm,"checkAll",r)},expression:"readerForm.checkAll"}},[e._v("全选 ")]),t("div",{staticStyle:{margin:"15px 0"}}),t("el-checkbox-group",{on:{change:e.rHandleCheckedChange},model:{value:e.readerForm.columns,callback:function(r){e.$set(e.readerForm,"columns",r)},expression:"readerForm.columns"}},e._l(e.rColumnList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1),t("el-form-item",{attrs:{label:"where条件：",prop:"where"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"where条件，不需要再加where 例如:createTime >= ${startCreateTime} and createTime <= ${endCreateTime}",type:"textarea"},model:{value:e.readerForm.where,callback:function(r){e.$set(e.readerForm,"where",r)},expression:"readerForm.where"}})],1)],1)],1)},f=[],b=(t("7db0"),t("d3b7"),t("f352")),p=t("7e39"),g=t("2b0e"),w=new g["default"]({data:function(){return{dataSourceId:""}}}),v={name:"RDBMSReader",data:function(){return{jdbcDsQuery:{current:1,size:200,ascs:"datasource_name",datahandle:1},rDsList:[],rTbList:[],schemaList:[],rColumnList:[],loading:!1,active:1,customFields:"",customType:"",customValue:"",dataSource:"",readerForm:{datasourceId:void 0,tableName:"",columns:[],where:"",querySql:"",checkAll:!1,isIndeterminate:!0,splitPk:"",tableSchema:""},rules:{datasourceId:[{required:!0,message:"this is required",trigger:"change"}],tableName:[{required:!0,message:"this is required",trigger:"change"}],tableSchema:[{required:!0,message:"this is required",trigger:"change"}]}}},watch:{"readerForm.datasourceId":function(e,r){"postgresql"===this.dataSource||"oracle"===this.dataSource||"sqlserver"===this.dataSource||"hana"===this.dataSource?this.getSchema():this.getTables("rdbmsReader")}},created:function(){var e=this;this.getJdbcDs(),Object(p["e"])({datahandle:1}).then((function(r){var t=r.content.data;e.rDsList=t}))},methods:{getJdbcDs:function(e){var r=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(e){var t=e.records;r.rDsList=t,r.loading=!1}))},getTables:function(e){var r=this;if("rdbmsReader"===e){var t={};t="postgresql"===this.dataSource||"oracle"===this.dataSource||"sqlserver"===this.dataSource||"hana"===this.dataSource?{datasourceId:this.readerForm.datasourceId,tableSchema:this.readerForm.tableSchema}:{datasourceId:this.readerForm.datasourceId},b["h"](t).then((function(e){e&&(r.rTbList=e)}))}},getSchema:function(){var e=this,r={datasourceId:this.readerForm.datasourceId};b["g"](r).then((function(r){e.schemaList=r}))},schemaChange:function(e){this.readerForm.tableSchema=e,this.getTables("rdbmsReader")},rDsChange:function(e){var r=this;this.readerForm.tableName="",this.readerForm.datasourceId=e,this.rDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),w.dataSourceId=e,this.$emit("selectDataSource",this.dataSource)},getTableColumns:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,tableName:this.readerForm.tableName};b["b"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumnsByQuerySql:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,querySql:this.readerForm.querySql};b["d"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumns:function(e){"reader"===e&&(""!==this.readerForm.querySql?this.getColumnsByQuerySql():this.getTableColumns())},rTbChange:function(e){this.readerForm.tableName=e,this.rColumnList=[],this.readerForm.columns=[],this.getColumns("reader")},rHandleCheckAllChange:function(e){this.readerForm.columns=e?this.rColumnList:[],this.readerForm.isIndeterminate=!1},rHandleCheckedChange:function(e){var r=e.length;this.readerForm.checkAll=r===this.rColumnList.length,this.readerForm.isIndeterminate=r>0&&r<this.rColumnList.length},getData:function(){return w.dataSourceId&&(this.readerForm.datasourceId=w.dataSourceId),this.readerForm}}},F=v,S=t("2877"),C=Object(S["a"])(F,h,f,!1,null,null,null),k=C.exports,y=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-form",{attrs:{"label-position":"left","label-width":"105px",model:e.readerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据源",prop:"datasourceId"}},[t("el-select",{attrs:{filterable:""},on:{change:e.rDsChange},model:{value:e.readerForm.datasourceId,callback:function(r){e.$set(e.readerForm,"datasourceId",r)},expression:"readerForm.datasourceId"}},e._l(e.rDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"HDFS路径",prop:"hdfsPath"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.hdfsPath,callback:function(r){e.$set(e.readerForm,"hdfsPath",r)},expression:"readerForm.hdfsPath"}})],1),t("el-form-item",{attrs:{label:"副本数",prop:"replication"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.replication,callback:function(r){e.$set(e.readerForm,"replication",r)},expression:"readerForm.replication"}})],1),t("el-form-item",{attrs:{label:"文件区分名称",prop:"format"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.format,callback:function(r){e.$set(e.readerForm,"format",r)},expression:"readerForm.format"}})],1),t("el-form-item",{attrs:{label:"生成文件大小",prop:"blocksize"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.blocksize,callback:function(r){e.$set(e.readerForm,"blocksize",r)},expression:"readerForm.blocksize"}})],1),t("el-form-item",{attrs:{label:"生成新文件间隔",prop:"rolloverInterval"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.rolloverInterval,callback:function(r){e.$set(e.readerForm,"rolloverInterval",r)},expression:"readerForm.rolloverInterval"}})],1),t("el-form-item",{attrs:{label:"文件名前缀",prop:"prefix"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.prefix,callback:function(r){e.$set(e.readerForm,"prefix",r)},expression:"readerForm.prefix"}})],1),t("el-form-item",{attrs:{label:"文件名后缀",prop:"suffix"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.suffix,callback:function(r){e.$set(e.readerForm,"suffix",r)},expression:"readerForm.suffix"}})],1),t("el-form-item",{attrs:{label:"正在生成文件前缀",prop:"inProgressPrefix"}},[t("el-input",{attrs:{placeholder:"请输入"},model:{value:e.readerForm.inProgressPrefix,callback:function(r){e.$set(e.readerForm,"inProgressPrefix",r)},expression:"readerForm.inProgressPrefix"}})],1)],1)],1)},I=[],x={name:"HiveReader",data:function(){return{jdbcDsQuery:{current:1,size:200},rDsList:[],rTbList:[],rColumnList:[],loading:!1,active:1,customFields:"",customType:"",customValue:"",dataSource:"",readerForm:{datasourceId:void 0,hdfsPath:"",replication:"",format:"",blocksize:"",rolloverInterval:"",prefix:"",suffix:"",inProgressPrefix:""},rules:{path:[{required:!0,message:"this is required",trigger:"blur"}],defaultFS:[{required:!0,message:"this is required",trigger:"blur"}],fileType:[{required:!0,message:"this is required",trigger:"change"}],datasourceId:[{required:!0,message:"this is required",trigger:"blur"}],tableName:[{required:!0,message:"this is required",trigger:"blur"}]},fileTypes:[{value:"text",label:"text"},{value:"orc",label:"orc"},{value:"rc",label:"rc"},{value:"seq",label:"seq"},{value:"csv",label:"csv"}],skipHeaderTypes:[{value:"true",label:"读取跳过表头"},{value:"false",label:"读取包含表头"}]}},watch:{"readerForm.datasourceId":function(e,r){this.getTables("hiveReader")}},created:function(){this.getJdbcDs()},methods:{getJdbcDs:function(e){var r=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(e){var t=e.records;r.rDsList=t,r.loading=!1}))},getTables:function(e){var r=this;if("hiveReader"===e){var t={datasourceId:this.readerForm.datasourceId};b["h"](t).then((function(e){r.rTbList=e}))}},rDsChange:function(e){var r=this;this.readerForm.tableName="",this.readerForm.datasourceId=e,this.rDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),w.dataSourceId=e,this.$emit("selectDataSource",this.dataSource),this.getTables("reader")},getTableColumns:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,tableName:this.readerForm.tableName};b["b"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumnsByQuerySql:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,querySql:this.readerForm.querySql};b["d"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumns:function(e){"reader"===e&&this.getTableColumns()},rTbChange:function(e){this.readerForm.tableName=e,this.rColumnList=[],this.readerForm.columns=[],this.getColumns("reader")},rHandleCheckAllChange:function(e){this.readerForm.columns=e?this.rColumnList:[],this.readerForm.isIndeterminate=!1},rHandleCheckedChange:function(e){var r=e.length;this.readerForm.checkAll=r===this.rColumnList.length,this.readerForm.isIndeterminate=r>0&&r<this.rColumnList.length},getData:function(){return w.dataSourceId&&(this.readerForm.datasourceId=w.dataSourceId),this.readerForm}}},D=x,T=Object(S["a"])(D,y,I,!1,null,null,null),L=T.exports,N=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-form",{attrs:{"label-position":"left","label-width":"105px",model:e.readerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据源",prop:"datasourceId"}},[t("el-select",{attrs:{filterable:""},on:{change:e.rDsChange},model:{value:e.readerForm.datasourceId,callback:function(r){e.$set(e.readerForm,"datasourceId",r)},expression:"readerForm.datasourceId"}},e._l(e.rDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"表",prop:"tableName"}},[t("el-select",{attrs:{filterable:""},on:{change:e.rTbChange},model:{value:e.readerForm.tableName,callback:function(r){e.$set(e.readerForm,"tableName",r)},expression:"readerForm.tableName"}},e._l(e.rTbList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),t("el-form-item",{attrs:{label:"mode",prop:"mode"}},[t("el-select",{attrs:{placeholder:"读取hbase的模式"},model:{value:e.readerForm.mode,callback:function(r){e.$set(e.readerForm,"mode",r)},expression:"readerForm.mode"}},e._l(e.modeTypes,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"maxVersion"}},[t("el-input",{staticStyle:{width:"50%"},attrs:{placeholder:"多版本模式下读取的版本数,取值只能为－1或者大于1的数字"},model:{value:e.readerForm.maxVersion,callback:function(r){e.$set(e.readerForm,"maxVersion",r)},expression:"readerForm.maxVersion"}})],1),t("el-form-item",{attrs:{label:"range"}},[t("el-input",{staticStyle:{width:"50%"},attrs:{placeholder:"startRowkey指定开始rowkey"},model:{value:e.readerForm.range.startRowkey,callback:function(r){e.$set(e.readerForm.range,"startRowkey",r)},expression:"readerForm.range.startRowkey"}})],1),t("el-form-item",[t("el-input",{staticStyle:{width:"50%"},attrs:{placeholder:"endRowkey指定结束rowkey"},model:{value:e.readerForm.range.endRowkey,callback:function(r){e.$set(e.readerForm.range,"endRowkey",r)},expression:"readerForm.range.endRowkey"}})],1),t("el-form-item",[t("el-select",{attrs:{placeholder:"转换方式"},model:{value:e.readerForm.range.isBinaryRowkey,callback:function(r){e.$set(e.readerForm.range,"isBinaryRowkey",r)},expression:"readerForm.range.isBinaryRowkey"}},e._l(e.binaryRowkeyTypes,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"字段"}},[t("el-checkbox",{attrs:{indeterminate:e.readerForm.isIndeterminate},on:{change:e.rHandleCheckAllChange},model:{value:e.readerForm.checkAll,callback:function(r){e.$set(e.readerForm,"checkAll",r)},expression:"readerForm.checkAll"}},[e._v("全选")]),t("div",{staticStyle:{margin:"15px 0"}}),t("el-checkbox-group",{on:{change:e.rHandleCheckedChange},model:{value:e.readerForm.columns,callback:function(r){e.$set(e.readerForm,"columns",r)},expression:"readerForm.columns"}},e._l(e.rColumnList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1)],1)],1)},q=[],$={name:"HBaseReader",data:function(){return{jdbcDsQuery:{current:1,size:200},rDsList:[],rTbList:[],rColumnList:[],loading:!1,active:1,customFields:"",customType:"",customValue:"",dataSource:"",readerForm:{datasourceId:void 0,tableName:"",columns:[],checkAll:!1,isIndeterminate:!0,mode:"",maxVersion:"",range:{startRowkey:"",endRowkey:"",isBinaryRowkey:""}},modeTypes:[{value:"normal",label:"normal"},{value:"multiVersionFixedColumn",label:"multiVersionFixedColumn"}],binaryRowkeyTypes:[{value:"true",label:"调用Bytes.toBytesBinary(rowkey)"},{value:"false",label:"调用Bytes.toBytes(rowkey)"}],rules:{mode:[{required:!0,message:"this is required",trigger:"blur"}],datasourceId:[{required:!0,message:"this is required",trigger:"blur"}],tableName:[{required:!0,message:"this is required",trigger:"blur"}]}}},watch:{"readerForm.datasourceId":function(e,r){this.getTables("hbaseReader")}},created:function(){this.getJdbcDs()},methods:{getJdbcDs:function(){var e=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(r){var t=r.records;e.rDsList=t,e.loading=!1}))},getTables:function(e){var r=this;if("hbaseReader"===e){var t={datasourceId:this.readerForm.datasourceId};b["h"](t).then((function(e){r.rTbList=e}))}},rDsChange:function(e){var r=this;this.readerForm.tableName="",this.readerForm.datasourceId=e,this.rDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),w.dataSourceId=e,this.$emit("selectDataSource",this.dataSource),this.getTables("reader")},getTableColumns:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,tableName:this.readerForm.tableName};b["b"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumnsByQuerySql:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,querySql:this.readerForm.querySql};b["d"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumns:function(e){"reader"===e&&this.getTableColumns()},rTbChange:function(e){this.readerForm.tableName=e,this.rColumnList=[],this.readerForm.columns=[],this.getColumns("reader")},rHandleCheckAllChange:function(e){this.readerForm.columns=e?this.rColumnList:[],this.readerForm.isIndeterminate=!1},rHandleCheckedChange:function(e){var r=e.length;this.readerForm.checkAll=r===this.rColumnList.length,this.readerForm.isIndeterminate=r>0&&r<this.rColumnList.length},getData:function(){return w.dataSourceId&&(this.readerForm.datasourceId=w.dataSourceId),this.readerForm}}},_=$,R=Object(S["a"])(_,N,q,!1,null,null,null),A=R.exports,j=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-form",{attrs:{"label-position":"left","label-width":"105px",model:e.readerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据源",prop:"datasourceId"}},[t("el-select",{attrs:{filterable:""},on:{change:e.rDsChange},model:{value:e.readerForm.datasourceId,callback:function(r){e.$set(e.readerForm,"datasourceId",r)},expression:"readerForm.datasourceId"}},e._l(e.rDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"文档",prop:"tableName"}},[t("el-select",{attrs:{filterable:""},on:{change:e.rTbChange},model:{value:e.readerForm.tableName,callback:function(r){e.$set(e.readerForm,"tableName",r)},expression:"readerForm.tableName"}},e._l(e.rTbList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),t("el-form-item",{attrs:{label:"字段"}},[t("el-checkbox",{attrs:{indeterminate:e.readerForm.isIndeterminate},on:{change:e.rHandleCheckAllChange},model:{value:e.readerForm.checkAll,callback:function(r){e.$set(e.readerForm,"checkAll",r)},expression:"readerForm.checkAll"}},[e._v("全选")]),t("div",{staticStyle:{margin:"15px 0"}}),t("el-checkbox-group",{on:{change:e.rHandleCheckedChange},model:{value:e.readerForm.columns,callback:function(r){e.$set(e.readerForm,"columns",r)},expression:"readerForm.columns"}},e._l(e.rColumnList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1)],1)],1)},H=[],P={name:"MongoDBReader",data:function(){return{jdbcDsQuery:{current:1,size:200},rDsList:[],rTbList:[],rColumnList:[],loading:!1,active:1,customFields:"",customType:"",customValue:"",dataSource:"",readerForm:{datasourceId:void 0,tableName:"",columns:[],checkAll:!1,isIndeterminate:!0},rules:{mode:[{required:!0,message:"this is required",trigger:"blur"}],datasourceId:[{required:!0,message:"this is required",trigger:"blur"}],tableName:[{required:!0,message:"this is required",trigger:"blur"}]}}},watch:{"readerForm.datasourceId":function(e,r){this.getTables("mongodbReader")}},created:function(){this.getJdbcDs()},methods:{getJdbcDs:function(){var e=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(r){var t=r.records;e.rDsList=t,e.loading=!1}))},getTables:function(e){var r=this;if("mongodbReader"===e){var t={datasourceId:this.readerForm.datasourceId};b["h"](t).then((function(e){r.rTbList=e}))}},rDsChange:function(e){var r=this;this.readerForm.tableName="",this.readerForm.datasourceId=e,this.rDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),w.dataSourceId=e,this.$emit("selectDataSource",this.dataSource),this.getTables("reader")},getTableColumns:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,tableName:this.readerForm.tableName};b["b"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumnsByQuerySql:function(){var e=this,r={datasourceId:this.readerForm.datasourceId,querySql:this.readerForm.querySql};b["d"](r).then((function(r){e.rColumnList=r,e.readerForm.columns=r,e.readerForm.checkAll=!0,e.readerForm.isIndeterminate=!1}))},getColumns:function(e){"reader"===e&&this.getTableColumns()},rTbChange:function(e){this.readerForm.tableName=e,this.rColumnList=[],this.readerForm.columns=[],this.getColumns("reader")},rHandleCheckAllChange:function(e){this.readerForm.columns=e?this.rColumnList:[],this.readerForm.isIndeterminate=!1},rHandleCheckedChange:function(e){var r=e.length;this.readerForm.checkAll=r===this.rColumnList.length,this.readerForm.isIndeterminate=r>0&&r<this.rColumnList.length},getData:function(){return w.dataSourceId&&(this.readerForm.datasourceId=w.dataSourceId),this.readerForm}}},J=P,E=Object(S["a"])(J,j,H,!1,null,null,null),O=E.exports,M={name:"Reader",components:{RDBMSReader:k,HiveReader:L,HBaseReader:A,MongoDBReader:O},data:function(){return{dataSource:""}},methods:{getData:function(){return"hive"===this.dataSource?this.$refs.hivereader.getData():"hbase"===this.dataSource?this.$refs.hbasereader.getData():"mongodb"===this.dataSource?this.$refs.mongodbreader.getData():this.$refs.rdbmsreader.getData()},showDataSource:function(e){this.dataSource=e,this.getData()}}},B=M,W=Object(S["a"])(B,u,m,!1,null,null,null),Q=W.exports,V=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("RDBMSWriter",{directives:[{name:"show",rawName:"v-show",value:"hive"!==e.dataSource&&"hbase"!==e.dataSource&&"mongodb"!==e.dataSource,expression:"dataSource!=='hive' && dataSource!=='hbase' && dataSource!=='mongodb'"}],ref:"rdbmswriter",on:{selectDataSource:e.showDataSource}}),t("HiveWriter",{directives:[{name:"show",rawName:"v-show",value:"hive"===e.dataSource,expression:"dataSource==='hive'"}],ref:"hivewriter",on:{selectDataSource:e.showDataSource}}),t("HBaseWriter",{directives:[{name:"show",rawName:"v-show",value:"hbase"===e.dataSource,expression:"dataSource==='hbase'"}],ref:"hbasewriter",on:{selectDataSource:e.showDataSource}}),t("MongoDBWriter",{directives:[{name:"show",rawName:"v-show",value:"mongodb"===e.dataSource,expression:"dataSource==='mongodb'"}],ref:"mongodbwriter",on:{selectDataSource:e.showDataSource}})],1)},z=[],U=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",[t("el-form",{attrs:{"label-position":"right","label-width":"150px",model:e.writerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据库源：",prop:"datasourceId"}},[t("el-select",{staticStyle:{width:"300px"},attrs:{filterable:""},on:{change:e.wDsChange},model:{value:e.writerForm.datasourceId,callback:function(r){e.$set(e.writerForm,"datasourceId",r)},expression:"writerForm.datasourceId"}},e._l(e.wDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"postgresql"===e.dataSource||"oracle"===e.dataSource||"sqlserver"===e.dataSource||"hana"===e.dataSource,expression:"dataSource==='postgresql' || dataSource==='oracle' ||dataSource==='sqlserver' || dataSource==='hana'"}],attrs:{label:"Schema：",prop:"tableSchema"}},[t("el-select",{staticStyle:{width:"300px"},attrs:{filterable:""},on:{change:e.schemaChange},model:{value:e.writerForm.tableSchema,callback:function(r){e.$set(e.writerForm,"tableSchema",r)},expression:"writerForm.tableSchema"}},e._l(e.schemaList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),t("el-form-item",{attrs:{label:"数据库表名：",prop:"tableName"}},[t("el-select",{staticStyle:{width:"300px"},attrs:{"allow-create":"","default-first-option":"",filterable:"",disabled:e.writerForm.ifCreateTable},on:{change:e.wTbChange},model:{value:e.fromTableName,callback:function(r){e.fromTableName=r},expression:"fromTableName"}},e._l(e.wTbList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1),t("el-input",{directives:[{name:"show",rawName:"v-show",value:e.writerForm.ifCreateTable,expression:"writerForm.ifCreateTable"}],staticStyle:{width:"200px"},attrs:{placeholder:e.readerForm.tableName},model:{value:e.writerForm.tableName,callback:function(r){e.$set(e.writerForm,"tableName",r)},expression:"writerForm.tableName"}})],1),t("div",{staticStyle:{margin:"5px 0"}}),t("el-form-item",{attrs:{label:"字段："}},[t("el-checkbox",{attrs:{indeterminate:e.writerForm.isIndeterminate},on:{change:e.wHandleCheckAllChange},model:{value:e.writerForm.checkAll,callback:function(r){e.$set(e.writerForm,"checkAll",r)},expression:"writerForm.checkAll"}},[e._v("全选")]),t("div",{staticStyle:{margin:"15px 0"}}),t("el-checkbox-group",{on:{change:e.wHandleCheckedChange},model:{value:e.writerForm.columns,callback:function(r){e.$set(e.writerForm,"columns",r)},expression:"writerForm.columns"}},e._l(e.fromColumnList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1),t("el-form-item",{attrs:{label:"前置sql语句："}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"写入数据到目的表前，会先执行这里的标准语句。",type:"textarea"},model:{value:e.writerForm.preSql,callback:function(r){e.$set(e.writerForm,"preSql",r)},expression:"writerForm.preSql"}})],1),t("el-form-item",{attrs:{label:"后置sql语句："}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"写入数据到目的表后，会执行这里的标准语句。",type:"textarea"},model:{value:e.writerForm.postSql,callback:function(r){e.$set(e.writerForm,"postSql",r)},expression:"writerForm.postSql"}})],1)],1)],1)},K=[],Y=new g["default"]({data:function(){return{dataSourceId:""}}}),G={name:"RDBMSWriter",data:function(){return{jdbcDsQuery:{current:1,size:200,ascs:"datasource_name",datahandle:1},wDsList:[],schemaList:[],fromTableName:"",fromColumnList:[],wTbList:[],dataSource:"",createTableName:"",writerForm:{datasourceId:void 0,tableName:"",columns:[],checkAll:!1,isIndeterminate:!0,preSql:"",postSql:"",ifCreateTable:!1,tableSchema:""},readerForm:this.getReaderData(),rules:{datasourceId:[{required:!0,message:"this is required",trigger:"change"}],tableName:[{required:!0,message:"this is required",trigger:"change"}],tableSchema:[{required:!0,message:"this is required",trigger:"change"}]}}},watch:{"writerForm.datasourceId":function(e,r){"postgresql"===this.dataSource||"oracle"===this.dataSource||"sqlserver"===this.dataSource||"hana"===this.dataSource?this.getSchema():this.getTables("rdbmsWriter")}},created:function(){var e=this;this.getJdbcDs(),Object(p["e"])({datahandle:1}).then((function(r){var t=r.content.data;e.wDsList=t}))},methods:{getJdbcDs:function(){var e=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(r){var t=r.records;e.wDsList=t,e.loading=!1}))},getTables:function(e){var r=this;if("rdbmsWriter"===e){var t={};t="postgresql"===this.dataSource||"oracle"===this.dataSource||"sqlserver"===this.dataSource||"hana"===this.dataSource?{datasourceId:this.writerForm.datasourceId,tableSchema:this.writerForm.tableSchema}:{datasourceId:this.writerForm.datasourceId},b["h"](t).then((function(e){r.wTbList=e}))}},getSchema:function(){var e=this,r={datasourceId:this.writerForm.datasourceId};b["g"](r).then((function(r){e.schemaList=r}))},schemaChange:function(e){this.writerForm.tableSchema=e,this.getTables("rdbmsWriter")},wDsChange:function(e){var r=this;this.writerForm.tableName="",this.writerForm.datasourceId=e,this.wDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),Y.dataSourceId=e,this.$emit("selectDataSource",this.dataSource)},getColumns:function(){var e=this,r={datasourceId:this.writerForm.datasourceId,tableName:this.writerForm.tableName};b["b"](r).then((function(r){e.fromColumnList=r,e.writerForm.columns=r,e.writerForm.checkAll=!0,e.writerForm.isIndeterminate=!1}))},wTbChange:function(e){this.writerForm.tableName=e,this.fromColumnList=[],this.writerForm.columns=[],this.getColumns("writer")},wHandleCheckAllChange:function(e){this.writerForm.columns=e?this.fromColumnList:[],this.writerForm.isIndeterminate=!1},wHandleCheckedChange:function(e){var r=e.length;this.writerForm.checkAll=r===this.fromColumnList.length,this.writerForm.isIndeterminate=r>0&&r<this.fromColumnList.length},getData:function(){return Y.dataSourceId&&(this.writerForm.datasourceId=Y.dataSourceId),this.writerForm},getReaderData:function(){return this.$parent.getReaderData()},getTableName:function(){return this.fromTableName},createTable:function(){var e=this,r={datasourceId:this.writerForm.datasourceId,tableName:this.createTableName};b["a"](r).then((function(r){e.$notify({title:"Success",message:"Create Table Successfully",type:"success",duration:2e3})})).catch((function(){return console.log("promise catch err")}))}}},X=G,Z=Object(S["a"])(X,U,K,!1,null,null,null),ee=Z.exports,re=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",[t("el-form",{attrs:{"label-position":"left","label-width":"105px",model:e.writerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据源",prop:"datasourceId"}},[t("el-select",{attrs:{filterable:""},on:{change:e.wDsChange},model:{value:e.writerForm.datasourceId,callback:function(r){e.$set(e.writerForm,"datasourceId",r)},expression:"writerForm.datasourceId"}},e._l(e.wDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-form-item",{attrs:{label:"HDFS路径",prop:"hdfsPath"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"hdfs://IP:PORT/d1/d2"},model:{value:e.writerForm.hdfsPath,callback:function(r){e.$set(e.writerForm,"hdfsPath",r)},expression:"writerForm.hdfsPath"}})],1),t("el-form-item",{attrs:{label:"副本数",prop:"replication"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"1"},model:{value:e.writerForm.replication,callback:function(r){e.$set(e.writerForm,"replication",r)},expression:"writerForm.replication"}})],1),t("el-form-item",{attrs:{label:"文件分区名称",prop:"format"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"yyyy-MM-dd--HH"},model:{value:e.writerForm.format,callback:function(r){e.$set(e.writerForm,"format",r)},expression:"writerForm.format"}})],1),t("el-form-item",{attrs:{label:"生成文件大小",prop:"blocksize"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"104857600"},model:{value:e.writerForm.blocksize,callback:function(r){e.$set(e.writerForm,"blocksize",r)},expression:"writerForm.blocksize"}})],1),t("el-form-item",{attrs:{label:"生成新文件间隔",prop:"rolloverInterval"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"120000"},model:{value:e.writerForm.rolloverInterval,callback:function(r){e.$set(e.writerForm,"rolloverInterval",r)},expression:"writerForm.rolloverInterval"}})],1),t("el-form-item",{attrs:{label:"文件名前缀",prop:"prefix"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"order_sink"},model:{value:e.writerForm.prefix,callback:function(r){e.$set(e.writerForm,"prefix",r)},expression:"writerForm.prefix"}})],1),t("el-form-item",{attrs:{label:"文件名后缀",prop:"suffix"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:""},model:{value:e.writerForm.suffix,callback:function(r){e.$set(e.writerForm,"suffix",r)},expression:"writerForm.suffix"}})],1),t("el-form-item",{attrs:{label:"正在生成文件前缀",prop:"inProgressPrefix"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:".in"},model:{value:e.writerForm.inProgressPrefix,callback:function(r){e.$set(e.writerForm,"inProgressPrefix",r)},expression:"writerForm.inProgressPrefix"}})],1)],1)],1)},te=[],ae={name:"HiveWriter",data:function(){return{jdbcDsQuery:{current:1,size:200},wDsList:[],fromTableName:"",fromColumnList:[],wTbList:[],dataSource:"",writerForm:{datasourceId:void 0,tableName:"",columns:[],checkAll:!1,isIndeterminate:!0,ifCreateTable:!1,defaultFS:"",fileType:"",path:"",fileName:"",writeMode:"",fieldDelimiter:""},rules:{hdfsPath:[{required:!0,message:"this is required",trigger:"blur"}],replication:[{required:!0,message:"this is required",trigger:"blur"}],format:[{required:!0,message:"this is required",trigger:"blur"}],blocksize:[{required:!0,message:"this is required",trigger:"change"}],rolloverInterval:[{required:!0,message:"this is required",trigger:"change"}],prefix:[{required:!0,message:"this is required",trigger:"blur"}],suffix:[{required:!0,message:"this is required",trigger:"blur"}],inProgressPrefix:[{required:!0,message:"this is required",trigger:"blur"}]},readerForm:this.getReaderData(),fileTypes:[{value:"text",label:"text"},{value:"orc",label:"orc"}],writeModes:[{value:"append",label:"append 写入前不做任何处理"},{value:"nonConflict",label:"nonConflict 目录下有fileName前缀的文件，直接报错"}]}},watch:{"writerForm.datasourceId":function(e,r){this.getTables("hiveWriter")}},created:function(){var e=this;this.getJdbcDs(),Object(p["e"])().then((function(r){var t=r.content.data;e.wDsList=t}))},methods:{getJdbcDs:function(){var e=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(r){var t=r.records;e.wDsList=t,e.loading=!1}))},getTables:function(e){var r=this;if("hiveWriter"===e){var t={datasourceId:this.writerForm.datasourceId};b["h"](t).then((function(e){r.wTbList=e}))}},wDsChange:function(e){var r=this;this.writerForm.tableName="",this.writerForm.datasourceId=e,this.wDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),Y.dataSourceId=e,this.$emit("selectDataSource",this.dataSource)},getColumns:function(){var e=this,r={datasourceId:this.writerForm.datasourceId,tableName:this.writerForm.tableName};b["b"](r).then((function(r){e.fromColumnList=r,e.writerForm.columns=r,e.writerForm.checkAll=!0,e.writerForm.isIndeterminate=!1}))},wTbChange:function(e){this.writerForm.tableName=e,this.fromColumnList=[],this.writerForm.columns=[]},wHandleCheckAllChange:function(e){this.writerForm.columns=e?this.fromColumnList:[],this.writerForm.isIndeterminate=!1},wHandleCheckedChange:function(e){var r=e.length;this.writerForm.checkAll=r===this.fromColumnList.length,this.writerForm.isIndeterminate=r>0&&r<this.fromColumnList.length},createTableCheckedChange:function(e){this.writerForm.tableName=e?this.readerForm.tableName:"",this.fromColumnList=this.readerForm.columns,this.writerForm.columns=this.readerForm.columns,this.writerForm.checkAll=!0,this.writerForm.isIndeterminate=!1},getData:function(){return Y.dataSourceId&&(this.writerForm.datasourceId=Y.dataSourceId),this.writerForm},getReaderData:function(){return this.$parent.getReaderData()},getTableName:function(){return this.fromTableName},createTable:function(){var e=this,r=this.fromTableName,t=this.writerForm.datasourceId,a=this.fromColumnList,o={};o["datasourceId"]=t,o["tableName"]=r,o["columns"]=a,console.info(o),b["a"](o).then((function(r){e.$notify({title:"Success",message:"Create Table Successfully",type:"success",duration:2e3})})).catch((function(){return console.log("promise catch err")}))}}},oe=ae,ie=Object(S["a"])(oe,re,te,!1,null,null,null),se=ie.exports,le=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",[t("el-form",{attrs:{"label-position":"left","label-width":"115px",model:e.writerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据源",prop:"datasourceId"}},[t("el-select",{attrs:{filterable:""},on:{change:e.wDsChange},model:{value:e.writerForm.datasourceId,callback:function(r){e.$set(e.writerForm,"datasourceId",r)},expression:"writerForm.datasourceId"}},e._l(e.wDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"表",prop:"fromTableName"}},[t("el-select",{attrs:{disabled:e.writerForm.ifCreateTable,filterable:""},on:{change:e.wTbChange},model:{value:e.fromTableName,callback:function(r){e.fromTableName=r},expression:"fromTableName"}},e._l(e.wTbList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1)],1),t("el-form-item",{attrs:{label:"rowkeyColumn",prop:"rowkeyColumn"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{autosize:{minRows:5,maxRows:20},type:"textarea"},model:{value:e.writerForm.rowkeyColumn,callback:function(r){e.$set(e.writerForm,"rowkeyColumn",r)},expression:"writerForm.rowkeyColumn"}})],1),t("el-form-item",{attrs:{label:"versionColumn"}},[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"index指定对应reader端column的索引"},model:{value:e.writerForm.versionColumn.index,callback:function(r){e.$set(e.writerForm.versionColumn,"index",r)},expression:"writerForm.versionColumn.index"}})],1),t("el-form-item",[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"value指定时间的值,long值"},model:{value:e.writerForm.versionColumn.value,callback:function(r){e.$set(e.writerForm.versionColumn,"value",r)},expression:"writerForm.versionColumn.value"}})],1),t("el-form-item",{attrs:{label:"nullMode"}},[t("el-select",{attrs:{placeholder:"null值转换方式"},model:{value:e.writerForm.nullMode,callback:function(r){e.$set(e.writerForm,"nullMode",r)},expression:"writerForm.nullMode"}},e._l(e.nullModeTypes,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"字段"}},[t("el-checkbox",{attrs:{indeterminate:e.writerForm.isIndeterminate},on:{change:e.wHandleCheckAllChange},model:{value:e.writerForm.checkAll,callback:function(r){e.$set(e.writerForm,"checkAll",r)},expression:"writerForm.checkAll"}},[e._v("全选")]),t("div",{staticStyle:{margin:"15px 0"}}),t("el-checkbox-group",{on:{change:e.wHandleCheckedChange},model:{value:e.writerForm.columns,callback:function(r){e.$set(e.writerForm,"columns",r)},expression:"writerForm.columns"}},e._l(e.fromColumnList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1)],1)],1)},ne=[],ce=t("53ca"),de=(t("d9e2"),{name:"HBaseWriter",data:function(){var e=function(e,r,t){if(r||t(new Error("不能为空")),"string"===typeof r)try{var a=JSON.parse(r);"object"===Object(ce["a"])(a)&&a||t(new Error("JSON格式错误")),a instanceof Array||t(new Error("JSON必须为数组"))}catch(o){t(new Error("JSON格式错误"))}};return{jdbcDsQuery:{current:1,size:200},wDsList:[],fromTableName:"",fromColumnList:[],wTbList:[],dataSource:"",writerForm:{datasourceId:void 0,tableName:"",columns:[],checkAll:!1,isIndeterminate:!0,ifCreateTable:!1,mode:"normal",rowkeyColumn:'[{\n\t"index": 0,\n\t"type": "string"\n}]',versionColumn:{index:"",value:""},nullMode:""},nullModeTypes:[{value:"skip",label:"不向hbase写这列"},{value:"empty",label:"写入new byte [0]"}],rules:{mode:[{required:!0,message:"this is required",trigger:"blur"}],datasourceId:[{required:!0,message:"this is required",trigger:"blur"}],fromTableName:[{required:!0,message:"this is required",trigger:"blur"}],rowkeyColumn:[{required:!0,trigger:"blur",validator:e}]},readerForm:this.getReaderData()}},watch:{"writerForm.datasourceId":function(e,r){this.getTables("hbaseWriter")}},created:function(){this.getJdbcDs()},methods:{getJdbcDs:function(e){var r=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(e){var t=e.records;r.wDsList=t,r.loading=!1}))},getTables:function(e){var r=this;if("hbaseWriter"===e){var t={datasourceId:this.writerForm.datasourceId};b["h"](t).then((function(e){r.wTbList=e}))}},wDsChange:function(e){var r=this;this.writerForm.tableName="",this.writerForm.datasourceId=e,this.wDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),Y.dataSourceId=e,this.$emit("selectDataSource",this.dataSource),this.getTables()},getColumns:function(){var e=this,r={datasourceId:this.writerForm.datasourceId,tableName:this.writerForm.tableName};b["b"](r).then((function(r){e.fromColumnList=r,e.writerForm.columns=r,e.writerForm.checkAll=!0,e.writerForm.isIndeterminate=!1}))},wTbChange:function(e){this.writerForm.tableName=e,this.fromColumnList=[],this.writerForm.columns=[],this.getColumns("writer")},wHandleCheckAllChange:function(e){this.writerForm.columns=e?this.fromColumnList:[],this.writerForm.isIndeterminate=!1},wHandleCheckedChange:function(e){var r=e.length;this.writerForm.checkAll=r===this.fromColumnList.length,this.writerForm.isIndeterminate=r>0&&r<this.fromColumnList.length},createTableCheckedChange:function(e){this.writerForm.tableName=e?this.readerForm.tableName:"",this.fromColumnList=this.readerForm.columns,this.writerForm.columns=this.readerForm.columns,this.writerForm.checkAll=!0,this.writerForm.isIndeterminate=!1},getData:function(){return Y.dataSourceId&&(this.writerForm.datasourceId=Y.dataSourceId),this.writerForm},getReaderData:function(){return this.$parent.getReaderData()},getTableName:function(){return this.fromTableName}}}),ue=de,me=Object(S["a"])(ue,le,ne,!1,null,null,null),he=me.exports,fe=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",[t("el-form",{attrs:{"label-position":"left","label-width":"105px",model:e.writerForm,rules:e.rules}},[t("el-form-item",{attrs:{label:"数据源",prop:"datasourceId"}},[t("el-select",{attrs:{filterable:""},on:{change:e.wDsChange},model:{value:e.writerForm.datasourceId,callback:function(r){e.$set(e.writerForm,"datasourceId",r)},expression:"writerForm.datasourceId"}},e._l(e.wDsList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1),t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("el-form-item",{attrs:{label:"表",prop:"fromTableName"}},[t("el-select",{attrs:{disabled:e.writerForm.ifCreateTable,filterable:""},on:{change:e.wTbChange},model:{value:e.fromTableName,callback:function(r){e.fromTableName=r},expression:"fromTableName"}},e._l(e.wTbList,(function(e){return t("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)],1)],1),t("el-form-item",{attrs:{label:"更新信息"}},[t("el-select",{attrs:{placeholder:"是否更新"},model:{value:e.writerForm.upsertInfo.isUpsert,callback:function(r){e.$set(e.writerForm.upsertInfo,"isUpsert",r)},expression:"writerForm.upsertInfo.isUpsert"}},e._l(e.upsertType,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",[t("el-input",{staticStyle:{width:"42%"},attrs:{placeholder:"更新的业务主键"},model:{value:e.writerForm.upsertInfo.upsertKey,callback:function(r){e.$set(e.writerForm.upsertInfo,"upsertKey",r)},expression:"writerForm.upsertInfo.upsertKey"}})],1),t("el-form-item",{attrs:{label:"字段"}},[t("el-checkbox",{attrs:{indeterminate:e.writerForm.isIndeterminate},on:{change:e.wHandleCheckAllChange},model:{value:e.writerForm.checkAll,callback:function(r){e.$set(e.writerForm,"checkAll",r)},expression:"writerForm.checkAll"}},[e._v("全选")]),t("div",{staticStyle:{margin:"15px 0"}}),t("el-checkbox-group",{on:{change:e.wHandleCheckedChange},model:{value:e.writerForm.columns,callback:function(r){e.$set(e.writerForm,"columns",r)},expression:"writerForm.columns"}},e._l(e.fromColumnList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1)],1)],1)},be=[],pe={name:"MongoDBWriter",data:function(){return{jdbcDsQuery:{current:1,size:200},wDsList:[],fromTableName:"",fromColumnList:[],wTbList:[],dataSource:"",writerForm:{datasourceId:void 0,columns:[],tableName:"",checkAll:!1,isIndeterminate:!0,ifCreateTable:!1,upsertInfo:{isUpsert:"",upsertKey:""}},upsertType:[{value:!0,label:"针对相同的upsertKey做更新"},{value:!1,label:"不做更新"}],rules:{mode:[{required:!0,message:"this is required",trigger:"blur"}],datasourceId:[{required:!0,message:"this is required",trigger:"blur"}],fromTableName:[{required:!0,message:"this is required",trigger:"blur"}]},readerForm:this.getReaderData()}},watch:{"writerForm.datasourceId":function(e,r){this.getTables("mongodbWriter")}},created:function(){this.getJdbcDs()},methods:{getJdbcDs:function(e){var r=this;this.loading=!0,Object(p["f"])(this.jdbcDsQuery).then((function(e){var t=e.records;r.wDsList=t,r.loading=!1}))},getTables:function(e){var r=this;if("mongodbWriter"===e){var t={datasourceId:this.writerForm.datasourceId};b["h"](t).then((function(e){r.wTbList=e}))}},wDsChange:function(e){var r=this;this.writerForm.tableName="",this.writerForm.datasourceId=e,this.wDsList.find((function(t){t.id===e&&(r.dataSource=t.datasource)})),Y.dataSourceId=e,this.$emit("selectDataSource",this.dataSource),this.getTables()},getColumns:function(){var e=this,r={datasourceId:this.writerForm.datasourceId,tableName:this.writerForm.tableName};b["b"](r).then((function(r){e.fromColumnList=r,e.writerForm.columns=r,e.writerForm.checkAll=!0,e.writerForm.isIndeterminate=!1}))},wTbChange:function(e){this.writerForm.tableName=e,this.fromColumnList=[],this.writerForm.columns=[],this.getColumns("writer")},wHandleCheckAllChange:function(e){this.writerForm.columns=e?this.fromColumnList:[],this.writerForm.isIndeterminate=!1},wHandleCheckedChange:function(e){var r=e.length;this.writerForm.checkAll=r===this.fromColumnList.length,this.writerForm.isIndeterminate=r>0&&r<this.fromColumnList.length},createTableCheckedChange:function(e){this.writerForm.tableName=e?this.readerForm.tableName:"",this.fromColumnList=this.readerForm.columns,this.writerForm.columns=this.readerForm.columns,this.writerForm.checkAll=!0,this.writerForm.isIndeterminate=!1},getData:function(){return Y.dataSourceId&&(this.writerForm.datasourceId=Y.dataSourceId),this.writerForm},getReaderData:function(){return this.$parent.getReaderData()},getTableName:function(){return this.fromTableName}}},ge=pe,we=Object(S["a"])(ge,fe,be,!1,null,null,null),ve=we.exports,Fe={name:"Writer",components:{RDBMSWriter:ee,HiveWriter:se,HBaseWriter:he,MongoDBWriter:ve},data:function(){return{dataSource:""}},methods:{getData:function(){return"hive"===this.dataSource?this.$refs.hivewriter.getData():"hbase"===this.dataSource?this.$refs.hbasewriter.getData():"mongodb"===this.dataSource?this.$refs.mongodbwriter.getData():this.$refs.rdbmswriter.getData()},getTableName:function(){return"hive"===this.dataSource?this.$refs.hivewriter.getTableName():"hbase"===this.dataSource?this.$refs.hbasewriter.getData():"mongodb"===this.dataSource?this.$refs.mongodbwriter.getData():this.$refs.rdbmswriter.getTableName()},getReaderData:function(){return this.$parent.getReaderData()},showDataSource:function(e){this.dataSource=e,this.getData()},sendTableNameAndColumns:function(e,r){this.$refs.hivewriter.fromTableName=e,this.$refs.hivewriter.fromColumnList=r}}},Se=Fe,Ce=Object(S["a"])(Se,V,z,!1,null,null,null),ke=Ce.exports,ye=t("f71e"),Ie=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("FieldMapper",{ref:"mapper"})],1)},xe=[],De=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-form",{attrs:{"label-position":"left","label-width":"80px",model:e.readerForm}},[t("el-form-item",{attrs:{label:"源端字段"}},[t("el-checkbox",{attrs:{indeterminate:e.readerForm.isIndeterminate},on:{change:e.lHandleCheckAllChange},model:{value:e.readerForm.lcheckAll,callback:function(r){e.$set(e.readerForm,"lcheckAll",r)},expression:"readerForm.lcheckAll"}},[e._v("全选")]),t("div",{staticStyle:{margin:"15px 0"}}),t("el-checkbox-group",{on:{change:e.lHandleCheckedChange},model:{value:e.readerForm.lcolumns,callback:function(r){e.$set(e.readerForm,"lcolumns",r)},expression:"readerForm.lcolumns"}},e._l(e.fromColumnsList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1),t("el-form-item",{attrs:{label:"目标字段"}},[t("el-checkbox",{attrs:{indeterminate:e.readerForm.isIndeterminate},on:{change:e.rHandleCheckAllChange},model:{value:e.readerForm.rcheckAll,callback:function(r){e.$set(e.readerForm,"rcheckAll",r)},expression:"readerForm.rcheckAll"}},[e._v("全选")]),t("div",{staticStyle:{margin:"20px 0"}}),t("el-checkbox-group",{on:{change:e.rHandleCheckedChange},model:{value:e.readerForm.rcolumns,callback:function(r){e.$set(e.readerForm,"rcolumns",r)},expression:"readerForm.rcolumns"}},e._l(e.toColumnsList,(function(r){return t("el-checkbox",{key:r,attrs:{label:r}},[e._v(e._s(r))])})),1)],1)],1)],1)},Te=[],Le={name:"FieldMapper",data:function(){return{mapperJson:{},fromColumnsList:[],toColumnsList:[],readerForm:{lcolumns:[],rcolumns:[],lcheckAll:!1,rcheckAll:!1,isIndeterminate:!0}}},mounted:function(){},methods:{lHandleCheckAllChange:function(e){this.readerForm.lcolumns=e?this.fromColumnsList:[],this.readerForm.isIndeterminate=!1},rHandleCheckAllChange:function(e){this.readerForm.rcolumns=e?this.toColumnsList:[],this.readerForm.isIndeterminate=!1},lHandleCheckedChange:function(e){var r=e.length;this.readerForm.checkAll=r===this.fromColumnsList.length,this.readerForm.isIndeterminate=r>0&&r<this.fromColumnsList.length},rHandleCheckedChange:function(e){var r=e.length;this.readerForm.checkAll=r===this.toColumnsList.length,this.readerForm.isIndeterminate=r>0&&r<this.toColumnsList.length},getLColumns:function(){return this.readerForm.lcolumns},getRColumns:function(){return this.readerForm.rcolumns}}},Ne=Le,qe=Object(S["a"])(Ne,De,Te,!1,null,null,null),$e=qe.exports,_e={name:"Mapper",components:{FieldMapper:$e},methods:{sendColumns:function(e,r){this.$refs.mapper.fromColumnsList=e,this.$refs.mapper.toColumnsList=r},getLColumns:function(){return this.$refs.mapper.getLColumns()},getRColumns:function(){return this.$refs.mapper.getRColumns()}}},Re=_e,Ae=Object(S["a"])(Re,Ie,xe,!1,null,null,null),je=Ae.exports,He={name:"JsonBuild",components:{Reader:Q,Writer:ke,Pagination:c["a"],JsonEditor:d["a"],Mapper:je},data:function(){return{configJson:"",active:1,jobTemplate:"",jobTemplateSelectDrawer:!1,list:null,currentRow:null,listLoading:!0,total:0,listQuery:{current:1,size:10,projectIds:"",jobDesc:"",glueType:""},blockStrategies:[{value:"SERIAL_EXECUTION",label:"单机串行"},{value:"DISCARD_LATER",label:"丢弃后续调度"},{value:"COVER_EARLY",label:"覆盖之前调度"}],routeStrategies:[{value:"FIRST",label:"第一个"},{value:"LAST",label:"最后一个"},{value:"ROUND",label:"轮询"},{value:"RANDOM",label:"随机"},{value:"CONSISTENT_HASH",label:"一致性HASH"},{value:"LEAST_FREQUENTLY_USED",label:"最不经常使用"},{value:"LEAST_RECENTLY_USED",label:"最近最久未使用"},{value:"FAILOVER",label:"故障转移"},{value:"BUSYOVER",label:"忙碌转移"}],triggerNextTimes:"",registerNode:[],jobJson:"",temp:{id:void 0,jobGroup:"",jobCron:"",jobDesc:"",executorRouteStrategy:"",executorBlockStrategy:"",childJobId:"",executorFailRetryCount:"",alarmEmail:"",executorTimeout:"",userId:0,jobConfigId:"",executorHandler:"executorJobHandler",glueType:"datax",jobJson:"",executorParam:"",replaceParam:"",jvmParam:"",incStartTime:"",kStype:""}}},created:function(){},methods:{next:function(){var e=this,r=this.$refs.reader.getData().columns,t=this.$refs.writer.getData().columns;1===this.active?this.active++:(2===this.active&&this.$refs.mapper.sendColumns(r,t),4===this.active?(this.temp.jobJson=this.configJson,n["d"](this.temp).then((function(){e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3}),e.$router.push("/datax-database-integrate/job-info")}))):this.active++)},last:function(){this.active>1&&this.active--},buildJson:function(e){var r=this,t=this.$refs.reader.getData(),a=this.$refs.writer.getData(),o=this.$refs.mapper.getLColumns(),i=this.$refs.mapper.getRColumns(),l={readerPath:t.path,readerDefaultFS:t.defaultFS,readerFileType:t.fileType,readerFieldDelimiter:t.fieldDelimiter,readerSkipHeader:t.skipHeader},n={writerDefaultFS:a.defaultFS,writerFileType:a.fileType,writerPath:a.path,writerFileName:a.fileName,writeMode:a.writeMode,writeFieldDelimiter:a.fieldDelimiter},c={readerMode:t.mode,readerMaxVersion:t.maxVersion,readerRange:t.range},d={writerMode:a.mode,writerRowkeyColumn:a.rowkeyColumn,writerVersionColumn:a.versionColumn,writeNullMode:a.nullMode},u={},m={upsertInfo:a.upsertInfo},h={readerSplitPk:t.splitPk,whereParams:t.where,querySql:t.querySql},f={preSql:a.preSql,postSql:a.postSql},b={readerDatasourceId:t.datasourceId,readerTables:[t.tableName],readerColumns:o,writerDatasourceId:a.datasourceId,writerTables:[a.tableName],writerColumns:i,hiveReader:l,hiveWriter:n,rdbmsReader:h,rdbmsWriter:f,hbaseReader:c,hbaseWriter:d,mongoDBReader:u,mongoDBWriter:m,type:e};s(b).then((function(e){r.configJson=JSON.parse(e)}))},handleCopy:function(e,r){Object(ye["a"])(this.configJson,r),this.$message({message:"copy success",type:"success"})},handleJobTemplateSelectDrawer:function(){this.jobTemplateSelectDrawer=!this.jobTemplateSelectDrawer,this.jobTemplateSelectDrawer&&(this.fetchData(),this.getExecutor())},getReaderData:function(){return this.$refs.reader.getData()},getExecutor:function(){var e=this;l["b"]().then((function(r){var t=r.content;e.executorList=t}))},fetchData:function(){var e=this;this.listLoading=!0,l["c"](this.listQuery).then((function(r){var t=r.content;e.total=t.recordsTotal,e.list=t.data,e.listLoading=!1}))},handleCurrentChange:function(e){var r=this;this.temp=Object.assign({},e),this.temp.id=void 0,this.temp.jobDesc=this.getReaderData().tableName,this.$refs.jobTemplateSelectDrawer.closeDrawer(),this.jobTemplate=e.id+"("+e.jobDesc+")";var t=this.$refs.reader.getData(),a=this.$refs.writer.getData(),o=this.$refs.mapper.getLColumns(),i=this.$refs.mapper.getRColumns(),l={readerPath:t.path,readerDefaultFS:t.defaultFS,readerFileType:t.fileType,readerFieldDelimiter:t.fieldDelimiter,readerSkipHeader:t.skipHeader},n={hdfsPath:a.hdfsPath,replication:a.replication,format:a.format,blocksize:a.blocksize,rolloverInterval:a.rolloverInterval,prefix:a.prefix,suffix:a.suffix,inProgressPrefix:a.inProgressPrefix},c={readerMode:t.mode,readerMaxVersion:t.maxVersion,readerRange:t.range},d={writerMode:a.mode,writerRowkeyColumn:a.rowkeyColumn,writerVersionColumn:a.versionColumn,writeNullMode:a.nullMode},u={},m={upsertInfo:a.upsertInfo},h={readerSplitPk:t.splitPk,whereParams:t.where,querySql:t.querySql},f={preSql:a.preSql,postSql:a.postSql},b={readerDatasourceId:t.datasourceId,readerTables:[t.tableName],readerColumns:o,writerDatasourceId:a.datasourceId,writerTables:[a.tableName],writerColumns:i,hiveReader:l,hiveWriter:n,rdbmsReader:h,rdbmsWriter:f,hbaseReader:c,hbaseWriter:d,mongoDBReader:u,mongoDBWriter:m,type:e.glueType,cores:e.cores};s(b).then((function(e){r.configJson=JSON.parse(e)}))}}},Pe=He,Je=Object(S["a"])(Pe,a,o,!1,null,"28eef2dd",null);r["default"]=Je.exports},"8e34":function(e,r,t){"use strict";t("16d2")},fa7e:function(e,r,t){"use strict";var a=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"json-editor"},[t("textarea",{ref:"textarea"})])},o=[],i=(t("e9c4"),t("56b3")),s=t.n(i);t("0dd0"),t("a7be"),t("acdf"),t("f9d4"),t("8822"),t("d2de");t("ae67");var l={name:"JsonEditor",props:["value"],data:function(){return{jsonEditor:!1}},watch:{value:function(e){var r=this.jsonEditor.getValue();e!==r&&this.jsonEditor.setValue(JSON.stringify(this.value,null,2))}},mounted:function(){var e=this;this.jsonEditor=s.a.fromTextArea(this.$refs.textarea,{lineNumbers:!0,mode:"application/json",gutters:["CodeMirror-lint-markers"],theme:"rubyblue",lint:!0}),this.jsonEditor.setValue(JSON.stringify(this.value,null,2)),this.jsonEditor.on("change",(function(r){e.$emit("changed",r.getValue()),e.$emit("input",r.getValue())}))},methods:{getValue:function(){return this.jsonEditor.getValue()}}},n=l,c=(t("8e34"),t("2877")),d=Object(c["a"])(n,a,o,!1,null,"7b489788",null);r["a"]=d.exports}}]);