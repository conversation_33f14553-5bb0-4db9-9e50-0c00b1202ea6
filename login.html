<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录 - 数据中台</title>
    <script>
    // 立即重定向到主页面
    console.log('🔄 登录页面被访问，立即重定向到主页面');
    
    // 设置登录状态
    const mockUser = {
        username: 'admin',
        token: 'mock-token-admin-123456',
        roles: ['admin'],
        name: '管理员'
    };
    
    localStorage.setItem('token', mockUser.token);
    localStorage.setItem('user', JSON.stringify(mockUser));
    localStorage.setItem('isLoggedIn', 'true');
    
    // 立即重定向
    const redirect = new URLSearchParams(window.location.search).get('redirect') || '/dashboard/index';
    window.location.href = redirect;
    </script>
</head>
<body>
    <div style="text-align: center; padding: 50px;">
        <h2>正在跳转到数据中台...</h2>
        <p>如果页面没有自动跳转，请<a href="/dashboard/index">点击这里</a></p>
    </div>
</body>
</html>
