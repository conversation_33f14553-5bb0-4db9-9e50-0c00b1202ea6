(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d80be4c8"],{"09f4":function(e,t,l){"use strict";l.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,l,a){return e/=a/2,e<1?l/2*e*e+t:(e--,-l/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,l){var o=n(),r=e-o,s=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=s;var n=Math.easeInOutQuad(u,o,r,t);i(n),u<t?a(e):l&&"function"===typeof l&&l()};c()}},3338:function(e,t,l){"use strict";l.r(t);var a={};l.r(a),l.d(a,"list",(function(){return r})),l.d(a,"updated",(function(){return s})),l.d(a,"created",(function(){return u})),l.d(a,"deleted",(function(){return c}));var i=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("div",{staticClass:"filter-container"},[l("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"福利类型",filterable:""},model:{value:e.listQuery.type,callback:function(t){e.$set(e.listQuery,"type",t)},expression:"listQuery.type"}},e._l(e.dataSourceTypeList,(function(e,t){return l("el-option",{key:"db-type-"+t,attrs:{label:e.label,value:e.value}})})),1),l("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"福利名称"},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}}),l("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"福利描述"},model:{value:e.listQuery.describe,callback:function(t){e.$set(e.listQuery,"describe",t)},expression:"listQuery.describe"}}),l("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")])],1),l("div",{staticClass:"table-box"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[l("el-table-column",{attrs:{align:"left",label:"序号",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),l("el-table-column",{attrs:{label:"福利类型",align:"left",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.type))]}}])}),l("el-table-column",{attrs:{label:"福利名称",align:"left",width:"220px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),l("el-table-column",{attrs:{label:"福利描述",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.describe))]}}])}),l("el-table-column",{attrs:{label:"下载量",align:"left",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.downloads))]}}])}),l("el-table-column",{attrs:{label:"创建时间",align:"left",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.createtime)+" ")]}}])}),l("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width",width:"160px"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[l("el-button",{attrs:{size:"small",type:"success",icon:"el-icon-search"},on:{click:function(t){return e.handleVisit(a)}}},[e._v(" 下载资源 ")])]}}])})],1)],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.pageNo,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageNo",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.fetchData}}),l("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"800px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[l("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[l("el-form-item",{attrs:{label:"标签",prop:"type"}},[l("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"标签"},model:{value:e.temp.type,callback:function(t){e.$set(e.temp,"type",t)},expression:"temp.type"}})],1),l("el-form-item",{attrs:{label:"福利名称",prop:"name"}},[l("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"福利名称"},model:{value:e.temp.name,callback:function(t){e.$set(e.temp,"name",t)},expression:"temp.name"}})],1),l("el-form-item",{attrs:{label:"描述",prop:"describe"}},[l("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"描述"},model:{value:e.temp.describe,callback:function(t){e.$set(e.temp,"describe",t)},expression:"temp.describe"}})],1),l("el-form-item",{attrs:{label:"标准规则",prop:"rule"}},[l("el-input",{staticStyle:{width:"80%"},attrs:{type:"textarea",rows:"2",placeholder:"标准规则"},model:{value:e.temp.rule,callback:function(t){e.$set(e.temp,"rule",t)},expression:"temp.rule"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),l("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确认 ")])],1)],1),l("el-dialog",{attrs:{visible:e.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(t){e.dialogPluginVisible=t}}},[l("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pluginData,border:"",fit:"","highlight-current-row":""}},[l("el-table-column",{attrs:{prop:"key",label:"Channel"}}),l("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),l("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogPvVisible=!1}}},[e._v("Confirm")])],1)],1)],1)},n=[],o=l("b775");function r(e){return Object(o["a"])({url:"/api/goods/list",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/api/goods/update",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/api/metadataStandard/add",method:"post",data:e})}function c(e){return Object(o["a"])({url:"/api/metadataStandard/remove",method:"post",params:e})}var d=l("67248"),p=l("333d"),m={name:"DevEnvSetting",components:{Pagination:p["a"]},directives:{waves:d["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:10,name:"",describe:"",type:""},pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"Edit",create:"Create"},rules:{name:[{required:!0,message:"this is required",trigger:"blur"}],description:[{required:!0,message:"this is required",trigger:"blur"}]},dataSourceTypeList:[{value:"",label:""},{value:"部署包",label:"部署包"},{value:"哲学书籍",label:"哲学书籍"},{value:"技术书籍",label:"技术书籍"},{value:"课程分享",label:"课程分享"},{value:"软件分享",label:"软件分享"}],temp:{id:void 0,name:"",description:""},visible:!0}},created:function(){this.fetchData()},methods:{fetchData:function(){var e=this;console.log(1),this.listLoading=!0,console.log(a),r(this.listQuery).then((function(t){var l=t.content;e.total=l.recordsTotal,e.list=l.data,e.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleVisit:function(e){var t=this;s(e).then((function(){t.fetchData(),window.open(e.links)}))}}},f=m,b=l("2877"),v=Object(b["a"])(f,i,n,!1,null,null,null);t["default"]=v.exports},67248:function(e,t,l){"use strict";l("8d41");var a="@@wavesContext";function i(e,t){function l(l){var a=Object.assign({},t.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),n=i.ele;if(n){n.style.position="relative",n.style.overflow="hidden";var o=n.getBoundingClientRect(),r=n.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",n.appendChild(r)),i.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(l.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(l.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=i.color,r.className="waves-ripple z-active",!1}}return e[a]?e[a].removeHandle=l:e[a]={removeHandle:l},l}var n={bind:function(e,t){e.addEventListener("click",i(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[a].removeHandle,!1),e.addEventListener("click",i(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[a].removeHandle,!1),e[a]=null,delete e[a]}},o=function(e){e.directive("waves",n)};window.Vue&&(window.waves=n,Vue.use(o)),n.install=o;t["a"]=n},"8d41":function(e,t,l){}}]);