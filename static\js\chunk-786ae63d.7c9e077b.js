(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-786ae63d"],{"09f4":function(e,t,i){"use strict";i.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,i,a){return e/=a/2,e<1?i/2*e*e+t:(e--,-i/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,i){var o=l(),r=e-o,s=20,c=0;t="undefined"===typeof t?500:t;var u=function e(){c+=s;var l=Math.easeInOutQuad(c,o,r,t);n(l),c<t?a(e):i&&"function"===typeof i&&i()};u()}},"331e":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"项目名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}}),i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"项目描述"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.describe,callback:function(t){e.$set(e.listQuery,"describe",t)},expression:"listQuery.describe"}}),i("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.handleCreate}},[e._v(" 新增 ")])],1),i("div",{staticClass:"table-box"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"left",label:"序号",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),i("el-table-column",{attrs:{label:"项目名称",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),i("el-table-column",{attrs:{label:"项目描述",align:"left",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.description))]}}])}),i("el-table-column",{attrs:{label:"所属用户",width:"100",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.userName)+" ")]}}])}),i("el-table-column",{attrs:{label:"创建时间",width:"200",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.createTime))]}}])}),i("el-table-column",{attrs:{label:"操作",align:"left",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[i("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-edit"},on:{click:function(t){return e.handleUpdate(a)}}},[e._v(" 编辑 ")]),"deleted"!==a.status?i("el-button",{attrs:{size:"small",icon:"el-icon-delete",type:"danger"},on:{click:function(t){return e.handleDelete(a)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.pageNo,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageNo",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.fetchData}}),i("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"800px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[i("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[i("el-form-item",{attrs:{label:"项目名称",prop:"name"}},[i("el-input",{staticStyle:{width:"40%"},attrs:{placeholder:"项目名称"},model:{value:e.temp.name,callback:function(t){e.$set(e.temp,"name",t)},expression:"temp.name"}})],1),i("el-form-item",{attrs:{label:"项目描述",prop:"description"}},[i("el-input",{staticStyle:{width:"40%"},attrs:{placeholder:"项目描述"},model:{value:e.temp.description,callback:function(t){e.$set(e.temp,"description",t)},expression:"temp.description"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),i("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确认 ")])],1)],1),i("el-dialog",{attrs:{visible:e.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(t){e.dialogPluginVisible=t}}},[i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pluginData,border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{prop:"key",label:"Channel"}}),i("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogPvVisible=!1}}},[e._v("Confirm")])],1)],1)],1)},n=[],l=(i("14d9"),i("a53d")),o=i("67248"),r=i("333d"),s={name:"JobProject",components:{Pagination:r["a"]},directives:{waves:o["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:10,name:"",describe:""},pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"修改 项目管理",create:"新增 项目管理"},rules:{name:[{required:!0,message:"项目名称 不能为空",trigger:"blur"}],description:[{required:!0,message:"项目描述 不能为空",trigger:"blur"}]},temp:{id:void 0,name:"",description:""},visible:!0}},created:function(){this.fetchData()},methods:{fetchData:function(){var e=this;this.listLoading=!0,l["d"](this.listQuery).then((function(t){var i=t.records,a=t.total;e.total=a,e.list=i,e.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&l["a"](e.temp).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 项目管理",message:"新增成功",type:"success",duration:2e3})}))}))},handleUpdate:function(e){var t=this;this.temp=Object.assign({},e),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var i=Object.assign({},e.temp);l["e"](i).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"修改 项目管理",message:"修改成功",type:"success",duration:2e3})}))}}))},handleDelete:function(e){var t=this;console.log("删除");var i=[];i.push(e.id),l["b"]({idList:e.id}).then((function(e){t.fetchData(),t.$notify({title:"删除 项目管理",message:"删除成功",type:"success",duration:2e3})}))}}},c=s,u=i("2877"),d=Object(u["a"])(c,a,n,!1,null,null,null);t["default"]=d.exports},67248:function(e,t,i){"use strict";i("8d41");var a="@@wavesContext";function n(e,t){function i(i){var a=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),l=n.ele;if(l){l.style.position="relative",l.style.overflow="hidden";var o=l.getBoundingClientRect(),r=l.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",l.appendChild(r)),n.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(i.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(i.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}return e[a]?e[a].removeHandle=i:e[a]={removeHandle:i},i}var l={bind:function(e,t){e.addEventListener("click",n(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[a].removeHandle,!1),e.addEventListener("click",n(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[a].removeHandle,!1),e[a]=null,delete e[a]}},o=function(e){e.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(o)),l.install=o;t["a"]=l},"8d41":function(e,t,i){},a53d:function(e,t,i){"use strict";i.d(t,"d",(function(){return n})),i.d(t,"e",(function(){return l})),i.d(t,"a",(function(){return o})),i.d(t,"b",(function(){return r})),i.d(t,"c",(function(){return s}));var a=i("b775");function n(e){return Object(a["a"])({url:"/api/jobProject",method:"get",params:e})}function l(e){return Object(a["a"])({url:"/api/jobProject",method:"put",data:e})}function o(e){return Object(a["a"])({url:"/api/jobProject",method:"post",data:e})}function r(e){return Object(a["a"])({url:"/api/jobProject",method:"delete",params:e})}function s(e){return Object(a["a"])({url:"api/jobProject/list",method:"get",params:e})}}}]);