(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-43155c8d"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(e,t,a){var n=o(),s=e-n,l=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=l;var o=Math.easeInOutQuad(u,n,s,t);r(o),u<t?i(e):a&&"function"===typeof a&&a()};c()}},1389:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("div",{staticClass:"search-header"},[a("el-form",{attrs:{"label-suffix":"：","label-width":"138px",inline:""}},[a("el-form-item",{attrs:{label:"请求地址","label-width":"auto"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"请求地址"},model:{value:e.listQuery.path,callback:function(t){e.$set(e.listQuery,"path",t)},expression:"listQuery.path"}})],1),a("el-form-item",{attrs:{label:"API名称"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"API名称"},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}})],1)],1),a("div",{staticClass:"search-opt"},[a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item search",attrs:{type:"primary round"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item reset-btn",attrs:{type:"primary round"},on:{click:e.handleReset}},[e._v(" 重置 ")])],1)],1),a("el-divider")],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{label:"请求方式",align:"left",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.type))]}}])}),a("el-table-column",{attrs:{label:"API名称",align:"left",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),a("el-table-column",{attrs:{label:"请求地址",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.url))]}}])}),a("el-table-column",{attrs:{label:"请求次数",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.times))]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"table-btn",on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(" 删除 ")])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}}),a("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"right","label-width":"100px"}},[a("el-form-item",{attrs:{label:"资源名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"资源名称"},model:{value:e.temp.name,callback:function(t){e.$set(e.temp,"name",t)},expression:"temp.name"}})],1),a("el-form-item",{attrs:{label:"资源地址",prop:"resourcePath"}},[a("el-input",{attrs:{placeholder:"资源地址"},model:{value:e.temp.resource_address,callback:function(t){e.$set(e.temp,"resource_address",t)},expression:"temp.resource_address"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("span",{staticClass:"cancel-btn",on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v("确定")])],1)],1),a("InfoDialog",{ref:"InfoDialog"})],1)},r=[],o=(a("b0c0"),a("8d6b")),n=a("67248"),s=a("333d"),l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{ref:"InfoDialog",attrs:{visible:e.dialogShow,title:e.infoTitle,"destroy-on-close":!0,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.dialogShow=t},close:e.close}},[e.formData?a("el-form",{ref:"infoDialogForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"100px"}},[a("el-form-item",{attrs:{label:"请求路径",prop:"path"}},[a("el-input",{attrs:{autocomplete:"off"},model:{value:e.formData.path,callback:function(t){e.$set(e.formData,"path",t)},expression:"formData.path"}})],1),a("el-form-item",{attrs:{label:"API名称",prop:"name"}},[a("el-input",{attrs:{autocomplete:"off"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),a("el-form-item",{attrs:{label:"返回值类型",prop:"group_id"}},[a("el-input",{model:{value:e.formData.group_id,callback:function(t){e.$set(e.formData,"group_id",t)},expression:"formData.group_id"}})],1),a("el-form-item",{attrs:{label:"数据源",prop:"datasource_id"}},[e.sourceList?a("el-select",{staticStyle:{width:"310px"},attrs:{placeholder:"请选择"},model:{value:e.formData.datasource_id,callback:function(t){e.$set(e.formData,"datasource_id",t)},expression:"formData.datasource_id"}},e._l(e.sourceList,(function(e){return a("el-option",{key:"datasource_id_"+e.id,attrs:{label:e.datasourceName,value:e.id}})})),1):e._e()],1),a("el-form-item",{attrs:{label:"SQL语句",prop:"sql_text"}},[a("SqlEditor",{model:{value:e.formData.sql_text,callback:function(t){e.$set(e.formData,"sql_text",t)},expression:"formData.sql_text"}})],1),a("el-form-item",{attrs:{label:"请求参数",prop:"params"}},[a("el-input",{staticStyle:{"white-space":"pre"},attrs:{autosize:{minRows:2,maxRows:12},type:"textarea",placeholder:"请输入"},model:{value:e.formData.params,callback:function(t){e.$set(e.formData,"params",t)},expression:"formData.params"}})],1),a("el-form-item",{attrs:{label:"描述",prop:"describe"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:4},placeholder:"请输入"},model:{value:e.formData.describe,callback:function(t){e.$set(e.formData,"describe",t)},expression:"formData.describe"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"info"},on:{click:e.handleCancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("提交")])],1)],1):e._e()],1)},u=[],c=a("1bf5"),d=a("b252"),m=a("f656"),f={name:"ChangePassword",components:{SqlEditor:c["a"]},data:function(){return{rules:{name:[{required:!0,message:"不能为空",trigger:"blur"},Object(m["b"])()],path:[{required:!0,message:"不能为空",trigger:"blur"}],group_id:[{required:!0,message:"不能为空",trigger:"blur"}],describe:[{required:!0,message:"不能为空",trigger:"blur"}],datasource_id:[{required:!0,message:"不能为空",trigger:"blur"}],params:[{required:!0,message:"不能为空",trigger:"blur"},Object(m["a"])()],sql_text:[{required:!0,message:"不能为空",trigger:"blur"}]},dialogShow:!1,onClose:null,onSubmit:null,mode:"edit",infoTitle:"详细信息",defaultFormData:{id:"",name:"",path:"",group_id:"",describe:"",datasource_id:"",params:"",sql_text:""},formData:null,sourceList:null}},created:function(){this.formData=Object.assign({},this.defaultFormData)},methods:{init:function(){var e=this;Object(d["c"])().then((function(t){e.sourceList=t.content.data}))},openDialog:function(e){var t=this,a=e.formData,i=void 0===a?{}:a,r=e.infoTitle,o=void 0===r?"":r,n=e.mode,s=void 0===n?"edit":n,l=e.onSubmit,u=void 0===l?null:l,c=e.onClose,d=void 0===c?null:c;this.mode=s,this.onSubmit=u,this.onClose=d,this.infoTitle=o,this.formData=null,setTimeout((function(){t.formData=Object.assign({},t.defaultFormData,i)}),200),this.dialogShow=!0,this.init()},handleSubmit:function(){var e=this;this.$refs["infoDialogForm"].validate((function(t){var a;return t?(null===(a=e.onSubmit)||void 0===a||a.call(e,e.formData),e.dialogShow=!1,!0):(e.$message.error("填写异常，请仔细检查"),!1)}))},handleCancel:function(){this.dialogShow=!1},close:function(){var e;null===(e=this.onClose)||void 0===e||e.call(this,this.formData)}}},p=f,h=(a("4c4b"),a("2877")),b=Object(h["a"])(p,l,u,!1,null,"292595e6",null),g=b.exports,v={name:"User",components:{Pagination:s["a"],InfoDialog:g},directives:{waves:n["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{current:1,size:10,name:"",path:""},roles:["ROLE_USER","ROLE_ADMIN"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"Edit",create:"Create"},rules:{role:[{required:!0,message:"role is required",trigger:"change"}],name:[{required:!0,message:"name is required",trigger:"blur"}],password:[{required:!1,message:"password is required",trigger:"blur"}]},temp:{id:void 0,role:"",name:"",password:"",permission:"",resource_address:""},resetTemp:function(){this.temp=this.$options.data().temp}}},created:function(){this.fetchData()},methods:{handleReset:function(){this.listQuery.path="",this.listQuery.name="",this.fetchData()},fetchData:function(){var e=this;this.listLoading=!0,o["a"](this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},handleAdd:function(){var e=this;this.$refs["InfoDialog"].openDialog({onSubmit:function(t){Object(d["a"])(t).then((function(){e.$message.success("新增成功"),e.fetchData()}))}})},handleUpdate:function(e){var t=this;this.$refs["InfoDialog"].openDialog({formData:e,onSubmit:function(e){Object(d["d"])(e).then((function(){t.$message.success("更新成功"),t.fetchData()}))}})},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={name:e.temp.name,resource_address:e.temp.resource_address};o["b"](a).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增操作",message:"新增操作 成功",type:"success",duration:2e3})}))}}))},handleVisit:function(e){window.open(e.resource_address)},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={id:e.temp.id,name:e.temp.name,resource_address:e.temp.resource_address},i=Object.assign({},a);o["d"](i).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(e){var t=this;o["c"](e.id).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))}}},w=v,y=Object(h["a"])(w,i,r,!1,null,null,null);t["default"]=y.exports},"4c4b":function(e,t,a){"use strict";a("7bf3")},67248:function(e,t,a){"use strict";a("8d41");var i="@@wavesContext";function r(e,t){function a(a){var i=Object.assign({},t.value),r=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),o=r.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var n=o.getBoundingClientRect(),s=o.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":(s=document.createElement("span"),s.className="waves-ripple",s.style.height=s.style.width=Math.max(n.width,n.height)+"px",o.appendChild(s)),r.type){case"center":s.style.top=n.height/2-s.offsetHeight/2+"px",s.style.left=n.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(a.pageY-n.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(a.pageX-n.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=r.color,s.className="waves-ripple z-active",!1}}return e[i]?e[i].removeHandle=a:e[i]={removeHandle:a},a}var o={bind:function(e,t){e.addEventListener("click",r(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[i].removeHandle,!1),e.addEventListener("click",r(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[i].removeHandle,!1),e[i]=null,delete e[i]}},n=function(e){e.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(n)),o.install=n;t["a"]=o},"7bf3":function(e,t,a){},"8d41":function(e,t,a){},"8d6b":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"d",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return s}));var i=a("b775");function r(e){return Object(i["a"])({url:"/api/base/resource/add",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/api/base/resource/update",method:"post",data:e})}function n(e){return Object(i["a"])({url:"/api/apiConfig/remove?id="+e,method:"post"})}function s(e){return Object(i["a"])({url:"/api/apiConfig/list",method:"get",params:e})}},b252:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"d",(function(){return o})),a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return s}));var i=a("b775");function r(e){return Object(i["a"])({url:"/api/apiConfig/add",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/api/apiConfig/update",method:"post",data:e})}function n(e){return Object(i["a"])({url:"/api/apiConfig/execute",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/api/apiConfig/listDataSourceName",method:"get",params:e})}},f656:function(e,t,a){"use strict";function i(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function r(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return r}))}}]);