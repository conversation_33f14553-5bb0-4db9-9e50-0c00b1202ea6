(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5d8ef9eb"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),Math.easeInOutQuad=function(e,t,a,r){return e/=r/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function l(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=o(),n=e-i,s=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=s;var o=Math.easeInOutQuad(u,i,n,t);l(o),u<t?r(e):a&&"function"===typeof a&&a()};c()}},"16d2":function(e,t,a){},"255f":function(e,t,a){},"2b10":function(e,t,a){"use strict";a.d(t,"g",(function(){return l})),a.d(t,"l",(function(){return o})),a.d(t,"j",(function(){return i})),a.d(t,"k",(function(){return n})),a.d(t,"e",(function(){return s})),a.d(t,"m",(function(){return u})),a.d(t,"d",(function(){return c})),a.d(t,"i",(function(){return d})),a.d(t,"b",(function(){return p})),a.d(t,"c",(function(){return m})),a.d(t,"h",(function(){return f})),a.d(t,"f",(function(){return h})),a.d(t,"a",(function(){return b}));var r=a("b775");function l(e){return Object(r["a"])({url:"api/job/pageList",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/api/job/trigger",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/api/job/start?id="+e,method:"post"})}function n(e){return Object(r["a"])({url:"/api/job/stop?id="+e,method:"post"})}function s(){return Object(r["a"])({url:"api/jobGroup/list",method:"get"})}function u(e){return Object(r["a"])({url:"/api/job/update",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/api/job/add/",method:"post",data:e})}function d(e){return Object(r["a"])({url:"/api/job/remove/"+e,method:"post"})}function p(e){return Object(r["a"])({url:"/api/job/batchDelete/"+e,method:"post"})}function m(e){return Object(r["a"])({url:"/api/job/batchDeleteLogs/"+e,method:"post"})}function f(e){return Object(r["a"])({url:"/api/job/nextTriggerTime?cron="+e,method:"get"})}function h(e){return Object(r["a"])({url:"api/job/list",method:"get",params:e})}function b(e){return Object(r["a"])({url:"/api/job/batchAdd",method:"post",data:e})}},"39ed":function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));var r=a("b775");function l(e){return Object(r["a"])({url:"/api/jobGroup/loadById?id="+e,method:"post"})}},"41c9":function(e,t,a){},"448c":function(e,t,a){"use strict";a("83af")},67248:function(e,t,a){"use strict";a("8d41");var r="@@wavesContext";function l(e,t){function a(a){var r=Object.assign({},t.value),l=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},r),o=l.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var i=o.getBoundingClientRect(),n=o.querySelector(".waves-ripple");switch(n?n.className="waves-ripple":(n=document.createElement("span"),n.className="waves-ripple",n.style.height=n.style.width=Math.max(i.width,i.height)+"px",o.appendChild(n)),l.type){case"center":n.style.top=i.height/2-n.offsetHeight/2+"px",n.style.left=i.width/2-n.offsetWidth/2+"px";break;default:n.style.top=(a.pageY-i.top-n.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",n.style.left=(a.pageX-i.left-n.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return n.style.backgroundColor=l.color,n.className="waves-ripple z-active",!1}}return e[r]?e[r].removeHandle=a:e[r]={removeHandle:a},a}var o={bind:function(e,t){e.addEventListener("click",l(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[r].removeHandle,!1),e.addEventListener("click",l(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[r].removeHandle,!1),e[r]=null,delete e[r]}},i=function(e){e.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(i)),o.install=i;t["a"]=o},"7e39":function(e,t,a){"use strict";a.d(t,"f",(function(){return l})),a.d(t,"c",(function(){return o})),a.d(t,"h",(function(){return i})),a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return s})),a.d(t,"g",(function(){return u})),a.d(t,"d",(function(){return c})),a.d(t,"e",(function(){return d}));var r=a("b775");function l(e){return Object(r["a"])({url:"/api/jobJdbcDatasource/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/api/jobJdbcDatasource/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/api/jobJdbcDatasource/update",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/api/jobJdbcDatasource",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/api/jobJdbcDatasource/remove?id="+e,method:"post"})}function u(e){return Object(r["a"])({url:"/api/jobJdbcDatasource/test",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/api/jobJdbcDatasource/findSourceName",method:"get",params:e})}function d(e){return Object(r["a"])({url:"/api/jobJdbcDatasource/list?current=1&size=200&ascs=datasource_name",method:"get",params:e})}},"83af":function(e,t,a){},"8d41":function(e,t,a){},"8e34":function(e,t,a){"use strict";a("16d2")},"8f79":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{multiple:"",placeholder:"所属项目"},model:{value:e.projectIds,callback:function(t){e.projectIds=t},expression:"projectIds"}},e._l(e.jobProjectList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"任务名称"},model:{value:e.listQuery.jobDesc,callback:function(t){e.$set(e.listQuery,"jobDesc",t)},expression:"listQuery.jobDesc"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"任务类型"},model:{value:e.listQuery.glueType,callback:function(t){e.$set(e.listQuery,"glueType",t)},expression:"listQuery.glueType"}},e._l(e.glueTypes,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),a("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"执行状态"},model:{value:e.listQuery.triggerStatus,callback:function(t){e.$set(e.listQuery,"triggerStatus",t)},expression:"listQuery.triggerStatus"}},e._l(e.triggerStatusl,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),a("el-date-picker",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"创建开始时间","end-placeholder":"创建结束时间"},model:{value:e.addTimes,callback:function(t){e.addTimes=t},expression:"addTimes"}}),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.batchDelete}},[e._v(" 批量删除 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"medium"},on:{"selection-change":e.selectionChangeHandle}},[a("el-table-column",{attrs:{type:"selection","header-align":"left",align:"left",width:"50",selectable:e.selectable}}),a("el-table-column",{attrs:{align:"left",label:"序号",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),a("el-table-column",{attrs:{label:"所属项目",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.projectName))]}}])}),a("el-table-column",{attrs:{label:"任务名称",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.jobDesc))]}}])}),a("el-table-column",{attrs:{label:"任务类型",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["datax"===t.row.glueType?a("el-tag",{attrs:{type:""}},[e._v("datax")]):"flinkx"===t.row.glueType?a("el-tag",{attrs:{type:"success"}},[e._v("flinkx")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"调度表达式",align:"left",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.jobCron))])]}}])}),a("el-table-column",{attrs:{label:"触发时间",align:"left",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"bottom",width:"160"},on:{show:function(a){return e.nextTriggerTime(t.row)}}},[a("h5",{domProps:{innerHTML:e._s(e.triggerNextTimes)}}),a("el-button",{attrs:{slot:"reference",size:"small"},slot:"reference"},[e._v("查看")])],1)]}}])}),a("el-table-column",{attrs:{label:"任务状态",align:"left",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-color":"#00A854","active-text":"启动","active-value":1,"inactive-color":"#F04134","inactive-text":"停止","inactive-value":0},on:{change:function(a){return e.changeSwitch(t.row)}},model:{value:t.row.triggerStatus,callback:function(a){e.$set(t.row,"triggerStatus",a)},expression:"scope.row.triggerStatus"}})]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"left",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.addTime))]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-dropdown",{attrs:{trigger:"click"}},[a("span",{staticClass:"el-dropdown-link"},[e._v(" 操作"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{nativeOn:{click:function(t){return e.handlerExecute(r)}}},[e._v("执行一次")]),a("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(t){return e.handlerUpdate(r)}}},[e._v("编辑")]),a("el-dropdown-item",{nativeOn:{click:function(t){return e.handlerDelete(r)}}},[e._v("删除")])],1)],1)]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}}),a("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"1000px","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"110px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所属项目",prop:"projectId"}},[a("el-select",{staticClass:"filter-item",attrs:{placeholder:"所属项目"},model:{value:e.temp.projectId,callback:function(t){e.$set(e.temp,"projectId",t)},expression:"temp.projectId"}},e._l(e.jobProjectList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务名称",prop:"jobDesc"}},[a("el-input",{attrs:{size:"medium",placeholder:"请输入任务名称"},model:{value:e.temp.jobDesc,callback:function(t){e.$set(e.temp,"jobDesc",t)},expression:"temp.jobDesc"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"路由策略",prop:"executorRouteStrategy"}},[a("el-select",{attrs:{placeholder:"请选择路由策略"},model:{value:e.temp.executorRouteStrategy,callback:function(t){e.$set(e.temp,"executorRouteStrategy",t)},expression:"temp.executorRouteStrategy"}},e._l(e.routeStrategies,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-dialog",{attrs:{title:"提示",visible:e.showCronBox,width:"60%","append-to-body":""},on:{"update:visible":function(t){e.showCronBox=t}}},[a("cron",{model:{value:e.temp.jobCron,callback:function(t){e.$set(e.temp,"jobCron",t)},expression:"temp.jobCron"}}),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.showCronBox=!1}}},[e._v("关闭")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.showCronBox=!1}}},[e._v("确 定")])],1)],1),a("el-form-item",{attrs:{label:"Cron",prop:"jobCron"}},[a("el-input",{attrs:{"auto-complete":"off",placeholder:"请输入Cron表达式"},model:{value:e.temp.jobCron,callback:function(t){e.$set(e.temp,"jobCron",t)},expression:"temp.jobCron"}},[e.showCronBox?a("el-button",{attrs:{slot:"append",icon:"el-icon-open",title:"关闭图形配置"},on:{click:function(t){e.showCronBox=!1}},slot:"append"}):a("el-button",{attrs:{slot:"append",icon:"el-icon-turn-off",title:"打开图形配置"},on:{click:function(t){e.showCronBox=!0}},slot:"append"})],1)],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"阻塞处理",prop:"executorBlockStrategy"}},[a("el-select",{attrs:{placeholder:"请选择阻塞处理策略"},model:{value:e.temp.executorBlockStrategy,callback:function(t){e.$set(e.temp,"executorBlockStrategy",t)},expression:"temp.executorBlockStrategy"}},e._l(e.blockStrategies,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"报警邮件"}},[a("el-input",{attrs:{placeholder:"请输入报警邮件，多个用逗号分隔"},model:{value:e.temp.alarmEmail,callback:function(t){e.$set(e.temp,"alarmEmail",t)},expression:"temp.alarmEmail"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务类型",prop:"glueType"}},[a("el-select",{attrs:{placeholder:"任务脚本类型"},model:{value:e.temp.glueType,callback:function(t){e.$set(e.temp,"glueType",t)},expression:"temp.glueType"}},e._l(e.glueTypes,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"失败重试次数"}},[a("el-input-number",{attrs:{min:0,max:20},model:{value:e.temp.executorFailRetryCount,callback:function(t){e.$set(e.temp,"executorFailRetryCount",t)},expression:"temp.executorFailRetryCount"}})],1)],1)],1),"datax"===e.temp.glueType?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"辅助参数",prop:"incrementType"}},[a("el-select",{attrs:{placeholder:"请选择参数类型",value:""},model:{value:e.temp.incrementType,callback:function(t){e.$set(e.temp,"incrementType",t)},expression:"temp.incrementType"}},e._l(e.incrementTypes,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1):e._e(),"datax"===e.temp.glueType&&1===e.temp.incrementType?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"增量主键开始ID",prop:"incStartId"}},[a("el-input",{staticStyle:{width:"56%"},attrs:{placeholder:"首次增量使用"},model:{value:e.temp.incStartId,callback:function(t){e.$set(e.temp,"incStartId",t)},expression:"temp.incStartId"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"ID增量参数",prop:"replaceParam"}},[a("el-input",{attrs:{placeholder:"-DstartId='%s' -DendId='%s'"},model:{value:e.temp.replaceParam,callback:function(t){e.$set(e.temp,"replaceParam",t)},expression:"temp.replaceParam"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"reader数据源",prop:"datasourceId"}},[a("el-select",{staticClass:"filter-item",attrs:{placeholder:"reader数据源"},model:{value:e.temp.datasourceId,callback:function(t){e.$set(e.temp,"datasourceId",t)},expression:"temp.datasourceId"}},e._l(e.dataSourceList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.datasourceName,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{span:7}},[a("el-form-item",{attrs:{label:"reader表",prop:"readerTable"}},[a("el-input",{attrs:{placeholder:"读表的表名"},model:{value:e.temp.readerTable,callback:function(t){e.$set(e.temp,"readerTable",t)},expression:"temp.readerTable"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"主键","label-width":"40px",prop:"primaryKey"}},[a("el-input",{attrs:{placeholder:"请填写主键字段名"},model:{value:e.temp.primaryKey,callback:function(t){e.$set(e.temp,"primaryKey",t)},expression:"temp.primaryKey"}})],1)],1)],1):e._e(),"datax"===e.temp.glueType&&2===e.temp.incrementType?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"增量开始时间",prop:"incStartTime"}},[a("el-date-picker",{staticStyle:{width:"57%"},attrs:{type:"datetime",placeholder:"首次增量使用",format:"yyyy-MM-dd HH:mm:ss"},model:{value:e.temp.incStartTime,callback:function(t){e.$set(e.temp,"incStartTime",t)},expression:"temp.incStartTime"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"增量时间字段",prop:"replaceParam"}},[a("el-input",{attrs:{placeholder:"-DstartCreateTime='%s' -DendCreateTime='%s'"},model:{value:e.temp.replaceParam,callback:function(t){e.$set(e.temp,"replaceParam",t)},expression:"temp.replaceParam"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"增量时间格式",prop:"replaceParamType"}},[a("el-select",{attrs:{placeholder:"增量时间格式"},on:{change:e.incStartTimeFormat},model:{value:e.temp.replaceParamType,callback:function(t){e.$set(e.temp,"replaceParamType",t)},expression:"temp.replaceParamType"}},e._l(e.replaceFormatTypes,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1):e._e(),"datax"===e.temp.glueType&&3===e.temp.incrementType?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"分区字段",prop:"partitionField"}},[a("el-input",{staticStyle:{width:"56%"},attrs:{placeholder:"请输入分区字段"},model:{value:e.partitionField,callback:function(t){e.partitionField=t},expression:"partitionField"}})],1)],1),a("el-col",{attrs:{span:7}},[a("el-form-item",{attrs:{label:"分区时间"}},[a("el-select",{attrs:{placeholder:"分区时间格式"},model:{value:e.timeFormatType,callback:function(t){e.timeFormatType=t},expression:"timeFormatType"}},e._l(e.timeFormatTypes,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-input-number",{staticStyle:{width:"65%"},attrs:{min:-20,max:0},model:{value:e.timeOffset,callback:function(t){e.timeOffset=t},expression:"timeOffset"}})],1)],1):e._e(),"datax"===e.temp.glueType?a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"执行内存(单位 M)"}},[a("el-input",{attrs:{placeholder:"512"},model:{value:e.temp.jvmParam,callback:function(t){e.$set(e.temp,"jvmParam",t)},expression:"temp.jvmParam"}})],1)],1)],1):e._e()],1),"datax"===e.temp.glueType?a("json-editor",{ref:"jsonEditor",model:{value:e.temp.jobJson,callback:function(t){e.$set(e.temp,"jobJson",t)},expression:"temp.jobJson"}}):e._e(),"GLUE_SHELL"===e.temp.glueType?a("shell-editor",{ref:"shellEditor",model:{value:e.glueSource,callback:function(t){e.glueSource=t},expression:"glueSource"}}):e._e(),"GLUE_PYTHON"===e.temp.glueType?a("python-editor",{ref:"pythonEditor",model:{value:e.glueSource,callback:function(t){e.glueSource=t},expression:"glueSource"}}):e._e(),"GLUE_POWERSHELL"===e.temp.glueType?a("powershell-editor",{ref:"powershellEditor",model:{value:e.glueSource,callback:function(t){e.glueSource=t},expression:"glueSource"}}):e._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确定 ")])],1)],1)],1)},l=[],o=(a("d9e2"),a("99af"),a("a15b"),a("14d9"),a("d3b7"),a("25f0"),a("39ed")),i=a("2b10"),n=a("67248"),s=a("5ec8"),u=a("333d"),c=a("fa7e"),d=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"shell-editor"},[a("textarea",{ref:"textarea"})])},p=[],m=a("56b3"),f=a.n(m);a("0dd0"),a("a7be"),a("acdf"),a("f9d4"),a("8822");a("02f0");var h={name:"ShellEditor",props:["value"],data:function(){return{shellEditor:!1}},watch:{value:function(e){var t=this.shellEditor.getValue();e!==t&&this.shellEditor.setValue(this.value)}},mounted:function(){var e=this;this.shellEditor=f.a.fromTextArea(this.$refs.textarea,{lineNumbers:!0,mode:"text/x-sh",gutters:["CodeMirror-lint-markers"],theme:"rubyblue",lint:!0}),this.shellEditor.setValue(this.value?this.value:""),this.shellEditor.on("change",(function(t){e.$emit("changed",t.getValue()),e.$emit("input",t.getValue())}))},methods:{getValue:function(){return this.shellEditor.getValue()}}},b=h,g=(a("9c5e"),a("2877")),y=Object(g["a"])(b,d,p,!1,null,"b001ad70",null),v=y.exports,j=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"python-editor"},[a("textarea",{ref:"textarea"})])},x=[];a("db91");var T={name:"PythonEditor",props:["value"],data:function(){return{pythonEditor:!1}},watch:{value:function(e){var t=this.pythonEditor.getValue();e!==t&&this.pythonEditor.setValue(this.value)}},mounted:function(){var e=this;this.pythonEditor=f.a.fromTextArea(this.$refs.textarea,{lineNumbers:!0,mode:"text/x-python",gutters:["CodeMirror-lint-markers"],theme:"rubyblue",lint:!0}),this.pythonEditor.setValue(this.value?this.value:""),this.pythonEditor.on("change",(function(t){e.$emit("changed",t.getValue()),e.$emit("input",t.getValue())}))},methods:{getValue:function(){return this.pythonEditor.getValue()}}},w=T,S=(a("fc8d"),Object(g["a"])(w,j,x,!1,null,"a3a66166",null)),k=S.exports,E=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"powershell-editor"},[a("textarea",{ref:"textarea"})])},I=[];a("9da3");var _={name:"PowershellEditor",props:["value"],data:function(){return{powershellEditor:!1}},watch:{value:function(e){var t=this.powershellEditor.getValue();e!==t&&this.powershellEditor.setValue(this.value)}},mounted:function(){var e=this;this.powershellEditor=f.a.fromTextArea(this.$refs.textarea,{lineNumbers:!0,mode:"powershell",gutters:["CodeMirror-lint-markers"],theme:"rubyblue",lint:!0}),this.powershellEditor.setValue(this.value?this.value:""),this.powershellEditor.on("change",(function(t){e.$emit("changed",t.getValue()),e.$emit("input",t.getValue())}))},methods:{getValue:function(){return this.powershellEditor.getValue()}}},C=_,$=(a("b74e"),Object(g["a"])(C,E,I,!1,null,"456ba0b7",null)),O=$.exports,L=a("7e39"),F=a("a53d"),D=a("61f7"),M={name:"JobInfo",components:{Pagination:u["a"],JsonEditor:c["a"],ShellEditor:v,PythonEditor:k,PowershellEditor:O,Cron:s["a"]},directives:{waves:n["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){var e=this,t=function(e,t,a){t||a(new Error("Increment parameters is required")),a()},a=function(t,a,r){e.partitionField||r(new Error("Partition parameters is required")),r()};return{addTimes:[],nameLists:[],projectIds:"",list:null,listLoading:!0,total:0,listQuery:{current:1,size:10,jobGroup:0,projectIds:"",triggerStatus:"",jobDesc:"",glueType:"",addTimeStart:"",addTimeEnd:""},showCronBox:!1,dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"调度任务修改",create:"新增"},rules:{jobGroup:[{required:!0,message:"jobGroup is required",trigger:"change"}],executorRouteStrategy:[{required:!0,message:"executorRouteStrategy is required",trigger:"change"}],executorBlockStrategy:[{required:!0,message:"executorBlockStrategy is required",trigger:"change"}],glueType:[{required:!0,message:"jobType is required",trigger:"change"}],projectId:[{required:!0,message:"projectId is required",trigger:"change"}],jobDesc:[{required:!0,message:"jobDesc is required",trigger:"blur"}],jobProject:[{required:!0,message:"jobProject is required",trigger:"blur"}],jobCron:[{required:!0,message:"jobCron is required",trigger:"blur"}],jobJson:[{required:!0,message:"JSON字符串必填",trigger:"change"}],executorHandler:[{required:!0,message:"执行器处理类必填",trigger:"change"}],incStartId:[{trigger:"blur",validator:t}],replaceParam:[{trigger:"blur",validator:t}],primaryKey:[{trigger:"blur",validator:t}],incStartTime:[{trigger:"change",validator:t}],replaceParamType:[{trigger:"change",validator:t}],partitionField:[{trigger:"blur",validator:a}],datasourceId:[{trigger:"change",validator:t}],readerTable:[{trigger:"blur",validator:t}]},temp:{id:void 0,jobGroup:"",jobCron:"",jobDesc:"",executorRouteStrategy:"",executorBlockStrategy:"",childJobId:"",executorFailRetryCount:"",alarmEmail:"",executorTimeout:"",userId:0,jobConfigId:"",executorHandler:"",glueType:"",glueSource:"",jobJson:"",executorParam:"",replaceParam:"",replaceParamType:"Timestamp",jvmParam:"",incStartTime:"",partitionInfo:"",incrementType:0,incStartId:"",primaryKey:"",projectId:"",datasourceId:"",readerTable:""},resetTemp:function(){this.temp=this.$options.data().temp,this.jobJson="",this.glueSource="",this.timeOffset=0,this.timeFormatType="yyyy-MM-dd",this.partitionField=""},executorList:"",jobIdList:"",jobProjectList:"",dataSourceList:"",blockStrategies:[{value:"SERIAL_EXECUTION",label:"单机串行"},{value:"DISCARD_LATER",label:"丢弃后续调度"},{value:"COVER_EARLY",label:"覆盖之前调度"}],routeStrategies:[{value:"FIRST",label:"第一个"},{value:"LAST",label:"最后一个"},{value:"ROUND",label:"轮询"},{value:"RANDOM",label:"随机"},{value:"CONSISTENT_HASH",label:"一致性HASH"},{value:"LEAST_FREQUENTLY_USED",label:"最不经常使用"},{value:"LEAST_RECENTLY_USED",label:"最近最久未使用"},{value:"FAILOVER",label:"故障转移"},{value:"BUSYOVER",label:"忙碌转移"}],glueTypes:[{value:"",label:""},{value:"datax",label:"datax"},{value:"flinkx",label:"flinkx"}],triggerStatusl:[{value:"",label:""},{value:"1",label:"启动"},{value:"0",label:"停止"}],incrementTypes:[{value:0,label:"无"},{value:1,label:"主键自增"},{value:2,label:"时间自增"},{value:3,label:"HIVE分区"}],triggerNextTimes:"",registerNode:[],jobJson:"",glueSource:"",timeOffset:0,timeFormatType:"yyyy-MM-dd",partitionField:"",timeFormatTypes:[{value:"yyyy-MM-dd",label:"yyyy-MM-dd"},{value:"yyyyMMdd",label:"yyyyMMdd"},{value:"yyyy/MM/dd",label:"yyyy/MM/dd"}],replaceFormatTypes:[{value:"yyyy/MM/dd",label:"yyyy/MM/dd"},{value:"yyyy-MM-dd",label:"yyyy-MM-dd"},{value:"HH:mm:ss",label:"HH:mm:ss"},{value:"yyyy/MM/dd HH:mm:ss",label:"yyyy/MM/dd HH:mm:ss"},{value:"yyyy-MM-dd HH:mm:ss",label:"yyyy-MM-dd HH:mm:ss"},{value:"Timestamp",label:"时间戳"}],statusList:[{value:500,label:"失败"},{value:502,label:"失败(超时)"},{value:200,label:"成功"},{value:0,label:"无"}]}},created:function(){this.fetchData(),this.getExecutor(),this.getJobIdList(),this.getJobProject(),this.getDataSourceList()},methods:{handleClose:function(e){this.$confirm("确认关闭？").then((function(t){e()})).catch((function(e){}))},getExecutor:function(){var e=this;i["e"]().then((function(t){var a=t.content;e.executorList=a}))},getJobIdList:function(){var e=this;i["f"]().then((function(t){var a=t.content;e.jobIdList=a}))},getJobProject:function(){var e=this;F["c"]().then((function(t){e.jobProjectList=t}))},getDataSourceList:function(){var e=this;L["d"]().then((function(t){e.dataSourceList=t}))},selectionChangeHandle:function(e){this.namelist=[];for(var t=0;t<e.length;t++)this.namelist=this.namelist.concat(e[t].id);this.nameLists=this.namelist},batchDelete:function(){var e=this;this.$confirm("确定批量删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.nameLists.length>0&&i["b"](e.nameLists+"").then((function(t){e.fetchData(),e.$notify({title:"批量删除操作",message:"批量删除成功",type:"success",duration:2e3})}))}))},fetchData:function(){var e=this;this.listLoading=!0,this.projectIds?this.listQuery.projectIds=this.projectIds.toString():this.listQuery.projectIds="",null!=this.addTimes&&this.addTimes.length?(this.listQuery.addTimeStart=this.addTimes[0]+" 00:00:00",this.listQuery.addTimeEnd=this.addTimes[1]+" 00:00:00"):(this.listQuery.addTimeStart="",this.listQuery.addTimeEnd=""),i["g"](this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},incStartTimeFormat:function(e){},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;"datax"!==this.temp.glueType||Object(D["c"])(this.jobJson)?this.$refs["dataForm"].validate((function(t){if(t){if(e.temp.childJobId){var a=[];for(var r in e.temp.childJobId)a.push(e.temp.childJobId[r].id);e.temp.childJobId=a.toString()}e.temp.jobJson=e.jobJson,e.temp.glueSource=e.glueSource,e.temp.executorHandler="datax"===e.temp.glueType?"executorJobHandler":"",e.partitionField&&(e.temp.partitionInfo=e.partitionField+","+e.timeOffset+","+e.timeFormatType),i["d"](e.temp).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}})):this.$notify({title:"Fail",message:"json格式错误",type:"error",duration:2e3})},handlerUpdate:function(e){var t=this;this.resetTemp(),this.temp=Object.assign({},e),this.glueSource=this.temp.glueSource;var a=[],r=[];if(this.jobIdList){for(var l in this.jobIdList)this.jobIdList[l].id!==this.temp.id&&r.push(this.jobIdList[l]);this.JobIdList=r}if(this.temp.childJobId){var o=this.temp.childJobId.split(",");for(var i in o)for(var n in this.jobIdList)this.jobIdList[n].id===parseInt(o[i])&&a.push(this.jobIdList[n]);this.temp.childJobId=a}if(this.temp.partitionInfo){var s=this.temp.partitionInfo.split(",");this.partitionField=s[0],this.timeOffset=s[1],this.timeFormatType=s[2]}this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},updateData:function(){var e=this;"datax"!==this.temp.glueType||Object(D["c"])(this.temp.jobJson)?this.$refs["dataForm"].validate((function(t){if(t){if(e.temp.childJobId){var a=[];for(var r in e.temp.childJobId)a.push(e.temp.childJobId[r].id);e.temp.childJobId=a.toString()}e.temp.executorHandler=(e.temp.glueType,"executorJobHandler"),e.temp.glueSource=e.glueSource,e.partitionField&&(e.temp.partitionInfo=e.partitionField+","+e.timeOffset+","+e.timeFormatType),i["m"](e.temp).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}})):this.$notify({title:"Fail",message:"json格式错误",type:"error",duration:2e3})},handlerDelete:function(e){var t=this;this.$confirm("确定删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i["i"](e.id).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))}))},handlerExecute:function(e){var t=this;this.$confirm("确定执行吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a={};a.jobId=e.id,a.executorParam=e.executorParam,i["l"](a).then((function(e){t.$notify({title:"执行操作",message:"执行成功",type:"success",duration:2e3})}))}))},handlerViewLog:function(e){this.$router.push({path:"/data/log",query:{jobId:e.id}})},handlerStart:function(e){var t=this;i["j"](e.id).then((function(e){t.$notify({title:"启动任务",message:"启动成功",type:"success",duration:2e3})}))},handlerStop:function(e){var t=this;i["k"](e.id).then((function(e){t.$notify({title:"停止任务",message:"停止成功",type:"success",duration:2e3})}))},changeSwitch:function(e){1===e.triggerStatus?this.handlerStart(e):this.handlerStop(e)},nextTriggerTime:function(e){var t=this;i["h"](e.jobCron).then((function(e){var a=e.content;t.triggerNextTimes=a.join("<br>")}))},loadById:function(e){var t=this;o["a"](e.jobGroup).then((function(e){t.registerNode=[];var a=e.content;t.registerNode.push(a)}))}}},J=M,P=(a("448c"),Object(g["a"])(J,r,l,!1,null,null,null));t["default"]=P.exports},"9c5e":function(e,t,a){"use strict";a("f709")},a53d:function(e,t,a){"use strict";a.d(t,"d",(function(){return l})),a.d(t,"e",(function(){return o})),a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return s}));var r=a("b775");function l(e){return Object(r["a"])({url:"/api/jobProject",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/api/jobProject",method:"put",data:e})}function i(e){return Object(r["a"])({url:"/api/jobProject",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/api/jobProject",method:"delete",params:e})}function s(e){return Object(r["a"])({url:"api/jobProject/list",method:"get",params:e})}},b74e:function(e,t,a){"use strict";a("255f")},f709:function(e,t,a){},fa7e:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"json-editor"},[a("textarea",{ref:"textarea"})])},l=[],o=(a("e9c4"),a("56b3")),i=a.n(o);a("0dd0"),a("a7be"),a("acdf"),a("f9d4"),a("8822"),a("d2de");a("ae67");var n={name:"JsonEditor",props:["value"],data:function(){return{jsonEditor:!1}},watch:{value:function(e){var t=this.jsonEditor.getValue();e!==t&&this.jsonEditor.setValue(JSON.stringify(this.value,null,2))}},mounted:function(){var e=this;this.jsonEditor=i.a.fromTextArea(this.$refs.textarea,{lineNumbers:!0,mode:"application/json",gutters:["CodeMirror-lint-markers"],theme:"rubyblue",lint:!0}),this.jsonEditor.setValue(JSON.stringify(this.value,null,2)),this.jsonEditor.on("change",(function(t){e.$emit("changed",t.getValue()),e.$emit("input",t.getValue())}))},methods:{getValue:function(){return this.jsonEditor.getValue()}}},s=n,u=(a("8e34"),a("2877")),c=Object(u["a"])(s,r,l,!1,null,"7b489788",null);t["a"]=c.exports},fc8d:function(e,t,a){"use strict";a("41c9")}}]);