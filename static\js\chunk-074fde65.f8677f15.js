(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-074fde65"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=l(),r=e-o,s=20,u=0;t="undefined"===typeof t?500:t;var d=function e(){u+=s;var l=Math.easeInOutQuad(u,o,r,t);i(l),u<t?n(e):a&&"function"===typeof a&&a()};d()}},1810:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return r})),a.d(t,"c",(function(){return s}));var n=a("b775");function i(e){return Object(n["a"])({url:"/api/devEnvUpload/remove",method:"post",params:e})}function l(e){return Object(n["a"])({url:"/api/devEnvUpload/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/api/devEnvUpload/add",method:"post",data:e})}function r(e){return Object(n["a"])({url:"/api/devEnvUpload/update",method:"post",data:e})}function s(){return Object(n["a"])({url:"/api/devEnvUpload/findDevName",method:"get"})}},67248:function(e,t,a){"use strict";a("8d41");var n="@@wavesContext";function i(e,t){function a(a){var n=Object.assign({},t.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},n),l=i.ele;if(l){l.style.position="relative",l.style.overflow="hidden";var o=l.getBoundingClientRect(),r=l.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",l.appendChild(r)),i.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(a.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(a.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=i.color,r.className="waves-ripple z-active",!1}}return e[n]?e[n].removeHandle=a:e[n]={removeHandle:a},a}var l={bind:function(e,t){e.addEventListener("click",i(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[n].removeHandle,!1),e.addEventListener("click",i(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[n].removeHandle,!1),e[n]=null,delete e[n]}},o=function(e){e.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(o)),l.install=o;t["a"]=l},"8bb3":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return o})),a.d(t,"f",(function(){return r})),a.d(t,"e",(function(){return s})),a.d(t,"d",(function(){return u}));var n=a("b775");function i(e){return Object(n["a"])({url:"api/devJar/add",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/api/devJar/update",method:"post",data:e})}function o(e){return e=Object.assign({sql_text:e.sql_text},e||{}),Object(n["a"])({url:"/api/deployTask/checkSQL",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/api/devTask/getAllJars",method:"get",params:e})}function s(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Object(n["a"])({url:"/api/devTask/upload",method:"post",data:e,timeout:0,onUploadProgress:function(e){var a=e.loaded/e.total*100|0;console.log("上传进度：".concat(a,"%")),null===t||void 0===t||t(a)}})}function u(e){return e=Object.assign({type:"SQL"},e||{}),Object(n["a"])({url:"/api/devJar/list",method:"get",params:e})}},"8d41":function(e,t,a){},"8d70":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.uploadData.uploading,expression:"uploadData.uploading",modifiers:{fullscreen:!0,lock:!0}}],staticClass:"app-container",attrs:{"element-loading-text":e.uploadData.process}},[a("div",{staticClass:"filter-container"},[a("div",{staticClass:"search-header"},[a("el-form",{attrs:{"label-suffix":"：","label-width":"138px",inline:""}},[a("el-form-item",{attrs:{label:"环境名称","label-width":"auto"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px","margin-right":"10px"},attrs:{clearable:"",placeholder:"环境名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}})],1),a("el-form-item",{attrs:{label:"JAR包名称"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px","margin-right":"10px"},attrs:{clearable:"",placeholder:"JAR包名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter(t)}},model:{value:e.listQuery.searchVal,callback:function(t){e.$set(e.listQuery,"searchVal",t)},expression:"listQuery.searchVal"}})],1)],1),a("div",{staticClass:"search-opt"},[a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item search",attrs:{type:"primary round"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item reset-btn",attrs:{type:"primary round"},on:{click:e.handleReset}},[e._v(" 重置 ")])],1)],1),a("el-divider"),a("el-button",{staticClass:"filter-item opt",staticStyle:{"margin-left":"10px"},attrs:{type:"success"},on:{click:e.handleAdd}},[e._v(" 新增作业 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[e._e(),a("el-table-column",{attrs:{label:"环境名称",align:"left",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),a("el-table-column",{attrs:{label:"环境地址",align:"left",width:"260"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.propValue))]}}])}),a("el-table-column",{attrs:{label:"JAR包名称",align:"left",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.jarName))]}}])}),a("el-table-column",{attrs:{label:"上传时间",width:"200",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.uploadTime)+" ")]}}])}),a("el-table-column",{attrs:{label:"运行主类",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.mainClass))]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left",width:"180","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return["deleted"!==n.status?a("span",{staticClass:"table-btn",on:{click:function(t){return e.handleDelete(n)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.pageNo,limit:e.listQuery.pageSize},on:{"update:page":function(t){return e.$set(e.listQuery,"pageNo",t)},"update:limit":function(t){return e.$set(e.listQuery,"pageSize",t)},pagination:e.fetchData}}),a("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"600px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[a("el-form-item",{attrs:{label:"环境名称",prop:"type"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"环境名称"},model:{value:e.temp.type,callback:function(t){e.$set(e.temp,"type",t)},expression:"temp.type"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:e.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(t){e.dialogPluginVisible=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogPvVisible=!1}}},[e._v("Confirm")])],1)],1)],1)},i=[],l=(a("b0c0"),a("1810")),o=a("67248"),r=a("333d"),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"base-widget-demo"},[e.options.observer?a("BaseWidget",{ref:"baseWidget",attrs:{"data-example":e.dataExample,"default-data":e.defaultData,options:e.options,"on-submit":e.onSubmit,mode:e.mode},scopedSlots:e._u([{key:"slot-devobj",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择","value-key":"id"},on:{change:function(a){return e.devObjChange(t.slotScope.formData)}},model:{value:t.slotScope.formData.devObj,callback:function(a){e.$set(t.slotScope.formData,"devObj",a)},expression:"scope.slotScope.formData.devObj"}},e._l(e.devNameArr,(function(e,t){return a("el-option",{key:"dev-obj-"+t,attrs:{label:e.name,value:e}})})),1)]}},{key:"slot-jarname",fn:function(t){return[a("span",{staticStyle:{display:"flex"}},[a("el-input",{attrs:{title:t.slotScope.formData.jarName,disabled:!0,placeholder:"请上传JAR包"},model:{value:t.slotScope.formData.jarName,callback:function(a){e.$set(t.slotScope.formData,"jarName",a)},expression:"scope.slotScope.formData.jarName"}}),a("el-button",{staticStyle:{"margin-left":"5px"},attrs:{disabled:!t.slotScope.formData.propValue},on:{click:function(a){return e.handleUpload(t.slotScope.formData)}}},[e._v(" 选择 ")])],1)]}}],null,!1,3294071903)}):e._e()],1)},u=[],d=a("eeb0"),c=a("57e7"),p={components:{BaseWidget:d["a"]},mixins:[c["a"]],props:{defaultData:{type:Object,default:null},defaultOptions:{type:Object,default:null},mode:{type:String,default:"edit"}},data:function(){return{dataExample:{devObj:null,file:null,id:"",jarName:"",propValue:""},options:{observer:null,afterInit:null,rules:{devObj:[{required:!0,message:"必须选择",trigger:"blur"}],propValue:[{required:!0,message:"必须填写",trigger:"blur"}],jarName:[{required:!0,message:"必须上传",trigger:"blur"}]},formItems:[{label:"环境名称",prop:"devObj",type:"slot",options:{typeFun:function(){return"slot"},opt:function(e){return e.name}}},{label:"环境地址",prop:"propValue",type:"input",options:{disabled:!0}},{label:"文件名",prop:"jarName",type:"slot",options:{typeFun:function(){return"slot"}}}]},devNameArr:[]}},mounted:function(){var e=this;Object(l["c"])().then((function(t){e.devNameArr=t.content.data})),this.init()},methods:{init:function(){},onSubmit:function(e){console.log("formData = ",e)},devObjChange:function(e){e.devObj?(e.name=e.devObj.id,e.propValue=e.devObj.propValue):(e.name="",e.propValue="")},handleUpload:function(e){var t=this;return this.$selectFile({}).then((function(a){var n=a[0];n.size>209715200?t.$notify({title:"提示",message:"请确保上传文件总大小不超过200M",type:"info",duration:3e3}):(e.jarName=n.name,e.file=n)}))}}},f=p,m=(a("de2e"),a("2877")),h=Object(m["a"])(f,s,u,!1,null,"7840149c",null),v=h.exports,b=a("8bb3"),g={name:"DevEnvSetting",components:{Pagination:r["a"]},directives:{waves:o["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:10,searchVal:"",name:""},pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"Edit",create:"新增作业"},rules:{name:[{required:!0,message:"this is required",trigger:"blur"}],description:[{required:!0,message:"this is required",trigger:"blur"}]},temp:{id:void 0,name:"",description:""},visible:!0,uploadData:{uploading:!1,process:""}}},created:function(){this.fetchData()},methods:{handleAdd:function(){var e=this;return this.$dialog.show("新增作业",v,{area:"500px"},{mode:"edit"}).then((function(t){var a=new FormData;return a.append("file",t.file),a.append("enctype","multipart/form-data"),a.append("filePath",t.file.name),a.append("tasktype","Flink"),a.append("propValue",t.propValue),a.append("devName",t.name),e.uploadData.uploading=!0,e.uploadData.process="0%",Object(b["e"])(a,(function(t){e.uploadData.process=t+"%"})).then((function(t){return e.uploadData.uploading=!1,e.uploadData.process="",e.fetchData()})).catch((function(t){e.uploadData.uploading=!1,e.uploadData.process=""}))}))},handleUpload:function(){var e=this;return this.$dialog.show("Jar上传",v,{area:"500px"},{mode:"edit"}).then((function(t){console.log("row = ",t);var a=new FormData;a.append("file",t.file),a.append("filePath",t.fileName),a.append("tasktype","Flink"),a.append("propValue",t.devObj.propValue),a.append("devName",t.devObj.name),e.uploadData.uploading=!0,e.uploadData.process="0%",Object(b["e"])(a,(function(t){e.uploadData.process=t+"%"})).then((function(t){e.uploadData.uploading=!1,e.uploadData.process="",e.fetchData(),e.$notify({title:"成功",message:"上传成功",type:"success",duration:2e3})})).catch((function(t){e.uploadData.uploading=!1,e.uploadData.process=""}))}))},handleReset:function(){this.listQuery.name="",this.listQuery.searchVal="",this.fetchData()},fetchData:function(){var e=this;return this.listLoading=!0,l["d"](this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&l["a"](e.temp).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}))},handleUpdate:function(e){var t=this;this.temp=Object.assign({},e),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a=Object.assign({},e.temp);l["e"](a).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(e){var t=this;console.log("删除");var a=Object.assign({},e);l["b"]({jid:a.jid,propValue:a.propValue}).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))}}},y=g,w=Object(m["a"])(y,n,i,!1,null,null,null);t["default"]=w.exports},b4f89:function(e,t,a){},de2e:function(e,t,a){"use strict";a("b4f89")}}]);