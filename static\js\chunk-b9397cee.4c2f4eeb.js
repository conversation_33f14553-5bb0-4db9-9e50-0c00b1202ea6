(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b9397cee"],{"2a666":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"mycontent"},[n("el-button",{attrs:{icon:"el-icon-d-arrow-left",type:"info",plain:"",size:"small"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")]),n("h2",[t._v("修改API")]),n("common",{ref:"apiEditCommon",attrs:{id:t.$route.query.id}}),n("el-button",{staticStyle:{margin:"10px 0"},on:{click:t.save}},[t._v("保存")])],1)},r=[],o=(n("b0c0"),n("e9c4"),n("eb18")),i={data:function(){return{}},components:{common:o["a"]},methods:{save:function(){var t=this.$refs.apiEditCommon.detail,e=this.$store.getters.getSql,n={name:t.name,path:t.path,note:t.note,groupId:t.groupId,previlege:t.previlege,cachePlugin:t.cachePlugin,transformPlugin:t.transformPlugin,cachePluginParams:t.cachePluginParams,transformPluginParams:t.transformPluginParams,datasourceId:this.$refs.apiEditCommon.$refs.sqlCode.datasourceId,sqlList:e,params:JSON.stringify(t.params),id:this.$route.query.id};""!=n.sql&&null!=n.datasourceId&&null!=n.name&&null!=n.path&&null!=n.groupId||this.$message.error("必填项未填")}},created:function(){}},s=i,c=(n("b716"),n("2877")),u=Object(c["a"])(s,a,r,!1,null,"1c38d7f5",null);e["default"]=u.exports},"9df0":function(t,e,n){},b716:function(t,e,n){"use strict";n("9df0")}}]);