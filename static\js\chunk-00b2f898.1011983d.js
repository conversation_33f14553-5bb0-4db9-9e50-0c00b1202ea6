(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-00b2f898"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(e,t,a){var s=l(),o=e-s,r=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=r;var l=Math.easeInOutQuad(u,s,o,t);n(l),u<t?i(e):a&&"function"===typeof a&&a()};c()}},67248:function(e,t,a){"use strict";a("8d41");var i="@@wavesContext";function n(e,t){function a(a){var i=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),l=n.ele;if(l){l.style.position="relative",l.style.overflow="hidden";var s=l.getBoundingClientRect(),o=l.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":(o=document.createElement("span"),o.className="waves-ripple",o.style.height=o.style.width=Math.max(s.width,s.height)+"px",l.appendChild(o)),n.type){case"center":o.style.top=s.height/2-o.offsetHeight/2+"px",o.style.left=s.width/2-o.offsetWidth/2+"px";break;default:o.style.top=(a.pageY-s.top-o.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",o.style.left=(a.pageX-s.left-o.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return o.style.backgroundColor=n.color,o.className="waves-ripple z-active",!1}}return e[i]?e[i].removeHandle=a:e[i]={removeHandle:a},a}var l={bind:function(e,t){e.addEventListener("click",n(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[i].removeHandle,!1),e.addEventListener("click",n(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[i].removeHandle,!1),e[i]=null,delete e[i]}},s=function(e){e.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(s)),l.install=s;t["a"]=l},"6b51a":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"登录账号"},model:{value:e.listQuery.user,callback:function(t){e.$set(e.listQuery,"user",t)},expression:"listQuery.user"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"用户地址"},model:{value:e.listQuery.address,callback:function(t){e.$set(e.listQuery,"address",t)},expression:"listQuery.address"}}),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.clearData}},[e._v(" 清除日志 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"left",label:"序号",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),a("el-table-column",{attrs:{label:"菜单名称",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.menuname))]}}])}),a("el-table-column",{attrs:{label:"目录名称",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.catalogname))]}}])}),a("el-table-column",{attrs:{label:"操作名称",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.operate))]}}])}),a("el-table-column",{attrs:{label:"用户账号",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.user))]}}])}),a("el-table-column",{attrs:{label:"主机地址",align:"left",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.address))]}}])}),a("el-table-column",{attrs:{label:"登录地点",align:"left",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.region))]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.createtime))]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return["deleted"!==i.status?a("el-button",{attrs:{size:"small",icon:"el-icon-delete",type:"danger"},on:{click:function(t){return e.handleDelete(i)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}}),a("el-dialog",{attrs:{title:"清除日志",visible:e.dialogFormVisible,width:"500px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",staticStyle:{width:"80%","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"right","label-width":"100px"}},[a("el-form-item",{attrs:{label:"清除周期",prop:"temp"}},[a("el-select",{attrs:{placeholder:"选择清除周期"},model:{value:e.temp.cycle,callback:function(t){e.$set(e.temp,"cycle",t)},expression:"temp.cycle"}},[a("el-option",{attrs:{label:"1个月之前",value:"1"}}),a("el-option",{attrs:{label:"2个月之前",value:"2"}}),a("el-option",{attrs:{label:"半年之前",value:"3"}})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitClearData(e.temp)}}},[e._v("确定")])],1)],1)],1)},n=[],l=(a("b0c0"),a("b775"));function s(e){return Object(l["a"])({url:"/api/base/resource/add",method:"post",data:e})}function o(e){return Object(l["a"])({url:"/api/base/resource/update",method:"post",data:e})}function r(e){return Object(l["a"])({url:"/api/log/removeLog?id="+e,method:"post"})}function u(e){return Object(l["a"])({url:"/api/log/list",method:"get",params:e})}function c(e){return Object(l["a"])({url:"/api/log/removeByData?indexType="+e,method:"post"})}var d=a("67248"),f=a("333d"),m={name:"User",components:{Pagination:f["a"]},directives:{waves:d["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{current:1,size:10,user:"",address:""},roles:["ROLE_USER","ROLE_ADMIN"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"Edit",create:"Create"},rules:{role:[{required:!0,message:"role is required",trigger:"change"}],name:[{required:!0,message:"name is required",trigger:"blur"}],password:[{required:!1,message:"password is required",trigger:"blur"}]},temp:{id:void 0,role:"",name:"",password:"",permission:"",resource_address:"",cycle:""},resetTemp:function(){this.temp=this.$options.data().temp}}},created:function(){this.fetchData()},methods:{fetchData:function(){var e=this;this.listLoading=!0,u(this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},submitClearData:function(e){var t=this;c(e.cycle).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},clearData:function(){this.dialogFormVisible=!0},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={name:e.temp.name,resource_address:e.temp.resource_address};s(a).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}}))},handleVisit:function(e){window.open(e.resource_address)},handleUpdate:function(e){var t=this;this.temp=Object.assign({},e),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={id:e.temp.id,name:e.temp.name,resource_address:e.temp.resource_address},i=Object.assign({},a);o(i).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(e){var t=this;r(e.id).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))}}},p=m,h=a("2877"),g=Object(h["a"])(p,i,n,!1,null,null,null);t["default"]=g.exports},"8d41":function(e,t,a){}}]);