(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-49a31025"],{"7a86":function(t,e,n){"use strict";n("8194")},8194:function(t,e,n){},ab3b:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("split-pane",{attrs:{split:"vertical"},on:{resize:t.resize}},[n("template",{slot:"paneL"},[n("el-input",{attrs:{type:"textarea",rows:36,placeholder:"请输入内容",clearable:""},on:{change:t.originChange},model:{value:t.originText,callback:function(e){t.originText=e},expression:"originText"}})],1),n("template",{slot:"paneR"},[n("div",{staticClass:"editor-container"},[n("JSONEditor",{attrs:{json:t.formatedValue}})],1)])],2)],1)},i=[],o=(n("b64b"),n("19ab")),r=n.n(o),s=n("33c3"),c={name:"JsonFormat",components:{splitPane:r.a,JSONEditor:s["a"]},data:function(){return{originText:"",formatedValue:""}},watch:{originText:function(t){try{this.formatedValue=JSON.parse(t)}catch(e){}}},methods:{resize:function(){},originChange:function(t){}}},l=c,u=(n("7a86"),n("2877")),p=Object(u["a"])(l,a,i,!1,null,"7505e034",null);e["default"]=p.exports}}]);