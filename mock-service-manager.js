// Mock服务管理器 - 统一管理所有Mock服务
// 提供服务状态监控、配置管理和调试功能

(function() {
    'use strict';
    
    console.log('🎛️ 启动Mock服务管理器');
    
    class MockServiceManager {
        constructor() {
            this.services = {
                login: { name: '自动登录服务', status: 'running', file: 'auto-skip-login.js' },
                data: { name: '数据生成服务', status: 'running', file: 'mock-data-service.js' },
                api: { name: 'API拦截服务', status: 'running', file: 'mock-api-service.js' },
                websocket: { name: 'WebSocket服务', status: 'running', file: 'mock-websocket-service.js' },
                storage: { name: '存储服务', status: 'running', file: 'mock-storage-service.js' }
            };
            
            this.config = {
                debug: true,
                logLevel: 'info',
                apiDelay: { min: 100, max: 500 },
                enableCache: true,
                enableRealtime: true
            };
            
            this.stats = {
                apiCalls: 0,
                websocketMessages: 0,
                storageOperations: 0,
                startTime: new Date()
            };
            
            this.init();
        }
        
        init() {
            this.createDebugPanel();
            this.setupEventListeners();
            this.startMonitoring();
            console.log('🎛️ Mock服务管理器初始化完成');
        }
        
        // 创建调试面板
        createDebugPanel() {
            if (!this.config.debug) return;
            
            const panel = document.createElement('div');
            panel.id = 'mock-debug-panel';
            panel.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                width: 300px;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                font-size: 12px;
                z-index: 10000;
                max-height: 400px;
                overflow-y: auto;
                display: none;
            `;
            
            panel.innerHTML = `
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                    <strong>🎛️ Mock服务面板</strong>
                    <button id="mock-panel-close" style="background: #f56c6c; color: white; border: none; padding: 2px 6px; border-radius: 3px; cursor: pointer;">×</button>
                </div>
                <div id="mock-services-status"></div>
                <div id="mock-stats" style="margin-top: 10px;"></div>
                <div id="mock-logs" style="margin-top: 10px; max-height: 150px; overflow-y: auto; background: rgba(255,255,255,0.1); padding: 5px; border-radius: 3px;"></div>
                <div style="margin-top: 10px;">
                    <button id="mock-clear-logs" style="background: #409eff; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; margin-right: 5px;">清空日志</button>
                    <button id="mock-export-data" style="background: #67c23a; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer;">导出数据</button>
                </div>
            `;
            
            document.body.appendChild(panel);
            
            // 添加事件监听
            document.getElementById('mock-panel-close').onclick = () => {
                panel.style.display = 'none';
            };
            
            document.getElementById('mock-clear-logs').onclick = () => {
                document.getElementById('mock-logs').innerHTML = '';
            };
            
            document.getElementById('mock-export-data').onclick = () => {
                this.exportAllData();
            };
            
            // 添加快捷键显示/隐藏面板
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.shiftKey && e.key === 'M') {
                    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
                }
            });
            
            this.debugPanel = panel;
            this.updateDebugPanel();
        }
        
        // 更新调试面板
        updateDebugPanel() {
            if (!this.debugPanel) return;
            
            const servicesHtml = Object.entries(this.services).map(([key, service]) => {
                const statusColor = service.status === 'running' ? '#67c23a' : '#f56c6c';
                return `<div>• ${service.name}: <span style="color: ${statusColor}">${service.status}</span></div>`;
            }).join('');
            
            const statsHtml = `
                <div><strong>📊 统计信息</strong></div>
                <div>API调用: ${this.stats.apiCalls}</div>
                <div>WebSocket消息: ${this.stats.websocketMessages}</div>
                <div>存储操作: ${this.stats.storageOperations}</div>
                <div>运行时间: ${Math.floor((Date.now() - this.stats.startTime) / 1000)}秒</div>
            `;
            
            document.getElementById('mock-services-status').innerHTML = servicesHtml;
            document.getElementById('mock-stats').innerHTML = statsHtml;
        }
        
        // 添加日志到调试面板
        addLog(message, type = 'info') {
            if (!this.debugPanel) return;
            
            const logsContainer = document.getElementById('mock-logs');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#409eff',
                success: '#67c23a',
                warning: '#e6a23c',
                error: '#f56c6c'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            // 限制日志数量
            if (logsContainer.children.length > 100) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
        }
        
        // 设置事件监听
        setupEventListeners() {
            // 监听API调用
            const originalFetch = window.fetch;
            window.fetch = (...args) => {
                this.stats.apiCalls++;
                this.addLog(`API调用: ${args[0]}`, 'info');
                this.updateDebugPanel();
                return originalFetch.apply(window, args);
            };
            
            // 监听存储操作
            const originalSetItem = localStorage.setItem;
            localStorage.setItem = (...args) => {
                this.stats.storageOperations++;
                this.updateDebugPanel();
                return originalSetItem.apply(localStorage, args);
            };
        }
        
        // 开始监控
        startMonitoring() {
            setInterval(() => {
                this.updateDebugPanel();
            }, 5000);
        }
        
        // 获取服务状态
        getServiceStatus(serviceName) {
            return this.services[serviceName]?.status || 'unknown';
        }
        
        // 设置服务状态
        setServiceStatus(serviceName, status) {
            if (this.services[serviceName]) {
                this.services[serviceName].status = status;
                this.addLog(`服务状态更新: ${serviceName} -> ${status}`, 'info');
                this.updateDebugPanel();
            }
        }
        
        // 获取统计信息
        getStats() {
            return { ...this.stats };
        }
        
        // 重置统计信息
        resetStats() {
            this.stats = {
                apiCalls: 0,
                websocketMessages: 0,
                storageOperations: 0,
                startTime: new Date()
            };
            this.addLog('统计信息已重置', 'success');
            this.updateDebugPanel();
        }
        
        // 导出所有数据
        exportAllData() {
            const data = {
                services: this.services,
                stats: this.stats,
                config: this.config,
                storageData: window.mockStorage?.export() || {},
                mockData: {
                    users: window.mockDataGenerator?.users || [],
                    dashboardData: window.mockDataGenerator?.dashboardData || {},
                    tableData: window.mockDataGenerator?.tableData || [],
                    chartData: window.mockDataGenerator?.chartData || {}
                },
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mock-data-export-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.addLog('数据导出完成', 'success');
        }
        
        // 测试所有服务
        testAllServices() {
            this.addLog('开始测试所有服务...', 'info');
            
            // 测试API服务
            fetch('/api/user/info')
                .then(() => this.addLog('API服务测试通过', 'success'))
                .catch(() => this.addLog('API服务测试失败', 'error'));
            
            // 测试WebSocket服务
            try {
                const ws = new WebSocket('ws://localhost/test');
                ws.onopen = () => {
                    this.addLog('WebSocket服务测试通过', 'success');
                    ws.close();
                };
                ws.onerror = () => this.addLog('WebSocket服务测试失败', 'error');
            } catch (e) {
                this.addLog('WebSocket服务测试失败', 'error');
            }
            
            // 测试存储服务
            try {
                window.mockStorage?.save('test', 'value');
                const value = window.mockStorage?.load('test');
                if (value === 'value') {
                    this.addLog('存储服务测试通过', 'success');
                } else {
                    this.addLog('存储服务测试失败', 'error');
                }
                window.mockStorage?.delete('test');
            } catch (e) {
                this.addLog('存储服务测试失败', 'error');
            }
        }
        
        // 显示帮助信息
        showHelp() {
            const helpMessage = `
🎛️ Mock服务管理器帮助

快捷键:
- Ctrl+Shift+M: 显示/隐藏调试面板

可用方法:
- mockServiceManager.testAllServices(): 测试所有服务
- mockServiceManager.resetStats(): 重置统计信息
- mockServiceManager.exportAllData(): 导出所有数据
- mockServiceManager.showHelp(): 显示此帮助

服务状态:
- 绿色: 运行中
- 红色: 已停止

日志类型:
- 蓝色: 信息
- 绿色: 成功
- 橙色: 警告
- 红色: 错误
            `;
            
            console.log(helpMessage);
            this.addLog('帮助信息已输出到控制台', 'info');
        }
    }
    
    // 创建全局服务管理器实例
    window.mockServiceManager = new MockServiceManager();
    
    // 页面加载完成后显示欢迎信息
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            console.log(`
🎉 数据中台Mock服务已全部启动！

📋 已启用的服务:
✅ 自动登录服务 - 跳过登录验证
✅ 数据生成服务 - 提供模拟数据
✅ API拦截服务 - 模拟所有接口
✅ WebSocket服务 - 实时数据推送
✅ 存储服务 - 本地数据持久化

🎛️ 调试面板:
按 Ctrl+Shift+M 打开调试面板

🔧 可用命令:
- mockServiceManager.testAllServices() - 测试所有服务
- mockServiceManager.showHelp() - 显示帮助
- mockStorage.save(key, value) - 保存数据
- mockStorage.load(key) - 加载数据

现在您可以正常使用数据中台的所有功能了！
            `);
            
            window.mockServiceManager.addLog('所有Mock服务启动完成', 'success');
            window.mockServiceManager.testAllServices();
        }, 1000);
    });
    
    console.log('✅ Mock服务管理器初始化完成');
    
})();
