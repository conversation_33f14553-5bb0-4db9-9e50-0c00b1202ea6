(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5d36bb00"],{"031a":function(e,a,t){"use strict";t("d785")},1810:function(e,a,t){"use strict";t.d(a,"b",(function(){return l})),t.d(a,"d",(function(){return n})),t.d(a,"a",(function(){return o})),t.d(a,"e",(function(){return s})),t.d(a,"c",(function(){return i}));var r=t("b775");function l(e){return Object(r["a"])({url:"/api/devEnvUpload/remove",method:"post",params:e})}function n(e){return Object(r["a"])({url:"/api/devEnvUpload/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/api/devEnvUpload/add",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/api/devEnvUpload/update",method:"post",data:e})}function i(){return Object(r["a"])({url:"/api/devEnvUpload/findDevName",method:"get"})}},"8bb3":function(e,a,t){"use strict";t.d(a,"a",(function(){return l})),t.d(a,"b",(function(){return n})),t.d(a,"c",(function(){return o})),t.d(a,"f",(function(){return s})),t.d(a,"e",(function(){return i})),t.d(a,"d",(function(){return u}));var r=t("b775");function l(e){return Object(r["a"])({url:"api/devJar/add",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/api/devJar/update",method:"post",data:e})}function o(e){return e=Object.assign({sql_text:e.sql_text},e||{}),Object(r["a"])({url:"/api/deployTask/checkSQL",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/api/devTask/getAllJars",method:"get",params:e})}function i(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Object(r["a"])({url:"/api/devTask/upload",method:"post",data:e,timeout:0,onUploadProgress:function(e){var t=e.loaded/e.total*100|0;console.log("上传进度：".concat(t,"%")),null===a||void 0===a||a(t)}})}function u(e){return e=Object.assign({type:"SQL"},e||{}),Object(r["a"])({url:"/api/devJar/list",method:"get",params:e})}},"968c":function(e,a,t){"use strict";t("b256")},b256:function(e,a,t){},b694:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"dev-sql"},[e.sqlDataList?t("div",{staticClass:"nav-bar"},[t("div",{staticClass:"search"},[t("el-input",{attrs:{size:"small","prefix-icon":"el-icon-search",placeholder:"任务名称"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleSearch(a)}},model:{value:e.searchInput,callback:function(a){e.searchInput=a},expression:"searchInput"}}),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"small"},on:{click:e.handleSearch}},[e._v(" 搜索 ")])],1),t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleAdd}},[e._v("新增")]),t("ul",e._l(e.sqlDataList,(function(a,r){return t("li",{key:"sql-data-list-"+r,class:{"cur-li":e.curData&&e.curData.id===a.id},on:{click:function(t){return e.setCurData(a)}}},[t("span",{staticClass:"icon"},[e._v(e._s(a.tasktype))]),t("span",{staticClass:"name",staticStyle:{"margin-left":"5px"}},[e._v(e._s(a.name))])])})),0)],1):e._e(),t("div",{staticClass:"app-main"},[t("div",{staticClass:"app-main--header"},[t("p",{staticClass:"label"},[e._v("任务编辑")]),t("el-button",{attrs:{type:"primary",size:"small",disabled:!e.curData},on:{click:function(){e.curData.sql_text=e.sqlText,e.updateData(e.curData)}}},[e._v(" 确认 ")])],1),e.curData?t("div",{staticClass:"editor"},[t("el-scrollbar",[t("el-form",{ref:"infoDialogForm",attrs:{model:e.curData,rules:e.rules,size:"medium","label-width":"160px"}},[t("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[t("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:e.curData.name,callback:function(a){e.$set(e.curData,"name","string"===typeof a?a.trim():a)},expression:"curData.name"}})],1),t("el-form-item",{attrs:{label:"任务类型",prop:"tasktype"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.curData.tasktype,callback:function(a){e.$set(e.curData,"tasktype",a)},expression:"curData.tasktype"}},[t("el-option",{attrs:{label:"Flink",value:"Flink"}}),t("el-option",{attrs:{label:"Spark",value:"Spark"}})],1)],1),t("el-form-item",{attrs:{label:"运行方式",prop:"runtype"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.curData.runtype,callback:function(a){e.$set(e.curData,"runtype",a)},expression:"curData.runtype"}},[t("el-option",{attrs:{label:"stantalone",value:"stantalone"}}),t("el-option",{attrs:{label:"yarn",value:"yarn"}})],1)],1),t("el-form-item",{attrs:{label:"环境名称",prop:"propvalue"}},[t("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:e.propvalueChange},model:{value:e.curData.propvalue,callback:function(a){e.$set(e.curData,"propvalue",a)},expression:"curData.propvalue"}},e._l(e.devNameArr,(function(e,a){return t("el-option",{key:"dev-obj-"+a,attrs:{label:e.name,value:e.propValue}})})),1)],1),t("el-form-item",{attrs:{label:"JAR包选择",prop:"jid"}},[t("el-select",{attrs:{disabled:!e.curData.propvalue,placeholder:"请选择"},model:{value:e.curData.jid,callback:function(a){e.$set(e.curData,"jid",a)},expression:"curData.jid"}},e._l(e.jarNameArr,(function(e,a){return t("el-option",{key:"dev-obj-"+a,attrs:{label:e.jarName,value:e.jid}})})),1)],1),t("el-form-item",{attrs:{label:"任务调度内存(MB)",prop:"jobManagerMemory"}},[t("el-input-number",{attrs:{clearable:!0,min:0,placeholder:"任务调度内存"},model:{value:e.curData.jobManagerMemory,callback:function(a){e.$set(e.curData,"jobManagerMemory",a)},expression:"curData.jobManagerMemory"}})],1),t("el-form-item",{attrs:{label:"任务执行内存(MB) ",prop:"taskManagerMemory"}},[t("el-input-number",{attrs:{clearable:!0,min:0,placeholder:"任务执行内存"},model:{value:e.curData.taskManagerMemory,callback:function(a){e.$set(e.curData,"taskManagerMemory",a)},expression:"curData.taskManagerMemory"}})],1),t("el-form-item",{attrs:{label:"任务槽数量",prop:"slot"}},[t("el-input-number",{attrs:{clearable:!0,min:0,placeholder:"任务槽数量"},model:{value:e.curData.slot,callback:function(a){e.$set(e.curData,"slot",a)},expression:"curData.slot"}})],1),t("el-form-item",{attrs:{label:"并行度",prop:"parameter"}},[t("el-input-number",{attrs:{clearable:!0,min:1,placeholder:"并行度"},model:{value:e.curData.parameter,callback:function(a){e.$set(e.curData,"parameter",a)},expression:"curData.parameter"}})],1),t("el-form-item",{attrs:{label:"运行主类",prop:"mainClass"}},[t("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:e.curData.mainClass,callback:function(a){e.$set(e.curData,"mainClass","string"===typeof a?a.trim():a)},expression:"curData.mainClass"}})],1),t("el-form-item",{attrs:{label:"运行参数",prop:"run_param"}},[t("el-input",{attrs:{clearable:!0,placeholder:"多个参数以空格分隔"},model:{value:e.curData.run_param,callback:function(a){e.$set(e.curData,"run_param",a)},expression:"curData.run_param"}})],1),t("el-form-item",{attrs:{label:"任务描述",prop:"task_describe"}},[t("el-input",{attrs:{clearable:!0,type:"textarea",autosize:{minRows:2,maxRows:4},placeholder:"请输入"},model:{value:e.curData.task_describe,callback:function(a){e.$set(e.curData,"task_describe","string"===typeof a?a.trim():a)},expression:"curData.task_describe"}})],1)],1)],1),t("div",{staticClass:"result",domProps:{innerHTML:e._s(e.jsonResult)}})],1):e._e()]),t("InfoDialog",{ref:"InfoDialog"})],1)},l=[],n=(t("b0c0"),t("e9c4"),t("d3b7"),t("159b"),function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("el-dialog",{ref:"InfoDialog",attrs:{visible:e.dialogShow,title:e.infoTitle,width:"750px","append-to-body":""},on:{"update:visible":function(a){e.dialogShow=a},close:e.close},scopedSlots:e._u([{key:"footer",fn:function(){return[t("div",{staticClass:"dialog-footer"},[t("el-button",{attrs:{type:"info"},on:{click:e.handleCancel}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确认")])],1)]},proxy:!0}])},[t("el-form",{ref:"infoDialogForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"120px"}},[t("el-row",{attrs:{gutter:24}},[t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[t("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:e.formData.name,callback:function(a){e.$set(e.formData,"name","string"===typeof a?a.trim():a)},expression:"formData.name"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"任务类型",prop:"tasktype"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.tasktype,callback:function(a){e.$set(e.formData,"tasktype",a)},expression:"formData.tasktype"}},[t("el-option",{attrs:{label:"Flink",value:"Flink"}}),t("el-option",{attrs:{label:"Spark",value:"Spark"}})],1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"运行方式",prop:"runtype"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.runtype,callback:function(a){e.$set(e.formData,"runtype",a)},expression:"formData.runtype"}},[t("el-option",{attrs:{label:"stantalone",value:"stantalone"}}),t("el-option",{attrs:{label:"yarn",value:"yarn"}})],1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"环境名称",prop:"propvalue"}},[t("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:e.propvalueChange},model:{value:e.formData.propvalue,callback:function(a){e.$set(e.formData,"propvalue",a)},expression:"formData.propvalue"}},e._l(e.devNameArr,(function(e,a){return t("el-option",{key:"dev-obj-"+a,attrs:{label:e.name,value:e.propValue}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"JAR包选择",prop:"jid"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.formData.propvalue,placeholder:"请选择"},model:{value:e.formData.jid,callback:function(a){e.$set(e.formData,"jid",a)},expression:"formData.jid"}},e._l(e.jarNameArr,(function(e,a){return t("el-option",{key:"dev-obj-"+a,attrs:{label:e.jarName,value:e.jid}})})),1)],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"任务调度内存(MB)",prop:"jobManagerMemory"}},[t("el-input-number",{attrs:{clearable:!0,min:0,placeholder:"任务调度内存"},model:{value:e.formData.jobManagerMemory,callback:function(a){e.$set(e.formData,"jobManagerMemory",a)},expression:"formData.jobManagerMemory"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"任务执行内存(MB) ",prop:"taskManagerMemory"}},[t("el-input-number",{attrs:{clearable:!0,min:0,placeholder:"任务执行内存"},model:{value:e.formData.taskManagerMemory,callback:function(a){e.$set(e.formData,"taskManagerMemory",a)},expression:"formData.taskManagerMemory"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"任务槽数量",prop:"slot"}},[t("el-input-number",{attrs:{clearable:!0,min:0,placeholder:"任务槽数量"},model:{value:e.formData.slot,callback:function(a){e.$set(e.formData,"slot",a)},expression:"formData.slot"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"运行主类",prop:"mainClass"}},[t("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:e.formData.mainClass,callback:function(a){e.$set(e.formData,"mainClass","string"===typeof a?a.trim():a)},expression:"formData.mainClass"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:"并行度",prop:"parameter"}},[t("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:e.formData.parameter,callback:function(a){e.$set(e.formData,"parameter","string"===typeof a?a.trim():a)},expression:"formData.parameter"}})],1)],1),t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"运行参数",prop:"run_param"}},[t("el-input",{attrs:{clearable:!0,placeholder:"多个参数以空格分隔"},model:{value:e.formData.run_param,callback:function(a){e.$set(e.formData,"run_param",a)},expression:"formData.run_param"}})],1)],1),t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:"任务描述",prop:"task_describe"}},[t("el-input",{attrs:{clearable:!0,type:"textarea",autosize:{minRows:2,maxRows:4},placeholder:"请输入"},model:{value:e.formData.task_describe,callback:function(a){e.$set(e.formData,"task_describe","string"===typeof a?a.trim():a)},expression:"formData.task_describe"}})],1)],1)],1)],1)],1)}),o=[],s=t("1810"),i=t("8bb3"),u={name:"ChangePassword",data:function(){return{rules:{name:[{required:!0,message:"不能为空",trigger:"blur"}],tasktype:[{required:!0,message:"不能为空",trigger:"blur"}],runtype:[{required:!0,message:"不能为空",trigger:"blur"}],sql_text:[{required:!0,message:"不能为空",trigger:"blur"}],run_param:[{required:!0,message:"不能为空",trigger:"blur"}],task_describe:[{required:!0,message:"不能为空",trigger:"blur"}],propvalue:[{required:!0,message:"不能为空",trigger:"blur"}],jarname:[{required:!0,message:"不能为空",trigger:"blur"}],mainClass:[{required:!0,message:"不能为空",trigger:"blur"}],parameter:[{required:!0,message:"不能为空",trigger:"blur"}],jid:[{required:!0,message:"不能为空",trigger:"blur"}],jobManagerMemory:[{required:!0,message:"任务调度内存不能为空",trigger:"blur"}],taskManagerMemory:[{required:!0,message:"任务执行内存不能为空",trigger:"blur"}],slot:[{required:!0,message:"任务槽数量不能为空",trigger:"blur"}]},dialogShow:!1,onClose:null,onSubmit:null,infoTitle:"详细信息",defaultFormData:{id:"",name:"",type:"JAR",tasktype:"Flink",runtype:"",run_param:"",sql_text:"",task_describe:"",propvalue:"",jarname:"",jid:"",mainClass:"",parameter:""},formData:null,devNameArr:[],jarNameArr:[]}},created:function(){var e=this;this.formData=Object.assign({},this.defaultFormData),Object(s["c"])().then((function(a){e.devNameArr=a.content.data}))},methods:{propvalueChange:function(){var e=this;this.formData.jarname="",this.formData.jid="",this.formData.propvalue&&Object(i["f"])({propValue:this.formData.propvalue}).then((function(a){console.log("getAllJars res:",a),e.jarNameArr=a.content.data}))},openDialog:function(e){var a=this,t=e.formData,r=void 0===t?{}:t,l=e.infoTitle,n=void 0===l?"":l,o=e.onSubmit,s=void 0===o?null:o,u=e.onClose,c=void 0===u?null:u;this.onSubmit=s,this.onClose=c,this.infoTitle=n,this.formData=Object.assign({},this.defaultFormData,r),this.formData.type="JAR",this.formData.jobManagerMemory=1024,this.formData.taskManagerMemory=1024,this.formData.slot=1,this.dialogShow=!0,this.formData.propvalue&&Object(i["f"])({propValue:this.formData.propvalue}).then((function(e){console.log("getAllJars res:",e),a.jarNameArr=e.content.data}))},handleSubmit:function(){var e=this;this.$refs["infoDialogForm"].validate((function(a){var t;return a?(null===(t=e.onSubmit)||void 0===t||t.call(e,e.formData),e.dialogShow=!1,!0):(e.$message.error("填写异常，请仔细检查"),!1)}))},handleCancel:function(){this.dialogShow=!1},close:function(){var e;null===(e=this.onClose)||void 0===e||e.call(this,this.formData)}}},c=u,p=(t("968c"),t("2877")),m=Object(p["a"])(c,n,o,!1,null,"26ab56ce",null),d=m.exports,f=t("f656"),b={nmae:"SQLlistManageDevelopment",components:{InfoDialog:d},data:function(){return{rules:{name:[{required:!0,message:"不能为空",trigger:"blur"},Object(f["b"])()],tasktype:[{required:!0,message:"不能为空",trigger:"blur"}],runtype:[{required:!0,message:"不能为空",trigger:"blur"}],sql_text:[{required:!0,message:"不能为空",trigger:"blur"}],run_param:[{required:!0,message:"不能为空",trigger:"blur"}],task_describe:[{required:!0,message:"不能为空",trigger:"blur"},Object(f["a"])()],propvalue:[{required:!0,message:"不能为空",trigger:"blur"}],jarname:[{required:!0,message:"不能为空",trigger:"blur"}],mainClass:[{required:!0,message:"不能为空",trigger:"blur"}],parameter:[{required:!0,message:"不能为空",trigger:"blur"}],jid:[{required:!0,message:"JAR包选择不能为空",trigger:"blur"}],jobManagerMemory:[{required:!0,message:"任务调度内存不能为空",trigger:"blur"}],taskManagerMemory:[{required:!0,message:"任务执行内存不能为空",trigger:"blur"}],slot:[{required:!0,message:"任务槽数量不能为空",trigger:"blur"}]},sqlDataList:null,searchInput:"",jsonResult:"",sqlText:"",curData:null,uploadData:{uploading:!1,process:""},dataList:[],devNameArr:[],jarNameArr:[]}},mounted:function(){var e=this;this.handleSearch().then((function(){e.sqlDataList.length&&(e.setCurData(e.sqlDataList[0]),e.curData.propvalue&&Object(i["f"])({propValue:e.curData.propvalue}).then((function(a){e.jarNameArr=a.content.data})))})),Object(s["c"])().then((function(a){e.devNameArr=a.content.data}))},methods:{jidChange:function(){console.log("this.curData.jid = ",this.curData.jid)},propvalueChange:function(){var e=this;console.log("in propvalueChange"),this.curData.jarname="",this.curData.jid="",this.curData.propvalue&&Object(i["f"])({propValue:this.curData.propvalue}).then((function(a){e.jarNameArr=a.content.data}))},getData:function(){var e=this,a={type:"JAR"};return this.searchInput&&(a["name"]=this.searchInput),Object(i["d"])(a).then((function(a){console.log("devTaskList res:",a.content.data),a.content.data.forEach((function(e){e.jid||(e.jid="")})),e.sqlDataList=a.content.data}))},setCurData:function(e){var a;this.curData=e,this.sqlText=(null===(a=this.curData)||void 0===a?void 0:a.sql_text)||""},handleSearch:function(){return this.getData()},handleUpload:function(e){var a=this,t=document.createElement("input");t.setAttribute("type","file"),t.setAttribute("accept","application/*, image/x-png, image/gif, image/jpeg, image/bmp"),t.style.display="none",t.addEventListener("change",(function(r){var l=new FormData;l.append("file",t.files[0]),l.append("filePath",t.files[0].name),l.append("tasktype",e.tasktype),a.uploadData.uploading=!0,Object(i["e"])(l,(function(e){a.curData.jarpath=e+"%"})).then((function(e){a.curData.jarpath=t.files[0].name,a.uploadData.uploading=!1,a.uploadData.process=""}))})),t.click()},updateData:function(e){var a=this;this.$refs.infoDialogForm.validate((function(t){if(!t)return!1;Object(i["b"])(e).then((function(){a.$message.success("修改任务成功"),a.getData()}))}))},handleUpdate:function(e){this.$refs["InfoDialog"].openDialog({formData:e,infoTitle:"更新",onSubmit:this.updateData})},handleAdd:function(){var e=this;this.$refs["InfoDialog"].openDialog({infoTitle:"新增任务",onSubmit:function(a){Object(i["a"])(a).then((function(){e.$message.success("新增任务成功"),e.getData()}))}})},executeData:function(e){var a=this;e=Object.assign({},e,{sql_text:this.sqlText}),Object(i["c"])(e).then((function(e){a.$message.success("运行成功"),a.jsonResult=JSON.stringify(e,null,2)}))}}},g=b,h=(t("031a"),Object(p["a"])(g,r,l,!1,null,"1f2772aa",null));a["default"]=h.exports},d785:function(e,a,t){},f656:function(e,a,t){"use strict";function r(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function l(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}t.d(a,"b",(function(){return r})),t.d(a,"a",(function(){return l}))}}]);