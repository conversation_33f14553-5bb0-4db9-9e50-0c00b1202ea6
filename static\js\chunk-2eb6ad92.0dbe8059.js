(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2eb6ad92"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,i,a){return t/=a/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function l(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,i){var o=n(),s=t-o,r=20,c=0;e="undefined"===typeof e?500:e;var u=function t(){c+=r;var n=Math.easeInOutQuad(c,o,s,e);l(n),c<e?a(t):i&&"function"===typeof i&&i()};u()}},67248:function(t,e,i){"use strict";i("8d41");var a="@@wavesContext";function l(t,e){function i(i){var a=Object.assign({},e.value),l=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),n=l.ele;if(n){n.style.position="relative",n.style.overflow="hidden";var o=n.getBoundingClientRect(),s=n.querySelector(".waves-ripple");switch(s?s.className="waves-ripple":(s=document.createElement("span"),s.className="waves-ripple",s.style.height=s.style.width=Math.max(o.width,o.height)+"px",n.appendChild(s)),l.type){case"center":s.style.top=o.height/2-s.offsetHeight/2+"px",s.style.left=o.width/2-s.offsetWidth/2+"px";break;default:s.style.top=(i.pageY-o.top-s.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",s.style.left=(i.pageX-o.left-s.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return s.style.backgroundColor=l.color,s.className="waves-ripple z-active",!1}}return t[a]?t[a].removeHandle=i:t[a]={removeHandle:i},i}var n={bind:function(t,e){t.addEventListener("click",l(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[a].removeHandle,!1),t.addEventListener("click",l(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[a].removeHandle,!1),t[a]=null,delete t[a]}},o=function(t){t.directive("waves",n)};window.Vue&&(window.waves=n,Vue.use(o)),n.install=o;e["a"]=n},"8d41":function(t,e,i){},"902e":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"违规账户"},model:{value:t.listQuery.username,callback:function(e){t.$set(t.listQuery,"username",e)},expression:"listQuery.username"}}),i("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"违规原因"},model:{value:t.listQuery.reason,callback:function(e){t.$set(t.listQuery,"reason",e)},expression:"listQuery.reason"}}),i("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:t.fetchData}},[t._v(" 搜索 ")]),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-edit"},on:{click:t.handleAddAll}},[t._v(" 黑名单导入 ")]),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-edit"},on:{click:function(e){t.clearDialogFormVisible=!0}}},[t._v(" 清空黑名单 ")])],1),i("div",{staticClass:"table-box"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:t.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"left",label:"序号",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.$index+1))]}}])}),i("el-table-column",{attrs:{label:"违规账户",align:"left",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.username))]}}])}),i("el-table-column",{attrs:{label:"违规原因",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.reason))]}}])}),i("el-table-column",{attrs:{label:"违规次数",align:"left",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.countnumber))]}}])}),i("el-table-column",{attrs:{label:"状态",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"0","inactive-value":"1","active-text":"启用","inactive-text":"停用"},on:{change:function(i){return t.statusChange(e.row)}},model:{value:e.row.status,callback:function(i){t.$set(e.row,"status",i)},expression:"scope.row.status"}})]}}])}),i("el-table-column",{attrs:{label:"创建时间",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.createtime))]}}])}),i("el-table-column",{attrs:{label:"操作",align:"left",width:"230","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[i("el-button",{attrs:{type:"warning",size:"small",icon:"el-icon-view"},on:{click:function(e){return t.handleView(a)}}},[t._v(" 详情 ")]),"deleted"!==a.status?i("el-button",{attrs:{size:"small",icon:"el-icon-delete",type:"danger"},on:{click:function(e){return t.handleDelete(a)}}},[t._v(" 删除 ")]):t._e()]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.pageNo,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageNo",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.fetchData}}),i("el-dialog",{attrs:{title:"详情",visible:t.dialogFormVisible,width:"800px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[i("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",rows:"12",placeholder:"详情",disabled:""},model:{value:t.temp.detail,callback:function(e){t.$set(t.temp,"detail",e)},expression:"temp.detail"}})],1)],1),i("el-dialog",{attrs:{visible:t.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(e){t.dialogPluginVisible=e}}},[i("el-table",{staticStyle:{width:"100%"},attrs:{data:t.pluginData,border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{prop:"key",label:"Channel"}}),i("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogPvVisible=!1}}},[t._v("Confirm")])],1)],1),i("el-dialog",{attrs:{title:"清除黑名单",visible:t.clearDialogFormVisible,width:"500px"},on:{"update:visible":function(e){t.clearDialogFormVisible=e}}},[i("el-form",{ref:"dataForm",staticStyle:{width:"80%","margin-left":"50px"},attrs:{rules:t.rules,model:t.temp,"label-position":"right","label-width":"100px"}},[i("el-form-item",{attrs:{label:"清除黑名单",prop:"temp"}},[i("el-select",{attrs:{placeholder:"选择清除黑名单"},model:{value:t.temp.cycle,callback:function(e){t.$set(t.temp,"cycle",e)},expression:"temp.cycle"}},[i("el-option",{attrs:{label:"全部数据",value:"4"}}),i("el-option",{attrs:{label:"1个月之前数据",value:"1"}}),i("el-option",{attrs:{label:"2个月之前数据",value:"2"}}),i("el-option",{attrs:{label:"半年之前数据",value:"3"}})],1)],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.clearDialogFormVisible=!1}}},[t._v("取消")]),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitClearData(t.temp)}}},[t._v("确定")])],1)],1)],1)},l=[],n=i("ade3"),o=(i("14d9"),i("b775"));function s(t){return Object(o["a"])({url:"/api/blackList/remove?id="+t,method:"post"})}function r(){return Object(o["a"])({url:"/api/blackList/removeAll",method:"post"})}function c(){return Object(o["a"])({url:"/api/blackList/add",method:"post"})}function u(t){return Object(o["a"])({url:"/api/blackList/list",method:"get",params:t})}function d(t){return Object(o["a"])({url:"/api/blackList/add",method:"post",data:t})}function p(t){return Object(o["a"])({url:"/api/blackList/update",method:"post",data:t})}function f(t){return Object(o["a"])({url:"/api/blackList/updateStatus",method:"get",params:t})}function m(t){return Object(o["a"])({url:"/api/blackList/removeByData?indexType="+t,method:"post"})}var h=i("67248"),b=i("333d"),g=(i("4362"),{name:"DevEnvSetting",components:{Pagination:b["a"]},directives:{waves:h["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return Object(n["a"])(Object(n["a"])({list:null,listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:10,username:"",reason:""},pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,clearDialogFormVisible:!1,dialogStatus:"",textMap:{update:"Edit",create:"Create"},temp:{cycle:""},rules:{name:[{required:!0,message:"this is required",trigger:"blur"}],description:[{required:!0,message:"this is required",trigger:"blur"}]}},"temp",{id:void 0,name:"",description:"",detail:""}),"visible",!0)},created:function(){this.fetchData()},methods:{statusChange:function(t){var e=this;f({id:t.id,status:t.status}).then((function(){e.$notify({title:"成功",message:"更新成功",type:"success",duration:2e3})}))},submitClearData:function(t){var e=this;m(t.cycle).then((function(){e.fetchData(),e.clearDialogFormVisible=!1,e.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},fetchData:function(){var t=this;console.log(1),this.listLoading=!0,u(this.listQuery).then((function(e){var i=e.content;t.total=i.recordsTotal,t.list=i.data,t.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&d(t.temp).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"新增 黑名单操作",message:"新增 黑名单 成功",type:"success",duration:2e3})}))}))},handleUpdate:function(t){var e=this;this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var i=Object.assign({},t.temp);p(i).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;console.log("删除");var i=[];i.push(t.id),s(t.id).then((function(t){e.fetchData(),e.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},handleDeleteAll:function(){var t=this;console.log("删除所有"),r().then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},handleView:function(t){this.temp=Object.assign({},t),this.dialogFormVisible=!0},handleAddAll:function(){var t=this;console.log("导入黑名单"),c().then((function(e){t.fetchData(),t.$notify({title:"导入黑名单",message:"导入成功",type:"success",duration:2e3})}))}}}),v=g,y=i("2877"),w=Object(y["a"])(v,a,l,!1,null,null,null);e["default"]=w.exports}}]);