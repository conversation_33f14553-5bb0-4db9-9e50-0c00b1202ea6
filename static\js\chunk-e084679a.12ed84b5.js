(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e084679a"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return l})),Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function i(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(t,e,a){var l=o(),r=t-l,s=20,u=0;e="undefined"===typeof e?500:e;var c=function t(){u+=s;var o=Math.easeInOutQuad(u,l,r,e);i(o),u<e?n(t):a&&"function"===typeof a&&a()};c()}},"173a":function(t,e,a){"use strict";a("82b3")},2941:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"文件类型"},model:{value:t.listQuery.filetype,callback:function(e){t.$set(t.listQuery,"filetype",e)},expression:"listQuery.filetype"}},[a("el-option",{attrs:{label:"",value:""}}),a("el-option",{attrs:{label:"SHELL",value:"SHELL"}}),a("el-option",{attrs:{label:"PYTHON",value:"PYTHON"}}),a("el-option",{attrs:{label:"DAG",value:"DAG"}})],1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{clearable:"",placeholder:"任务名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.job_name,callback:function(e){t.$set(t.listQuery,"job_name",e)},expression:"listQuery.job_name"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{filterable:"",placeholder:"任务状态"},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},[a("el-option",{attrs:{label:"",value:""}}),a("el-option",{attrs:{label:"启用",value:"1"}}),a("el-option",{attrs:{label:"停用",value:"0"}})],1),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:t.fetchData}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v(" 新增任务 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:t.addDAG}},[t._v(" 任务编排 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:t.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"left",label:"序号",width:"95"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.$index+1))]}}])}),a("el-table-column",{attrs:{label:"任务类型",align:"left",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.filetype))]}}])}),t._e(),a("el-table-column",{attrs:{label:"任务名称",align:"left",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.job_name))]}}])}),a("el-table-column",{attrs:{label:"调度文件名称",align:"left",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.filename))]}}])}),a("el-table-column",{attrs:{label:"调度表达式",align:"left",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.cron))]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"left",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.createtime))]}}])}),a("el-table-column",{attrs:{label:"任务状态",align:"left",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"1","inactive-value":"0","active-text":"启用","inactive-text":"停用"},on:{change:function(a){return t.statusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"触发时间",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popover",{attrs:{placement:"bottom",width:"160"},on:{show:function(a){return t.nextTriggerTime(e.row)}}},[a("h5",{domProps:{innerHTML:t._s(t.triggerNextTimes)}}),a("el-button",{attrs:{slot:"reference",size:"small"},slot:"reference"},[t._v("查看")])],1)]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[a("el-dropdown",{attrs:{trigger:"click"}},[a("span",{staticClass:"el-dropdown-link"},[t._v(" 操作"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{nativeOn:{click:function(e){return t.handlerExecuteHandler(n)}}},[t._v("执行一次")]),"DAG"!=n.filetype?a("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(e){return t.handleUpdate(n)}}},[t._v("编辑")]):t._e(),"DAG"==n.filetype?a("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(e){return t.editDAG(n)}}},[t._v("编辑DAG图")]):t._e(),a("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(e){return t.handleDelete(n)}}},[t._v("删除")])],1)],1)]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.pageNo,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageNo",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.fetchData}}),a("el-dialog",{attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,width:"600px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[a("el-form-item",{attrs:{label:"任务名称",prop:"job_name"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"任务名称"},model:{value:t.temp.job_name,callback:function(e){t.$set(t.temp,"job_name",e)},expression:"temp.job_name"}})],1),a("el-dialog",{attrs:{title:"配置调度表达式",visible:t.showCronBox,width:"60%","append-to-body":""},on:{"update:visible":function(e){t.showCronBox=e}}},[a("cron",{model:{value:t.temp.cron,callback:function(e){t.$set(t.temp,"cron",e)},expression:"temp.cron"}}),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.showCronBox=!1}}},[t._v("关闭")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.showCronBox=!1}}},[t._v("确 定")])],1)],1),a("el-form-item",{attrs:{label:"调度表达式",prop:"cron"}},[a("el-input",{attrs:{"auto-complete":"off",placeholder:"请输入Cron表达式"},model:{value:t.temp.cron,callback:function(e){t.$set(t.temp,"cron",e)},expression:"temp.cron"}},[t.showCronBox?a("el-button",{attrs:{slot:"append",icon:"el-icon-open",title:"关闭图形配置"},on:{click:function(e){t.showCronBox=!1}},slot:"append"}):a("el-button",{attrs:{slot:"append",icon:"el-icon-turn-off",title:"打开图形配置"},on:{click:function(e){t.showCronBox=!0}},slot:"append"})],1)],1),a("el-form-item",{attrs:{label:"任务状态",prop:"status"}},[a("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"任务状态"},model:{value:t.temp.status,callback:function(e){t.$set(t.temp,"status",e)},expression:"temp.status"}},[a("el-option",{attrs:{label:"启用",value:"启用"}}),a("el-option",{attrs:{label:"停用",value:"停用"}})],1)],1),a("el-form-item",{attrs:{label:"调度文件",prop:"filename"}},[a("el-select",{attrs:{placeholder:"调度文件"},model:{value:t.temp.filename,callback:function(e){t.$set(t.temp,"filename",e)},expression:"temp.filename"}},t._l(t.scheduleFileNameList,(function(t,e){return a("el-option",{key:"person-"+e,attrs:{label:t.filename,value:t.filename}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){"create"===t.dialogStatus?t.createData():t.updateData()}}},[t._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:t.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(e){t.dialogPluginVisible=e}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogPvVisible=!1}}},[t._v("Confirm")])],1)],1),a("el-dialog",{attrs:{title:"执行参数设置",visible:t.dialogVisible,width:"45%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{ref:"executeForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[a("el-form-item",{attrs:{label:"执行参数",prop:"eparams"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{type:"textarea",rows:"5",placeholder:"执行参数，多个空格分隔"},model:{value:t.temp.eparams,callback:function(e){t.$set(t.temp,"eparams",e)},expression:"temp.eparams"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.handlerExecute()}}},[t._v(" 确认 ")])],1)],1)],1)},i=[],o=(a("a15b"),a("14d9"),a("32e8")),l=a("b775");function r(t){return Object(l["a"])({url:"/api/schedulerlist/list",method:"get",params:t})}function s(){return Object(l["a"])({url:"/api/warnrule/listWarnPerson",method:"get"})}function u(){return Object(l["a"])({url:"/api/warnrule/listScheduleFileName",method:"get"})}function c(t){return Object(l["a"])({url:"/api/schedulerlist/update",method:"post",data:t})}function d(t){return Object(l["a"])({url:"/api/schedulerlist/add",method:"post",data:t})}function p(t){return Object(l["a"])({url:"/api/schedulerlist/remove",method:"post",params:t})}function f(t){return Object(l["a"])({url:"/api/schedulerlist/updateStatus",method:"get",params:t})}function m(t){return Object(l["a"])({url:"/api/schedulerlist/trigger",method:"post",data:t})}var h=a("3a8d"),g=a("5ec8"),b=a("67248"),v=a("333d"),w={name:"DevEnvSetting",components:{Pagination:v["a"],cron:g["a"]},directives:{waves:b["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return{list:null,listLoading:!0,dialogVisible:!1,total:0,listQuery:{pageNo:1,pageSize:10,filetype:"",job_name:"",status:""},showCronBox:!1,pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑任务调度",create:"新增任务调度"},rules:{job_name:[{required:!0,message:"任务名称不能为空",trigger:"blur"}],cron:[{required:!0,message:"调度表达式不能为空",trigger:"blur"}],status:[{required:!0,message:"任务状态不能为空",trigger:"blur"}],filename:[{required:!0,message:"调度文件不能为空",trigger:"blur"}]},warnpersonList:[],scheduleFileNameList:[],temp:{id:void 0,cron:"0 0 * * * ? *",job_name:"",describe:"",status:"",filename:"",warnperson:""},triggerNextTimes:"",visible:!0,jobLogQuery:{executorAddress:""},logContent:"",logShow:!1,logLoading:!1}},created:function(){this.fetchData(),this.listWarnPerson(),this.listScheduleFileName()},methods:{addDAG:function(){this.$router.push({name:"schedule-center-task-dag",params:{origin:"createTask"}})},editDAG:function(t){this.$router.push({name:"schedule-center-task-dag",params:{data:t,origin:"updateTask"}})},changeCron:function(t){this.temp.cron=t},statusChange:function(t){var e=this;f({id:t.id,status:t.status,filetype:t.filetype}).then((function(){e.$notify({title:"切换状态",message:"切换成功",type:"success",duration:2e3})}))},listWarnPerson:function(){var t=this;s({}).then((function(e){t.warnpersonList=e.content.data}))},listScheduleFileName:function(){var t=this;u({}).then((function(e){t.scheduleFileNameList=e.content.data}))},fetchData:function(){var t=this;console.log(1),this.listLoading=!0,r(this.listQuery).then((function(e){var a=e.content;t.total=a.recordsTotal,t.list=a.data,t.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,cron:"0 0 * * * ? *",job_name:"",describe:"",status:"",filename:"",warnperson:""}},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&d(t.temp).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"新增任务调度",message:"新增成功！",type:"success",duration:2e3})}))}))},handleUpdate:function(t){var e=this;this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a=Object.assign({},t.temp);c(a).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"更新任务调度",message:"更新成功！",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;console.log("删除");var a=[];a.push(t.id),p({id:t.id}).then((function(t){e.fetchData(),e.$notify({title:"删除状态",message:"删除成功",type:"success",duration:2e3})}))},handlerExecute:function(){var t=this,e={};e.filetype=this.temp.filetype,e.filename=this.temp.filename,e.remark=this.temp.remark,e.logpath=this.temp.logpath,e.logname=this.temp.logname,e.job_name=this.temp.job_name,e.taskcode=this.temp.taskcode,e.eparams=this.temp.eparams,m(e).then((function(e){t.dialogVisible=!1,t.$notify({title:"执行状态",message:"执行成功",type:"success",duration:2e3})}))},handlerExecuteHandler:function(t){this.dialogVisible=!0,this.temp=Object.assign({},t)},nextTriggerTime:function(t){var e=this;h["d"](t.cron).then((function(t){var a=t.content;e.triggerNextTimes=a.join("<br>")}))},handleViewJobLog:function(t){this.logContent="",this.dialogVisible=!0,this.jobLogQuery.executorAddress=t.logpath+"/"+t.logname,!1===this.logShow&&(this.logShow=!0)},loadLog:function(){var t=this;this.logLoading=!0,o["e"](this.jobLogQuery.executorAddress).then((function(e){"\n"===e.content.logContent||(t.logContent=e.content.logContent),t.logLoading=!1}))}}},y=w,k=(a("173a"),a("2877")),x=Object(k["a"])(y,n,i,!1,null,"224f5206",null);e["default"]=x.exports},"32e8":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return o})),a.d(e,"c",(function(){return l})),a.d(e,"e",(function(){return r})),a.d(e,"d",(function(){return s}));var n=a("b775");function i(t){return Object(n["a"])({url:"api/log/pageList",method:"get",params:t})}function o(t,e,a){return Object(n["a"])({url:"/api/log/clearLog?jobGroup="+t+"&jobId="+e+"&type="+a,method:"post"})}function l(t){return Object(n["a"])({url:"/api/log/killJob",method:"post",data:t})}function r(t,e,a,i){return Object(n["a"])({url:"/api/log/logDetailCat?executorAddress="+t+"&triggerTime="+e+"&logId="+a+"&fromLineNum="+i,method:"get"})}function s(t,e,a,i,o){return Object(n["a"])({url:"/api/schedulerlog/logDetailCat?executorAddress="+t+"&tasktype="+e+"&triggerTime="+a+"&logId="+i+"&fromLineNum="+o,method:"get"})}},"3a8d":function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return o})),a.d(e,"f",(function(){return l})),a.d(e,"a",(function(){return r})),a.d(e,"e",(function(){return s})),a.d(e,"d",(function(){return u}));var n=a("b775");function i(t){return Object(n["a"])({url:"/api/jobTemplate/pageList",method:"get",params:t})}function o(){return Object(n["a"])({url:"/api/jobGroup/list",method:"get"})}function l(t){return Object(n["a"])({url:"/api/jobTemplate/update",method:"post",data:t})}function r(t){return Object(n["a"])({url:"/api/jobTemplate/add/",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/api/jobTemplate/remove/"+t,method:"post"})}function u(t){return Object(n["a"])({url:"/api/jobTemplate/nextTriggerTime?cron="+t,method:"get"})}},67248:function(t,e,a){"use strict";a("8d41");var n="@@wavesContext";function i(t,e){function a(a){var n=Object.assign({},e.value),i=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},n),o=i.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var l=o.getBoundingClientRect(),r=o.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(l.width,l.height)+"px",o.appendChild(r)),i.type){case"center":r.style.top=l.height/2-r.offsetHeight/2+"px",r.style.left=l.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(a.pageY-l.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(a.pageX-l.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=i.color,r.className="waves-ripple z-active",!1}}return t[n]?t[n].removeHandle=a:t[n]={removeHandle:a},a}var o={bind:function(t,e){t.addEventListener("click",i(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[n].removeHandle,!1),t.addEventListener("click",i(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[n].removeHandle,!1),t[n]=null,delete t[n]}},l=function(t){t.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(l)),o.install=l;e["a"]=o},"82b3":function(t,e,a){},"8d41":function(t,e,a){}}]);