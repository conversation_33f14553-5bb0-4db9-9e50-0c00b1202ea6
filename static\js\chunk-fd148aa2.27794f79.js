(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fd148aa2"],{"3ff3":function(t,n,e){"use strict";e("fdc3")},a4a6:function(t,n,e){"use strict";e.r(n);var o=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"mycontent"},[e("el-button",{attrs:{icon:"el-icon-d-arrow-left",type:"info",plain:"",size:"small"},on:{click:function(n){return t.$router.go(-1)}}},[t._v("返回")]),e("h2",[t._v("API详情")]),e("common",{attrs:{id:t.$route.query.id}})],1)},c=[],a=e("eb18"),i={data:function(){return{}},components:{common:a["a"]},methods:{},created:function(){}},r=i,u=(e("3ff3"),e("2877")),f=Object(u["a"])(r,o,c,!1,null,"cefad9da",null);n["default"]=f.exports},fdc3:function(t,n,e){}}]);