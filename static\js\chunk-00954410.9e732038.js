(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-00954410"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),Math.easeInOutQuad=function(e,t,a,s){return e/=s/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var s=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function i(e,t,a){var i=o(),l=e-i,c=20,n=0;t="undefined"===typeof t?500:t;var d=function e(){n+=c;var o=Math.easeInOutQuad(n,i,l,t);r(o),n<t?s(e):a&&"function"===typeof a&&a()};d()}},"1ccf5":function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"d",(function(){return l})),a.d(t,"e",(function(){return c})),a.d(t,"f",(function(){return n})),a.d(t,"h",(function(){return d})),a.d(t,"b",(function(){return u}));var s=a("b775");function r(e){return Object(s["a"])({url:"/api/metadataManager/removeAll",method:"post",data:e})}function o(e){return Object(s["a"])({url:"/api/metadataManager/add",method:"post",data:e})}function i(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(s["a"])({url:"/api/metadataManager/findDBByType",method:"get",params:e})}function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(s["a"])({url:"/api/metadataManager/findDBNameAndTable",method:"get",params:e})}function c(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(s["a"])({url:"/api/jobJdbcDatasource/findSourceName",method:"get",params:e})}function n(e){return Object(s["a"])({url:"/api/metadataManager/list",method:"get",params:e})}function d(e){return Object(s["a"])({url:"/api/metadataManager/update",method:"post",data:e})}function u(e){return Object(s["a"])({url:"/api/devEnvSetting",method:"delete",params:e})}},"1cf5":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("div",{ref:"headerFormRef",class:["search-header",1==e.isCollapseHeaderForm?"collapse":""]},[a("el-form",{attrs:{"label-suffix":"：","label-width":"138px",inline:""}},[a("el-form-item",{attrs:{label:"数据源类型","label-width":"auto"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"数据源类型",filterable:"",clearable:""},on:{change:e.dataSourceChange},model:{value:e.listQuery.datasource,callback:function(t){e.$set(e.listQuery,"datasource",t)},expression:"listQuery.datasource"}},e._l(e.dataSources,(function(e,t){return a("el-option",{key:"data-source-"+t,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"数据源名称"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"数据源名称",filterable:"",clearable:""},model:{value:e.listQuery.datasource_name,callback:function(t){e.$set(e.listQuery,"datasource_name",t)},expression:"listQuery.datasource_name"}},e._l(e.dataSourceName,(function(e,t){return a("el-option",{key:"data-source-"+t,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"管道类型"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{filterable:"",clearable:"",placeholder:"管道类型"},model:{value:e.listQuery.datasource_group,callback:function(t){e.$set(e.listQuery,"datasource_group",t)},expression:"listQuery.datasource_group"}},[a("el-option",{attrs:{label:"source",value:"source"}}),a("el-option",{attrs:{label:"sink",value:"sink"}}),a("el-option",{attrs:{label:"web",value:"web"}})],1)],1)],1),e.showExtendFold?a("div",{staticClass:"collapseBtn",on:{click:e.handleCollapse}},[a("svg-icon",{attrs:{"icon-class":[e.isCollapseHeaderForm?"extendBtn":"foldBtn"]}})],1):e._e(),a("div",{staticClass:"search-opt"},[a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item search",attrs:{type:"primary round"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item reset-btn",attrs:{type:"primary round"},on:{click:e.handleReset}},[e._v(" 重置 ")])],1)],1),a("el-divider"),a("el-button",{staticClass:"filter-item opt",staticStyle:{"margin-left":"10px"},attrs:{type:"success"},on:{click:e.handleCreate}},[e._v(" 新增 ")])],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","row-style":{height:"58px"}}},[a("el-table-column",{attrs:{label:"数据源类型","min-width":"90",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.datasource))]}}])}),a("el-table-column",{attrs:{label:"数据源名称","min-width":"150",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.datasourceName))]}}])}),a("el-table-column",{attrs:{label:"jdbc连接串","min-width":"200",align:"left","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.jdbcUrl?t.row.jdbcUrl:"-"))]}}])}),a("el-table-column",{attrs:{label:"用途","min-width":"90",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.comments)+" ")]}}])}),a("el-table-column",{attrs:{label:"管道类型","min-width":"90",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.datasourceGroup)+" ")]}}])}),a("el-table-column",{attrs:{label:"连接状态","min-width":"90",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("正常")]):a("el-tag",{attrs:{type:"danger"}},[e._v("异常")])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left","min-width":"150","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("span",{staticClass:"table-btn",on:{click:function(t){return e.handleUpdate(s)}}},[e._v(" 编辑 ")]),a("span",{staticClass:"table-btn",attrs:{type:"text"},on:{click:function(t){return e.testDataSource2(s)}}},[e._v(" 测试连接 ")]),"deleted"!=s.status?a("span",{staticClass:"table-btn",on:{click:function(t){return e.handleDelete(s)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}}),a("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"540px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"right","label-width":"120px"}},[a("el-form-item",{attrs:{label:"数据源类型",prop:"datasource"}},[a("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:"数据源类型"},on:{change:function(t){return e.selectDataSource(e.temp.datasource)}},model:{value:e.temp.datasource,callback:function(t){e.$set(e.temp,"datasource",t)},expression:"temp.datasource"}},e._l(e.dataSources,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"数据源名称",prop:"datasourceName"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"数据源名称"},model:{value:e.temp.datasourceName,callback:function(t){e.$set(e.temp,"datasourceName",t)},expression:"temp.datasourceName"}})],1),a("el-form-item",{attrs:{label:"用途",prop:"comments"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"300px"},attrs:{filterable:"",placeholder:"用途"},model:{value:e.temp.comments,callback:function(t){e.$set(e.temp,"comments",t)},expression:"temp.comments"}},[a("el-option",{attrs:{label:"普通库",value:"普通库"}}),a("el-option",{attrs:{label:"离线数仓",value:"离线数仓"}}),a("el-option",{attrs:{label:"实时数仓",value:"实时数仓"}}),a("el-option",{attrs:{label:"消息队列",value:"消息队列"}})],1)],1),"mysql"===e.temp.datasource||"oracle"===e.temp.datasource||"hana"===e.temp.datasource||"postgresql"===e.temp.datasource||"sqlserver"===e.temp.datasource||"hive"===e.temp.datasource||"hbase"===e.temp.datasource||"mongodb"===e.temp.datasource||"clickhouse"===e.temp.datasource||"es"===e.temp.datasource||"kafka"===e.temp.datasource||"doris"===e.temp.datasource||"starRocks"===e.temp.datasource||"db2"===e.temp.datasource||"kingbase"===e.temp.datasource||"dm"===e.temp.datasource||"gbase"===e.temp.datasource||"hdfs"===e.temp.datasource||"redis"===e.temp.datasource?a("el-form-item",{attrs:{label:"管道类型",prop:"datasourceGroup"}},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"300px"},attrs:{filterable:"",placeholder:"管道类型"},model:{value:e.temp.datasourceGroup,callback:function(t){e.$set(e.temp,"datasourceGroup",t)},expression:"temp.datasourceGroup"}},[a("el-option",{attrs:{label:"source",value:"source"}}),a("el-option",{attrs:{label:"sink",value:"sink"}}),a("el-option",{attrs:{label:"web",value:"web"}})],1)],1):e._e(),"mysql"===e.temp.datasource||"oracle"===e.temp.datasource||"hana"===e.temp.datasource||"postgresql"===e.temp.datasource||"sqlserver"===e.temp.datasource||"hive"===e.temp.datasource||"clickhouse"===e.temp.datasource||"doris"===e.temp.datasource||"starRocks"===e.temp.datasource||"db2"===e.temp.datasource||"es"===e.temp.datasource||"kingbase"===e.temp.datasource||"dm"===e.temp.datasource||"gbase"===e.temp.datasource||"redis"===e.temp.datasource?a("el-form-item",{attrs:{label:"用户名"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"用户名"},model:{value:e.temp.jdbcUsername,callback:function(t){e.$set(e.temp,"jdbcUsername",t)},expression:"temp.jdbcUsername"}})],1):e._e(),e.visible?a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"mysql"===e.temp.datasource||"oracle"===e.temp.datasource||"hana"===e.temp.datasource||"postgresql"===e.temp.datasource||"sqlserver"===e.temp.datasource||"hive"===e.temp.datasource||"clickhouse"===e.temp.datasource||"doris"===e.temp.datasource||"starRocks"===e.temp.datasource||"db2"===e.temp.datasource||"es"===e.temp.datasource||"kingbase"===e.temp.datasource||"dm"===e.temp.datasource||"gbase"===e.temp.datasource||"redis"===e.temp.datasource,expression:"\n          temp.datasource === 'mysql' ||\n          temp.datasource === 'oracle' ||\n          temp.datasource === 'hana' ||\n          temp.datasource === 'postgresql' ||\n          temp.datasource === 'sqlserver' ||\n          temp.datasource === 'hive' ||\n          temp.datasource === 'clickhouse' ||\n          temp.datasource === 'doris' ||\n          temp.datasource === 'starRocks' ||\n          temp.datasource === 'db2' ||\n          temp.datasource === 'es' ||\n          temp.datasource === 'kingbase' ||\n          temp.datasource === 'dm' ||\n          temp.datasource === 'gbase' ||\n          temp.datasource === 'redis'\n        "}],attrs:{label:"密码"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{type:"password",placeholder:"密码"},model:{value:e.temp.jdbcPassword,callback:function(t){e.$set(e.temp,"jdbcPassword",t)},expression:"temp.jdbcPassword"}},[a("i",{staticClass:"el-icon-view",staticStyle:{cursor:"pointer"},attrs:{slot:"suffix",title:"显示密码"},on:{click:function(t){return e.changePass("show")}},slot:"suffix"})])],1):a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"mysql"===e.temp.datasource||"oracle"===e.temp.datasource||"hana"===e.temp.datasource||"postgresql"===e.temp.datasource||"sqlserver"===e.temp.datasource||"hive"===e.temp.datasource||"clickhouse"===e.temp.datasource||"es"===e.temp.datasource||"kingbase"===e.temp.datasource||"dm"===e.temp.datasource,expression:"\n          temp.datasource === 'mysql' ||\n          temp.datasource === 'oracle' ||\n          temp.datasource === 'hana' ||\n          temp.datasource === 'postgresql' ||\n          temp.datasource === 'sqlserver' ||\n          temp.datasource === 'hive' ||\n          temp.datasource === 'clickhouse' ||\n          temp.datasource === 'es' ||\n          temp.datasource === 'kingbase' ||\n          temp.datasource === 'dm'\n        "}],attrs:{label:"密码"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{type:"text",placeholder:"密码"},model:{value:e.temp.jdbcPassword,callback:function(t){e.$set(e.temp,"jdbcPassword",t)},expression:"temp.jdbcPassword"}},[a("i",{staticClass:"el-icon-check",staticStyle:{cursor:"pointer"},attrs:{slot:"suffix",title:"隐藏密码"},on:{click:function(t){return e.changePass("hide")}},slot:"suffix"})])],1),"mysql"===e.temp.datasource||"oracle"===e.temp.datasource||"hana"===e.temp.datasource||"postgresql"===e.temp.datasource||"sqlserver"===e.temp.datasource||"hive"===e.temp.datasource||"mongodb"===e.temp.datasource||"clickhouse"===e.temp.datasource||"es"===e.temp.datasource||"kafka"===e.temp.datasource||"doris"===e.temp.datasource||"starRocks"===e.temp.datasource||"db2"===e.temp.datasource||"kingbase"===e.temp.datasource||"dm"===e.temp.datasource||"gbase"===e.temp.datasource||"hdfs"===e.temp.datasource||"redis"===e.temp.datasource?a("el-form-item",{attrs:{label:"url地址",prop:"jdbcUrl"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{autosize:{minRows:3,maxRows:6},type:"textarea",placeholder:"127.0.0.1:27017 || hdfs://127.0.0.1:9000 || 127.0.0.1:9200 || 127.0.0.1:6379"},model:{value:e.temp.jdbcUrl,callback:function(t){e.$set(e.temp,"jdbcUrl",t)},expression:"temp.jdbcUrl"}})],1):e._e(),"mysql"===e.temp.datasource||"oracle"===e.temp.datasource||"hana"===e.temp.datasource||"postgresql"===e.temp.datasource||"sqlserver"===e.temp.datasource||"hive"===e.temp.datasource||"clickhouse"===e.temp.datasource||"kingbase"===e.temp.datasource||"dm"===e.temp.datasource?a("el-form-item",{attrs:{label:"jdbc驱动类",prop:"jdbcDriverClass"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"jdbc驱动类"},model:{value:e.temp.jdbcDriverClass,callback:function(t){e.$set(e.temp,"jdbcDriverClass",t)},expression:"temp.jdbcDriverClass"}})],1):e._e(),"hbase"===e.temp.datasource?a("el-form-item",{attrs:{label:"ZK地址",prop:"zkAdress"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"127.0.0.1:2181"},model:{value:e.temp.zkAdress,callback:function(t){e.$set(e.temp,"zkAdress",t)},expression:"temp.zkAdress"}})],1):e._e(),"mongodb"===e.temp.datasource?a("el-form-item",{attrs:{label:"数据库名称",prop:"databaseName"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:"数据库名称"},model:{value:e.temp.databaseName,callback:function(t){e.$set(e.temp,"databaseName",t)},expression:"temp.databaseName"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),a("el-button",{on:{click:function(t){return e.testDataSource()}}},[e._v(" 测试连接 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:e.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(t){e.dialogPluginVisible=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogPvVisible=!1}}},[e._v("Confirm")])],1)],1)],1)},r=[],o=(a("d81d"),a("14d9"),a("d3b7"),a("159b"),a("7e39")),i=a("67248"),l=a("ed08"),c=a("f656"),n=a("333d"),d=a("1ccf5"),u={name:"JdbcDatasource",components:{Pagination:n["a"]},directives:{waves:i["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{current:1,size:10},pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"修改数据源",create:"新增数据源"},rules:{datasourceName:[{required:!0,message:"数据源名称不能为空",trigger:"blur"},Object(c["b"])()],jdbcUsername:[{required:!0,message:"用户名不能为空",trigger:"blur"}],jdbcPassword:[{required:!0,message:"密码不能为空",trigger:"blur"}],jdbcUrl:[{required:!0,message:"url地址不能为空",trigger:"blur"}],jdbcDriverClass:[{required:!0,message:"jdbc驱动类不能为空",trigger:"blur"}],datasource:[{required:!0,message:"数据库名称不能为空",trigger:"change"}],zkAdress:[{required:!0,message:"ZK地址不能为空",trigger:"blur"}],databaseName:[{required:!0,message:"数据库名称不能为空",trigger:"blur"}],comments:[{required:!0,message:"用途不能为空",trigger:"blur"}],datasourceGroup:[{required:!0,message:"管道类型不能为空",trigger:"blur"}]},temp:{id:void 0,datasourceName:"",datasourceGroup:"",jdbcUsername:"",jdbcPassword:"",jdbcUrl:"",jdbcDriverClass:"",comments:"",datasource:"",zkAdress:"",databaseName:""},visible:!0,dataSources:[{value:"mysql",label:"mysql"},{value:"es",label:"es"}],dataSourceName:[],jdbc:!0,hbase:!1,mongodb:!1,isCollapseHeaderForm:!0,showExtendFold:!1}},created:function(){this.fetchData()},mounted:function(){var e=this,t=this.$refs.headerFormRef;console.log(t,"headerFormRef"),t&&(this.headerFormOserver=new ResizeObserver((function(a){null===a||void 0===a||a.forEach((function(a){var s=t.querySelectorAll(".el-form-item"),r=t.querySelectorAll(".el-form-item__label");r.forEach((function(e){e.classList.remove("first-in-row")}));var o=0,i=null;s.forEach((function(e){var t=e.getBoundingClientRect();if(null===i||t.top>i){i=t.top,o++;var a=e.querySelector(".el-form-item__label");a.classList.add("first-in-row")}})),e.showExtendFold=o>1}))})),this.headerFormOserver.observe(t))},methods:{handleCollapse:function(){this.isCollapseHeaderForm=!this.isCollapseHeaderForm},handleReset:function(){this.listQuery.datasource=null,this.listQuery.datasource_name=null,this.listQuery.datasource_group=null,this.fetchData()},dataSourceChange:function(e){var t=this;return this.dataSourceName=[],Object(d["c"])({type:e}).then((function(e){for(var a=e.content.data,s=0;s<a.length;s++)a[s].label=a[s].dbname,a[s].value=a[s].dbname;t.dataSourceName=a}))},selectDataSource:function(e){"mysql"===e||"doris"===e||"starRocks"===e?(this.temp.jdbcUrl="jdbc:mysql://{host}:{port}/{database}?serverTimezone=UTC&useSSL=false",this.temp.jdbcDriverClass="com.mysql.jdbc.Driver"):"kingbase"===e?(this.temp.jdbcUrl="jdbc:kingbase8://{host}:{port}/{database}",this.temp.jdbcDriverClass="com.kingbase8.Driver"):"dm"===e?(this.temp.jdbcUrl="jdbc:dm://{host}:{port}/{database}",this.temp.jdbcDriverClass="dm.jdbc.driver.DmDriver"):"gbase"===e?(this.temp.jdbcUrl="jdbc:gbase://{host}:{port}/{database}",this.temp.jdbcDriverClass="com.gbase.jdbc.Driver"):"db2"===e?(this.temp.jdbcUrl="jdbc:db2://{host}:{port}/{database}",this.temp.jdbcDriverClass="com.ibm.db2.jcc.DB2Driver"):"hana"===e?(this.temp.jdbcUrl="jdbc:sap://{host}:{port}/{instanceNumber}",this.temp.jdbcDriverClass="com.sap.db.jdbc.Driver"):"oracle"===e?(this.temp.jdbcUrl="jdbc:oracle:thin:@//{host}:{port}/{serviceName}",this.temp.jdbcDriverClass="oracle.jdbc.OracleDriver"):"postgresql"===e?(this.temp.jdbcUrl="jdbc:postgresql://{host}:{port}/{database}",this.temp.jdbcDriverClass="org.postgresql.Driver"):"sqlserver"===e?(this.temp.jdbcUrl="jdbc:jtds:sqlserver://{host}:{port};DatabaseName={database}",this.temp.jdbcDriverClass="net.sourceforge.jtds.jdbc.Driver"):"clickhouse"===e?(this.temp.jdbcUrl="jdbc:clickhouse://{host}:{port}/{database}",this.temp.jdbcDriverClass="ru.yandex.clickhouse.ClickHouseDriver"):"hive"===e?(this.temp.jdbcUrl="jdbc:hive2://{host}:{port}/{database}",this.temp.jdbcDriverClass="org.apache.hive.jdbc.HiveDriver",this.hbase=this.mongodb=!1,this.jdbc=!0):"kafka"===e&&(this.temp.jdbcUrl="{host}:9092,{host}:9092/{topicname}"),this.getShowStrategy(e)},fetchData:function(){var e=this;this.listLoading=!0,o["f"](this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,datasourceName:"",datasourceGroup:"",jdbcUsername:"",jdbcPassword:"",jdbcUrl:"",jdbcDriverClass:"",comments:""}},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&o["a"](e.temp).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}))},testDataSource:function(){var e=this;this.$refs["dataForm"].validate((function(t){o["g"](e.temp).then((function(t){!1===t.data?e.$notify({title:"测试连接",message:"数据库连接出错，请联系管理员！！！",type:"fail",duration:2e3}):e.$notify({title:"测试连接",message:"连接成功",type:"success",duration:2e3})}))}))},testDataSource2:function(e){var t=this;this.temp=Object.assign({},e),o["g"](this.temp).then((function(e){t.fetchData(),!1===e.data?t.$notify({title:"测试连接",message:"数据源连接异常",type:"error",duration:2e3}):t.$notify({title:"测试连接",message:"连接成功",type:"success",duration:2e3})}))},handleUpdate:function(e){var t=this;this.getShowStrategy(e.datasource),this.temp=Object.assign({},e),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a=Object.assign({},e.temp);o["h"](a).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},getShowStrategy:function(e){"hbase"===e?(this.jdbc=this.mongodb=!1,this.hbase=!0):"mongodb"===e?(this.jdbc=this.hbase=!1,this.mongodb=!0,this.temp.jdbcUrl="mongodb://[username:password@]host1[:port1][,...hostN[:portN]]][/[database][?options]]"):(this.hbase=this.mongodb=!1,this.jdbc=!0)},handleDelete:function(e){var t=this;this.$confirm("确认删除该数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a=[];a.push(e.id),o["b"](e.id).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))})).catch((function(){}))},handleFetchPv:function(e){var t=this;o["c"](e).then((function(e){t.pluginData=e,t.dialogPvVisible=!0}))},formatJson:function(e,t){return t.map((function(t){return e.map((function(e){return"timestamp"===e?Object(l["h"])(t[e]):t[e]}))}))},changePass:function(e){this.visible=!("show"===e)}}},m=u,p=a("2877"),b=Object(p["a"])(m,s,r,!1,null,null,null);t["default"]=b.exports},67248:function(e,t,a){"use strict";a("8d41");var s="@@wavesContext";function r(e,t){function a(a){var s=Object.assign({},t.value),r=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},s),o=r.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var i=o.getBoundingClientRect(),l=o.querySelector(".waves-ripple");switch(l?l.className="waves-ripple":(l=document.createElement("span"),l.className="waves-ripple",l.style.height=l.style.width=Math.max(i.width,i.height)+"px",o.appendChild(l)),r.type){case"center":l.style.top=i.height/2-l.offsetHeight/2+"px",l.style.left=i.width/2-l.offsetWidth/2+"px";break;default:l.style.top=(a.pageY-i.top-l.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",l.style.left=(a.pageX-i.left-l.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return l.style.backgroundColor=r.color,l.className="waves-ripple z-active",!1}}return e[s]?e[s].removeHandle=a:e[s]={removeHandle:a},a}var o={bind:function(e,t){e.addEventListener("click",r(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[s].removeHandle,!1),e.addEventListener("click",r(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[s].removeHandle,!1),e[s]=null,delete e[s]}},i=function(e){e.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(i)),o.install=i;t["a"]=o},"7e39":function(e,t,a){"use strict";a.d(t,"f",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"h",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return c})),a.d(t,"g",(function(){return n})),a.d(t,"d",(function(){return d})),a.d(t,"e",(function(){return u}));var s=a("b775");function r(e){return Object(s["a"])({url:"/api/jobJdbcDatasource/list",method:"get",params:e})}function o(e){return Object(s["a"])({url:"/api/jobJdbcDatasource/"+e,method:"get"})}function i(e){return Object(s["a"])({url:"/api/jobJdbcDatasource/update",method:"post",data:e})}function l(e){return Object(s["a"])({url:"/api/jobJdbcDatasource",method:"post",data:e})}function c(e){return Object(s["a"])({url:"/api/jobJdbcDatasource/remove?id="+e,method:"post"})}function n(e){return Object(s["a"])({url:"/api/jobJdbcDatasource/test",method:"post",data:e})}function d(e){return Object(s["a"])({url:"/api/jobJdbcDatasource/findSourceName",method:"get",params:e})}function u(e){return Object(s["a"])({url:"/api/jobJdbcDatasource/list?current=1&size=200&ascs=datasource_name",method:"get",params:e})}},"8d41":function(e,t,a){},f656:function(e,t,a){"use strict";function s(){return{min:1,max:50,message:"长度在1-50个字符",trigger:["blur","change"]}}function r(){return{max:200,message:"长度最多为200个字符",trigger:["blur","change"]}}a.d(t,"b",(function(){return s})),a.d(t,"a",(function(){return r}))}}]);