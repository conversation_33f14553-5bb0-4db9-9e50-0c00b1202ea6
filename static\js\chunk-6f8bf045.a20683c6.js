(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6f8bf045","chunk-17a231b2"],{"0f7c":function(t,e,a){},1810:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"d",(function(){return l})),a.d(e,"a",(function(){return o})),a.d(e,"e",(function(){return i})),a.d(e,"c",(function(){return s}));var r=a("b775");function n(t){return Object(r["a"])({url:"/api/devEnvUpload/remove",method:"post",params:t})}function l(t){return Object(r["a"])({url:"/api/devEnvUpload/list",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/api/devEnvUpload/add",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/api/devEnvUpload/update",method:"post",data:t})}function s(){return Object(r["a"])({url:"/api/devEnvUpload/findDevName",method:"get"})}},"3dc9":function(t,e,a){},"6c35":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dev-sql"},[t.sqlDataList?a("div",{staticClass:"nav-bar"},[a("div",{staticClass:"search"},[a("el-input",{attrs:{size:"small","prefix-icon":"el-icon-search",placeholder:"任务名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleSearch(e)}},model:{value:t.searchInput,callback:function(e){t.searchInput=e},expression:"searchInput"}}),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"small"},on:{click:t.handleSearch}},[t._v(" 查询 ")])],1),a("ul",t._l(t.sqlDataList,(function(e,r){return a("li",{key:"sql-data-list-"+r,class:{"cur-li":t.curData&&t.curData.id===e.id},on:{click:function(a){return t.setCurData(e)}}},[a("div",{staticClass:"icon-name"},[a("span",{staticClass:"icon"},[t._v(t._s(e.tasktype))]),a("span",{staticStyle:{"margin-left":"5px"}},[t._v(t._s(e.name))])]),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),t.handleUpdate(e)}}},[t._v(" 修改任务 ")])],1)})),0)]):t._e(),a("div",{staticClass:"app-main"},[a("div",{staticClass:"buttons"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.handleAdd}},[t._v("新增任务")]),a("el-button",{attrs:{type:"primary",size:"small",disabled:!t.curData},on:{click:t.formartSql}},[t._v("格式化SQL")]),a("el-button",{attrs:{type:"primary",size:"small",disabled:!t.curData},on:{click:t.executeData}},[t._v("验证SQL")]),a("el-button",{attrs:{type:"primary",size:"small",disabled:!t.curData},on:{click:function(){t.curData.sql_text=t.sqlText,t.updateData(t.curData)}}},[t._v(" 修改SQL ")])],1),t.curData?a("div",{staticClass:"editor"},[a("SqlEditor",{attrs:{height:"100%"},model:{value:t.sqlText,callback:function(e){t.sqlText=e},expression:"sqlText"}})],1):t._e()]),a("InfoDialog",{ref:"InfoDialog"})],1)},n=[],l=(a("b0c0"),a("e9c4"),a("ac1f"),a("5319"),a("1bf5")),o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{ref:"InfoDialog",attrs:{visible:t.dialogShow,title:t.infoTitle,width:"600px","append-to-body":""},on:{"update:visible":function(e){t.dialogShow=e},close:t.close}},[a("el-form",{ref:"infoDialogForm",attrs:{model:t.formData,rules:t.rules,size:"medium","label-width":"100px"}},[a("el-row",{attrs:{gutter:24}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"任务名称",prop:"name"}},[a("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name","string"===typeof e?e.trim():e)},expression:"formData.name"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务类型",prop:"tasktype"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.formData.tasktype,callback:function(e){t.$set(t.formData,"tasktype",e)},expression:"formData.tasktype"}},[a("el-option",{attrs:{label:"Flink",value:"Flink"}}),a("el-option",{attrs:{label:"Spark",value:"Spark"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"运行方式",prop:"runtype"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.formData.runtype,callback:function(e){t.$set(t.formData,"runtype",e)},expression:"formData.runtype"}},[a("el-option",{attrs:{label:"stantalone",value:"stantalone"}}),a("el-option",{attrs:{label:"yarn",value:"yarn"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"环境名称",prop:"propvalue"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:t.propvalueChange},model:{value:t.formData.propvalue,callback:function(e){t.$set(t.formData,"propvalue",e)},expression:"formData.propvalue"}},t._l(t.devNameArr,(function(t,e){return a("el-option",{key:"dev-obj-"+e,attrs:{label:t.name,value:t.propValue}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"JAR包选择",prop:"jid"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!t.formData.propvalue,placeholder:"请选择"},model:{value:t.formData.jid,callback:function(e){t.$set(t.formData,"jid",e)},expression:"formData.jid"}},t._l(t.jarNameArr,(function(t,e){return a("el-option",{key:"dev-obj-"+e,attrs:{label:t.jarName,value:t.jid}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"运行主类",prop:"mainClass"}},[a("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:t.formData.mainClass,callback:function(e){t.$set(t.formData,"mainClass","string"===typeof e?e.trim():e)},expression:"formData.mainClass"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"并行度",prop:"parameter"}},[a("el-input",{attrs:{clearable:!0,placeholder:"请输入"},model:{value:t.formData.parameter,callback:function(e){t.$set(t.formData,"parameter","string"===typeof e?e.trim():e)},expression:"formData.parameter"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"运行参数",prop:"run_param"}},[a("el-input",{attrs:{clearable:!0,placeholder:"多个参数以空格分隔"},model:{value:t.formData.run_param,callback:function(e){t.$set(t.formData,"run_param",e)},expression:"formData.run_param"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"任务描述",prop:"task_describe"}},[a("el-input",{attrs:{clearable:!0,type:"textarea",autosize:{minRows:2,maxRows:4},placeholder:"请输入"},model:{value:t.formData.task_describe,callback:function(e){t.$set(t.formData,"task_describe","string"===typeof e?e.trim():e)},expression:"formData.task_describe"}})],1)],1)],1),a("div",{staticStyle:{display:"flex","align-items":"left","justify-content":"right"}},[a("el-button",{on:{click:t.handleCancel}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleSubmit}},[t._v("确定")])],1)],1)],1)},i=[],s=a("1810"),u=a("8bb3"),c={name:"ChangePassword",data:function(){return{rules:{name:[{required:!0,message:"不能为空",trigger:"blur"}],tasktype:[{required:!0,message:"不能为空",trigger:"blur"}],runtype:[{required:!0,message:"不能为空",trigger:"blur"}],sql_text:[{required:!0,message:"不能为空",trigger:"blur"}],run_param:[{required:!0,message:"不能为空",trigger:"blur"}],task_describe:[{required:!0,message:"不能为空",trigger:"blur"}],propvalue:[{required:!0,message:"不能为空",trigger:"blur"}],jarname:[{required:!0,message:"不能为空",trigger:"blur"}],mainClass:[{required:!0,message:"不能为空",trigger:"blur"}],parameter:[{required:!0,message:"不能为空",trigger:"blur"}],jid:[{required:!0,message:"不能为空",trigger:"blur"}]},dialogShow:!1,onClose:null,onSubmit:null,infoTitle:"详细信息",defaultFormData:{id:"",name:"",type:"SQL",tasktype:"Flink",runtype:"yarn-session",run_param:"",sql_text:"",task_describe:"",propvalue:"",jarname:"",jid:"",mainClass:"",parameter:""},formData:null,devNameArr:[],jarNameArr:[]}},created:function(){var t=this;this.formData=Object.assign({},this.defaultFormData),Object(s["c"])().then((function(e){t.devNameArr=e.content.data}))},methods:{propvalueChange:function(){var t=this;this.formData.jarname="",this.formData.jid="",this.formData.propvalue&&Object(u["f"])({propValue:this.formData.propvalue}).then((function(e){t.jarNameArr=e.content.data}))},openDialog:function(t){var e=this,a=t.formData,r=void 0===a?{}:a,n=t.infoTitle,l=void 0===n?"":n,o=t.onSubmit,i=void 0===o?null:o,s=t.onClose,c=void 0===s?null:s;this.onSubmit=i,this.onClose=c,this.infoTitle=l,this.formData=Object.assign({},this.defaultFormData,r),this.formData.type="SQL",this.dialogShow=!0,this.formData.propvalue&&Object(u["f"])({propValue:this.formData.propvalue}).then((function(t){e.jarNameArr=t.content.data}))},handleSubmit:function(){var t=this;this.$refs["infoDialogForm"].validate((function(e){var a;return e?(null===(a=t.onSubmit)||void 0===a||a.call(t,t.formData),t.dialogShow=!1,!0):(t.$message.error("填写异常，请仔细检查"),!1)}))},handleCancel:function(){this.dialogShow=!1},close:function(){var t;null===(t=this.onClose)||void 0===t||t.call(this,this.formData)}}},p=c,m=(a("c36a"),a("2877")),d=Object(m["a"])(p,o,i,!1,null,"18b9c64e",null),f=d.exports,h=a("db05"),b={nmae:"SQLlistManageDevelopment",components:{InfoDialog:f,SqlEditor:l["a"]},data:function(){return{sqlDataList:null,searchInput:"",jsonResult:"",sqlText:"",curData:null,dataList:[]}},mounted:function(){var t=this;this.handleSearch().then((function(){t.sqlDataList.length&&t.setCurData(t.sqlDataList[0])}))},methods:{getData:function(){var t=this,e={type:"SQL"};return this.searchInput&&(e["name"]=this.searchInput),Object(u["d"])(e).then((function(e){t.sqlDataList=e.content.data}))},setCurData:function(t){var e=this,a=function(){e.curData=null,setTimeout((function(){var a;e.curData=t,e.sqlText=(null===(a=e.curData)||void 0===a?void 0:a.sql_text)||""}),200)};this.curData&&this.curData.sql_text!==this.sqlText?this.$confirm("该操作将覆盖当前未保存修改，是否继续","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(t){return a()})):a()},handleSearch:function(){return this.getData()},updateData:function(t){var e=this;Object(u["b"])(t).then((function(){e.$message.success("修改SQL成功"),e.getData()}))},handleUpdate:function(t){this.$refs["InfoDialog"].openDialog({formData:t,infoTitle:"修改配置",onSubmit:this.updateData})},handleAdd:function(){var t=this;this.$refs["InfoDialog"].openDialog({infoTitle:"新增任务",onSubmit:function(e){Object(u["a"])(e).then((function(){t.$message.success("新增任务成功"),t.getData()}))}})},formartSql:function(){var t=this;this.sqlText=Object(h["format"])(this.sqlText).replace(/# /g,"#").replace(/{ /g,"{").replace(/ }/g,"}").replace(/< foreach/g,"\n<foreach\n").replace(/< \/ foreach >/g,"\n</foreach>\n").replace(/< if/g,"\n<if").replace(/< \/ if >/g,"\n</if>\n").replace(/<\nwhere\n {2}>/g,"\n<where>\n").replace(/< \/\nwhere\n {2}>/g,"\n</where>\n").replace(/< trim/g,"\n<trim").replace(/< \/ trim >/g,"\n</trim>\n").toLowerCase();var e=this.curData;this.curData=null,setTimeout((function(){t.curData=e}),200)},executeData:function(t){var e=this,a={sql_text:this.sqlText};Object(u["c"])(a).then((function(t){e.$message.success("验证SQL通过"),e.jsonResult=JSON.stringify(t,null,2)}))}}},v=b,g=(a("c30e"),Object(m["a"])(v,r,n,!1,null,"69473f59",null));e["default"]=g.exports},"8bb3":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return l})),a.d(e,"c",(function(){return o})),a.d(e,"f",(function(){return i})),a.d(e,"e",(function(){return s})),a.d(e,"d",(function(){return u}));var r=a("b775");function n(t){return Object(r["a"])({url:"api/devJar/add",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/api/devJar/update",method:"post",data:t})}function o(t){return t=Object.assign({sql_text:t.sql_text},t||{}),Object(r["a"])({url:"/api/deployTask/checkSQL",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/api/devTask/getAllJars",method:"get",params:t})}function s(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Object(r["a"])({url:"/api/devTask/upload",method:"post",data:t,timeout:0,onUploadProgress:function(t){var a=t.loaded/t.total*100|0;console.log("上传进度：".concat(a,"%")),null===e||void 0===e||e(a)}})}function u(t){return t=Object.assign({type:"SQL"},t||{}),Object(r["a"])({url:"/api/devJar/list",method:"get",params:t})}},a7be:function(t,e,a){},c30e:function(t,e,a){"use strict";a("3dc9")},c36a:function(t,e,a){"use strict";a("e389")},e389:function(t,e,a){}}]);