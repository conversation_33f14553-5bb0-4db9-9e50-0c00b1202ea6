(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d800437c"],{"0806":function(t,e,i){"use strict";i.d(e,"a",(function(){return h}));var n=i("5728"),s=i("8a60"),o=i.n(s),r=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class a extends n["i"]{get graph(){return this.options.graph}constructor(t){super(),this.options=t;const e=this.graph.getPlugin("scroller");this.container=e?e.container:this.graph.container,t.global?this.target=document:(this.target=this.container,this.disabled||this.target.setAttribute("tabindex","-1"),this.graph.on("cell:mouseup",this.focus,this),this.graph.on("blank:mouseup",this.focus,this)),this.mousetrap=a.createMousetrap(this)}get disabled(){return!0!==this.options.enabled}enable(){this.disabled&&(this.options.enabled=!0,this.target instanceof HTMLElement&&this.target.setAttribute("tabindex","-1"))}disable(){this.disabled||(this.options.enabled=!1,this.target instanceof HTMLElement&&this.target.removeAttribute("tabindex"))}on(t,e,i){this.mousetrap.bind(this.getKeys(t),e,i)}off(t,e){this.mousetrap.unbind(this.getKeys(t),e)}clear(){this.mousetrap.reset()}trigger(t,e){this.mousetrap.trigger(t,e)}focus(t){const e=this.isInputEvent(t.e);if(e)return;const i=this.target;i.focus({preventScroll:!0})}getKeys(t){return(Array.isArray(t)?t:[t]).map(t=>this.formatkey(t))}formatkey(t){const e=t.toLocaleLowerCase().replace(/\s/g,"").replace("delete","del").replace("cmd","command").replace("arrowup","up").replace("arrowright","right").replace("arrowdown","down").replace("arrowleft","left"),i=this.options.format;return i?n["k"].call(i,this.graph,e):e}isGraphEvent(t){const e=t.target,i=t.currentTarget;return!!e&&(e===this.target||i===this.target||e===document.body||n["j"].contains(this.container,e))}isInputEvent(t){var e;const i=t.target,s=null===(e=null===i||void 0===i?void 0:i.tagName)||void 0===e?void 0:e.toLowerCase();let o=["input","textarea"].includes(s);return"true"===n["j"].attr(i,"contenteditable")&&(o=!0),o}isEnabledForEvent(t){const e=!this.disabled&&this.isGraphEvent(t),i=this.isInputEvent(t);if(e){if(i&&("Backspace"===t.key||"Delete"===t.key))return!1;if(this.options.guard)return n["k"].call(this.options.guard,this.graph,t)}return e}dispose(){this.mousetrap.reset()}}r([n["i"].dispose()],a.prototype,"dispose",null),function(t){function e(t){const e=new o.a(t.target),i=e.stopCallback;return e.stopCallback=(n,s,o)=>!t.isEnabledForEvent(n)||!!i&&i.call(e,n,s,o),e}t.createMousetrap=e}(a||(a={})),n["m"].prototype.isKeyboardEnabled=function(){const t=this.getPlugin("keyboard");return!!t&&t.isEnabled()},n["m"].prototype.enableKeyboard=function(){const t=this.getPlugin("keyboard");return t&&t.enable(),this},n["m"].prototype.disableKeyboard=function(){const t=this.getPlugin("keyboard");return t&&t.disable(),this},n["m"].prototype.toggleKeyboard=function(t){const e=this.getPlugin("keyboard");return e&&e.toggleEnabled(t),this},n["m"].prototype.bindKey=function(t,e,i){const n=this.getPlugin("keyboard");return n&&n.bindKey(t,e,i),this},n["m"].prototype.unbindKey=function(t,e){const i=this.getPlugin("keyboard");return i&&i.unbindKey(t,e),this},n["m"].prototype.clearKeys=function(){const t=this.getPlugin("keyboard");return t&&t.clear(),this},n["m"].prototype.triggerKey=function(t,e){const i=this.getPlugin("keyboard");return i&&i.trigger(t,e),this};var l=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class h extends n["i"]{constructor(t={}){super(),this.name="keyboard",this.options=Object.assign({enabled:!0},t)}init(t){this.keyboardImpl=new a(Object.assign(Object.assign({},this.options),{graph:t}))}isEnabled(){return!this.keyboardImpl.disabled}enable(){this.keyboardImpl.enable()}disable(){this.keyboardImpl.disable()}toggleEnabled(t){return null!=t?t!==this.isEnabled()&&(t?this.enable():this.disable()):this.isEnabled()?this.disable():this.enable(),this}bindKey(t,e,i){return this.keyboardImpl.on(t,e,i),this}trigger(t,e){return this.keyboardImpl.trigger(t,e),this}clear(){return this.keyboardImpl.clear(),this}unbindKey(t,e){return this.keyboardImpl.off(t,e),this}dispose(){this.keyboardImpl.dispose()}}l([n["i"].dispose()],h.prototype,"dispose",null)},"17df":function(t,e,i){"use strict";i.d(e,"a",(function(){return o}));var n=i("5728");n["m"].prototype.toSVG=function(t,e){const i=this.getPlugin("export");i&&i.toSVG(t,e)},n["m"].prototype.toPNG=function(t,e){const i=this.getPlugin("export");i&&i.toPNG(t,e)},n["m"].prototype.toJPEG=function(t,e){const i=this.getPlugin("export");i&&i.toJPEG(t,e)},n["m"].prototype.exportPNG=function(t,e){const i=this.getPlugin("export");i&&i.exportPNG(t,e)},n["m"].prototype.exportJPEG=function(t,e){const i=this.getPlugin("export");i&&i.exportJPEG(t,e)},n["m"].prototype.exportSVG=function(t,e){const i=this.getPlugin("export");i&&i.exportSVG(t,e)};var s=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class o extends n["c"]{constructor(){super(),this.name="export"}get view(){return this.graph.view}init(t){this.graph=t}exportPNG(t="chart",e={}){this.toPNG(e=>{n["h"].downloadDataUri(e,t)},e)}exportJPEG(t="chart",e={}){this.toPNG(e=>{n["h"].downloadDataUri(e,t)},e)}exportSVG(t="chart",e={}){this.toSVG(e=>{n["h"].downloadDataUri(n["h"].svgToDataUrl(e),t)},e)}toSVG(t,e={}){this.notify("before:export",e);const i=this.view.svg,s=n["v"].create(i).clone();let o=s.node;const r=s.findOne("."+this.view.prefixClassName("graph-svg-stage")),a=e.viewBox||this.graph.graphToLocal(this.graph.getContentBBox()),l=e.preserveDimensions;if(l){const t="boolean"===typeof l?a:l;s.attr({width:t.width,height:t.height})}if(s.removeAttribute("style").attr("viewBox",[a.x,a.y,a.width,a.height].join(" ")),r.removeAttribute("transform"),!1!==e.copyStyles){const t=i.ownerDocument,e=Array.from(i.querySelectorAll("*")),s=Array.from(o.querySelectorAll("*")),r=t.styleSheets.length,a=[];for(let i=r-1;i>=0;i-=1)a[i]=t.styleSheets[i],t.styleSheets[i].disabled=!0;const l={};e.forEach((t,e)=>{const i=window.getComputedStyle(t,null),n={};Object.keys(i).forEach(t=>{n[t]=i.getPropertyValue(t)}),l[e]=n}),r!==t.styleSheets.length&&a.forEach((e,i)=>{t.styleSheets[i]=e});for(let i=0;i<r;i+=1)t.styleSheets[i].disabled=!1;const h={};e.forEach((t,e)=>{const i=window.getComputedStyle(t,null),s=l[e],o={};Object.keys(i).forEach(t=>{n["q"].isNumber(t)||i.getPropertyValue(t)===s[t]||(o[t]=i.getPropertyValue(t))}),h[e]=o}),s.forEach((t,e)=>{n["j"].css(t,h[e])})}const h=e.stylesheet;if("string"===typeof h){const t=i.ownerDocument.implementation.createDocument(null,"xml",null).createCDATASection(h);s.prepend(n["v"].create("style",{type:"text/css"},[t]))}const c=()=>{const i=e.beforeSerialize;if("function"===typeof i){const t=n["k"].call(i,this.graph,o);t instanceof SVGSVGElement&&(o=t)}const s=(new XMLSerializer).serializeToString(o).replace(/&nbsp;/g," ");this.notify("after:export",e),t(s)};if(e.serializeImages){const t=s.find("image").map(t=>new Promise(e=>{const i=t.attr("xlink:href")||t.attr("href");n["h"].imageToDataUri(i,(i,n)=>{!i&&n&&t.attr("xlink:href",n),e()})}));Promise.all(t).then(c)}else c()}toDataURL(t,e){let i=e.viewBox||this.graph.getContentBBox();const s=n["q"].normalizeSides(e.padding);e.width&&e.height&&(s.left+s.right>=e.width&&(s.left=s.right=0),s.top+s.bottom>=e.height&&(s.top=s.bottom=0));const o=new n["t"](-s.left,-s.top,s.left+s.right,s.top+s.bottom);if(e.width&&e.height){const t=i.width+s.left+s.right,n=i.height+s.top+s.bottom;o.scale(t/e.width,n/e.height)}i=n["t"].create(i).moveAndExpand(o);const r="number"===typeof e.width&&"number"===typeof e.height?{width:e.width,height:e.height}:i;let a=e.ratio?parseFloat(e.ratio):1;Number.isFinite(a)&&0!==a||(a=1);const l={width:Math.max(Math.round(r.width*a),1),height:Math.max(Math.round(r.height*a),1)};{const t=document.createElement("canvas"),e=t.getContext("2d");t.width=l.width,t.height=l.height;const i=l.width-1,n=l.height-1;e.fillStyle="rgb(1,1,1)",e.fillRect(i,n,1,1);const s=e.getImageData(i,n,1,1).data;if(1!==s[0]||1!==s[1]||1!==s[2])throw new Error("size exceeded")}const h=new Image;h.onload=()=>{const i=document.createElement("canvas");i.width=l.width,i.height=l.height;const n=i.getContext("2d");n.fillStyle=e.backgroundColor||"white",n.fillRect(0,0,l.width,l.height);try{n.drawImage(h,0,0,l.width,l.height);const s=i.toDataURL(e.type,e.quality);t(s)}catch(s){}},this.toSVG(t=>{h.src="data:image/svg+xml,"+encodeURIComponent(t)},Object.assign(Object.assign({},e),{viewBox:i,serializeImages:!0,preserveDimensions:Object.assign({},l)}))}toPNG(t,e={}){this.toDataURL(t,Object.assign(Object.assign({},e),{type:"image/png"}))}toJPEG(t,e={}){this.toDataURL(t,Object.assign(Object.assign({},e),{type:"image/jpeg"}))}notify(t,e){this.trigger(t,e),this.graph.trigger(t,e)}dispose(){this.off()}}s([n["c"].dispose()],o.prototype,"dispose",null)},"2b91":function(t,e,i){"use strict";i.d(e,"a",(function(){return g}));var n=i("5728");const s=".x6-widget-dnd {\n  position: absolute;\n  top: -10000px;\n  left: -10000px;\n  z-index: 999999;\n  display: none;\n  cursor: move;\n  opacity: 0.7;\n  pointer-events: 'cursor';\n}\n.x6-widget-dnd.dragging {\n  display: inline-block;\n}\n.x6-widget-dnd.dragging * {\n  pointer-events: none !important;\n}\n.x6-widget-dnd .x6-graph {\n  background: transparent;\n  box-shadow: none;\n}\n";var o,r=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class a extends n["w"]{get targetScroller(){const t=this.options.target,e=t.getPlugin("scroller");return e}get targetGraph(){return this.options.target}get targetModel(){return this.targetGraph.model}get snapline(){const t=this.options.target,e=t.getPlugin("snapline");return e}constructor(t){super(),this.name="dnd",this.options=Object.assign(Object.assign({},a.defaults),t),this.init()}init(){n["g"].ensure(this.name,s),this.container=document.createElement("div"),n["j"].addClass(this.container,this.prefixClassName("widget-dnd")),this.draggingGraph=new n["m"](Object.assign(Object.assign({},this.options.delegateGraphOptions),{container:document.createElement("div"),width:1,height:1,async:!1})),n["j"].append(this.container,this.draggingGraph.container)}start(t,e){const i=e;i.preventDefault(),this.targetModel.startBatch("dnd"),n["j"].addClass(this.container,"dragging"),n["j"].appendTo(this.container,this.options.draggingContainer||document.body),this.sourceNode=t,this.prepareDragging(t,i.clientX,i.clientY);const s=this.updateNodePosition(i.clientX,i.clientY);this.isSnaplineEnabled()&&(this.snapline.captureCursorOffset({e:i,node:t,cell:t,view:this.draggingView,x:s.x,y:s.y}),this.draggingNode.on("change:position",this.snap,this)),this.delegateDocumentEvents(a.documentEvents,i.data)}isSnaplineEnabled(){return this.snapline&&this.snapline.isEnabled()}prepareDragging(t,e,i){const n=this.draggingGraph,s=n.model,o=this.options.getDragNode(t,{sourceNode:t,draggingGraph:n,targetGraph:this.targetGraph});o.position(0,0);let r=5;if(this.isSnaplineEnabled()&&(r+=this.snapline.options.tolerance||0),this.isSnaplineEnabled()||this.options.scaled){const t=this.targetGraph.transform.getScale();n.scale(t.sx,t.sy),r*=Math.max(t.sx,t.sy)}else n.scale(1,1);this.clearDragging(),s.resetCells([o]);const a=n.findViewByCell(o);a.undelegateEvents(),a.cell.off("changed"),n.fitToContent({padding:r,allowNewOrigin:"any",useCellGeometry:!1});const l=a.getBBox();this.geometryBBox=a.getBBox({useCellGeometry:!0}),this.delta=this.geometryBBox.getTopLeft().diff(l.getTopLeft()),this.draggingNode=o,this.draggingView=a,this.draggingBBox=o.getBBox(),this.padding=r,this.originOffset=this.updateGraphPosition(e,i)}updateGraphPosition(t,e){const i=document.body.scrollTop||document.documentElement.scrollTop,s=document.body.scrollLeft||document.documentElement.scrollLeft,o=this.delta,r=this.geometryBBox,a=this.padding||5,l={left:t-o.x-r.width/2-a+s,top:e-o.y-r.height/2-a+i};return this.draggingGraph&&n["j"].css(this.container,{left:l.left+"px",top:l.top+"px"}),l}updateNodePosition(t,e){const i=this.targetGraph.clientToLocal(t,e),n=this.draggingBBox;return i.x-=n.width/2,i.y-=n.height/2,this.draggingNode.position(i.x,i.y),i}snap({cell:t,current:e,options:i}){const n=t;if(i.snapped){const t=this.draggingBBox;n.position(t.x+i.tx,t.y+i.ty,{silent:!0}),this.draggingView.translate(),n.position(e.x,e.y,{silent:!0}),this.snapOffset={x:i.tx,y:i.ty}}else this.snapOffset=null}onDragging(t){const e=this.draggingView;if(e){t.preventDefault();const i=this.normalizeEvent(t),n=i.clientX,s=i.clientY;this.updateGraphPosition(n,s);const o=this.updateNodePosition(n,s),r=this.targetGraph.options.embedding.enabled,a=(r||this.isSnaplineEnabled())&&this.isInsideValidArea({x:n,y:s});if(r){e.setEventData(i,{graph:this.targetGraph,candidateEmbedView:this.candidateEmbedView});const t=e.getEventData(i);a?e.processEmbedding(i,t):e.clearEmbedding(t),this.candidateEmbedView=t.candidateEmbedView}this.isSnaplineEnabled()&&(a?this.snapline.snapOnMoving({e:i,view:e,x:o.x,y:o.y}):this.snapline.hide())}}onDragEnd(t){const e=this.draggingNode;if(e){const i=this.normalizeEvent(t),s=this.draggingView,o=this.draggingBBox,r=this.snapOffset;let a=o.x,l=o.y;r&&(a+=r.x,l+=r.y),e.position(a,l,{silent:!0});const h=this.drop(e,{x:i.clientX,y:i.clientY}),c=t=>{t?(this.onDropped(e),this.targetGraph.options.embedding.enabled&&s&&(s.setEventData(i,{cell:t,graph:this.targetGraph,candidateEmbedView:this.candidateEmbedView}),s.finalizeEmbedding(i,s.getEventData(i)))):this.onDropInvalid(),this.candidateEmbedView=null,this.targetModel.stopBatch("dnd")};n["k"].isAsync(h)?(this.undelegateDocumentEvents(),h.then(c)):c(h)}}clearDragging(){this.draggingNode&&(this.sourceNode=null,this.draggingNode.remove(),this.draggingNode=null,this.draggingView=null,this.delta=null,this.padding=null,this.snapOffset=null,this.originOffset=null,this.undelegateDocumentEvents())}onDropped(t){this.draggingNode===t&&(this.clearDragging(),n["j"].removeClass(this.container,"dragging"),n["j"].remove(this.container))}onDropInvalid(){const t=this.draggingNode;t&&this.onDropped(t)}isInsideValidArea(t){let e,i=null;const n=this.targetGraph,s=this.targetScroller;this.options.dndContainer&&(i=this.getDropArea(this.options.dndContainer));const o=i&&i.containsPoint(t);if(s)if(s.options.autoResize)e=this.getDropArea(s.container);else{const t=this.getDropArea(s.container);e=this.getDropArea(n.container).intersectsWithRect(t)}else e=this.getDropArea(n.container);return!o&&e&&e.containsPoint(t)}getDropArea(t){const e=n["j"].offset(t),i=document.body.scrollTop||document.documentElement.scrollTop,s=document.body.scrollLeft||document.documentElement.scrollLeft;return n["t"].create({x:e.left+parseInt(n["j"].css(t,"border-left-width"),10)-s,y:e.top+parseInt(n["j"].css(t,"border-top-width"),10)-i,width:t.clientWidth,height:t.clientHeight})}drop(t,e){if(this.isInsideValidArea(e)){const i=this.targetGraph,s=i.model,o=i.clientToLocal(e),r=this.sourceNode,a=this.options.getDropNode(t,{sourceNode:r,draggingNode:t,targetGraph:this.targetGraph,draggingGraph:this.draggingGraph}),l=a.getBBox();o.x+=l.x-l.width/2,o.y+=l.y-l.height/2;const h=this.snapOffset?1:i.getGridSize();a.position(n["l"].snapToGrid(o.x,h),n["l"].snapToGrid(o.y,h)),a.removeZIndex();const c=this.options.validateNode,d=!c||c(a,{sourceNode:r,draggingNode:t,droppingNode:a,targetGraph:i,draggingGraph:this.draggingGraph});return"boolean"===typeof d?d?(s.addCell(a,{stencil:this.cid}),a):null:n["k"].toDeferredBoolean(d).then(t=>t?(s.addCell(a,{stencil:this.cid}),a):null)}return null}onRemove(){this.draggingGraph&&(this.draggingGraph.view.remove(),this.draggingGraph.dispose())}dispose(){this.remove(),n["g"].clean(this.name)}}function l(t,e={}){const i=n["n"].isModel(t)?t:(new n["n"]).resetCells(t,{sort:!1,dryrun:!0}),s=i.getNodes(),r=e.columns||1,a=Math.ceil(s.length/r),l=e.dx||0,h=e.dy||0,c=!1!==e.center,d=!0===e.resizeToFit,p=e.marginX||0,g=e.marginY||0,u=[];let f=e.columnWidth;if("compact"===f)for(let n=0;n<r;n+=1){const t=o.getNodesInColumn(s,n,r);u.push(o.getMaxDim(t,"width")+l)}else{null!=f&&"auto"!==f||(f=o.getMaxDim(s,"width")+l);for(let t=0;t<r;t+=1)u.push(f)}const m=o.accumulate(u,p),b=[];let y=e.rowHeight;if("compact"===y)for(let n=0;n<a;n+=1){const t=o.getNodesInRow(s,n,r);b.push(o.getMaxDim(t,"height")+h)}else{null!=y&&"auto"!==y||(y=o.getMaxDim(s,"height")+h);for(let t=0;t<a;t+=1)b.push(y)}const x=o.accumulate(b,g);i.startBatch("layout"),s.forEach((t,i)=>{const n=i%r,s=Math.floor(i/r),o=u[n],a=b[s];let p=0,g=0,f=t.getSize();if(d){let i=o-2*l,n=a-2*h;const s=f.height*(f.width?i/f.width:1),r=f.width*(f.height?n/f.height:1);a<s?i=r:n=s,f={width:i,height:n},t.setSize(f,e)}c&&(p=(o-f.width)/2,g=(a-f.height)/2),t.position(m[n]+l+p,x[s]+h+g,e)}),i.stopBatch("layout")}r([n["w"].dispose()],a.prototype,"dispose",null),function(t){t.defaults={getDragNode:t=>t.clone(),getDropNode:t=>t.clone()},t.documentEvents={mousemove:"onDragging",touchmove:"onDragging",mouseup:"onDragEnd",touchend:"onDragEnd",touchcancel:"onDragEnd"}}(a||(a={})),function(t){function e(t,e){return t.reduce((t,i)=>Math.max(null===i||void 0===i?void 0:i.getSize()[e],t),0)}function i(t,e,i){const n=[];for(let s=i*e,o=s+i;s<o;s+=1)t[s]&&n.push(t[s]);return n}function n(t,e,i){const n=[];for(let s=e,o=t.length;s<o;s+=i)t[s]&&n.push(t[s]);return n}function s(t,e){return t.reduce((t,e,i)=>(t.push(t[i]+e),t),[e||0])}t.getMaxDim=e,t.getNodesInRow=i,t.getNodesInColumn=n,t.accumulate=s}(o||(o={}));const h=".x6-widget-dnd {\n  position: absolute;\n  top: -10000px;\n  left: -10000px;\n  z-index: 999999;\n  display: none;\n  cursor: move;\n  opacity: 0.7;\n  pointer-events: 'cursor';\n}\n.x6-widget-dnd.dragging {\n  display: inline-block;\n}\n.x6-widget-dnd.dragging * {\n  pointer-events: none !important;\n}\n.x6-widget-dnd .x6-graph {\n  background: transparent;\n  box-shadow: none;\n}\n.x6-widget-stencil {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n.x6-widget-stencil::after {\n  position: absolute;\n  top: 0;\n  display: block;\n  width: 100%;\n  height: 20px;\n  padding: 8px 0;\n  line-height: 20px;\n  text-align: center;\n  opacity: 0;\n  transition: top 0.1s linear, opacity 0.1s linear;\n  content: ' ';\n  pointer-events: none;\n}\n.x6-widget-stencil-content {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  height: auto;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n.x6-widget-stencil .x6-node [magnet]:not([magnet='passive']) {\n  pointer-events: none;\n}\n.x6-widget-stencil-group {\n  padding: 0;\n  padding-bottom: 8px;\n  overflow: hidden;\n  user-select: none;\n}\n.x6-widget-stencil-group.collapsed {\n  height: auto;\n  padding-bottom: 0;\n}\n.x6-widget-stencil-group-title {\n  position: relative;\n  margin-top: 0;\n  margin-bottom: 0;\n  padding: 4px;\n  cursor: pointer;\n}\n.x6-widget-stencil-title,\n.x6-widget-stencil-group > .x6-widget-stencil-group-title {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  user-select: none;\n}\n.x6-widget-stencil .unmatched {\n  opacity: 0.3;\n}\n.x6-widget-stencil .x6-node.unmatched {\n  display: none;\n}\n.x6-widget-stencil-group.unmatched {\n  display: none;\n}\n.x6-widget-stencil-search-text {\n  position: relative;\n  z-index: 1;\n  box-sizing: border-box;\n  width: 100%;\n  height: 30px;\n  max-height: 30px;\n  line-height: 30px;\n  outline: 0;\n}\n.x6-widget-stencil.not-found::after {\n  opacity: 1;\n  content: attr(data-not-found-text);\n}\n.x6-widget-stencil.not-found.searchable::after {\n  top: 30px;\n}\n.x6-widget-stencil.not-found.searchable.collapsable::after {\n  top: 50px;\n}\n.x6-widget-stencil {\n  color: #333;\n  background: #f5f5f5;\n}\n.x6-widget-stencil-content {\n  position: absolute;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-content {\n  top: 32px;\n}\n.x6-widget-stencil.searchable > .x6-widget-stencil-content {\n  top: 80px;\n}\n.x6-widget-stencil.not-found::after {\n  position: absolute;\n}\n.x6-widget-stencil.not-found.searchable.collapsable::after {\n  top: 80px;\n}\n.x6-widget-stencil.not-found.searchable::after {\n  top: 60px;\n}\n.x6-widget-stencil-group {\n  height: auto;\n  margin-bottom: 1px;\n  padding: 0;\n  transition: none;\n}\n.x6-widget-stencil-group .x6-graph {\n  background: transparent;\n  box-shadow: none;\n}\n.x6-widget-stencil-group.collapsed {\n  height: auto;\n  max-height: 31px;\n}\n.x6-widget-stencil-title,\n.x6-widget-stencil-group > .x6-widget-stencil-group-title {\n  position: relative;\n  left: 0;\n  box-sizing: border-box;\n  width: 100%;\n  height: 32px;\n  padding: 0 5px 0 8px;\n  color: #666;\n  font-weight: 700;\n  font-size: 12px;\n  line-height: 32px;\n  cursor: default;\n  transition: all 0.3;\n}\n.x6-widget-stencil-title:hover,\n.x6-widget-stencil-group > .x6-widget-stencil-group-title:hover {\n  color: #444;\n}\n.x6-widget-stencil-title {\n  background: #e9e9e9;\n}\n.x6-widget-stencil-group > .x6-widget-stencil-group-title {\n  background: #ededed;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title {\n  padding-left: 32px;\n  cursor: pointer;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title::before,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title::before {\n  position: absolute;\n  top: 6px;\n  left: 8px;\n  display: block;\n  width: 18px;\n  height: 18px;\n  margin: 0;\n  padding: 0;\n  background-color: transparent;\n  background-repeat: no-repeat;\n  background-position: 0 0;\n  border: none;\n  content: ' ';\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title::before,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title::before {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iIzAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj48cGF0aCBkPSJNOS4zNzUuNUM0LjY4Ny41Ljg3NSA0LjMxMy44NzUgOWMwIDQuNjg4IDMuODEyIDguNSA4LjUgOC41IDQuNjg3IDAgOC41LTMuODEyIDguNS04LjUgMC00LjY4Ny0zLjgxMy04LjUtOC41LTguNXptMCAxNS44ODZDNS4zMDMgMTYuMzg2IDEuOTkgMTMuMDcyIDEuOTkgOXMzLjMxMi03LjM4NSA3LjM4NS03LjM4NVMxNi43NiA0LjkyOCAxNi43NiA5YzAgNC4wNzItMy4zMTMgNy4zODYtNy4zODUgNy4zODZ6Ii8+PHBhdGggZD0iTTEyLjc1MyA4LjQ0M0g1Ljk5N2EuNTU4LjU1OCAwIDAwMCAxLjExNmg2Ljc1NmEuNTU4LjU1OCAwIDAwMC0xLjExNnoiLz48L2c+PC9zdmc+');\n  opacity: 0.4;\n  transition: all 0.3s;\n}\n.x6-widget-stencil.collapsable > .x6-widget-stencil-title:hover::before,\n.x6-widget-stencil-group.collapsable > .x6-widget-stencil-group-title:hover::before {\n  opacity: 0.6;\n}\n.x6-widget-stencil.collapsable.collapsed > .x6-widget-stencil-title::before,\n.x6-widget-stencil-group.collapsable.collapsed > .x6-widget-stencil-group-title::before {\n  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0iIzAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj48cGF0aCBkPSJNOS4zNzUuNUM0LjY4Ny41Ljg3NSA0LjMxMy44NzUgOWMwIDQuNjg4IDMuODEyIDguNSA4LjUgOC41IDQuNjg3IDAgOC41LTMuODEyIDguNS04LjUgMC00LjY4Ny0zLjgxMy04LjUtOC41LTguNXptMCAxNS44ODZDNS4zMDMgMTYuMzg2IDEuOTkgMTMuMDcyIDEuOTkgOXMzLjMxMi03LjM4NSA3LjM4NS03LjM4NVMxNi43NiA0LjkyOCAxNi43NiA5YzAgNC4wNzItMy4zMTMgNy4zODYtNy4zODUgNy4zODZ6Ii8+PHBhdGggZD0iTTEyLjc1MyA4LjQ0M0g1Ljk5N2EuNTU4LjU1OCAwIDAwMCAxLjExNmg2Ljc1NmEuNTU4LjU1OCAwIDAwMC0xLjExNnoiLz48cGF0aCBkPSJNOC44MTcgNS42MjN2Ni43NTZhLjU1OC41NTggMCAwMDEuMTE2IDBWNS42MjNhLjU1OC41NTggMCAxMC0xLjExNiAweiIvPjwvZz48L3N2Zz4=');\n  opacity: 0.4;\n}\n.x6-widget-stencil.collapsable.collapsed > .x6-widget-stencil-title:hover::before,\n.x6-widget-stencil-group.collapsable.collapsed > .x6-widget-stencil-group-title:hover::before {\n  opacity: 0.6;\n}\n.x6-widget-stencil input[type='search'] {\n  appearance: textfield;\n}\n.x6-widget-stencil input[type='search']::-webkit-search-cancel-button,\n.x6-widget-stencil input[type='search']::-webkit-search-decoration {\n  appearance: none;\n}\n.x6-widget-stencil-search-text {\n  display: block;\n  width: 90%;\n  margin: 8px 5%;\n  padding-left: 8px;\n  color: #333;\n  background: #fff;\n  border: 1px solid #e9e9e9;\n  border-radius: 12px;\n  outline: 0;\n}\n.x6-widget-stencil-search-text:focus {\n  outline: 0;\n}\n.x6-widget-stencil::after {\n  color: #808080;\n  font-weight: 600;\n  font-size: 12px;\n  background: 0 0;\n}\n";var c,d,p=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class g extends n["w"]{get targetScroller(){const t=this.options.target,e=t.getPlugin("scroller");return e}get targetGraph(){return this.options.target}get targetModel(){return this.targetGraph.model}constructor(t={}){super(),this.name="stencil",n["g"].ensure(this.name,h),this.graphs={},this.groups={},this.options=Object.assign(Object.assign({},g.defaultOptions),t),this.init()}init(){this.dnd=new a(this.options),this.onSearch=n["k"].debounce(this.onSearch,200),this.initContainer(),this.initSearch(),this.initContent(),this.initGroups(),this.setTitle(),this.startListening()}load(t,e){return Array.isArray(t)?this.loadGroup(t,e):this.options.groups&&Object.keys(this.options.groups).forEach(e=>{t[e]&&this.loadGroup(t[e],e)}),this}unload(t,e){return Array.isArray(t)?this.loadGroup(t,e,!0):this.options.groups&&Object.keys(this.options.groups).forEach(e=>{t[e]&&this.loadGroup(t[e],e,!0)}),this}toggleGroup(t){return this.isGroupCollapsed(t)?this.expandGroup(t):this.collapseGroup(t),this}collapseGroup(t){if(this.isGroupCollapsable(t)){const e=this.groups[t];e&&!this.isGroupCollapsed(t)&&(this.trigger("group:collapse",{name:t}),n["j"].addClass(e,"collapsed"))}return this}expandGroup(t){if(this.isGroupCollapsable(t)){const e=this.groups[t];e&&this.isGroupCollapsed(t)&&(this.trigger("group:expand",{name:t}),n["j"].removeClass(e,"collapsed"))}return this}isGroupCollapsable(t){const e=this.groups[t];return n["j"].hasClass(e,"collapsable")}isGroupCollapsed(t){const e=this.groups[t];return e&&n["j"].hasClass(e,"collapsed")}collapseGroups(){return Object.keys(this.groups).forEach(t=>this.collapseGroup(t)),this}expandGroups(){return Object.keys(this.groups).forEach(t=>this.expandGroup(t)),this}resizeGroup(t,e){const i=this.graphs[t];return i&&i.resize(e.width,e.height),this}addGroup(t){const e=Array.isArray(t)?t:[t];this.options.groups?this.options.groups.push(...e):this.options.groups=e,e.forEach(t=>this.initGroup(t))}removeGroup(t){const e=Array.isArray(t)?t:[t];this.options.groups&&(this.options.groups=this.options.groups.filter(t=>!e.includes(t.name)),e.forEach(t=>{const e=this.graphs[t];this.unregisterGraphEvents(e),e.dispose(),delete this.graphs[t];const i=this.groups[t];n["j"].remove(i),delete this.groups[t]}))}initContainer(){this.container=document.createElement("div"),n["j"].addClass(this.container,this.prefixClassName(c.base)),n["j"].attr(this.container,"data-not-found-text",this.options.notFoundText||"No matches found")}initContent(){this.content=document.createElement("div"),n["j"].addClass(this.content,this.prefixClassName(c.content)),n["j"].appendTo(this.content,this.container)}initSearch(){this.options.search&&(n["j"].addClass(this.container,"searchable"),n["j"].append(this.container,this.renderSearch()))}initGroup(t){const e=this.options.stencilGraphOptions||{},i=document.createElement("div");n["j"].addClass(i,this.prefixClassName(c.group)),n["j"].attr(i,"data-name",t.name),(null==t.collapsable&&this.options.collapsable||!1!==t.collapsable)&&n["j"].addClass(i,"collapsable"),n["j"].toggleClass(i,"collapsed",!0===t.collapsed);const s=document.createElement("h3");n["j"].addClass(s,this.prefixClassName(c.groupTitle)),s.innerHTML=t.title||t.name;const o=document.createElement("div");n["j"].addClass(o,this.prefixClassName(c.groupContent));const r=t.graphOptions,a=new n["m"](Object.assign(Object.assign(Object.assign({},e),r),{container:document.createElement("div"),model:e.model||new n["n"],width:t.graphWidth||this.options.stencilGraphWidth,height:t.graphHeight||this.options.stencilGraphHeight,interacting:!1,preventDefaultBlankAction:!1}));this.registerGraphEvents(a),n["j"].append(o,a.container),n["j"].append(i,[s,o]),n["j"].appendTo(i,this.content),this.groups[t.name]=i,this.graphs[t.name]=a}initGroups(){if(this.clearGroups(),this.setCollapsableState(),this.options.groups&&this.options.groups.length)this.options.groups.forEach(t=>{this.initGroup(t)});else{const t=this.options.stencilGraphOptions||{},e=new n["m"](Object.assign(Object.assign({},t),{container:document.createElement("div"),model:t.model||new n["n"],width:this.options.stencilGraphWidth,height:this.options.stencilGraphHeight,interacting:!1,preventDefaultBlankAction:!1}));n["j"].append(this.content,e.container),this.graphs[d.defaultGroupName]=e}}setCollapsableState(){if(this.options.collapsable=this.options.collapsable&&this.options.groups&&this.options.groups.some(t=>!1!==t.collapsable),this.options.collapsable){n["j"].addClass(this.container,"collapsable");const t=this.options.groups&&this.options.groups.every(t=>t.collapsed||!1===t.collapsable);t?n["j"].addClass(this.container,"collapsed"):n["j"].removeClass(this.container,"collapsed")}else n["j"].removeClass(this.container,"collapsable")}setTitle(){const t=document.createElement("div");n["j"].addClass(t,this.prefixClassName(c.title)),t.innerHTML=this.options.title,n["j"].appendTo(t,this.container)}renderSearch(){const t=document.createElement("div");n["j"].addClass(t,this.prefixClassName(c.search));const e=document.createElement("input");return n["j"].attr(e,{type:"search",placeholder:this.options.placeholder||"Search"}),n["j"].addClass(e,this.prefixClassName(c.searchText)),n["j"].append(t,e),t}startListening(){const t=this.prefixClassName(c.title),e=this.prefixClassName(c.searchText),i=this.prefixClassName(c.groupTitle);this.delegateEvents({["click ."+t]:"onTitleClick",["touchstart ."+t]:"onTitleClick",["click ."+i]:"onGroupTitleClick",["touchstart ."+i]:"onGroupTitleClick",["input ."+e]:"onSearch",["focusin ."+e]:"onSearchFocusIn",["focusout ."+e]:"onSearchFocusOut"})}stopListening(){this.undelegateEvents()}registerGraphEvents(t){t.on("cell:mousedown",this.onDragStart,this)}unregisterGraphEvents(t){t.off("cell:mousedown",this.onDragStart,this)}loadGroup(t,e,i){const s=this.getModel(e);if(s){const e=t.map(t=>n["p"].isNode(t)?t:n["p"].create(t));!0===i?s.removeCells(e):s.resetCells(e)}const o=this.getGroup(e);let r=this.options.stencilGraphHeight;o&&null!=o.graphHeight&&(r=o.graphHeight);const a=o&&o.layout||this.options.layout;if(a&&s&&n["k"].call(a,this,s,o),!r){const t=this.getGraph(e);t.fitToContent({minWidth:t.options.width,gridHeight:1,padding:o&&o.graphPadding||this.options.stencilGraphPadding||10})}return this}onDragStart(t){const{e:e,node:i}=t,n=this.getGroupByNode(i);n&&!1===n.nodeMovable||this.dnd.start(i,e)}filter(t,e){const i=Object.keys(this.graphs).reduce((i,s)=>{const o=this.graphs[s],r=s===d.defaultGroupName?null:s,a=o.model.getNodes().filter(i=>{let s=!1;s="function"===typeof e?n["k"].call(e,this,i,t,r,this):"boolean"===typeof e?e:this.isCellMatched(i,t,e,t.toLowerCase()!==t);const a=o.renderer.findViewByCell(i);return a&&n["j"].toggleClass(a.container,"unmatched",!s),s}),l=a.length>0,h=this.options,c=new n["n"];return c.resetCells(a),h.layout&&n["k"].call(h.layout,this,c,this.getGroup(s)),this.groups[s]&&n["j"].toggleClass(this.groups[s],"unmatched",!l),o.fitToContent({gridWidth:1,gridHeight:1,padding:h.stencilGraphPadding||10}),i||l},!1);n["j"].toggleClass(this.container,"not-found",!i)}isCellMatched(t,e,i,n){return!e||!i||Object.keys(i).some(s=>{if("*"===s||t.shape===s){const o=i[s];if("boolean"===typeof o)return o;const r=Array.isArray(o)?o:[o];return r.some(i=>{let s=t.getPropByPath(i);return null!=s&&(s=""+s,n||(s=s.toLowerCase()),s.indexOf(e)>=0)})}return!1})}onSearch(t){this.filter(t.target.value,this.options.search)}onSearchFocusIn(){n["j"].addClass(this.container,"is-focused")}onSearchFocusOut(){n["j"].removeClass(this.container,"is-focused")}onTitleClick(){this.options.collapsable&&(n["j"].toggleClass(this.container,"collapsed"),n["j"].hasClass(this.container,"collapsed")?this.collapseGroups():this.expandGroups())}onGroupTitleClick(t){const e=t.target.closest("."+this.prefixClassName(c.group));e&&this.toggleGroup(n["j"].attr(e,"data-name")||"");const i=Object.keys(this.groups).every(t=>{const e=this.getGroup(t),i=this.groups[t];return e&&!1===e.collapsable||n["j"].hasClass(i,"collapsed")});n["j"].toggleClass(this.container,"collapsed",i)}getModel(t){const e=this.getGraph(t);return e?e.model:null}getGraph(t){return this.graphs[t||d.defaultGroupName]}getGroup(t){const e=this.options.groups;return null!=t&&e&&e.length?e.find(e=>e.name===t):null}getGroupByNode(t){const e=this.options.groups;return e?e.find(e=>{const i=this.getModel(e.name);return!!i&&i.has(t.id)}):null}clearGroups(){Object.keys(this.graphs).forEach(t=>{const e=this.graphs[t];this.unregisterGraphEvents(e),e.dispose()}),Object.keys(this.groups).forEach(t=>{const e=this.groups[t];n["j"].remove(e)}),this.graphs={},this.groups={}}onRemove(){this.clearGroups(),this.dnd.remove(),this.stopListening(),this.undelegateDocumentEvents()}dispose(){this.remove(),n["g"].clean(this.name)}}p([n["w"].dispose()],g.prototype,"dispose",null),function(t){t.defaultOptions=Object.assign({stencilGraphWidth:200,stencilGraphHeight:800,title:"Stencil",collapsable:!1,placeholder:"Search",notFoundText:"No matches found",layout(t,e){const i={columnWidth:this.options.stencilGraphWidth/2-10,columns:2,rowHeight:80,resizeToFit:!1,dx:10,dy:10};l(t,Object.assign(Object.assign(Object.assign({},i),this.options.layoutOptions),e?e.layoutOptions:{}))}},a.defaults)}(g||(g={})),function(t){t.base="widget-stencil",t.title=t.base+"-title",t.search=t.base+"-search",t.searchText=t.search+"-text",t.content=t.base+"-content",t.group=t.base+"-group",t.groupTitle=t.group+"-title",t.groupContent=t.group+"-content"}(c||(c={})),function(t){t.defaultGroupName="__default__"}(d||(d={}))},4904:function(t,e,i){"use strict";i.d(e,"a",(function(){return h}));var n,s=i("5728"),o=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class r extends s["w"]{get model(){return this.graph.model}get view(){return this.graph.renderer.findViewByCell(this.node)}get containerClassName(){return this.prefixClassName("widget-transform")}get resizeClassName(){return this.containerClassName+"-resize"}get rotateClassName(){return this.containerClassName+"-rotate"}constructor(t,e,i){super(),this.node=e,this.graph=i,this.options=Object.assign(Object.assign({},n.defaultOptions),t),this.render(),this.startListening()}startListening(){this.delegateEvents({["mousedown ."+this.resizeClassName]:"startResizing",["touchstart ."+this.resizeClassName]:"startResizing",["mousedown ."+this.rotateClassName]:"startRotating",["touchstart ."+this.rotateClassName]:"startRotating"}),this.model.on("*",this.update,this),this.graph.on("scale",this.update,this),this.graph.on("translate",this.update,this),this.node.on("removed",this.remove,this),this.model.on("reseted",this.remove,this),this.view.on("cell:knob:mousedown",this.onKnobMouseDown,this),this.view.on("cell:knob:mouseup",this.onKnobMouseUp,this)}stopListening(){this.undelegateEvents(),this.model.off("*",this.update,this),this.graph.off("scale",this.update,this),this.graph.off("translate",this.update,this),this.node.off("removed",this.remove,this),this.model.off("reseted",this.remove,this),this.view.off("cell:knob:mousedown",this.onKnobMouseDown,this),this.view.off("cell:knob:mouseup",this.onKnobMouseUp,this)}renderHandles(){this.container=document.createElement("div");const t=document.createElement("div");s["j"].attr(t,"draggable","false");const e=t.cloneNode(!0);s["j"].addClass(e,this.rotateClassName);const i=n.POSITIONS.map(e=>{const i=t.cloneNode(!0);return s["j"].addClass(i,this.resizeClassName),s["j"].attr(i,"data-position",e),i});this.empty(),s["j"].append(this.container,[...i,e])}render(){return this.renderHandles(),this.view&&this.view.addClass(n.NODE_CLS),s["j"].addClass(this.container,this.containerClassName),s["j"].toggleClass(this.container,"no-orth-resize",this.options.preserveAspectRatio||!this.options.orthogonalResizing),s["j"].toggleClass(this.container,"no-resize",!this.options.resizable),s["j"].toggleClass(this.container,"no-rotate",!this.options.rotatable),this.options.className&&s["j"].addClass(this.container,this.options.className),this.graph.container.appendChild(this.container),this.update()}update(){const t=this.graph.matrix(),e=this.node.getBBox();e.x*=t.a,e.x+=t.e,e.y*=t.d,e.y+=t.f,e.width*=t.a,e.height*=t.d;const i=s["a"].normalize(this.node.getAngle()),n=0!==i?`rotate(${i}deg)`:"";return s["j"].css(this.container,{transform:n,width:e.width,height:e.height,left:e.x,top:e.y}),this.updateResizerDirections(),this}remove(){return this.view&&this.view.removeClass(n.NODE_CLS),super.remove()}onKnobMouseDown(){this.startHandle()}onKnobMouseUp(){this.stopHandle()}updateResizerDirections(){const t=s["a"].normalize(this.node.getAngle()),e=Math.floor(t*(n.DIRECTIONS.length/360));if(e!==this.prevShift){const t=n.DIRECTIONS.slice(e).concat(n.DIRECTIONS.slice(0,e)),i=t=>`${this.containerClassName}-cursor-${t}`,o=this.container.querySelectorAll("."+this.resizeClassName);o.forEach((e,o)=>{s["j"].removeClass(e,n.DIRECTIONS.map(t=>i(t)).join(" ")),s["j"].addClass(e,i(t[o]))}),this.prevShift=e}}getTrueDirection(t){const e=s["a"].normalize(this.node.getAngle());let i=n.POSITIONS.indexOf(t);return i+=Math.floor(e*(n.POSITIONS.length/360)),i%=n.POSITIONS.length,n.POSITIONS[i]}toValidResizeDirection(t){return{top:"top-left",bottom:"bottom-right",left:"bottom-left",right:"top-right"}[t]||t}startResizing(t){t.stopPropagation(),this.model.startBatch("resize",{cid:this.cid});const e=s["j"].attr(t.target,"data-position");this.prepareResizing(t,e),this.startAction(t)}prepareResizing(t,e){const i=this.getTrueDirection(e);let n=0,o=0;e.split("-").forEach(t=>{n={left:-1,right:1}[t]||n,o={top:-1,bottom:1}[t]||o});const r=this.toValidResizeDirection(e),a={"top-right":"bottomLeft","top-left":"bottomRight","bottom-left":"topRight","bottom-right":"topLeft"}[r],l=s["a"].normalize(this.node.getAngle());this.setEventData(t,{selector:a,direction:r,trueDirection:i,relativeDirection:e,angle:l,resizeX:n,resizeY:o,action:"resizing"})}startRotating(t){t.stopPropagation(),this.model.startBatch("rotate",{cid:this.cid});const e=this.node.getBBox().getCenter(),i=this.normalizeEvent(t),n=this.graph.snapToGrid(i.clientX,i.clientY);this.setEventData(t,{center:e,action:"rotating",angle:s["a"].normalize(this.node.getAngle()),start:s["s"].create(n).theta(e)}),this.startAction(t)}onMouseMove(t){const e=this.graph.findViewByCell(this.node);let i=this.getEventData(t);if(i.action){const n=this.normalizeEvent(t);let o=n.clientX,r=n.clientY;const a=this.graph.getPlugin("scroller"),l=this.options.restrictedResizing;if(!0===l||"number"===typeof l){const t=!0===l?0:l,e=a?Math.max(t,8):t,i=this.graph.container.getBoundingClientRect();o=s["q"].clamp(o,i.left+e,i.right-e),r=s["q"].clamp(r,i.top+e,i.bottom-e)}else this.options.autoScrollOnResizing&&a&&a.autoScroll(o,r);const h=this.graph.snapToGrid(o,r),c=this.graph.getGridSize(),d=this.node,p=this.options;if("resizing"===i.action){i=i,i.resized||(e&&(e.addClass("node-resizing"),this.notify("node:resize",t,e)),i.resized=!0);const n=d.getBBox(),o=s["s"].create(h).rotate(i.angle,n.getCenter()).diff(n[i.selector]);let r=i.resizeX?o.x*i.resizeX:n.width,a=i.resizeY?o.y*i.resizeY:n.height;const l=r,g=a;if(r=s["l"].snapToGrid(r,c),a=s["l"].snapToGrid(a,c),r=Math.max(r,p.minWidth||c),a=Math.max(a,p.minHeight||c),r=Math.min(r,p.maxWidth||1/0),a=Math.min(a,p.maxHeight||1/0),p.preserveAspectRatio){const t=n.width*a/n.height,e=n.height*r/n.width;r<t?a=e:r=t}const u=i.relativeDirection;if(p.allowReverse&&(l<=-r||g<=-a)){let e;"left"===u?l<=-r&&(e="right"):"right"===u?l<=-r&&(e="left"):"top"===u?g<=-a&&(e="bottom"):"bottom"===u?g<=-a&&(e="top"):"top-left"===u?l<=-r&&g<=-a?e="bottom-right":l<=-r?e="top-right":g<=-a&&(e="bottom-left"):"top-right"===u?l<=-r&&g<=-a?e="bottom-left":l<=-r?e="top-left":g<=-a&&(e="bottom-right"):"bottom-left"===u?l<=-r&&g<=-a?e="top-right":l<=-r?e="bottom-right":g<=-a&&(e="top-left"):"bottom-right"===u&&(l<=-r&&g<=-a?e="top-left":l<=-r?e="bottom-left":g<=-a&&(e="top-right"));const i=e;this.stopHandle();const n=this.container.querySelector(`.${this.resizeClassName}[data-position="${i}"]`);this.startHandle(n),this.prepareResizing(t,i),this.onMouseMove(t)}if(n.width!==r||n.height!==a){const n={ui:!0,direction:i.direction,relativeDirection:i.relativeDirection,trueDirection:i.trueDirection,minWidth:p.minWidth,minHeight:p.minHeight,maxWidth:p.maxWidth,maxHeight:p.maxHeight,preserveAspectRatio:!0===p.preserveAspectRatio};d.resize(r,a,n),this.notify("node:resizing",t,e)}}else if("rotating"===i.action){i=i,i.rotated||(e&&(e.addClass("node-rotating"),this.notify("node:rotate",t,e)),i.rotated=!0);const n=d.getAngle(),o=i.start-s["s"].create(h).theta(i.center);let r=i.angle+o;p.rotateGrid&&(r=s["l"].snapToGrid(r,p.rotateGrid)),r=s["a"].normalize(r),n!==r&&(d.rotate(r,{absolute:!0}),this.notify("node:rotating",t,e))}}}onMouseUp(t){const e=this.getEventData(t);e.action&&(this.stopAction(t),this.model.stopBatch("resizing"===e.action?"resize":"rotate",{cid:this.cid}))}startHandle(t){if(this.handle=t||null,s["j"].addClass(this.container,this.containerClassName+"-active"),t){s["j"].addClass(t,this.containerClassName+"-active-handle");const e=t.getAttribute("data-position");if(e){const t=n.DIRECTIONS[n.POSITIONS.indexOf(e)];s["j"].addClass(this.container,`${this.containerClassName}-cursor-${t}`)}}}stopHandle(){if(s["j"].removeClass(this.container,this.containerClassName+"-active"),this.handle){s["j"].removeClass(this.handle,this.containerClassName+"-active-handle");const t=this.handle.getAttribute("data-position");if(t){const e=n.DIRECTIONS[n.POSITIONS.indexOf(t)];s["j"].removeClass(this.container,`${this.containerClassName}-cursor-${e}`)}this.handle=null}}startAction(t){this.startHandle(t.target),this.graph.view.undelegateEvents(),this.delegateDocumentEvents(n.documentEvents,t.data)}stopAction(t){this.stopHandle(),this.undelegateDocumentEvents(),this.graph.view.delegateEvents();const e=this.graph.findViewByCell(this.node),i=this.getEventData(t);e&&(e.removeClass("node-"+i.action),"resizing"===i.action&&i.resized?this.notify("node:resized",t,e):"rotating"===i.action&&i.rotated&&this.notify("node:rotated",t,e))}notify(t,e,i,n={}){if(i){const s=i.graph,o=s.view.normalizeEvent(e),r=s.snapToGrid(o.clientX,o.clientY);this.trigger(t,Object.assign({e:o,view:i,node:i.cell,cell:i.cell,x:r.x,y:r.y},n))}}dispose(){this.stopListening(),this.remove(),this.off()}}o([s["w"].dispose()],r.prototype,"dispose",null),function(t){t.NODE_CLS="has-widget-transform",t.DIRECTIONS=["nw","n","ne","e","se","s","sw","w"],t.POSITIONS=["top-left","top","top-right","right","bottom-right","bottom","bottom-left","left"],t.documentEvents={mousemove:"onMouseMove",touchmove:"onMouseMove",mouseup:"onMouseUp",touchend:"onMouseUp"},t.defaultOptions={minWidth:0,minHeight:0,maxWidth:1/0,maxHeight:1/0,rotateGrid:15,rotatable:!0,preserveAspectRatio:!1,orthogonalResizing:!0,restrictedResizing:!1,autoScrollOnResizing:!0,allowReverse:!0}}(n||(n={}));const a=".x6-widget-transform {\n  position: absolute;\n  box-sizing: content-box !important;\n  margin: -5px 0 0 -5px;\n  padding: 4px;\n  border: 1px dashed #000;\n  border-radius: 5px;\n  user-select: none;\n  pointer-events: none;\n}\n.x6-widget-transform > div {\n  position: absolute;\n  box-sizing: border-box;\n  background-color: #fff;\n  border: 1px solid #000;\n  transition: background-color 0.2s;\n  pointer-events: auto;\n  -webkit-user-drag: none;\n  user-drag: none;\n  /* stylelint-disable-line */\n}\n.x6-widget-transform > div:hover {\n  background-color: #d3d3d3;\n}\n.x6-widget-transform-cursor-n {\n  cursor: n-resize;\n}\n.x6-widget-transform-cursor-s {\n  cursor: s-resize;\n}\n.x6-widget-transform-cursor-e {\n  cursor: e-resize;\n}\n.x6-widget-transform-cursor-w {\n  cursor: w-resize;\n}\n.x6-widget-transform-cursor-ne {\n  cursor: ne-resize;\n}\n.x6-widget-transform-cursor-nw {\n  cursor: nw-resize;\n}\n.x6-widget-transform-cursor-se {\n  cursor: se-resize;\n}\n.x6-widget-transform-cursor-sw {\n  cursor: sw-resize;\n}\n.x6-widget-transform-resize {\n  width: 10px;\n  height: 10px;\n  border-radius: 6px;\n}\n.x6-widget-transform-resize[data-position='top-left'] {\n  top: -5px;\n  left: -5px;\n}\n.x6-widget-transform-resize[data-position='top-right'] {\n  top: -5px;\n  right: -5px;\n}\n.x6-widget-transform-resize[data-position='bottom-left'] {\n  bottom: -5px;\n  left: -5px;\n}\n.x6-widget-transform-resize[data-position='bottom-right'] {\n  right: -5px;\n  bottom: -5px;\n}\n.x6-widget-transform-resize[data-position='top'] {\n  top: -5px;\n  left: 50%;\n  margin-left: -5px;\n}\n.x6-widget-transform-resize[data-position='bottom'] {\n  bottom: -5px;\n  left: 50%;\n  margin-left: -5px;\n}\n.x6-widget-transform-resize[data-position='left'] {\n  top: 50%;\n  left: -5px;\n  margin-top: -5px;\n}\n.x6-widget-transform-resize[data-position='right'] {\n  top: 50%;\n  right: -5px;\n  margin-top: -5px;\n}\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='top'],\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='bottom'],\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='left'],\n.x6-widget-transform.prevent-aspect-ratio .x6-widget-transform-resize[data-position='right'] {\n  display: none;\n}\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='bottom'],\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='left'],\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='right'],\n.x6-widget-transform.no-orth-resize .x6-widget-transform-resize[data-position='top'] {\n  display: none;\n}\n.x6-widget-transform.no-resize .x6-widget-transform-resize {\n  display: none;\n}\n.x6-widget-transform-rotate {\n  top: -20px;\n  left: -20px;\n  width: 12px;\n  height: 12px;\n  border-radius: 6px;\n  cursor: crosshair;\n}\n.x6-widget-transform.no-rotate .x6-widget-transform-rotate {\n  display: none;\n}\n.x6-widget-transform-active {\n  border-color: transparent;\n  pointer-events: all;\n}\n.x6-widget-transform-active > div {\n  display: none;\n}\n.x6-widget-transform-active > .x6-widget-transform-active-handle {\n  display: block;\n  background-color: #808080;\n}\n";s["m"].prototype.createTransformWidget=function(t){const e=this.getPlugin("transform");return e&&e.createWidget(t),this},s["m"].prototype.clearTransformWidgets=function(){const t=this.getPlugin("transform");return t&&t.clearWidgets(),this};var l=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class h extends s["c"]{constructor(t={}){super(),this.name="transform",this.widgets=new Map,this.disabled=!1,this.options=t,s["g"].ensure(this.name,a)}init(t){this.graph=t,this.disabled||this.startListening()}startListening(){this.graph.on("node:click",this.onNodeClick,this),this.graph.on("blank:mousedown",this.onBlankMouseDown,this)}stopListening(){this.graph.off("node:click",this.onNodeClick,this),this.graph.off("blank:mousedown",this.onBlankMouseDown,this)}enable(){this.disabled&&(this.disabled=!1,this.startListening())}disable(){this.disabled||(this.disabled=!0,this.stopListening())}isEnabled(){return!this.disabled}createWidget(t){this.clearWidgets();const e=this.createTransform(t);e&&(this.widgets.set(t,e),e.on("*",(t,e)=>{this.trigger(t,e),this.graph.trigger(t,e)}))}onNodeClick({node:t}){this.createWidget(t)}onBlankMouseDown(){this.clearWidgets()}createTransform(t){const e=this.getTransformOptions(t);return e.resizable||e.rotatable?new r(e,t,this.graph):null}getTransformOptions(t){this.options.resizing||(this.options.resizing={enabled:!1}),this.options.rotating||(this.options.rotating={enabled:!1}),"boolean"===typeof this.options.resizing&&(this.options.resizing={enabled:this.options.resizing}),"boolean"===typeof this.options.rotating&&(this.options.rotating={enabled:this.options.rotating});const e=h.parseOptionGroup(this.graph,t,this.options.resizing),i=h.parseOptionGroup(this.graph,t,this.options.rotating),n={resizable:!!e.enabled,minWidth:e.minWidth||0,maxWidth:e.maxWidth||Number.MAX_SAFE_INTEGER,minHeight:e.minHeight||0,maxHeight:e.maxHeight||Number.MAX_SAFE_INTEGER,orthogonalResizing:"boolean"!==typeof e.orthogonal||e.orthogonal,restrictedResizing:!!e.restrict,autoScrollOnResizing:"boolean"!==typeof e.autoScroll||e.autoScroll,preserveAspectRatio:!!e.preserveAspectRatio,allowReverse:"boolean"!==typeof e.allowReverse||e.allowReverse,rotatable:!!i.enabled,rotateGrid:i.grid||15};return n}clearWidgets(){this.widgets.forEach((t,e)=>{this.graph.getCellById(e.id)&&t.dispose()}),this.widgets.clear()}dispose(){this.clearWidgets(),this.stopListening(),this.off(),s["g"].clean(this.name)}}l([s["c"].dispose()],h.prototype,"dispose",null),function(t){function e(t,e,i){const n={};return Object.keys(i||{}).forEach(s=>{const o=i[s];n[s]="function"===typeof o?o.call(t,e):o}),n}t.parseOptionGroup=e}(h||(h={}))},"4ec9":function(t,e,i){i("6f48")},"545c":function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=i("5728");n["m"].prototype.isHistoryEnabled=function(){const t=this.getPlugin("history");return!!t&&t.isEnabled()},n["m"].prototype.enableHistory=function(){const t=this.getPlugin("history");return t&&t.enable(),this},n["m"].prototype.disableHistory=function(){const t=this.getPlugin("history");return t&&t.disable(),this},n["m"].prototype.toggleHistory=function(t){const e=this.getPlugin("history");return e&&e.toggleEnabled(t),this},n["m"].prototype.undo=function(t){const e=this.getPlugin("history");return e&&e.undo(t),this},n["m"].prototype.redo=function(t){const e=this.getPlugin("history");return e&&e.redo(t),this},n["m"].prototype.undoAndCancel=function(t){const e=this.getPlugin("history");return e&&e.cancel(t),this},n["m"].prototype.canUndo=function(){const t=this.getPlugin("history");return!!t&&t.canUndo()},n["m"].prototype.canRedo=function(){const t=this.getPlugin("history");return!!t&&t.canRedo()},n["m"].prototype.cleanHistory=function(t){const e=this.getPlugin("history");return e&&e.clean(t),this},n["m"].prototype.getHistoryStackSize=function(){const t=this.getPlugin("history");return t.getSize()},n["m"].prototype.getUndoStackSize=function(){const t=this.getPlugin("history");return t.getUndoSize()},n["m"].prototype.getRedoStackSize=function(){const t=this.getPlugin("history");return t.getRedoSize()},n["m"].prototype.getUndoRemainSize=function(){const t=this.getPlugin("history");return t.getUndoRemainSize()};var s,o=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class r extends n["c"]{constructor(t={}){super(),this.name="history",this.batchCommands=null,this.batchLevel=0,this.lastBatchIndex=-1,this.freezed=!1,this.stackSize=0,this.handlers=[];const{stackSize:e=0}=t;this.stackSize=e,this.options=s.getOptions(t),this.validator=new r.Validator({history:this,cancelInvalid:this.options.cancelInvalid})}init(t){this.graph=t,this.model=this.graph.model,this.clean(),this.startListening()}isEnabled(){return!this.disabled}enable(){this.disabled&&(this.options.enabled=!0)}disable(){this.disabled||(this.options.enabled=!1)}toggleEnabled(t){return null!=t?t!==this.isEnabled()&&(t?this.enable():this.disable()):this.isEnabled()?this.disable():this.enable(),this}undo(t={}){if(!this.disabled){const e=this.undoStack.pop();e&&(this.revertCommand(e,t),this.redoStack.push(e),this.notify("undo",e,t))}return this}redo(t={}){if(!this.disabled){const e=this.redoStack.pop();e&&(this.applyCommand(e,t),this.undoStackPush(e),this.notify("redo",e,t))}return this}cancel(t={}){if(!this.disabled){const e=this.undoStack.pop();e&&(this.revertCommand(e,t),this.redoStack=[],this.notify("cancel",e,t))}return this}getSize(){return this.stackSize}getUndoRemainSize(){const t=this.undoStack.length;return this.stackSize-t}getUndoSize(){return this.undoStack.length}getRedoSize(){return this.redoStack.length}canUndo(){return!this.disabled&&this.undoStack.length>0}canRedo(){return!this.disabled&&this.redoStack.length>0}clean(t={}){return this.undoStack=[],this.redoStack=[],this.notify("clean",null,t),this}get disabled(){return!0!==this.options.enabled}validate(t,...e){return this.validator.validate(t,...e),this}startListening(){this.model.on("batch:start",this.initBatchCommand,this),this.model.on("batch:stop",this.storeBatchCommand,this),this.options.eventNames&&this.options.eventNames.forEach((t,e)=>{this.handlers[e]=this.addCommand.bind(this,t),this.model.on(t,this.handlers[e])}),this.validator.on("invalid",t=>this.trigger("invalid",t))}stopListening(){this.model.off("batch:start",this.initBatchCommand,this),this.model.off("batch:stop",this.storeBatchCommand,this),this.options.eventNames&&(this.options.eventNames.forEach((t,e)=>{this.model.off(t,this.handlers[e])}),this.handlers.length=0),this.validator.off("invalid")}createCommand(t){return{batch:!!t&&t.batch,data:{}}}revertCommand(t,e){this.freezed=!0;const i=Array.isArray(t)?s.sortBatchCommands(t):[t];for(let s=i.length-1;s>=0;s-=1){const t=i[s],o=Object.assign(Object.assign({},e),n["r"].pick(t.options,this.options.revertOptionsList||[]));this.executeCommand(t,!0,o)}this.freezed=!1}applyCommand(t,e){this.freezed=!0;const i=Array.isArray(t)?s.sortBatchCommands(t):[t];for(let s=0;s<i.length;s+=1){const t=i[s],o=Object.assign(Object.assign({},e),n["r"].pick(t.options,this.options.applyOptionsList||[]));this.executeCommand(t,!1,o)}this.freezed=!1}executeCommand(t,e,i){const o=this.model,r=o.getCell(t.data.id),a=t.event;if(s.isAddEvent(a)&&e||s.isRemoveEvent(a)&&!e)r&&r.remove(i);else if(s.isAddEvent(a)&&!e||s.isRemoveEvent(a)&&e){const e=t.data;e.node?o.addNode(e.props,i):e.edge&&o.addEdge(e.props,i)}else if(s.isChangeEvent(a)){const n=t.data,s=n.key;if(s&&r){const t=e?n.prev[s]:n.next[s];if("attrs"===n.key){const o=this.ensureUndefinedAttrs(t,e?n.next[s]:n.prev[s]);o&&(i.dirty=!0)}r.prop(s,t,i)}}else{const s=this.options.executeCommand;s&&n["k"].call(s,this,t,e,i)}}addCommand(t,e){if(this.freezed||this.disabled)return;const i=e,o=i.options||{};if(o.dryrun)return;if(s.isAddEvent(t)&&this.options.ignoreAdd||s.isRemoveEvent(t)&&this.options.ignoreRemove||s.isChangeEvent(t)&&this.options.ignoreChange)return;const r=this.options.beforeAddCommand;if(null!=r&&!1===n["k"].call(r,this,t,e))return;"cell:change:*"===t&&(t="cell:change:"+i.key);const a=i.cell,l=n["n"].isModel(a);let h;if(this.batchCommands){h=this.batchCommands[Math.max(this.lastBatchIndex,0)];const e=l&&!h.modelChange||h.data.id!==a.id,i=h.event!==t;if(this.lastBatchIndex>=0&&(e||i)){const e=this.batchCommands.findIndex(e=>(l&&e.modelChange||e.data.id===a.id)&&e.event===t);e<0||s.isAddEvent(t)||s.isRemoveEvent(t)?h=this.createCommand({batch:!0}):(h=this.batchCommands[e],this.batchCommands.splice(e,1)),this.batchCommands.push(h),this.lastBatchIndex=this.batchCommands.length-1}}else h=this.createCommand({batch:!1});if(s.isAddEvent(t)||s.isRemoveEvent(t)){const e=h.data;return h.event=t,h.options=o,e.id=a.id,e.props=n["r"].cloneDeep(a.toJSON()),a.isEdge()?e.edge=!0:a.isNode()&&(e.node=!0),this.push(h,o)}if(s.isChangeEvent(t)){const i=e.key,s=h.data;return h.batch&&h.event||(h.event=t,h.options=o,s.key=i,null==s.prev&&(s.prev={}),s.prev[i]=n["r"].cloneDeep(a.previous(i)),l?h.modelChange=!0:s.id=a.id),null==s.next&&(s.next={}),s.next[i]=n["r"].cloneDeep(a.prop(i)),this.push(h,o)}const c=this.options.afterAddCommand;c&&n["k"].call(c,this,t,e,h),this.push(h,o)}initBatchCommand(t){this.freezed||(this.batchCommands?this.batchLevel+=1:(this.batchCommands=[this.createCommand({batch:!0})],this.batchLevel=0,this.lastBatchIndex=-1))}storeBatchCommand(t){if(!this.freezed)if(this.batchCommands&&this.batchLevel<=0){const e=this.filterBatchCommand(this.batchCommands);e.length>0&&(this.redoStack=[],this.undoStackPush(e),this.consolidateCommands(),this.notify("add",e,t)),this.batchCommands=null,this.lastBatchIndex=-1,this.batchLevel=0}else this.batchCommands&&this.batchLevel>0&&(this.batchLevel-=1)}filterBatchCommand(t){let e=t.slice();const i=[];while(e.length>0){const t=e.shift(),o=t.event,r=t.data.id;if(null!=o&&(null!=r||t.modelChange)){if(s.isAddEvent(o)){const t=e.findIndex(t=>s.isRemoveEvent(t.event)&&t.data.id===r);if(t>=0){e=e.filter((e,i)=>t<i||e.data.id!==r);continue}}else if(s.isRemoveEvent(o)){const t=e.findIndex(t=>s.isAddEvent(t.event)&&t.data.id===r);if(t>=0){e.splice(t,1);continue}}else if(s.isChangeEvent(o)){const e=t.data;if(n["r"].isEqual(e.prev,e.next))continue}i.push(t)}}return i}notify(t,e,i){const n=null==e?null:Array.isArray(e)?e:[e];this.emit(t,{cmds:n,options:i}),this.graph.trigger("history:"+t,{cmds:n,options:i}),this.emit("change",{cmds:n,options:i}),this.graph.trigger("history:change",{cmds:n,options:i})}push(t,e){this.redoStack=[],t.batch?(this.lastBatchIndex=Math.max(this.lastBatchIndex,0),this.emit("batch",{cmd:t,options:e})):(this.undoStackPush(t),this.consolidateCommands(),this.notify("add",t,e))}consolidateCommands(){var t;const e=this.undoStack[this.undoStack.length-1],i=this.undoStack[this.undoStack.length-2];if(!Array.isArray(e))return;const n=new Set(e.map(t=>t.event));if(2!==n.size||!n.has("cell:change:parent")||!n.has("cell:change:children"))return;if(!e.every(t=>{var e;return t.batch&&(null===(e=t.options)||void 0===e?void 0:e.ui)}))return;if(!Array.isArray(i)||1!==i.length)return;const s=i[0];"cell:change:position"===s.event&&(null===(t=s.options)||void 0===t?void 0:t.ui)&&(i.push(...e),this.undoStack.pop())}undoStackPush(t){0!==this.stackSize?(this.undoStack.length>=this.stackSize&&this.undoStack.shift(),this.undoStack.push(t)):this.undoStack.push(t)}ensureUndefinedAttrs(t,e){let i=!1;return null!==t&&null!==e&&"object"===typeof t&&"object"===typeof e&&Object.keys(e).forEach(n=>{void 0===t[n]&&void 0!==e[n]?(t[n]=void 0,i=!0):"object"===typeof t[n]&&"object"===typeof e[n]&&(i=this.ensureUndefinedAttrs(t[n],e[n]))}),i}dispose(){this.validator.dispose(),this.clean(),this.stopListening(),this.off()}}o([n["c"].dispose()],r.prototype,"dispose",null),function(t){class e extends n["c"]{constructor(t){super(),this.map={},this.command=t.history,this.cancelInvalid=!1!==t.cancelInvalid,this.command.on("add",this.onCommandAdded,this)}onCommandAdded({cmds:t}){return Array.isArray(t)?t.every(t=>this.isValidCommand(t)):this.isValidCommand(t)}isValidCommand(t){if(t.options&&!1===t.options.validation)return!0;const e=t.event&&this.map[t.event]||[];let i=null;return e.forEach(e=>{let n=0;const s=o=>{const r=e[n];n+=1;try{if(!r)return void(i=o);r(o,t,s)}catch(o){s(o)}};s(i)}),!i||(this.cancelInvalid&&this.command.cancel(),this.emit("invalid",{err:i}),!1)}validate(t,...e){const i=Array.isArray(t)?t:t.split(/\s+/);return e.forEach(t=>{if("function"!==typeof t)throw new Error(i.join(" ")+" requires callback functions.")}),i.forEach(t=>{null==this.map[t]&&(this.map[t]=[]),this.map[t].push(e)}),this}dispose(){this.command.off("add",this.onCommandAdded,this)}}o([n["c"].dispose()],e.prototype,"dispose",null),t.Validator=e}(r||(r={})),function(t){function e(t){return"cell:added"===t}function i(t){return"cell:removed"===t}function n(t){return null!=t&&t.startsWith("cell:change:")}function s(e){const i=["cell:added","cell:removed","cell:change:*"],n=["batch:start","batch:stop"],s=e.eventNames?e.eventNames.filter(e=>!(t.isChangeEvent(e)||i.includes(e)||n.includes(e))):i;return Object.assign(Object.assign({enabled:!0},e),{eventNames:s,applyOptionsList:e.applyOptionsList||["propertyPath"],revertOptionsList:e.revertOptionsList||["propertyPath"]})}function o(e){const i=[];for(let n=0,s=e.length;n<s;n+=1){const s=e[n];let o=null;if(t.isAddEvent(s.event)){const t=s.data.id;for(let i=0;i<n;i+=1)if(e[i].data.id===t){o=i;break}}null!==o?i.splice(o,0,s):i.push(s)}return i}t.isAddEvent=e,t.isRemoveEvent=i,t.isChangeEvent=n,t.getOptions=s,t.sortBatchCommands=o}(s||(s={}))},"54cf":function(t,e,i){"use strict";i.d(e,"a",(function(){return h}));var n,s=i("5728"),o=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class r extends s["w"]{get graph(){return this.options.graph}get boxClassName(){return this.prefixClassName(n.classNames.box)}get $boxes(){return s["j"].children(this.container,this.boxClassName)}get handleOptions(){return this.options}constructor(t){super(),this.options=t,this.options.model&&(this.options.collection=this.options.model.collection),this.options.collection?this.collection=this.options.collection:(this.collection=new s["e"]([],{comparator:n.depthComparator}),this.options.collection=this.collection),this.boxCount=0,this.createContainer(),this.startListening()}startListening(){const t=this.graph,e=this.collection;this.delegateEvents({["mousedown ."+this.boxClassName]:"onSelectionBoxMouseDown",["touchstart ."+this.boxClassName]:"onSelectionBoxMouseDown"},!0),t.on("scale",this.onGraphTransformed,this),t.on("translate",this.onGraphTransformed,this),t.model.on("updated",this.onModelUpdated,this),e.on("added",this.onCellAdded,this),e.on("removed",this.onCellRemoved,this),e.on("reseted",this.onReseted,this),e.on("updated",this.onCollectionUpdated,this),e.on("node:change:position",this.onNodePositionChanged,this),e.on("cell:changed",this.onCellChanged,this)}stopListening(){const t=this.graph,e=this.collection;this.undelegateEvents(),t.off("scale",this.onGraphTransformed,this),t.off("translate",this.onGraphTransformed,this),t.model.off("updated",this.onModelUpdated,this),e.off("added",this.onCellAdded,this),e.off("removed",this.onCellRemoved,this),e.off("reseted",this.onReseted,this),e.off("updated",this.onCollectionUpdated,this),e.off("node:change:position",this.onNodePositionChanged,this),e.off("cell:changed",this.onCellChanged,this)}onRemove(){this.stopListening()}onGraphTransformed(){this.updateSelectionBoxes()}onCellChanged(){this.updateSelectionBoxes()}onNodePositionChanged({node:t,options:e}){const{showNodeSelectionBox:i,pointerEvents:n}=this.options,{ui:s,selection:o,translateBy:r,snapped:a}=e,l=(!0!==i||n&&"none"===this.getPointerEventsValue(n))&&!this.translating&&!o,h=s&&r&&t.id===r;if(l&&(h||a)){this.translating=!0;const i=t.position(),n=t.previous("position"),s=i.x-n.x,o=i.y-n.y;0===s&&0===o||this.translateSelectedNodes(s,o,t,e),this.translating=!1}}onModelUpdated({removed:t}){t&&t.length&&this.unselect(t)}isEmpty(){return this.length<=0}isSelected(t){return this.collection.has(t)}get length(){return this.collection.length}get cells(){return this.collection.toArray()}select(t,e={}){e.dryrun=!0;const i=this.filter(Array.isArray(t)?t:[t]);return this.collection.add(i,e),this}unselect(t,e={}){return e.dryrun=!0,this.collection.remove(Array.isArray(t)?t:[t],e),this}reset(t,e={}){if(t){if(e.batch){const i=this.filter(Array.isArray(t)?t:[t]);return this.collection.reset(i,Object.assign(Object.assign({},e),{ui:!0})),this}const i=this.cells,n=this.filter(Array.isArray(t)?t:[t]),s={},o={};i.forEach(t=>s[t.id]=t),n.forEach(t=>o[t.id]=t);const r=[],a=[];return n.forEach(t=>{s[t.id]||r.push(t)}),i.forEach(t=>{o[t.id]||a.push(t)}),a.length&&this.unselect(a,Object.assign(Object.assign({},e),{ui:!0})),r.length&&this.select(r,Object.assign(Object.assign({},e),{ui:!0})),0===a.length&&0===r.length&&this.updateContainer(),this}return this.clean(e)}clean(t={}){return this.length&&(!1===t.batch?this.unselect(this.cells,t):this.collection.reset([],Object.assign(Object.assign({},t),{ui:!0}))),this}setFilter(t){this.options.filter=t}setContent(t){this.options.content=t}startSelecting(t){let e,i;t=this.normalizeEvent(t),this.clean();const o=this.graph.container;if(null!=t.offsetX&&null!=t.offsetY&&o.contains(t.target))e=t.offsetX,i=t.offsetY;else{const n=s["j"].offset(o),r=o.scrollLeft,a=o.scrollTop;e=t.clientX-n.left+window.pageXOffset+r,i=t.clientY-n.top+window.pageYOffset+a}s["j"].css(this.container,{top:i,left:e,width:1,height:1}),this.setEventData(t,{action:"selecting",clientX:t.clientX,clientY:t.clientY,offsetX:e,offsetY:i,scrollerX:0,scrollerY:0,moving:!1}),this.delegateDocumentEvents(n.documentEvents,t.data)}filter(t){const e=this.options.filter;return t.filter(t=>Array.isArray(e)?e.some(e=>"string"===typeof e?t.shape===e:t.id===e.id):"function"!==typeof e||s["k"].call(e,this.graph,t))}stopSelecting(t){const e=this.graph,i=this.getEventData(t),n=i.action;switch(n){case"selecting":{let t=s["j"].width(this.container),i=s["j"].height(this.container);const n=s["j"].offset(this.container),o=e.pageToLocal(n.left,n.top),r=e.transform.getScale();t/=r.sx,i/=r.sy;const a=new s["t"](o.x,o.y,t,i),l=this.getCellViewsInArea(a).map(t=>t.cell);this.reset(l,{batch:!0}),this.hideRubberband();break}case"translating":{const n=e.snapToGrid(t.clientX,t.clientY);if(!this.options.following){const t=i;this.updateSelectedNodesPosition({dx:t.clientX-t.originX,dy:t.clientY-t.originY})}this.graph.model.stopBatch("move-selection"),this.notifyBoxEvent("box:mouseup",t,n.x,n.y);break}default:this.clean();break}}onMouseUp(t){const e=this.getEventData(t).action;e&&(this.stopSelecting(t),this.undelegateDocumentEvents())}onSelectionBoxMouseDown(t){this.options.following||t.stopPropagation();const e=this.normalizeEvent(t);this.options.movable&&this.startTranslating(e);const i=this.getCellViewFromElem(e.target);this.setEventData(e,{activeView:i});const s=this.graph.snapToGrid(e.clientX,e.clientY);this.notifyBoxEvent("box:mousedown",e,s.x,s.y),this.delegateDocumentEvents(n.documentEvents,e.data)}startTranslating(t){this.graph.model.startBatch("move-selection");const e=this.graph.snapToGrid(t.clientX,t.clientY);this.setEventData(t,{action:"translating",clientX:e.x,clientY:e.y,originX:e.x,originY:e.y})}getRestrictArea(){const t=this.graph.options.translating.restrict,e="function"===typeof t?s["k"].call(t,this.graph,null):t;return"number"===typeof e?this.graph.transform.getGraphArea().inflate(e):!0===e?this.graph.transform.getGraphArea():e||null}getSelectionOffset(t,e){let i=t.x-e.clientX,n=t.y-e.clientY;const o=this.getRestrictArea();if(o){const r=this.collection.toArray(),a=s["d"].getCellsBBox(r,{deep:!0})||s["t"].create(),l=o.x-a.x,h=o.y-a.y,c=o.x+o.width-(a.x+a.width),d=o.y+o.height-(a.y+a.height);if(i<l&&(i=l),n<h&&(n=h),c<i&&(i=c),d<n&&(n=d),!this.options.following){const s=t.x-e.originX,o=t.y-e.originY;i=s<=l||s>=c?0:i,n=o<=h||o>=d?0:n}}return{dx:i,dy:n}}updateElementPosition(t,e,i){const n=s["j"].css(t,"left"),o=s["j"].css(t,"top"),r=n?parseFloat(n):0,a=o?parseFloat(o):0;s["j"].css(t,"left",r+e),s["j"].css(t,"top",a+i)}updateSelectedNodesPosition(t){const{dx:e,dy:i}=t;if(e||i)if(this.translateSelectedNodes(e,i),this.boxesUpdated)this.collection.length>1&&this.updateSelectionBoxes();else{const t=this.graph.transform.getScale();for(let n=0,s=this.$boxes,o=s.length;n<o;n+=1)this.updateElementPosition(s[n],e*t.sx,i*t.sy);this.updateElementPosition(this.selectionContainer,e*t.sx,i*t.sy)}}autoScrollGraph(t,e){const i=this.graph.getPlugin("scroller");return i?i.autoScroll(t,e):{scrollerX:0,scrollerY:0}}adjustSelection(t){const e=this.normalizeEvent(t),i=this.getEventData(e),n=i.action;switch(n){case"selecting":{const t=i;!0!==t.moving&&(s["j"].appendTo(this.container,this.graph.container),this.showRubberband(),t.moving=!0);const{scrollerX:n,scrollerY:o}=this.autoScrollGraph(e.clientX,e.clientY);t.scrollerX+=n,t.scrollerY+=o;const r=e.clientX-t.clientX+t.scrollerX,a=e.clientY-t.clientY+t.scrollerY,l=parseInt(s["j"].css(this.container,"left")||"0",10),h=parseInt(s["j"].css(this.container,"top")||"0",10);s["j"].css(this.container,{left:r<0?t.offsetX+r:l,top:a<0?t.offsetY+a:h,width:Math.abs(r),height:Math.abs(a)});break}case"translating":{const n=this.graph.snapToGrid(e.clientX,e.clientY),s=i,o=this.getSelectionOffset(n,s);this.options.following?this.updateSelectedNodesPosition(o):this.updateContainerPosition(o),o.dx&&(s.clientX=n.x),o.dy&&(s.clientY=n.y),this.notifyBoxEvent("box:mousemove",t,n.x,n.y);break}default:break}this.boxesUpdated=!1}translateSelectedNodes(t,e,i,n){const s={},o=[];if(i&&(s[i.id]=!0),this.collection.toArray().forEach(t=>{t.getDescendants({deep:!0}).forEach(t=>{s[t.id]=!0})}),n&&n.translateBy){const t=this.graph.getCellById(n.translateBy);t&&(s[t.id]=!0,t.getDescendants({deep:!0}).forEach(t=>{s[t.id]=!0}),o.push(t))}this.collection.toArray().forEach(i=>{if(!s[i.id]){const r=Object.assign(Object.assign({},n),{selection:this.cid,exclude:o});i.translate(t,e,r),this.graph.model.getConnectedEdges(i).forEach(i=>{s[i.id]||(i.translate(t,e,r),s[i.id]=!0)})}})}getCellViewsInArea(t){const e=this.graph,i={strict:this.options.strict};let n=[];return this.options.rubberNode&&(n=n.concat(e.model.getNodesInArea(t,i).map(t=>e.renderer.findViewByCell(t)).filter(t=>null!=t))),this.options.rubberEdge&&(n=n.concat(e.model.getEdgesInArea(t,i).map(t=>e.renderer.findViewByCell(t)).filter(t=>null!=t))),n}notifyBoxEvent(t,e,i,n){const s=this.getEventData(e),o=s.activeView;this.trigger(t,{e:e,view:o,x:i,y:n,cell:o.cell})}getSelectedClassName(t){return this.prefixClassName((t.isNode()?"node":"edge")+"-selected")}addCellSelectedClassName(t){const e=this.graph.renderer.findViewByCell(t);e&&e.addClass(this.getSelectedClassName(t))}removeCellUnSelectedClassName(t){const e=this.graph.renderer.findViewByCell(t);e&&e.removeClass(this.getSelectedClassName(t))}destroySelectionBox(t){this.removeCellUnSelectedClassName(t),this.canShowSelectionBox(t)&&(s["j"].remove(this.container.querySelector(`[data-cell-id="${t.id}"]`)),0===this.$boxes.length&&this.hide(),this.boxCount=Math.max(0,this.boxCount-1))}destroyAllSelectionBoxes(t){t.forEach(t=>this.removeCellUnSelectedClassName(t)),this.hide(),s["j"].remove(this.$boxes),this.boxCount=0}hide(){s["j"].removeClass(this.container,this.prefixClassName(n.classNames.rubberband)),s["j"].removeClass(this.container,this.prefixClassName(n.classNames.selected))}showRubberband(){s["j"].addClass(this.container,this.prefixClassName(n.classNames.rubberband))}hideRubberband(){s["j"].removeClass(this.container,this.prefixClassName(n.classNames.rubberband))}showSelected(){s["j"].removeAttribute(this.container,"style"),s["j"].addClass(this.container,this.prefixClassName(n.classNames.selected))}createContainer(){this.container=document.createElement("div"),s["j"].addClass(this.container,this.prefixClassName(n.classNames.root)),this.options.className&&s["j"].addClass(this.container,this.options.className),this.selectionContainer=document.createElement("div"),s["j"].addClass(this.selectionContainer,this.prefixClassName(n.classNames.inner)),this.selectionContent=document.createElement("div"),s["j"].addClass(this.selectionContent,this.prefixClassName(n.classNames.content)),s["j"].append(this.selectionContainer,this.selectionContent),s["j"].attr(this.selectionContainer,"data-selection-length",this.collection.length),s["j"].prepend(this.container,this.selectionContainer)}updateContainerPosition(t){(t.dx||t.dy)&&this.updateElementPosition(this.selectionContainer,t.dx,t.dy)}updateContainer(){const t={x:1/0,y:1/0},e={x:0,y:0},i=this.collection.toArray().filter(t=>this.canShowSelectionBox(t));i.forEach(i=>{const n=this.graph.renderer.findViewByCell(i);if(n){const i=n.getBBox({useCellGeometry:!0});t.x=Math.min(t.x,i.x),t.y=Math.min(t.y,i.y),e.x=Math.max(e.x,i.x+i.width),e.y=Math.max(e.y,i.y+i.height)}}),s["j"].css(this.selectionContainer,{position:"absolute",pointerEvents:"none",left:t.x,top:t.y,width:e.x-t.x,height:e.y-t.y}),s["j"].attr(this.selectionContainer,"data-selection-length",this.collection.length);const n=this.options.content;if(n)if("function"===typeof n){const t=s["k"].call(n,this.graph,this,this.selectionContent);t&&(this.selectionContent.innerHTML=t)}else this.selectionContent.innerHTML=n;this.collection.length>0&&!this.container.parentNode?s["j"].appendTo(this.container,this.graph.container):this.collection.length<=0&&this.container.parentNode&&this.container.parentNode.removeChild(this.container)}canShowSelectionBox(t){return t.isNode()&&!0===this.options.showNodeSelectionBox||t.isEdge()&&!0===this.options.showEdgeSelectionBox}getPointerEventsValue(t){return"string"===typeof t?t:t(this.cells)}createSelectionBox(t){if(this.addCellSelectedClassName(t),this.canShowSelectionBox(t)){const e=this.graph.renderer.findViewByCell(t);if(e){const i=e.getBBox({useCellGeometry:!0}),n=this.boxClassName,o=document.createElement("div"),r=this.options.pointerEvents;s["j"].addClass(o,n),s["j"].addClass(o,`${n}-${t.isNode()?"node":"edge"}`),s["j"].attr(o,"data-cell-id",t.id),s["j"].css(o,{position:"absolute",left:i.x,top:i.y,width:i.width,height:i.height,pointerEvents:r?this.getPointerEventsValue(r):"auto"}),s["j"].appendTo(o,this.container),this.showSelected(),this.boxCount+=1}}}updateSelectionBoxes(){this.collection.length>0&&(this.boxesUpdated=!0,this.confirmUpdate())}confirmUpdate(){if(this.boxCount){this.hide();for(let t=0,e=this.$boxes,i=e.length;t<i;t+=1){const i=e[t],n=s["j"].attr(i,"data-cell-id");s["j"].remove(i),this.boxCount-=1;const o=this.collection.get(n);o&&this.createSelectionBox(o)}this.updateContainer()}return 0}getCellViewFromElem(t){const e=t.getAttribute("data-cell-id");if(e){const t=this.collection.get(e);if(t)return this.graph.renderer.findViewByCell(t)}return null}onCellRemoved({cell:t}){this.destroySelectionBox(t),this.updateContainer()}onReseted({previous:t,current:e}){this.destroyAllSelectionBoxes(t),e.forEach(t=>{this.listenCellRemoveEvent(t),this.createSelectionBox(t)}),this.updateContainer()}onCellAdded({cell:t}){this.listenCellRemoveEvent(t),this.createSelectionBox(t),this.updateContainer()}listenCellRemoveEvent(t){t.off("removed",this.onCellRemoved,this),t.on("removed",this.onCellRemoved,this)}onCollectionUpdated({added:t,removed:e,options:i}){t.forEach(t=>{this.trigger("cell:selected",{cell:t,options:i}),t.isNode()?this.trigger("node:selected",{cell:t,options:i,node:t}):t.isEdge()&&this.trigger("edge:selected",{cell:t,options:i,edge:t})}),e.forEach(t=>{this.trigger("cell:unselected",{cell:t,options:i}),t.isNode()?this.trigger("node:unselected",{cell:t,options:i,node:t}):t.isEdge()&&this.trigger("edge:unselected",{cell:t,options:i,edge:t})});const n={added:t,removed:e,options:i,selected:this.cells.filter(t=>!!this.graph.getCellById(t.id))};this.trigger("selection:changed",n)}dispose(){this.clean(),this.remove(),this.off()}}o([s["w"].dispose()],r.prototype,"dispose",null),function(t){const e="widget-selection";function i(t){return t.getAncestors().length}t.classNames={root:e,inner:e+"-inner",box:e+"-box",content:e+"-content",rubberband:e+"-rubberband",selected:e+"-selected"},t.documentEvents={mousemove:"adjustSelection",touchmove:"adjustSelection",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},t.depthComparator=i}(n||(n={}));const a=".x6-widget-selection {\n  position: absolute;\n  top: 0;\n  left: 0;\n  display: none;\n  width: 0;\n  height: 0;\n  touch-action: none;\n}\n.x6-widget-selection-rubberband {\n  display: block;\n  overflow: visible;\n  opacity: 0.3;\n}\n.x6-widget-selection-selected {\n  display: block;\n}\n.x6-widget-selection-box {\n  cursor: move;\n}\n.x6-widget-selection-inner[data-selection-length='0'],\n.x6-widget-selection-inner[data-selection-length='1'] {\n  display: none;\n}\n.x6-widget-selection-content {\n  position: absolute;\n  top: 100%;\n  right: -20px;\n  left: -20px;\n  margin-top: 30px;\n  padding: 6px;\n  line-height: 14px;\n  text-align: center;\n  border-radius: 6px;\n}\n.x6-widget-selection-content:empty {\n  display: none;\n}\n.x6-widget-selection-rubberband {\n  background-color: #3498db;\n  border: 2px solid #2980b9;\n}\n.x6-widget-selection-box {\n  box-sizing: content-box !important;\n  margin-top: -4px;\n  margin-left: -4px;\n  padding-right: 4px;\n  padding-bottom: 4px;\n  border: 2px dashed #feb663;\n  box-shadow: 2px 2px 5px #d3d3d3;\n}\n.x6-widget-selection-inner {\n  box-sizing: content-box !important;\n  margin-top: -8px;\n  margin-left: -8px;\n  padding-right: 12px;\n  padding-bottom: 12px;\n  border: 2px solid #feb663;\n  box-shadow: 2px 2px 5px #d3d3d3;\n}\n.x6-widget-selection-content {\n  color: #fff;\n  font-size: 10px;\n  background-color: #6a6b8a;\n}\n";s["m"].prototype.isSelectionEnabled=function(){const t=this.getPlugin("selection");return!!t&&t.isEnabled()},s["m"].prototype.enableSelection=function(){const t=this.getPlugin("selection");return t&&t.enable(),this},s["m"].prototype.disableSelection=function(){const t=this.getPlugin("selection");return t&&t.disable(),this},s["m"].prototype.toggleSelection=function(t){const e=this.getPlugin("selection");return e&&e.toggleEnabled(t),this},s["m"].prototype.isMultipleSelection=function(){const t=this.getPlugin("selection");return!!t&&t.isMultipleSelection()},s["m"].prototype.enableMultipleSelection=function(){const t=this.getPlugin("selection");return t&&t.enableMultipleSelection(),this},s["m"].prototype.disableMultipleSelection=function(){const t=this.getPlugin("selection");return t&&t.disableMultipleSelection(),this},s["m"].prototype.toggleMultipleSelection=function(t){const e=this.getPlugin("selection");return e&&e.toggleMultipleSelection(t),this},s["m"].prototype.isSelectionMovable=function(){const t=this.getPlugin("selection");return!!t&&t.isSelectionMovable()},s["m"].prototype.enableSelectionMovable=function(){const t=this.getPlugin("selection");return t&&t.enableSelectionMovable(),this},s["m"].prototype.disableSelectionMovable=function(){const t=this.getPlugin("selection");return t&&t.disableSelectionMovable(),this},s["m"].prototype.toggleSelectionMovable=function(t){const e=this.getPlugin("selection");return e&&e.toggleSelectionMovable(t),this},s["m"].prototype.isRubberbandEnabled=function(){const t=this.getPlugin("selection");return!!t&&t.isRubberbandEnabled()},s["m"].prototype.enableRubberband=function(){const t=this.getPlugin("selection");return t&&t.enableRubberband(),this},s["m"].prototype.disableRubberband=function(){const t=this.getPlugin("selection");return t&&t.disableRubberband(),this},s["m"].prototype.toggleRubberband=function(t){const e=this.getPlugin("selection");return e&&e.toggleRubberband(t),this},s["m"].prototype.isStrictRubberband=function(){const t=this.getPlugin("selection");return!!t&&t.isStrictRubberband()},s["m"].prototype.enableStrictRubberband=function(){const t=this.getPlugin("selection");return t&&t.enableStrictRubberband(),this},s["m"].prototype.disableStrictRubberband=function(){const t=this.getPlugin("selection");return t&&t.disableStrictRubberband(),this},s["m"].prototype.toggleStrictRubberband=function(t){const e=this.getPlugin("selection");return e&&e.toggleStrictRubberband(t),this},s["m"].prototype.setRubberbandModifiers=function(t){const e=this.getPlugin("selection");return e&&e.setRubberbandModifiers(t),this},s["m"].prototype.setSelectionFilter=function(t){const e=this.getPlugin("selection");return e&&e.setSelectionFilter(t),this},s["m"].prototype.setSelectionDisplayContent=function(t){const e=this.getPlugin("selection");return e&&e.setSelectionDisplayContent(t),this},s["m"].prototype.isSelectionEmpty=function(){const t=this.getPlugin("selection");return!t||t.isEmpty()},s["m"].prototype.cleanSelection=function(t){const e=this.getPlugin("selection");return e&&e.clean(t),this},s["m"].prototype.resetSelection=function(t,e){const i=this.getPlugin("selection");return i&&i.reset(t,e),this},s["m"].prototype.getSelectedCells=function(){const t=this.getPlugin("selection");return t?t.getSelectedCells():[]},s["m"].prototype.getSelectedCellCount=function(){const t=this.getPlugin("selection");return t?t.getSelectedCellCount():0},s["m"].prototype.isSelected=function(t){const e=this.getPlugin("selection");return!!e&&e.isSelected(t)},s["m"].prototype.select=function(t,e){const i=this.getPlugin("selection");return i&&i.select(t,e),this},s["m"].prototype.unselect=function(t,e){const i=this.getPlugin("selection");return i&&i.unselect(t,e),this};var l=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class h extends s["c"]{get rubberbandDisabled(){return!0!==this.options.enabled||!0!==this.options.rubberband}get disabled(){return!0!==this.options.enabled}get length(){return this.selectionImpl.length}get cells(){return this.selectionImpl.cells}constructor(t={}){super(),this.name="selection",this.movedMap=new WeakMap,this.unselectMap=new WeakMap,this.options=Object.assign(Object.assign({enabled:!0},h.defaultOptions),t),s["g"].ensure(this.name,a)}init(t){this.graph=t,this.selectionImpl=new r(Object.assign(Object.assign({},this.options),{graph:t})),this.setup(),this.startListening()}isEnabled(){return!this.disabled}enable(){this.disabled&&(this.options.enabled=!0)}disable(){this.disabled||(this.options.enabled=!1)}toggleEnabled(t){return null!=t?t!==this.isEnabled()&&(t?this.enable():this.disable()):this.isEnabled()?this.disable():this.enable(),this}isMultipleSelection(){return this.isMultiple()}enableMultipleSelection(){return this.enableMultiple(),this}disableMultipleSelection(){return this.disableMultiple(),this}toggleMultipleSelection(t){return null!=t?t!==this.isMultipleSelection()&&(t?this.enableMultipleSelection():this.disableMultipleSelection()):this.isMultipleSelection()?this.disableMultipleSelection():this.enableMultipleSelection(),this}isSelectionMovable(){return!1!==this.options.movable}enableSelectionMovable(){return this.selectionImpl.options.movable=!0,this}disableSelectionMovable(){return this.selectionImpl.options.movable=!1,this}toggleSelectionMovable(t){return null!=t?t!==this.isSelectionMovable()&&(t?this.enableSelectionMovable():this.disableSelectionMovable()):this.isSelectionMovable()?this.disableSelectionMovable():this.enableSelectionMovable(),this}isRubberbandEnabled(){return!this.rubberbandDisabled}enableRubberband(){return this.rubberbandDisabled&&(this.options.rubberband=!0),this}disableRubberband(){return this.rubberbandDisabled||(this.options.rubberband=!1),this}toggleRubberband(t){return null!=t?t!==this.isRubberbandEnabled()&&(t?this.enableRubberband():this.disableRubberband()):this.isRubberbandEnabled()?this.disableRubberband():this.enableRubberband(),this}isStrictRubberband(){return!0===this.selectionImpl.options.strict}enableStrictRubberband(){return this.selectionImpl.options.strict=!0,this}disableStrictRubberband(){return this.selectionImpl.options.strict=!1,this}toggleStrictRubberband(t){return null!=t?t!==this.isStrictRubberband()&&(t?this.enableStrictRubberband():this.disableStrictRubberband()):this.isStrictRubberband()?this.disableStrictRubberband():this.enableStrictRubberband(),this}setRubberbandModifiers(t){this.setModifiers(t)}setSelectionFilter(t){return this.setFilter(t),this}setSelectionDisplayContent(t){return this.setContent(t),this}isEmpty(){return this.length<=0}clean(t={}){return this.selectionImpl.clean(t),this}reset(t,e={}){return this.selectionImpl.reset(t?this.getCells(t):[],e),this}getSelectedCells(){return this.cells}getSelectedCellCount(){return this.length}isSelected(t){return this.selectionImpl.isSelected(t)}select(t,e={}){const i=this.getCells(t);return i.length&&(this.isMultiple()?this.selectionImpl.select(i,e):this.reset(i.slice(0,1),e)),this}unselect(t,e={}){return this.selectionImpl.unselect(this.getCells(t),e),this}setup(){this.selectionImpl.on("*",(t,e)=>{this.trigger(t,e),this.graph.trigger(t,e)})}startListening(){this.graph.on("blank:mousedown",this.onBlankMouseDown,this),this.graph.on("blank:click",this.onBlankClick,this),this.graph.on("cell:mousemove",this.onCellMouseMove,this),this.graph.on("cell:mouseup",this.onCellMouseUp,this),this.selectionImpl.on("box:mousedown",this.onBoxMouseDown,this)}stopListening(){this.graph.off("blank:mousedown",this.onBlankMouseDown,this),this.graph.off("blank:click",this.onBlankClick,this),this.graph.off("cell:mousemove",this.onCellMouseMove,this),this.graph.off("cell:mouseup",this.onCellMouseUp,this),this.selectionImpl.off("box:mousedown",this.onBoxMouseDown,this)}onBlankMouseDown({e:t}){if(!this.allowBlankMouseDown(t))return;const e=this.graph.panning.allowPanning(t,!0),i=this.graph.getPlugin("scroller"),n=i&&i.allowPanning(t,!0);(this.allowRubberband(t,!0)||this.allowRubberband(t)&&!n&&!e)&&this.startRubberband(t)}allowBlankMouseDown(t){const e=this.options.eventTypes;return(null===e||void 0===e?void 0:e.includes("leftMouseDown"))&&0===t.button||(null===e||void 0===e?void 0:e.includes("mouseWheelDown"))&&1===t.button}onBlankClick(){this.clean()}allowRubberband(t,e){return!this.rubberbandDisabled&&s["o"].isMatch(t,this.options.modifiers,e)}allowMultipleSelection(t){return this.isMultiple()&&s["o"].isMatch(t,this.options.multipleSelectionModifiers)}onCellMouseMove({cell:t}){this.movedMap.set(t,!0)}onCellMouseUp({e:t,cell:e}){const i=this.options;let n=this.disabled;!n&&this.movedMap.has(e)&&(n=!1===i.selectCellOnMoved,n||(n=!1===i.selectNodeOnMoved&&e.isNode()),n||(n=!1===i.selectEdgeOnMoved&&e.isEdge())),n||(this.allowMultipleSelection(t)?this.unselectMap.has(e)?this.unselectMap.delete(e):this.isSelected(e)?this.unselect(e):this.select(e):this.reset(e)),this.movedMap.delete(e)}onBoxMouseDown({e:t,cell:e}){this.disabled||this.allowMultipleSelection(t)&&(this.unselect(e),this.unselectMap.set(e,!0))}getCells(t){return(Array.isArray(t)?t:[t]).map(t=>"string"===typeof t?this.graph.getCellById(t):t).filter(t=>null!=t)}startRubberband(t){return this.rubberbandDisabled||this.selectionImpl.startSelecting(t),this}isMultiple(){return!1!==this.options.multiple}enableMultiple(){return this.options.multiple=!0,this}disableMultiple(){return this.options.multiple=!1,this}setModifiers(t){return this.options.modifiers=t,this}setContent(t){return this.selectionImpl.setContent(t),this}setFilter(t){return this.selectionImpl.setFilter(t),this}dispose(){this.stopListening(),this.off(),this.selectionImpl.dispose(),s["g"].clean(this.name)}}l([s["c"].dispose()],h.prototype,"dispose",null),function(t){t.defaultOptions={rubberband:!1,rubberNode:!0,rubberEdge:!1,pointerEvents:"auto",multiple:!0,multipleSelectionModifiers:["ctrl","meta"],movable:!0,strict:!1,selectCellOnMoved:!1,selectNodeOnMoved:!1,selectEdgeOnMoved:!1,following:!0,content:null,eventTypes:["leftMouseDown","mouseWheelDown"]}}(h||(h={}))},"6f48":function(t,e,i){"use strict";var n=i("6d61"),s=i("6566");n("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),s)},"8a60":function(t,e,i){var n;(function(s,o,r){if(s){for(var a,l={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},h={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},c={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},d={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},p=1;p<20;++p)l[111+p]="f"+p;for(p=0;p<=9;++p)l[p+96]=p.toString();M.prototype.bind=function(t,e,i){var n=this;return t=t instanceof Array?t:[t],n._bindMultiple.call(n,t,e,i),n},M.prototype.unbind=function(t,e){var i=this;return i.bind.call(i,t,(function(){}),e)},M.prototype.trigger=function(t,e){var i=this;return i._directMap[t+":"+e]&&i._directMap[t+":"+e]({},t),i},M.prototype.reset=function(){var t=this;return t._callbacks={},t._directMap={},t},M.prototype.stopCallback=function(t,e){var i=this;if((" "+e.className+" ").indexOf(" mousetrap ")>-1)return!1;if(E(e,i.target))return!1;if("composedPath"in t&&"function"===typeof t.composedPath){var n=t.composedPath()[0];n!==t.target&&(e=n)}return"INPUT"==e.tagName||"SELECT"==e.tagName||"TEXTAREA"==e.tagName||e.isContentEditable},M.prototype.handleKey=function(){var t=this;return t._handleKey.apply(t,arguments)},M.addKeycodes=function(t){for(var e in t)t.hasOwnProperty(e)&&(l[e]=t[e]);a=null},M.init=function(){var t=M(o);for(var e in t)"_"!==e.charAt(0)&&(M[e]=function(e){return function(){return t[e].apply(t,arguments)}}(e))},M.init(),s.Mousetrap=M,t.exports&&(t.exports=M),n=function(){return M}.call(e,i,e,t),n===r||(t.exports=n)}function g(t,e,i){t.addEventListener?t.addEventListener(e,i,!1):t.attachEvent("on"+e,i)}function u(t){if("keypress"==t.type){var e=String.fromCharCode(t.which);return t.shiftKey||(e=e.toLowerCase()),e}return l[t.which]?l[t.which]:h[t.which]?h[t.which]:String.fromCharCode(t.which).toLowerCase()}function f(t,e){return t.sort().join(",")===e.sort().join(",")}function m(t){var e=[];return t.shiftKey&&e.push("shift"),t.altKey&&e.push("alt"),t.ctrlKey&&e.push("ctrl"),t.metaKey&&e.push("meta"),e}function b(t){t.preventDefault?t.preventDefault():t.returnValue=!1}function y(t){t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}function x(t){return"shift"==t||"ctrl"==t||"alt"==t||"meta"==t}function v(){if(!a)for(var t in a={},l)t>95&&t<112||l.hasOwnProperty(t)&&(a[l[t]]=t);return a}function w(t,e,i){return i||(i=v()[t]?"keydown":"keypress"),"keypress"==i&&e.length&&(i="keydown"),i}function C(t){return"+"===t?["+"]:(t=t.replace(/\+{2}/g,"+plus"),t.split("+"))}function S(t,e){var i,n,s,o=[];for(i=C(t),s=0;s<i.length;++s)n=i[s],d[n]&&(n=d[n]),e&&"keypress"!=e&&c[n]&&(n=c[n],o.push("shift")),x(n)&&o.push(n);return e=w(n,o,e),{key:n,modifiers:o,action:e}}function E(t,e){return null!==t&&t!==o&&(t===e||E(t.parentNode,e))}function M(t){var e=this;if(t=t||o,!(e instanceof M))return new M(t);e.target=t,e._callbacks={},e._directMap={};var i,n={},s=!1,r=!1,a=!1;function l(t){t=t||{};var e,i=!1;for(e in n)t[e]?i=!0:n[e]=0;i||(a=!1)}function h(t,i,s,o,r,a){var l,h,c=[],d=s.type;if(!e._callbacks[t])return[];for("keyup"==d&&x(t)&&(i=[t]),l=0;l<e._callbacks[t].length;++l)if(h=e._callbacks[t][l],(o||!h.seq||n[h.seq]==h.level)&&d==h.action&&("keypress"==d&&!s.metaKey&&!s.ctrlKey||f(i,h.modifiers))){var p=!o&&h.combo==r,g=o&&h.seq==o&&h.level==a;(p||g)&&e._callbacks[t].splice(l,1),c.push(h)}return c}function c(t,i,n,s){e.stopCallback(i,i.target||i.srcElement,n,s)||!1===t(i,n)&&(b(i),y(i))}function d(t){"number"!==typeof t.which&&(t.which=t.keyCode);var i=u(t);i&&("keyup"!=t.type||s!==i?e.handleKey(i,m(t),t):s=!1)}function p(){clearTimeout(i),i=setTimeout(l,1e3)}function v(t,e,i,o){function r(e){return function(){a=e,++n[t],p()}}function h(e){c(i,e,t),"keyup"!==o&&(s=u(e)),setTimeout(l,10)}n[t]=0;for(var d=0;d<e.length;++d){var g=d+1===e.length,f=g?h:r(o||S(e[d+1]).action);w(e[d],f,o,t,d)}}function w(t,i,n,s,o){e._directMap[t+":"+n]=i,t=t.replace(/\s+/g," ");var r,a=t.split(" ");a.length>1?v(t,a,i,n):(r=S(t,n),e._callbacks[r.key]=e._callbacks[r.key]||[],h(r.key,r.modifiers,{type:r.action},s,t,o),e._callbacks[r.key][s?"unshift":"push"]({callback:i,modifiers:r.modifiers,action:r.action,seq:s,level:o,combo:t}))}e._handleKey=function(t,e,i){var n,s=h(t,e,i),o={},d=0,p=!1;for(n=0;n<s.length;++n)s[n].seq&&(d=Math.max(d,s[n].level));for(n=0;n<s.length;++n)if(s[n].seq){if(s[n].level!=d)continue;p=!0,o[s[n].seq]=1,c(s[n].callback,i,s[n].combo,s[n].seq)}else p||c(s[n].callback,i,s[n].combo);var g="keypress"==i.type&&r;i.type!=a||x(t)||g||l(o),r=p&&"keydown"==i.type},e._bindMultiple=function(t,e,i){for(var n=0;n<t.length;++n)w(t[n],e,i)},g(t,"keypress",d),g(t,"keydown",d),g(t,"keyup",d)}})("undefined"!==typeof window?window:null,"undefined"!==typeof window?document:null)},b458:function(t,e,i){"use strict";i.d(e,"a",(function(){return h}));var n=i("5728"),s=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r},o=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(t);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(t,n[s])&&(i[n[s]]=t[n[s]])}return i};class r extends n["w"]{get model(){return this.graph.model}get containerClassName(){return this.prefixClassName("widget-snapline")}get verticalClassName(){return this.containerClassName+"-vertical"}get horizontalClassName(){return this.containerClassName+"-horizontal"}constructor(t){super();const{graph:e}=t,i=o(t,["graph"]);this.graph=e,this.options=Object.assign({},i),this.offset={x:0,y:0},this.render(),this.disabled||this.startListening()}get disabled(){return!0!==this.options.enabled}enable(){this.disabled&&(this.options.enabled=!0,this.startListening())}disable(){this.disabled||(this.options.enabled=!1,this.stopListening())}setFilter(t){this.options.filter=t}render(){const t=this.containerWrapper=new n["v"]("svg"),e=this.horizontal=new n["v"]("line"),i=this.vertical=new n["v"]("line");t.addClass(this.containerClassName),e.addClass(this.horizontalClassName),i.addClass(this.verticalClassName),t.setAttribute("width","100%"),t.setAttribute("height","100%"),e.setAttribute("display","none"),i.setAttribute("display","none"),t.append([e,i]),this.options.className&&t.addClass(this.options.className),this.container=this.containerWrapper.node}startListening(){this.stopListening(),this.graph.on("node:mousedown",this.captureCursorOffset,this),this.graph.on("node:mousemove",this.snapOnMoving,this),this.model.on("batch:stop",this.onBatchStop,this),this.delegateDocumentEvents({mouseup:"hide",touchend:"hide"})}stopListening(){this.graph.off("node:mousedown",this.captureCursorOffset,this),this.graph.off("node:mousemove",this.snapOnMoving,this),this.model.off("batch:stop",this.onBatchStop,this),this.undelegateDocumentEvents()}onBatchStop({name:t,data:e}){"resize"===t&&this.snapOnResizing(e.cell,e)}captureCursorOffset({view:t,x:e,y:i}){const n=t.getDelegatedView();if(n&&this.isNodeMovable(n)){const n=t.cell.getPosition();this.offset={x:e-n.x,y:i-n.y}}}isNodeMovable(t){return t&&t.cell.isNode()&&t.can("nodeMovable")}getRestrictArea(t){const e=this.graph.options.translating.restrict,i="function"===typeof e?n["k"].call(e,this.graph,t):e;return"number"===typeof i?this.graph.transform.getGraphArea().inflate(i):!0===i?this.graph.transform.getGraphArea():i||null}snapOnResizing(t,e){if(this.options.resizing&&!e.snapped&&e.ui&&e.direction&&e.trueDirection){const i=this.graph.renderer.findViewByCell(t);if(i&&i.cell.isNode()){const s=t.getBBox(),o=s.bbox(t.getAngle()),r=o.getTopLeft(),a=o.getBottomRight(),l=n["a"].normalize(t.getAngle()),h=this.options.tolerance||0;let c,d,p,g,u,f;const m={vertical:0,horizontal:0},b=e.direction,y=e.trueDirection,x=e.relativeDirection;-1!==y.indexOf("right")?m.vertical=a.x:m.vertical=r.x,-1!==y.indexOf("bottom")?m.horizontal=a.y:m.horizontal=r.y,this.model.getNodes().some(e=>{if(this.isIgnored(t,e))return!1;const i=e.getBBox().bbox(e.getAngle()),s=i.getTopLeft(),r=i.getBottomRight(),l={vertical:[s.x,r.x],horizontal:[s.y,r.y]},b={};return Object.keys(l).forEach(t=>{const e=t,i=l[e].map(t=>({position:t,distance:Math.abs(t-m[e])})).filter(t=>t.distance<=h);b[e]=n["b"].sortBy(i,t=>t.distance)}),null==c&&b.vertical.length>0&&(c=b.vertical[0].position,d=Math.min(o.y,i.y),p=Math.max(a.y,r.y)-d),null==g&&b.horizontal.length>0&&(g=b.horizontal[0].position,u=Math.min(o.x,i.x),f=Math.max(a.x,r.x)-u),null!=c&&null!=g}),this.hide();let v=0,w=0;null==g&&null==c||(null!=c&&(v=-1!==y.indexOf("right")?c-a.x:r.x-c),null!=g&&(w=-1!==y.indexOf("bottom")?g-a.y:r.y-g));let C=0,S=0;if(l%90===0)90===l||270===l?(C=w,S=v):(C=v,S=w);else{const t=l>=0&&l<90?1:l>=90&&l<180?4:l>=180&&l<270?3:2;null!=g&&null!=c&&(v<w?(w=0,g=void 0):(v=0,c=void 0));const e=n["a"].toRad(l%90);v&&(C=3===t?v/Math.cos(e):v/Math.sin(e)),w&&(S=3===t?w/Math.cos(e):w/Math.sin(e));const i=1===t||3===t;switch(x){case"top":case"bottom":S=w?w/(i?Math.cos(e):Math.sin(e)):v/(i?Math.sin(e):Math.cos(e));break;case"left":case"right":C=v?v/(i?Math.cos(e):Math.sin(e)):w/(i?Math.sin(e):Math.cos(e));break;default:break}}switch(x){case"top":case"bottom":C=0;break;case"left":case"right":S=0;break;default:break}const E=this.graph.getGridSize();let M=Math.max(s.width+C,E),j=Math.max(s.height+S,E);e.minWidth&&e.minWidth>E&&(M=Math.max(M,e.minWidth)),e.minHeight&&e.minHeight>E&&(j=Math.max(j,e.minHeight)),e.maxWidth&&(M=Math.min(M,e.maxWidth)),e.maxHeight&&(j=Math.min(j,e.maxHeight)),e.preserveAspectRatio&&(S<C?j=M*(s.height/s.width):M=j*(s.width/s.height)),M===s.width&&j===s.height||(t.resize(M,j,{direction:b,relativeDirection:x,trueDirection:y,snapped:!0,snaplines:this.cid,restrict:this.getRestrictArea(i)}),p&&(p+=j-s.height),f&&(f+=M-s.width));const O=t.getBBox().bbox(l);c&&Math.abs(O.x-c)>1&&Math.abs(O.width+O.x-c)>1&&(c=void 0),g&&Math.abs(O.y-g)>1&&Math.abs(O.height+O.y-g)>1&&(g=void 0),this.update({verticalLeft:c,verticalTop:d,verticalHeight:p,horizontalTop:g,horizontalLeft:u,horizontalWidth:f})}}}snapOnMoving({view:t,e:e,x:i,y:s}){const o=t.getEventData(e).delegatedView||t;if(!this.isNodeMovable(o))return;const r=o.cell,a=r.getSize(),l=r.getPosition(),h=new n["t"](i-this.offset.x,s-this.offset.y,a.width,a.height),c=r.getAngle(),d=h.getCenter(),p=h.bbox(c),g=p.getTopLeft(),u=p.getBottomRight(),f=this.options.tolerance||0;let m,b,y,x,v,w,C=0,S=0;if(this.model.getNodes().some(t=>{if(this.isIgnored(r,t))return!1;const e=t.getBBox().bbox(t.getAngle()),i=e.getCenter(),n=e.getTopLeft(),s=e.getBottomRight();return null==m&&(Math.abs(i.x-d.x)<f?(m=i.x,C=.5):Math.abs(n.x-g.x)<f?(m=n.x,C=0):Math.abs(n.x-u.x)<f?(m=n.x,C=1):Math.abs(s.x-u.x)<f?(m=s.x,C=1):Math.abs(s.x-g.x)<f&&(m=s.x),null!=m&&(b=Math.min(p.y,e.y),y=Math.max(u.y,s.y)-b)),null==x&&(Math.abs(i.y-d.y)<f?(x=i.y,S=.5):Math.abs(n.y-g.y)<f?x=n.y:Math.abs(n.y-u.y)<f?(x=n.y,S=1):Math.abs(s.y-u.y)<f?(x=s.y,S=1):Math.abs(s.y-g.y)<f&&(x=s.y),null!=x&&(v=Math.min(p.x,e.x),w=Math.max(u.x,s.x)-v)),null!=m&&null!=x}),this.hide(),null!=x||null!=m){null!=x&&(p.y=x-S*p.height),null!=m&&(p.x=m-C*p.width);const t=p.getCenter(),e=t.x-h.width/2,i=t.y-h.height/2,n=e-l.x,s=i-l.y;0===n&&0===s||(r.translate(n,s,{snapped:!0,restrict:this.getRestrictArea(o)}),w&&(w+=n),y&&(y+=s)),this.update({verticalLeft:m,verticalTop:b,verticalHeight:y,horizontalTop:x,horizontalLeft:v,horizontalWidth:w})}}isIgnored(t,e){return e.id===t.id||e.isDescendantOf(t)||!this.filter(e)}filter(t){const e=this.options.filter;return Array.isArray(e)?e.some(e=>"string"===typeof e?t.shape===e:t.id===e.id):"function"!==typeof e||n["k"].call(e,this.graph,t)}update(t){if(t.horizontalTop){const e=this.graph.localToGraph(new n["s"](t.horizontalLeft,t.horizontalTop)),i=this.graph.localToGraph(new n["s"](t.horizontalLeft+t.horizontalWidth,t.horizontalTop));this.horizontal.setAttributes({x1:this.options.sharp?""+e.x:"0",y1:""+e.y,x2:this.options.sharp?""+i.x:"100%",y2:""+i.y,display:"inherit"})}else this.horizontal.setAttribute("display","none");if(t.verticalLeft){const e=this.graph.localToGraph(new n["s"](t.verticalLeft,t.verticalTop)),i=this.graph.localToGraph(new n["s"](t.verticalLeft,t.verticalTop+t.verticalHeight));this.vertical.setAttributes({x1:""+e.x,y1:this.options.sharp?""+e.y:"0",x2:""+i.x,y2:this.options.sharp?""+i.y:"100%",display:"inherit"})}else this.vertical.setAttribute("display","none");this.show()}resetTimer(){this.timer&&(clearTimeout(this.timer),this.timer=null)}show(){return this.resetTimer(),null==this.container.parentNode&&this.graph.container.appendChild(this.container),this}hide(){this.resetTimer(),this.vertical.setAttribute("display","none"),this.horizontal.setAttribute("display","none");const t=this.options.clean,e="number"===typeof t?t:!1!==t?3e3:0;return e>0&&(this.timer=window.setTimeout(()=>{null!==this.container.parentNode&&this.unmount()},e)),this}onRemove(){this.stopListening(),this.hide()}dispose(){this.remove()}}s([n["w"].dispose()],r.prototype,"dispose",null);const a=".x6-widget-snapline {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  pointer-events: none;\n}\n.x6-widget-snapline-vertical,\n.x6-widget-snapline-horizontal {\n  stroke: #2ecc71;\n  stroke-width: 1px;\n}\n";n["m"].prototype.isSnaplineEnabled=function(){const t=this.getPlugin("snapline");return!!t&&t.isEnabled()},n["m"].prototype.enableSnapline=function(){const t=this.getPlugin("snapline");return t&&t.enable(),this},n["m"].prototype.disableSnapline=function(){const t=this.getPlugin("snapline");return t&&t.disable(),this},n["m"].prototype.toggleSnapline=function(){const t=this.getPlugin("snapline");return t&&t.toggleEnabled(),this},n["m"].prototype.hideSnapline=function(){const t=this.getPlugin("snapline");return t&&t.hide(),this},n["m"].prototype.setSnaplineFilter=function(t){const e=this.getPlugin("snapline");return e&&e.setFilter(t),this},n["m"].prototype.isSnaplineOnResizingEnabled=function(){const t=this.getPlugin("snapline");return!!t&&t.isOnResizingEnabled()},n["m"].prototype.enableSnaplineOnResizing=function(){const t=this.getPlugin("snapline");return t&&t.enableOnResizing(),this},n["m"].prototype.disableSnaplineOnResizing=function(){const t=this.getPlugin("snapline");return t&&t.disableOnResizing(),this},n["m"].prototype.toggleSnaplineOnResizing=function(t){const e=this.getPlugin("snapline");return e&&e.toggleOnResizing(t),this},n["m"].prototype.isSharpSnapline=function(){const t=this.getPlugin("snapline");return!!t&&t.isSharp()},n["m"].prototype.enableSharpSnapline=function(){const t=this.getPlugin("snapline");return t&&t.enableSharp(),this},n["m"].prototype.disableSharpSnapline=function(){const t=this.getPlugin("snapline");return t&&t.disableSharp(),this},n["m"].prototype.toggleSharpSnapline=function(t){const e=this.getPlugin("snapline");return e&&e.toggleSharp(t),this},n["m"].prototype.getSnaplineTolerance=function(){const t=this.getPlugin("snapline");if(t)return t.getTolerance()},n["m"].prototype.setSnaplineTolerance=function(t){const e=this.getPlugin("snapline");return e&&e.setTolerance(t),this};var l=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r};class h extends n["i"]{constructor(t={}){super(),this.name="snapline",this.options=Object.assign({enabled:!0,tolerance:10},t),n["g"].ensure(this.name,a)}init(t){this.snaplineImpl=new r(Object.assign(Object.assign({},this.options),{graph:t}))}isEnabled(){return!this.snaplineImpl.disabled}enable(){this.snaplineImpl.enable()}disable(){this.snaplineImpl.disable()}toggleEnabled(t){if(null==t)return this.isEnabled()?this.disable():this.enable(),this;t!==this.isEnabled()&&(t?this.enable():this.disable())}hide(){return this.snaplineImpl.hide(),this}setFilter(t){return this.snaplineImpl.setFilter(t),this}isOnResizingEnabled(){return!0===this.snaplineImpl.options.resizing}enableOnResizing(){return this.snaplineImpl.options.resizing=!0,this}disableOnResizing(){return this.snaplineImpl.options.resizing=!1,this}toggleOnResizing(t){return null!=t?t!==this.isOnResizingEnabled()&&(t?this.enableOnResizing():this.disableOnResizing()):this.isOnResizingEnabled()?this.disableOnResizing():this.enableOnResizing(),this}isSharp(){return!0===this.snaplineImpl.options.sharp}enableSharp(){return this.snaplineImpl.options.sharp=!0,this}disableSharp(){return this.snaplineImpl.options.sharp=!1,this}toggleSharp(t){return null!=t?t!==this.isSharp()&&(t?this.enableSharp():this.disableSharp()):this.isSharp()?this.disableSharp():this.enableSharp(),this}getTolerance(){return this.snaplineImpl.options.tolerance}setTolerance(t){return this.snaplineImpl.options.tolerance=t,this}captureCursorOffset(t){this.snaplineImpl.captureCursorOffset(t)}snapOnMoving(t){this.snaplineImpl.snapOnMoving(t)}dispose(){this.snaplineImpl.dispose(),n["g"].clean(this.name)}}l([n["i"].dispose()],h.prototype,"dispose",null)},e619:function(t,e,i){"use strict";i.d(e,"a",(function(){return l}));var n,s=i("5728");class o{constructor(){this.cells=[]}copy(t,e,i={}){this.options=Object.assign({},i);const n=s["n"].isModel(e)?e:e.model,o=n.cloneSubGraph(t,i);this.cells=s["b"].sortBy(Object.keys(o).map(t=>o[t]),t=>t.isEdge()?2:1),this.serialize(i)}cut(t,e,i={}){this.copy(t,e,i);const n=s["m"].isGraph(e)?e.model:e;n.batchUpdate("cut",()=>{t.forEach(t=>t.remove())})}paste(t,e={}){const i=Object.assign(Object.assign({},this.options),e),{offset:n,edgeProps:o,nodeProps:r}=i;let a=20,l=20;n&&(a="number"===typeof n?n:n.dx,l="number"===typeof n?n:n.dy),this.deserialize(i);const h=this.cells;h.forEach(t=>{t.model=null,t.removeProp("zIndex"),(a||l)&&t.translate(a,l),r&&t.isNode()&&t.prop(r),o&&t.isEdge()&&t.prop(o)});const c=s["m"].isGraph(t)?t.model:t;return c.batchUpdate("paste",()=>{c.addCells(this.cells)}),this.copy(h,t,e),h}serialize(t){!1!==t.useLocalStorage&&n.save(this.cells)}deserialize(t){if(t.useLocalStorage){const t=n.fetch();t&&(this.cells=t)}}isEmpty(t={}){return t.useLocalStorage&&this.deserialize(t),this.cells.length<=0}clean(){this.options={},this.cells=[],n.clean()}}(function(t){const e=s["f"].prefixCls+".clipboard.cells";function i(t){if(window.localStorage){const i=t.map(t=>t.toJSON());localStorage.setItem(e,JSON.stringify(i))}}function n(){if(window.localStorage){const t=localStorage.getItem(e),i=t?JSON.parse(t):[];if(i)return s["n"].fromJSON(i)}}function o(){window.localStorage&&localStorage.removeItem(e)}t.save=i,t.fetch=n,t.clean=o})(n||(n={})),s["m"].prototype.isClipboardEnabled=function(){const t=this.getPlugin("clipboard");return!!t&&t.isEnabled()},s["m"].prototype.enableClipboard=function(){const t=this.getPlugin("clipboard");return t&&t.enable(),this},s["m"].prototype.disableClipboard=function(){const t=this.getPlugin("clipboard");return t&&t.disable(),this},s["m"].prototype.toggleClipboard=function(t){const e=this.getPlugin("clipboard");return e&&e.toggleEnabled(t),this},s["m"].prototype.isClipboardEmpty=function(t){const e=this.getPlugin("clipboard");return!e||e.isEmpty(t)},s["m"].prototype.getCellsInClipboard=function(){const t=this.getPlugin("clipboard");return t?t.getCellsInClipboard():[]},s["m"].prototype.cleanClipboard=function(){const t=this.getPlugin("clipboard");return t&&t.clean(),this},s["m"].prototype.copy=function(t,e){const i=this.getPlugin("clipboard");return i&&i.copy(t,e),this},s["m"].prototype.cut=function(t,e){const i=this.getPlugin("clipboard");return i&&i.cut(t,e),this},s["m"].prototype.paste=function(t,e){const i=this.getPlugin("clipboard");return i?i.paste(t,e):[]};var r=function(t,e,i,n){var s,o=arguments.length,r=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(r=(o<3?s(r):o>3?s(e,i,r):s(e,i))||r);return o>3&&r&&Object.defineProperty(e,i,r),r},a=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(t);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(t,n[s])&&(i[n[s]]=t[n[s]])}return i};class l extends s["c"]{get disabled(){return!0!==this.options.enabled}get cells(){return this.clipboardImpl.cells}constructor(t={}){super(),this.name="clipboard",this.options=Object.assign({enabled:!0},t)}init(t){this.graph=t,this.clipboardImpl=new o,this.clipboardImpl.deserialize(this.options)}isEnabled(){return!this.disabled}enable(){this.disabled&&(this.options.enabled=!0)}disable(){this.disabled||(this.options.enabled=!1)}toggleEnabled(t){return null!=t?t!==this.isEnabled()&&(t?this.enable():this.disable()):this.isEnabled()?this.disable():this.enable(),this}isEmpty(t={}){return this.clipboardImpl.isEmpty(t)}getCellsInClipboard(){return this.cells}clean(t){return this.disabled&&!t||(this.clipboardImpl.clean(),this.notify("clipboard:changed",{cells:[]})),this}copy(t,e={}){return this.disabled||(this.clipboardImpl.copy(t,this.graph,Object.assign(Object.assign({},this.commonOptions),e)),this.notify("clipboard:changed",{cells:t})),this}cut(t,e={}){return this.disabled||(this.clipboardImpl.cut(t,this.graph,Object.assign(Object.assign({},this.commonOptions),e)),this.notify("clipboard:changed",{cells:t})),this}paste(t={},e=this.graph){return this.disabled?[]:this.clipboardImpl.paste(e,Object.assign(Object.assign({},this.commonOptions),t))}get commonOptions(){const t=this.options,{enabled:e}=t,i=a(t,["enabled"]);return i}notify(t,e){this.trigger(t,e),this.graph.trigger(t,e)}dispose(){this.clean(!0),this.off()}}r([s["c"].dispose()],l.prototype,"dispose",null)}}]);