// 完整的Mock数据服务 - 模拟所有数据和接口
// 为数据中台提供完整的假数据支持

(function() {
    'use strict';
    
    console.log('🗄️ 启动Mock数据服务');
    
    // Mock数据生成器
    class MockDataGenerator {
        constructor() {
            this.initMockData();
        }
        
        // 初始化所有Mock数据
        initMockData() {
            this.users = this.generateUsers();
            this.dashboardData = this.generateDashboardData();
            this.tableData = this.generateTableData();
            this.chartData = this.generateChartData();
            this.menuData = this.generateMenuData();
            this.systemConfig = this.generateSystemConfig();
            this.reportData = this.generateReportData();
        }
        
        // 生成用户数据
        generateUsers() {
            return [
                {
                    id: 1,
                    username: 'admin',
                    name: '系统管理员',
                    email: '<EMAIL>',
                    phone: '13800138000',
                    avatar: '/user.png',
                    roles: ['admin', 'super_admin'],
                    department: '技术部',
                    status: 'active',
                    lastLogin: new Date().toISOString(),
                    createTime: '2024-01-01T00:00:00Z'
                },
                {
                    id: 2,
                    username: 'user001',
                    name: '张三',
                    email: '<EMAIL>',
                    phone: '13800138001',
                    avatar: '/user.png',
                    roles: ['user'],
                    department: '业务部',
                    status: 'active',
                    lastLogin: new Date(Date.now() - 86400000).toISOString(),
                    createTime: '2024-01-15T00:00:00Z'
                },
                {
                    id: 3,
                    username: 'user002',
                    name: '李四',
                    email: '<EMAIL>',
                    phone: '13800138002',
                    avatar: '/user.png',
                    roles: ['user', 'analyst'],
                    department: '数据部',
                    status: 'active',
                    lastLogin: new Date(Date.now() - 172800000).toISOString(),
                    createTime: '2024-02-01T00:00:00Z'
                }
            ];
        }
        
        // 生成仪表板数据
        generateDashboardData() {
            return {
                overview: {
                    totalUsers: 1250,
                    activeUsers: 980,
                    totalRevenue: 2580000,
                    monthlyGrowth: 12.5,
                    todayVisits: 3420,
                    conversionRate: 3.2
                },
                recentActivities: [
                    { id: 1, user: '张三', action: '登录系统', time: '2分钟前', type: 'login' },
                    { id: 2, user: '李四', action: '查看报表', time: '5分钟前', type: 'view' },
                    { id: 3, user: '王五', action: '导出数据', time: '10分钟前', type: 'export' },
                    { id: 4, user: '赵六', action: '修改配置', time: '15分钟前', type: 'config' },
                    { id: 5, user: '钱七', action: '创建报告', time: '20分钟前', type: 'create' }
                ],
                notifications: [
                    { id: 1, title: '系统维护通知', content: '系统将于今晚22:00-24:00进行维护', type: 'warning', time: '1小时前' },
                    { id: 2, title: '数据更新完成', content: '今日数据已更新完成', type: 'success', time: '2小时前' },
                    { id: 3, title: '新功能上线', content: '数据可视化功能已上线', type: 'info', time: '1天前' }
                ]
            };
        }
        
        // 生成表格数据
        generateTableData() {
            const data = [];
            for (let i = 1; i <= 100; i++) {
                data.push({
                    id: i,
                    name: `数据项目${i}`,
                    type: ['数据库', '文件', 'API', '流数据'][Math.floor(Math.random() * 4)],
                    status: ['运行中', '已停止', '维护中', '错误'][Math.floor(Math.random() * 4)],
                    size: `${(Math.random() * 1000).toFixed(2)}MB`,
                    updateTime: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString(),
                    owner: ['张三', '李四', '王五', '赵六'][Math.floor(Math.random() * 4)],
                    description: `这是数据项目${i}的描述信息`,
                    tags: ['重要', '生产', '测试', '开发'].slice(0, Math.floor(Math.random() * 3) + 1)
                });
            }
            return data;
        }
        
        // 生成图表数据
        generateChartData() {
            const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            const days = Array.from({length: 30}, (_, i) => `${i + 1}日`);
            
            return {
                lineChart: {
                    labels: months,
                    datasets: [
                        {
                            label: '用户访问量',
                            data: months.map(() => Math.floor(Math.random() * 1000) + 500),
                            borderColor: '#409EFF',
                            backgroundColor: 'rgba(64, 158, 255, 0.1)'
                        },
                        {
                            label: '数据处理量',
                            data: months.map(() => Math.floor(Math.random() * 800) + 300),
                            borderColor: '#67C23A',
                            backgroundColor: 'rgba(103, 194, 58, 0.1)'
                        }
                    ]
                },
                barChart: {
                    labels: ['数据库', 'API', '文件', '流数据', '缓存'],
                    datasets: [{
                        label: '数据源分布',
                        data: [45, 32, 28, 15, 12],
                        backgroundColor: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
                    }]
                },
                pieChart: {
                    labels: ['正常', '警告', '错误', '离线'],
                    datasets: [{
                        data: [65, 20, 10, 5],
                        backgroundColor: ['#67C23A', '#E6A23C', '#F56C6C', '#909399']
                    }]
                },
                realtimeData: days.map(day => ({
                    time: day,
                    value: Math.floor(Math.random() * 100) + 50
                }))
            };
        }
        
        // 生成菜单数据
        generateMenuData() {
            return [
                {
                    id: 1,
                    name: '首页',
                    path: '/dashboard',
                    icon: 'el-icon-s-home',
                    children: []
                },
                {
                    id: 2,
                    name: '数据管理',
                    path: '/data',
                    icon: 'el-icon-s-grid',
                    children: [
                        { id: 21, name: '数据源', path: '/data/source', icon: 'el-icon-database' },
                        { id: 22, name: '数据表', path: '/data/table', icon: 'el-icon-s-order' },
                        { id: 23, name: '数据质量', path: '/data/quality', icon: 'el-icon-s-check' }
                    ]
                },
                {
                    id: 3,
                    name: '报表分析',
                    path: '/report',
                    icon: 'el-icon-s-data',
                    children: [
                        { id: 31, name: '实时报表', path: '/report/realtime', icon: 'el-icon-s-marketing' },
                        { id: 32, name: '历史报表', path: '/report/history', icon: 'el-icon-s-opportunity' },
                        { id: 33, name: '自定义报表', path: '/report/custom', icon: 'el-icon-s-custom' }
                    ]
                },
                {
                    id: 4,
                    name: '系统管理',
                    path: '/system',
                    icon: 'el-icon-s-tools',
                    children: [
                        { id: 41, name: '用户管理', path: '/system/user', icon: 'el-icon-user' },
                        { id: 42, name: '角色管理', path: '/system/role', icon: 'el-icon-s-custom' },
                        { id: 43, name: '权限管理', path: '/system/permission', icon: 'el-icon-key' },
                        { id: 44, name: '系统配置', path: '/system/config', icon: 'el-icon-setting' }
                    ]
                }
            ];
        }
        
        // 生成系统配置
        generateSystemConfig() {
            return {
                system: {
                    name: '数据中台管理系统',
                    version: '1.0.0',
                    copyright: '© 2024 数据中台',
                    logo: '/logo.png',
                    theme: 'default'
                },
                database: {
                    host: 'localhost',
                    port: 3306,
                    name: 'datacenter',
                    maxConnections: 100
                },
                cache: {
                    type: 'redis',
                    host: 'localhost',
                    port: 6379,
                    ttl: 3600
                },
                security: {
                    tokenExpire: 7200,
                    maxLoginAttempts: 5,
                    passwordPolicy: {
                        minLength: 8,
                        requireUppercase: true,
                        requireNumbers: true
                    }
                }
            };
        }
        
        // 生成报表数据
        generateReportData() {
            return {
                dailyReport: {
                    date: new Date().toISOString().split('T')[0],
                    totalVisits: 12580,
                    uniqueVisitors: 8960,
                    pageViews: 45230,
                    bounceRate: 0.32,
                    avgSessionDuration: 245
                },
                weeklyReport: {
                    week: '2024年第' + Math.ceil(new Date().getDate() / 7) + '周',
                    totalVisits: 89650,
                    growth: 15.2,
                    topPages: [
                        { page: '/dashboard', visits: 25680 },
                        { page: '/data/table', visits: 18950 },
                        { page: '/report/realtime', visits: 15230 }
                    ]
                },
                monthlyReport: {
                    month: new Date().getFullYear() + '年' + (new Date().getMonth() + 1) + '月',
                    revenue: 358900,
                    orders: 1250,
                    customers: 890,
                    growth: 22.5
                }
            };
        }
        
        // 随机生成数据
        generateRandomData(type, count = 10) {
            const data = [];
            for (let i = 0; i < count; i++) {
                switch (type) {
                    case 'user':
                        data.push({
                            id: i + 1,
                            name: `用户${i + 1}`,
                            email: `user${i + 1}@example.com`,
                            status: Math.random() > 0.5 ? 'active' : 'inactive',
                            createTime: new Date(Date.now() - Math.random() * 86400000 * 365).toISOString()
                        });
                        break;
                    case 'order':
                        data.push({
                            id: i + 1,
                            orderNo: `ORD${Date.now()}${i}`,
                            amount: (Math.random() * 1000).toFixed(2),
                            status: ['pending', 'paid', 'shipped', 'completed'][Math.floor(Math.random() * 4)],
                            createTime: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString()
                        });
                        break;
                    default:
                        data.push({
                            id: i + 1,
                            value: Math.random() * 100,
                            timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString()
                        });
                }
            }
            return data;
        }
    }
    
    // 创建Mock数据生成器实例
    window.mockDataGenerator = new MockDataGenerator();
    
    console.log('✅ Mock数据服务初始化完成');
    
})();
