(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7c47e8a0"],{"09f4":function(e,t,l){"use strict";l.d(t,"a",(function(){return a})),Math.easeInOutQuad=function(e,t,l,i){return e/=i/2,e<1?l/2*e*e+t:(e--,-l/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function a(e,t,l){var a=o(),r=e-a,s=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=s;var o=Math.easeInOutQuad(u,a,r,t);n(o),u<t?i(e):l&&"function"===typeof l&&l()};c()}},"2b10":function(e,t,l){"use strict";l.d(t,"g",(function(){return n})),l.d(t,"l",(function(){return o})),l.d(t,"j",(function(){return a})),l.d(t,"k",(function(){return r})),l.d(t,"e",(function(){return s})),l.d(t,"m",(function(){return u})),l.d(t,"d",(function(){return c})),l.d(t,"i",(function(){return d})),l.d(t,"b",(function(){return g})),l.d(t,"c",(function(){return f})),l.d(t,"h",(function(){return p})),l.d(t,"f",(function(){return m})),l.d(t,"a",(function(){return b}));var i=l("b775");function n(e){return Object(i["a"])({url:"api/job/pageList",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/api/job/trigger",method:"post",data:e})}function a(e){return Object(i["a"])({url:"/api/job/start?id="+e,method:"post"})}function r(e){return Object(i["a"])({url:"/api/job/stop?id="+e,method:"post"})}function s(){return Object(i["a"])({url:"api/jobGroup/list",method:"get"})}function u(e){return Object(i["a"])({url:"/api/job/update",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/api/job/add/",method:"post",data:e})}function d(e){return Object(i["a"])({url:"/api/job/remove/"+e,method:"post"})}function g(e){return Object(i["a"])({url:"/api/job/batchDelete/"+e,method:"post"})}function f(e){return Object(i["a"])({url:"/api/job/batchDeleteLogs/"+e,method:"post"})}function p(e){return Object(i["a"])({url:"/api/job/nextTriggerTime?cron="+e,method:"get"})}function m(e){return Object(i["a"])({url:"api/job/list",method:"get",params:e})}function b(e){return Object(i["a"])({url:"/api/job/batchAdd",method:"post",data:e})}},"32e8":function(e,t,l){"use strict";l.d(t,"b",(function(){return n})),l.d(t,"a",(function(){return o})),l.d(t,"c",(function(){return a})),l.d(t,"e",(function(){return r})),l.d(t,"d",(function(){return s}));var i=l("b775");function n(e){return Object(i["a"])({url:"api/log/pageList",method:"get",params:e})}function o(e,t,l){return Object(i["a"])({url:"/api/log/clearLog?jobGroup="+e+"&jobId="+t+"&type="+l,method:"post"})}function a(e){return Object(i["a"])({url:"/api/log/killJob",method:"post",data:e})}function r(e,t,l,n){return Object(i["a"])({url:"/api/log/logDetailCat?executorAddress="+e+"&triggerTime="+t+"&logId="+l+"&fromLineNum="+n,method:"get"})}function s(e,t,l,n,o){return Object(i["a"])({url:"/api/schedulerlog/logDetailCat?executorAddress="+e+"&tasktype="+t+"&triggerTime="+l+"&logId="+n+"&fromLineNum="+o,method:"get"})}},"51a4":function(e,t,l){"use strict";l("5ac2")},"5ac2":function(e,t,l){},67248:function(e,t,l){"use strict";l("8d41");var i="@@wavesContext";function n(e,t){function l(l){var i=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),o=n.ele;if(o){o.style.position="relative",o.style.overflow="hidden";var a=o.getBoundingClientRect(),r=o.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(a.width,a.height)+"px",o.appendChild(r)),n.type){case"center":r.style.top=a.height/2-r.offsetHeight/2+"px",r.style.left=a.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(l.pageY-a.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(l.pageX-a.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}return e[i]?e[i].removeHandle=l:e[i]={removeHandle:l},l}var o={bind:function(e,t){e.addEventListener("click",n(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[i].removeHandle,!1),e.addEventListener("click",n(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[i].removeHandle,!1),e[i]=null,delete e[i]}},a=function(e){e.directive("waves",o)};window.Vue&&(window.waves=o,Vue.use(a)),o.install=a;t["a"]=o},"8ba2":function(e,t,l){"use strict";l.r(t);var i=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("div",{staticClass:"filter-container"},[l("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"任务类型"},model:{value:e.listQuery.jobGroup,callback:function(t){e.$set(e.listQuery,"jobGroup",t)},expression:"listQuery.jobGroup"}},e._l(e.executorList,(function(e){return l("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})})),1),l("el-date-picker",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"调度开始时间","end-placeholder":"调度结束时间"},model:{value:e.triggerTimes,callback:function(t){e.triggerTimes=t},expression:"triggerTimes"}}),l("el-select",{staticClass:"filter-item",staticStyle:{width:"150px","margin-right":"10px"},attrs:{filterable:"",placeholder:"调度结果"},model:{value:e.listQuery.triggerCode,callback:function(t){e.$set(e.listQuery,"triggerCode",t)},expression:"listQuery.triggerCode"}},e._l(e.statusList,(function(e){return l("el-option",{key:e.label,attrs:{label:e.label,value:e.value}})})),1),l("el-date-picker",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"执行开始时间","end-placeholder":"执行结束时间"},model:{value:e.handleTimes,callback:function(t){e.handleTimes=t},expression:"handleTimes"}}),l("el-select",{staticClass:"filter-item",staticStyle:{width:"150px","margin-right":"10px"},attrs:{filterable:"",placeholder:"执行结果"},model:{value:e.listQuery.handleCode,callback:function(t){e.$set(e.listQuery,"handleCode",t)},expression:"listQuery.handleCode"}},e._l(e.statusList,(function(e){return l("el-option",{key:e.label,attrs:{label:e.label,value:e.value}})})),1),l("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),l("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.batchDelete}},[e._v(" 批量删除 ")])],1),l("div",{staticClass:"table-box"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""},on:{"selection-change":e.selectionChangeHandle}},[l("el-table-column",{attrs:{type:"selection","header-align":"left",align:"left",width:"50",selectable:e.selectable}}),l("el-table-column",{attrs:{align:"left",label:"序号",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),e.show?l("el-table-column",{attrs:{align:"left",label:"任务ID",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.jobId))]}}],null,!1,2259765239)}):e._e(),l("el-table-column",{attrs:{align:"left",label:"任务名称",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.jobDesc))]}}])}),l("el-table-column",{attrs:{label:"调度时间",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.triggerTime))]}}])}),l("el-table-column",{attrs:{label:"调度结果",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[500===t.row.triggerCode?l("el-tag",{attrs:{type:"danger"}},[e._v("失败")]):200===t.row.triggerCode?l("el-tag",{attrs:{type:"success"}},[e._v("成功")]):l("el-tag",{attrs:{type:"warning"}},[e._v(e._s(e.statusList.find((function(e){return e.value===t.row.triggerCode})).label))])]}}])}),l("el-table-column",{attrs:{label:"调度备注",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-popover",{attrs:{placement:"bottom",width:"400",trigger:"click"}},[l("h5",{domProps:{innerHTML:e._s(t.row.triggerMsg)}}),l("el-button",{staticClass:"small-button",attrs:{slot:"reference"},slot:"reference"},[e._v("查看")])],1)]}}])}),l("el-table-column",{attrs:{label:"执行时间",align:"left",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.handleTime))]}}])}),l("el-table-column",{attrs:{label:"执行结果",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[500===t.row.handleCode?l("el-tag",{attrs:{type:"danger"}},[e._v("失败")]):200===t.row.handleCode?l("el-tag",{attrs:{type:"success"}},[e._v("成功")]):l("el-tag",{attrs:{type:"warning"}},[e._v(e._s(e.statusList.find((function(e){return e.value===t.row.handleCode})).label))])]}}])}),l("el-table-column",{attrs:{label:"执行备注",align:"left",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-popover",{attrs:{placement:"bottom",width:"400",trigger:"click"}},[l("h5",{domProps:{innerHTML:e._s(t.row.handleMsg)}}),l("el-button",{staticClass:"small-button",attrs:{slot:"reference"},slot:"reference"},[e._v("查看")])],1)]}}])}),l("el-table-column",{attrs:{label:"操作",align:"left",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[l("el-button",{directives:[{name:"show",rawName:"v-show",value:i.jobId,expression:"row.jobId"}],attrs:{type:"warning",icon:"el-icon-tickets"},on:{click:function(t){return e.handleViewJobLog(i)}}},[e._v("日志查看")]),l("el-button",{directives:[{name:"show",rawName:"v-show",value:0===i.handleCode&&200===i.triggerCode,expression:"row.handleCode === 0 && row.triggerCode === 200"}],attrs:{type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.killRunningJob(i)}}},[e._v(" 终止任务 ")])]}}])})],1)],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}}),l("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible,width:"600px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[l("el-form",{ref:"dataForm",attrs:{rules:e.rules,model:e.temp,"label-position":"center","label-width":"100px"}},[l("el-row",[l("el-col",{attrs:{span:14,offset:5}},[l("el-form-item",{attrs:{label:"执行器"}},[l("el-input",{attrs:{size:"medium",value:"全部",disabled:!0}})],1)],1)],1),l("el-row",[l("el-col",{attrs:{span:14,offset:5}},[l("el-form-item",{attrs:{label:"任务"}},[l("el-input",{attrs:{size:"medium",value:"全部",disabled:!0}})],1)],1)],1),l("el-row",[l("el-col",{attrs:{span:14,offset:5}},[l("el-form-item",{attrs:{label:"执行器"}},[l("el-select",{staticStyle:{width:"230px"},attrs:{placeholder:"请选择执行器"},model:{value:e.temp.deleteType,callback:function(t){e.$set(e.temp,"deleteType",t)},expression:"temp.deleteType"}},e._l(e.deleteTypeList,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),l("el-button",{attrs:{type:"primary"},on:{click:e.deleteLog}},[e._v(" 确定 ")])],1)],1),l("el-dialog",{attrs:{title:"日志查看",visible:e.dialogVisible,width:"95%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[l("div",{staticClass:"log-container"},[l("pre",{attrs:{loading:e.logLoading},domProps:{textContent:e._s(e.logContent)}})]),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v(" 关闭 ")]),l("el-button",{attrs:{type:"primary"},on:{click:e.loadLog}},[e._v(" 刷新日志 ")])],1)])],1)},n=[],o=(l("99af"),l("32e8")),a=l("2b10"),r=l("67248"),s=l("333d"),u={name:"JobLog",components:{Pagination:s["a"]},directives:{waves:r["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{triggerTimes:[],handleTimes:[],nameLists:[],dialogVisible:!1,list:null,listLoading:!0,total:0,listQuery:{current:1,size:10,jobGroup:"",triggerCode:"",handleCode:"",jobId:"",logStatus:"",filterTime:"",triggerTimeStart:"",triggerTimeEnd:"",handleTimeStart:"",handleTimeEnd:""},dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",executorList:"",textMap:{create:"Clear"},rules:{},temp:{deleteType:1,jobGroup:0,jobId:0},statusList:[{value:"",label:""},{value:500,label:"失败"},{value:502,label:"失败(超时)"},{value:200,label:"成功"},{value:0,label:"无"}],deleteTypeList:[{value:1,label:"清理一个月之前日志数据"},{value:2,label:"清理三个月之前日志数据"},{value:3,label:"清理六个月之前日志数据"},{value:4,label:"清理一年之前日志数据"},{value:5,label:"清理一千条以前日志数据"},{value:6,label:"清理一万条以前日志数据"},{value:7,label:"清理三万条以前日志数据"},{value:8,label:"清理十万条以前日志数据"},{value:9,label:"清理所有日志数据"}],logStatusList:[{value:-1,label:"全部"},{value:1,label:"成功"},{value:2,label:"失败"},{value:3,label:"进行中"}],jobLogQuery:{executorAddress:"",triggerTime:"",id:"",fromLineNum:1},logContent:"",logShow:!1,logLoading:!1}},mounted:function(){this.fetchData(),this.getExecutor()},methods:{fetchData:function(){var e=this;this.listLoading=!0;var t=Object.assign({});console.debug(),t.jobGroup=this.listQuery.jobGroup,t.triggerCode=this.listQuery.triggerCode,t.handleCode=this.listQuery.handleCode,t.current=this.listQuery.current,t.size=this.listQuery.size,null!=this.triggerTimes&&this.triggerTimes.length?(t.triggerTimeStart=this.triggerTimes[0]+" 00:00:00",t.triggerTimeEnd=this.triggerTimes[1]+" 00:00:00"):(t.triggerTimeStart="",t.triggerTimeEnd=""),null!=this.handleTimes&&this.handleTimes.length?(t.handleTimeStart=this.handleTimes[0]+" 00:00:00",t.handleTimeEnd=this.handleTimes[1]+" 00:00:00"):(t.handleTimeStart="",t.handleTimeEnd=""),o["b"](t).then((function(t){var l=t.content;e.total=l.recordsTotal,e.list=l.data,e.listLoading=!1}))},selectionChangeHandle:function(e){this.namelist=[];for(var t=0;t<e.length;t++)this.namelist=this.namelist.concat(e[t].id);this.nameLists=this.namelist},batchDelete:function(){var e=this;this.$confirm("确定批量删除吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.nameLists.length>0&&a["c"](e.nameLists+"").then((function(t){e.fetchData(),e.$notify({title:"批量删除操作",message:"批量删除成功",type:"success",duration:2e3})}))}))},getExecutor:function(){var e=this;a["e"]().then((function(t){e.listQuery.jobGroup=t;var l=t.content;e.executorList=l;var i={id:"",title:""};e.executorList.unshift(i),e.listQuery.jobGroup=e.executorList[0].id}))},handlerDelete:function(){var e=this;this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},deleteLog:function(){var e=this;o["a"](this.temp.jobGroup,this.temp.jobId,this.temp.deleteType).then((function(t){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"清理日志",message:"清理成功",type:"success",duration:2e3})}))},handleViewJobLog:function(e){this.dialogVisible=!0,this.jobLogQuery.executorAddress=e.executorAddress,this.jobLogQuery.id=e.id,this.jobLogQuery.triggerTime=Date.parse(e.triggerTime),!1===this.logShow&&(this.logShow=!0),this.loadLog()},loadLog:function(){var e=this;this.logLoading=!0,o["e"](this.jobLogQuery.executorAddress,this.jobLogQuery.triggerTime,this.jobLogQuery.id,this.jobLogQuery.fromLineNum).then((function(t){"\n"===t.content.logContent||(e.logContent=t.content.logContent),e.logLoading=!1}))},killRunningJob:function(e){var t=this;o["c"](e).then((function(e){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"停止作业",message:"停止成功",type:"success",duration:2e3})}))}}},c=u,d=(l("51a4"),l("2877")),g=Object(d["a"])(c,i,n,!1,null,"ebbcf8e6",null);t["default"]=g.exports},"8d41":function(e,t,l){}}]);