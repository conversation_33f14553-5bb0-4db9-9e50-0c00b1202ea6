(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-139a3e43"],{"991c":function(e,t,i){(function(e){e(i("56b3"),i("ffda"))})((function(e){"use strict";var t,i,r,n,a={QUERY_DIV:";",ALIAS_KEYWORD:"AS"},o=e.Pos,s=e.cmpPos;function l(e){return"[object Array]"==Object.prototype.toString.call(e)}function c(t){var i=t.doc.modeOption;return"sql"===i&&(i="text/x-sql"),e.resolveMode(i).keywords}function u(t){var i=t.doc.modeOption;return"sql"===i&&(i="text/x-sql"),e.resolveMode(i).identifierQuote||"`"}function d(e){return"string"==typeof e?e:e.text}function m(e,t){return l(t)&&(t={columns:t}),t.text||(t.text=e),t}function p(e){var t={};if(l(e))for(var i=e.length-1;i>=0;i--){var r=e[i];t[d(r).toUpperCase()]=m(d(r),r)}else if(e)for(var n in e)t[n.toUpperCase()]=m(n,e[n]);return t}function h(e){return t[e.toUpperCase()]}function f(e){var t={};for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function g(e,t){var i=e.length,r=d(t).substr(0,i);return e.toUpperCase()===r.toUpperCase()}function b(e,t,i,r){if(l(i))for(var n=0;n<i.length;n++)g(t,i[n])&&e.push(r(i[n]));else for(var a in i)if(i.hasOwnProperty(a)){var o=i[a];o=o&&!0!==o?o.displayText?{text:o.text,displayText:o.displayText}:o.text:a,g(t,o)&&e.push(r(o))}}function _(e){"."==e.charAt(0)&&(e=e.substr(1));for(var t=e.split(n+n),i=0;i<t.length;i++)t[i]=t[i].replace(new RegExp(n,"g"),"");return t.join(n)}function v(e){for(var t=d(e).split("."),i=0;i<t.length;i++)t[i]=n+t[i].replace(new RegExp(n,"g"),n+n)+n;var r=t.join(".");return"string"==typeof e?r:(e=f(e),e.text=r,e)}function y(e,r,a,s){var l=!1,c=[],u=r.start,d=!0;while(d)d="."==r.string.charAt(0),l=l||r.string.charAt(0)==n,u=r.start,c.unshift(_(r.string)),r=s.getTokenAt(o(e.line,r.start)),"."==r.string&&(d=!0,r=s.getTokenAt(o(e.line,r.start)));var m=c.join(".");b(a,m,t,(function(e){return l?v(e):e})),b(a,m,i,(function(e){return l?v(e):e})),m=c.pop();var p=c.join("."),g=!1,y=p;if(!h(p)){var x=p;p=w(p,s),p!==x&&(g=!0)}var k=h(p);return k&&k.columns&&(k=k.columns),k&&b(a,m,k,(function(e){var t=p;return 1==g&&(t=y),"string"==typeof e?e=t+"."+e:(e=f(e),e.text=t+"."+e.text),l?v(e):e})),u}function x(e,t){for(var i=e.split(/\s+/),r=0;r<i.length;r++)i[r]&&t(i[r].replace(/[`,;]/g,""))}function w(e,t){var i=t.doc,r=i.getValue(),n=e.toUpperCase(),l="",c="",u=[],d={start:o(0,0),end:o(t.lastLine(),t.getLineHandle(t.lastLine()).length)},m=r.indexOf(a.QUERY_DIV);while(-1!=m)u.push(i.posFromIndex(m)),m=r.indexOf(a.QUERY_DIV,m+1);u.unshift(o(0,0)),u.push(o(t.lastLine(),t.getLineHandle(t.lastLine()).text.length));for(var p=null,f=t.getCursor(),g=0;g<u.length;g++){if((null==p||s(f,p)>0)&&s(f,u[g])<=0){d={start:p,end:u[g]};break}p=u[g]}if(d.start){var b=i.getRange(d.start,d.end,!1);for(g=0;g<b.length;g++){var _=b[g];if(x(_,(function(e){var t=e.toUpperCase();t===n&&h(l)&&(c=l),t!==a.ALIAS_KEYWORD&&(l=e)})),c)break}}return c}e.registerHelper("hint","sql",(function(e,a){t=p(a&&a.tables);var s=a&&a.defaultTable,l=a&&a.disableKeywords;i=s&&h(s),r=c(e),n=u(e),s&&!i&&(i=w(s,e)),i=i||[],i.columns&&(i=i.columns);var d,m,f,g=e.getCursor(),_=[],v=e.getTokenAt(g);if(v.end>g.ch&&(v.end=g.ch,v.string=v.string.slice(0,g.ch-v.start)),v.string.match(/^[.`"'\w@][\w$#]*$/g)?(f=v.string,d=v.start,m=v.end):(d=m=g.ch,f=""),"."==f.charAt(0)||f.charAt(0)==n)d=y(g,v,_,e);else{var x=function(e,t){return"object"===typeof e?e.className=t:e={text:e,className:t},e};b(_,f,i,(function(e){return x(e,"CodeMirror-hint-table CodeMirror-hint-default-table")})),b(_,f,t,(function(e){return x(e,"CodeMirror-hint-table")})),l||b(_,f,r,(function(e){return x(e.toUpperCase(),"CodeMirror-hint-keyword")}))}return{list:_,from:o(g.line,d),to:o(g.line,m)}}))}))},"9b74":function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";var t="CodeMirror-hint",i="CodeMirror-hint-active";function r(e,t){if(this.cm=e,this.options=t,this.widget=null,this.debounce=0,this.tick=0,this.startPos=this.cm.getCursor("start"),this.startLen=this.cm.getLine(this.startPos.line).length-this.cm.getSelection().length,this.options.updateOnCursorActivity){var i=this;e.on("cursorActivity",this.activityFunc=function(){i.cursorActivity()})}}e.showHint=function(e,t,i){if(!t)return e.showHint(i);i&&i.async&&(t.async=!0);var r={hint:t};if(i)for(var n in i)r[n]=i[n];return e.showHint(r)},e.defineExtension("showHint",(function(t){t=o(this,this.getCursor("start"),t);var i=this.listSelections();if(!(i.length>1)){if(this.somethingSelected()){if(!t.hint.supportsSelection)return;for(var n=0;n<i.length;n++)if(i[n].head.line!=i[n].anchor.line)return}this.state.completionActive&&this.state.completionActive.close();var a=this.state.completionActive=new r(this,t);a.options.hint&&(e.signal(this,"startCompletion",this),a.update(!0))}})),e.defineExtension("closeHint",(function(){this.state.completionActive&&this.state.completionActive.close()}));var n=window.requestAnimationFrame||function(e){return setTimeout(e,1e3/60)},a=window.cancelAnimationFrame||clearTimeout;function o(e,t,i){var r=e.options.hintOptions,n={};for(var a in h)n[a]=h[a];if(r)for(var a in r)void 0!==r[a]&&(n[a]=r[a]);if(i)for(var a in i)void 0!==i[a]&&(n[a]=i[a]);return n.hint.resolve&&(n.hint=n.hint.resolve(e,t)),n}function s(e){return"string"==typeof e?e:e.text}function l(e,t){var i={Up:function(){t.moveFocus(-1)},Down:function(){t.moveFocus(1)},PageUp:function(){t.moveFocus(1-t.menuSize(),!0)},PageDown:function(){t.moveFocus(t.menuSize()-1,!0)},Home:function(){t.setFocus(0)},End:function(){t.setFocus(t.length-1)},Enter:t.pick,Tab:t.pick,Esc:t.close},r=/Mac/.test(navigator.platform);r&&(i["Ctrl-P"]=function(){t.moveFocus(-1)},i["Ctrl-N"]=function(){t.moveFocus(1)});var n=e.options.customKeys,a=n?{}:i;function o(e,r){var n;n="string"!=typeof r?function(e){return r(e,t)}:i.hasOwnProperty(r)?i[r]:r,a[e]=n}if(n)for(var s in n)n.hasOwnProperty(s)&&o(s,n[s]);var l=e.options.extraKeys;if(l)for(var s in l)l.hasOwnProperty(s)&&o(s,l[s]);return a}function c(e,t){while(t&&t!=e){if("LI"===t.nodeName.toUpperCase()&&t.parentNode==e)return t;t=t.parentNode}}function u(r,n){this.id="cm-complete-"+Math.floor(Math.random(1e6)),this.completion=r,this.data=n,this.picked=!1;var a=this,o=r.cm,u=o.getInputField().ownerDocument,d=u.defaultView||u.parentWindow,m=this.hints=u.createElement("ul");m.setAttribute("role","listbox"),m.setAttribute("aria-expanded","true"),m.id=this.id;var p=r.cm.options.theme;m.className="CodeMirror-hints "+p,this.selectedHint=n.selectedHint||0;for(var h=n.list,f=0;f<h.length;++f){var g=m.appendChild(u.createElement("li")),b=h[f],_=t+(f!=this.selectedHint?"":" "+i);null!=b.className&&(_=b.className+" "+_),g.className=_,f==this.selectedHint&&g.setAttribute("aria-selected","true"),g.id=this.id+"-"+f,g.setAttribute("role","option"),b.render?b.render(g,n,b):g.appendChild(u.createTextNode(b.displayText||s(b))),g.hintId=f}var v=r.options.container||u.body,y=o.cursorCoords(r.options.alignWithWord?n.from:null),x=y.left,w=y.bottom,k=!0,q=0,C=0;if(v!==u.body){var z=-1!==["absolute","relative","fixed"].indexOf(d.getComputedStyle(v).position),A=z?v:v.offsetParent,S=A.getBoundingClientRect(),M=u.body.getBoundingClientRect();q=S.left-M.left-A.scrollLeft,C=S.top-M.top-A.scrollTop}m.style.left=x-q+"px",m.style.top=w-C+"px";var H=d.innerWidth||Math.max(u.body.offsetWidth,u.documentElement.offsetWidth),T=d.innerHeight||Math.max(u.body.offsetHeight,u.documentElement.offsetHeight);v.appendChild(m),o.getInputField().setAttribute("aria-autocomplete","list"),o.getInputField().setAttribute("aria-owns",this.id),o.getInputField().setAttribute("aria-activedescendant",this.id+"-"+this.selectedHint);var O,F=r.options.moveOnOverlap?m.getBoundingClientRect():new DOMRect,E=!!r.options.paddingForScrollbar&&m.scrollHeight>m.clientHeight+1;setTimeout((function(){O=o.getScrollInfo()}));var N=F.bottom-T;if(N>0){var j=F.bottom-F.top,I=y.top-(y.bottom-F.top);if(I-j>0)m.style.top=(w=y.top-j-C)+"px",k=!1;else if(j>T){m.style.height=T-5+"px",m.style.top=(w=y.bottom-F.top-C)+"px";var L=o.getCursor();n.from.ch!=L.ch&&(y=o.cursorCoords(L),m.style.left=(x=y.left-q)+"px",F=m.getBoundingClientRect())}}var Q,P=F.right-H;if(E&&(P+=o.display.nativeBarWidth),P>0&&(F.right-F.left>H&&(m.style.width=H-5+"px",P-=F.right-F.left-H),m.style.left=(x=Math.max(y.left-P-q,0))+"px"),E)for(var R=m.firstChild;R;R=R.nextSibling)R.style.paddingRight=o.display.nativeBarWidth+"px";(o.addKeyMap(this.keyMap=l(r,{moveFocus:function(e,t){a.changeActive(a.selectedHint+e,t)},setFocus:function(e){a.changeActive(e)},menuSize:function(){return a.screenAmount()},length:h.length,close:function(){r.close()},pick:function(){a.pick()},data:n})),r.options.closeOnUnfocus)&&(o.on("blur",this.onBlur=function(){Q=setTimeout((function(){r.close()}),100)}),o.on("focus",this.onFocus=function(){clearTimeout(Q)}));o.on("scroll",this.onScroll=function(){var e=o.getScrollInfo(),t=o.getWrapperElement().getBoundingClientRect();O||(O=o.getScrollInfo());var i=w+O.top-e.top,n=i-(d.pageYOffset||(u.documentElement||u.body).scrollTop);if(k||(n+=m.offsetHeight),n<=t.top||n>=t.bottom)return r.close();m.style.top=i+"px",m.style.left=x+O.left-e.left+"px"}),e.on(m,"dblclick",(function(e){var t=c(m,e.target||e.srcElement);t&&null!=t.hintId&&(a.changeActive(t.hintId),a.pick())})),e.on(m,"click",(function(e){var t=c(m,e.target||e.srcElement);t&&null!=t.hintId&&(a.changeActive(t.hintId),r.options.completeOnSingleClick&&a.pick())})),e.on(m,"mousedown",(function(){setTimeout((function(){o.focus()}),20)}));var U=this.getSelectedHintRange();return 0===U.from&&0===U.to||this.scrollToActive(),e.signal(n,"select",h[this.selectedHint],m.childNodes[this.selectedHint]),!0}function d(e,t){if(!e.somethingSelected())return t;for(var i=[],r=0;r<t.length;r++)t[r].supportsSelection&&i.push(t[r]);return i}function m(e,t,i,r){if(e.async)e(t,r,i);else{var n=e(t,i);n&&n.then?n.then(r):r(n)}}function p(t,i){var r,n=t.getHelpers(i,"hint");if(n.length){var a=function(e,t,i){var r=d(e,n);function a(n){if(n==r.length)return t(null);m(r[n],e,i,(function(e){e&&e.list.length>0?t(e):a(n+1)}))}a(0)};return a.async=!0,a.supportsSelection=!0,a}return(r=t.getHelper(t.getCursor(),"hintWords"))?function(t){return e.hint.fromList(t,{words:r})}:e.hint.anyword?function(t,i){return e.hint.anyword(t,i)}:function(){}}r.prototype={close:function(){this.active()&&(this.cm.state.completionActive=null,this.tick=null,this.options.updateOnCursorActivity&&this.cm.off("cursorActivity",this.activityFunc),this.widget&&this.data&&e.signal(this.data,"close"),this.widget&&this.widget.close(),e.signal(this.cm,"endCompletion",this.cm))},active:function(){return this.cm.state.completionActive==this},pick:function(t,i){var r=t.list[i],n=this;this.cm.operation((function(){r.hint?r.hint(n.cm,t,r):n.cm.replaceRange(s(r),r.from||t.from,r.to||t.to,"complete"),e.signal(t,"pick",r),n.cm.scrollIntoView()})),this.options.closeOnPick&&this.close()},cursorActivity:function(){this.debounce&&(a(this.debounce),this.debounce=0);var e=this.startPos;this.data&&(e=this.data.from);var t=this.cm.getCursor(),i=this.cm.getLine(t.line);if(t.line!=this.startPos.line||i.length-t.ch!=this.startLen-this.startPos.ch||t.ch<e.ch||this.cm.somethingSelected()||!t.ch||this.options.closeCharacters.test(i.charAt(t.ch-1)))this.close();else{var r=this;this.debounce=n((function(){r.update()})),this.widget&&this.widget.disable()}},update:function(e){if(null!=this.tick){var t=this,i=++this.tick;m(this.options.hint,this.cm,this.options,(function(r){t.tick==i&&t.finishUpdate(r,e)}))}},finishUpdate:function(t,i){this.data&&e.signal(this.data,"update");var r=this.widget&&this.widget.picked||i&&this.options.completeSingle;this.widget&&this.widget.close(),this.data=t,t&&t.list.length&&(r&&1==t.list.length?this.pick(t,0):(this.widget=new u(this,t),e.signal(t,"shown")))}},u.prototype={close:function(){if(this.completion.widget==this){this.completion.widget=null,this.hints.parentNode&&this.hints.parentNode.removeChild(this.hints),this.completion.cm.removeKeyMap(this.keyMap);var e=this.completion.cm.getInputField();e.removeAttribute("aria-activedescendant"),e.removeAttribute("aria-owns");var t=this.completion.cm;this.completion.options.closeOnUnfocus&&(t.off("blur",this.onBlur),t.off("focus",this.onFocus)),t.off("scroll",this.onScroll)}},disable:function(){this.completion.cm.removeKeyMap(this.keyMap);var e=this;this.keyMap={Enter:function(){e.picked=!0}},this.completion.cm.addKeyMap(this.keyMap)},pick:function(){this.completion.pick(this.data,this.selectedHint)},changeActive:function(t,r){if(t>=this.data.list.length?t=r?this.data.list.length-1:0:t<0&&(t=r?0:this.data.list.length-1),this.selectedHint!=t){var n=this.hints.childNodes[this.selectedHint];n&&(n.className=n.className.replace(" "+i,""),n.removeAttribute("aria-selected")),n=this.hints.childNodes[this.selectedHint=t],n.className+=" "+i,n.setAttribute("aria-selected","true"),this.completion.cm.getInputField().setAttribute("aria-activedescendant",n.id),this.scrollToActive(),e.signal(this.data,"select",this.data.list[this.selectedHint],n)}},scrollToActive:function(){var e=this.getSelectedHintRange(),t=this.hints.childNodes[e.from],i=this.hints.childNodes[e.to],r=this.hints.firstChild;t.offsetTop<this.hints.scrollTop?this.hints.scrollTop=t.offsetTop-r.offsetTop:i.offsetTop+i.offsetHeight>this.hints.scrollTop+this.hints.clientHeight&&(this.hints.scrollTop=i.offsetTop+i.offsetHeight-this.hints.clientHeight+r.offsetTop)},screenAmount:function(){return Math.floor(this.hints.clientHeight/this.hints.firstChild.offsetHeight)||1},getSelectedHintRange:function(){var e=this.completion.options.scrollMargin||0;return{from:Math.max(0,this.selectedHint-e),to:Math.min(this.data.list.length-1,this.selectedHint+e)}}},e.registerHelper("hint","auto",{resolve:p}),e.registerHelper("hint","fromList",(function(t,i){var r,n=t.getCursor(),a=t.getTokenAt(n),o=e.Pos(n.line,a.start),s=n;a.start<n.ch&&/\w/.test(a.string.charAt(n.ch-a.start-1))?r=a.string.substr(0,n.ch-a.start):(r="",o=n);for(var l=[],c=0;c<i.words.length;c++){var u=i.words[c];u.slice(0,r.length)==r&&l.push(u)}if(l.length)return{list:l,from:o,to:s}})),e.commands.autocomplete=e.showHint;var h={hint:e.hint.auto,completeSingle:!0,alignWithWord:!0,closeCharacters:/[\s()\[\]{};:>,]/,closeOnPick:!0,closeOnUnfocus:!0,updateOnCursorActivity:!0,completeOnSingleClick:!0,container:null,customKeys:null,extraKeys:null,paddingForScrollbar:!0,moveOnOverlap:!0};e.defineOption("hintOptions",null)}))},f6b6:function(e,t,i){},ffda:function(e,t,i){(function(e){e(i("56b3"))})((function(e){"use strict";function t(e){var t;while(null!=(t=e.next()))if("`"==t&&!e.eat("`"))return"variable-2";return e.backUp(e.current().length-1),e.eatWhile(/\w/)?"variable-2":null}function i(e){var t;while(null!=(t=e.next()))if('"'==t&&!e.eat('"'))return"variable-2";return e.backUp(e.current().length-1),e.eatWhile(/\w/)?"variable-2":null}function r(e){return e.eat("@")&&(e.match("session."),e.match("local."),e.match("global.")),e.eat("'")?(e.match(/^.*'/),"variable-2"):e.eat('"')?(e.match(/^.*"/),"variable-2"):e.eat("`")?(e.match(/^.*`/),"variable-2"):e.match(/^[0-9a-zA-Z$\.\_]+/)?"variable-2":null}function n(e){return e.eat("N")?"atom":e.match(/^[a-zA-Z.#!?]/)?"variable-2":null}e.defineMode("sql",(function(t,i){var r=i.client||{},n=i.atoms||{false:!0,true:!0,null:!0},l=i.builtin||o(s),c=i.keywords||o(a),u=i.operatorChars||/^[*+\-%<>!=&|~^\/]/,d=i.support||{},m=i.hooks||{},p=i.dateSQL||{date:!0,time:!0,timestamp:!0},h=!1!==i.backslashStringEscapes,f=i.brackets||/^[\{}\(\)\[\]]/,g=i.punctuation||/^[;.,:]/;function b(e,t){var i=e.next();if(m[i]){var a=m[i](e,t);if(!1!==a)return a}if(d.hexNumber&&("0"==i&&e.match(/^[xX][0-9a-fA-F]+/)||("x"==i||"X"==i)&&e.match(/^'[0-9a-fA-F]+'/)))return"number";if(d.binaryNumber&&(("b"==i||"B"==i)&&e.match(/^'[01]+'/)||"0"==i&&e.match(/^b[01]+/)))return"number";if(i.charCodeAt(0)>47&&i.charCodeAt(0)<58)return e.match(/^[0-9]*(\.[0-9]+)?([eE][-+]?[0-9]+)?/),d.decimallessFloat&&e.match(/^\.(?!\.)/),"number";if("?"==i&&(e.eatSpace()||e.eol()||e.eat(";")))return"variable-3";if("'"==i||'"'==i&&d.doubleQuote)return t.tokenize=_(i),t.tokenize(e,t);if((d.nCharCast&&("n"==i||"N"==i)||d.charsetCast&&"_"==i&&e.match(/[a-z][a-z0-9]*/i))&&("'"==e.peek()||'"'==e.peek()))return"keyword";if(d.escapeConstant&&("e"==i||"E"==i)&&("'"==e.peek()||'"'==e.peek()&&d.doubleQuote))return t.tokenize=function(e,t){return(t.tokenize=_(e.next(),!0))(e,t)},"keyword";if(d.commentSlashSlash&&"/"==i&&e.eat("/"))return e.skipToEnd(),"comment";if(d.commentHash&&"#"==i||"-"==i&&e.eat("-")&&(!d.commentSpaceRequired||e.eat(" ")))return e.skipToEnd(),"comment";if("/"==i&&e.eat("*"))return t.tokenize=v(1),t.tokenize(e,t);if("."!=i){if(u.test(i))return e.eatWhile(u),"operator";if(f.test(i))return"bracket";if(g.test(i))return e.eatWhile(g),"punctuation";if("{"==i&&(e.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/)||e.match(/^( )*(d|D|t|T|ts|TS)( )*"[^"]*"( )*}/)))return"number";e.eatWhile(/^[_\w\d]/);var o=e.current().toLowerCase();return p.hasOwnProperty(o)&&(e.match(/^( )+'[^']*'/)||e.match(/^( )+"[^"]*"/))?"number":n.hasOwnProperty(o)?"atom":l.hasOwnProperty(o)?"type":c.hasOwnProperty(o)?"keyword":r.hasOwnProperty(o)?"builtin":null}return d.zerolessFloat&&e.match(/^(?:\d+(?:e[+-]?\d+)?)/i)?"number":e.match(/^\.+/)?null:d.ODBCdotTable&&e.match(/^[\w\d_$#]+/)?"variable-2":void 0}function _(e,t){return function(i,r){var n,a=!1;while(null!=(n=i.next())){if(n==e&&!a){r.tokenize=b;break}a=(h||t)&&!a&&"\\"==n}return"string"}}function v(e){return function(t,i){var r=t.match(/^.*?(\/\*|\*\/)/);return r?"/*"==r[1]?i.tokenize=v(e+1):i.tokenize=e>1?v(e-1):b:t.skipToEnd(),"comment"}}function y(e,t,i){t.context={prev:t.context,indent:e.indentation(),col:e.column(),type:i}}function x(e){e.indent=e.context.indent,e.context=e.context.prev}return{startState:function(){return{tokenize:b,context:null}},token:function(e,t){if(e.sol()&&t.context&&null==t.context.align&&(t.context.align=!1),t.tokenize==b&&e.eatSpace())return null;var i=t.tokenize(e,t);if("comment"==i)return i;t.context&&null==t.context.align&&(t.context.align=!0);var r=e.current();return"("==r?y(e,t,")"):"["==r?y(e,t,"]"):t.context&&t.context.type==r&&x(t),i},indent:function(i,r){var n=i.context;if(!n)return e.Pass;var a=r.charAt(0)==n.type;return n.align?n.col+(a?0:1):n.indent+(a?0:t.indentUnit)},blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:d.commentSlashSlash?"//":d.commentHash?"#":"--",closeBrackets:"()[]{}''\"\"``"}}));var a="alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit ";function o(e){for(var t={},i=e.split(" "),r=0;r<i.length;++r)t[i[r]]=!0;return t}var s="bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric";e.defineMIME("text/x-sql",{name:"sql",keywords:o(a+"begin"),builtin:o(s),atoms:o("false true null unknown"),dateSQL:o("date time timestamp"),support:o("ODBCdotTable doubleQuote binaryNumber hexNumber")}),e.defineMIME("text/x-mssql",{name:"sql",client:o("$partition binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id"),keywords:o(a+"begin trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx updlock with"),builtin:o("bigint numeric bit smallint decimal smallmoney int tinyint money float real char varchar text nchar nvarchar ntext binary varbinary image cursor timestamp hierarchyid uniqueidentifier sql_variant xml table "),atoms:o("is not null like and or in left right between inner outer join all any some cross unpivot pivot exists"),operatorChars:/^[*+\-%<>!=^\&|\/]/,brackets:/^[\{}\(\)]/,punctuation:/^[;.,:/]/,backslashStringEscapes:!1,dateSQL:o("date datetimeoffset datetime2 smalldatetime datetime time"),hooks:{"@":r}}),e.defineMIME("text/x-mysql",{name:"sql",client:o("charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee"),keywords:o(a+"accessible action add after algorithm all analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general get global grant grants group group_concat handler hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show signal slave slow smallint snapshot soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views warnings when while with work write xa xor year_month zerofill begin do then else loop repeat"),builtin:o("bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric"),atoms:o("false true null unknown"),operatorChars:/^[*+\-%<>!=&|^]/,dateSQL:o("date time timestamp"),support:o("ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired"),hooks:{"@":r,"`":t,"\\":n}}),e.defineMIME("text/x-mariadb",{name:"sql",client:o("charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee"),keywords:o(a+"accessible action add after algorithm all always analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general generated get global grant grants group group_concat handler hard hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password persistent phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show shutdown signal slave slow smallint snapshot soft soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views virtual warnings when while with work write xa xor year_month zerofill begin do then else loop repeat"),builtin:o("bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric"),atoms:o("false true null unknown"),operatorChars:/^[*+\-%<>!=&|^]/,dateSQL:o("date time timestamp"),support:o("ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired"),hooks:{"@":r,"`":t,"\\":n}}),e.defineMIME("text/x-sqlite",{name:"sql",client:o("auth backup bail binary changes check clone databases dbinfo dump echo eqp exit explain fullschema headers help import imposter indexes iotrace limit lint load log mode nullvalue once open output print prompt quit read restore save scanstats schema separator session shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width"),keywords:o(a+"abort action add after all analyze attach autoincrement before begin cascade case cast check collate column commit conflict constraint cross current_date current_time current_timestamp database default deferrable deferred detach each else end escape except exclusive exists explain fail for foreign full glob if ignore immediate index indexed initially inner instead intersect isnull key left limit match natural no notnull null of offset outer plan pragma primary query raise recursive references regexp reindex release rename replace restrict right rollback row savepoint temp temporary then to transaction trigger unique using vacuum view virtual when with without"),builtin:o("bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text clob bigint int int2 int8 integer float double char varchar date datetime year unsigned signed numeric real"),atoms:o("null current_date current_time current_timestamp"),operatorChars:/^[*+\-%<>!=&|/~]/,dateSQL:o("date time timestamp datetime"),support:o("decimallessFloat zerolessFloat"),identifierQuote:'"',hooks:{"@":r,":":r,"?":r,$:r,'"':i,"`":t}}),e.defineMIME("text/x-cassandra",{name:"sql",client:{},keywords:o("add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime"),builtin:o("ascii bigint blob boolean counter decimal double float frozen inet int list map static text timestamp timeuuid tuple uuid varchar varint"),atoms:o("false true infinity NaN"),operatorChars:/^[<>=]/,dateSQL:{},support:o("commentSlashSlash decimallessFloat"),hooks:{}}),e.defineMIME("text/x-plsql",{name:"sql",client:o("appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap"),keywords:o("abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work"),builtin:o("abs acos add_months ascii asin atan atan2 average bfile bfilename bigserial bit blob ceil character chartorowid chr clob concat convert cos cosh count dec decode deref dual dump dup_val_on_index empty error exp false float floor found glb greatest hextoraw initcap instr instrb int integer isopen last_day least length lengthb ln lower lpad ltrim lub make_ref max min mlslabel mod months_between natural naturaln nchar nclob new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null number numeric nvarchar2 nvl others power rawtohex real reftohex round rowcount rowidtochar rowtype rpad rtrim serial sign signtype sin sinh smallint soundex sqlcode sqlerrm sqrt stddev string substr substrb sum sysdate tan tanh to_char text to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid unlogged upper user userenv varchar varchar2 variance varying vsize xml"),operatorChars:/^[*\/+\-%<>!=~]/,dateSQL:o("date time timestamp"),support:o("doubleQuote nCharCast zerolessFloat binaryNumber hexNumber")}),e.defineMIME("text/x-hive",{name:"sql",keywords:o("select alter $elem$ $key$ $value$ add after all analyze and archive as asc before between binary both bucket buckets by cascade case cast change cluster clustered clusterstatus collection column columns comment compute concatenate continue create cross cursor data database databases dbproperties deferred delete delimited desc describe directory disable distinct distribute drop else enable end escaped exclusive exists explain export extended external fetch fields fileformat first format formatted from full function functions grant group having hold_ddltime idxproperties if import in index indexes inpath inputdriver inputformat insert intersect into is items join keys lateral left like limit lines load local location lock locks mapjoin materialized minus msck no_drop nocompress not of offline on option or order out outer outputdriver outputformat overwrite partition partitioned partitions percent plus preserve procedure purge range rcfile read readonly reads rebuild recordreader recordwriter recover reduce regexp rename repair replace restrict revoke right rlike row schema schemas semi sequencefile serde serdeproperties set shared show show_database sort sorted ssl statistics stored streamtable table tables tablesample tblproperties temporary terminated textfile then tmp to touch transform trigger unarchive undo union uniquejoin unlock update use using utc utc_tmestamp view when where while with admin authorization char compact compactions conf cube current current_date current_timestamp day decimal defined dependency directories elem_type exchange file following for grouping hour ignore inner interval jar less logical macro minute month more none noscan over owner partialscan preceding pretty principals protection reload rewrite role roles rollup rows second server sets skewed transactions truncate unbounded unset uri user values window year"),builtin:o("bool boolean long timestamp tinyint smallint bigint int float double date datetime unsigned string array struct map uniontype key_type utctimestamp value_type varchar"),atoms:o("false true null unknown"),operatorChars:/^[*+\-%<>!=]/,dateSQL:o("date timestamp"),support:o("ODBCdotTable doubleQuote binaryNumber hexNumber")}),e.defineMIME("text/x-pgsql",{name:"sql",client:o("source"),keywords:o(a+"a abort abs absent absolute access according action ada add admin after aggregate alias all allocate also alter always analyse analyze and any are array array_agg array_max_cardinality as asc asensitive assert assertion assignment asymmetric at atomic attach attribute attributes authorization avg backward base64 before begin begin_frame begin_partition bernoulli between bigint binary bit bit_length blob blocked bom boolean both breadth by c cache call called cardinality cascade cascaded case cast catalog catalog_name ceil ceiling chain char char_length character character_length character_set_catalog character_set_name character_set_schema characteristics characters check checkpoint class class_origin clob close cluster coalesce cobol collate collation collation_catalog collation_name collation_schema collect column column_name columns command_function command_function_code comment comments commit committed concurrently condition condition_number configuration conflict connect connection connection_name constant constraint constraint_catalog constraint_name constraint_schema constraints constructor contains content continue control conversion convert copy corr corresponding cost count covar_pop covar_samp create cross csv cube cume_dist current current_catalog current_date current_default_transform_group current_path current_role current_row current_schema current_time current_timestamp current_transform_group_for_type current_user cursor cursor_name cycle data database datalink datatype date datetime_interval_code datetime_interval_precision day db deallocate debug dec decimal declare default defaults deferrable deferred defined definer degree delete delimiter delimiters dense_rank depends depth deref derived desc describe descriptor detach detail deterministic diagnostics dictionary disable discard disconnect dispatch distinct dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue do document domain double drop dump dynamic dynamic_function dynamic_function_code each element else elseif elsif empty enable encoding encrypted end end_frame end_partition endexec enforced enum equals errcode error escape event every except exception exclude excluding exclusive exec execute exists exit exp explain expression extension external extract false family fetch file filter final first first_value flag float floor following for force foreach foreign fortran forward found frame_row free freeze from fs full function functions fusion g general generated get global go goto grant granted greatest group grouping groups handler having header hex hierarchy hint hold hour id identity if ignore ilike immediate immediately immutable implementation implicit import in include including increment indent index indexes indicator info inherit inherits initially inline inner inout input insensitive insert instance instantiable instead int integer integrity intersect intersection interval into invoker is isnull isolation join k key key_member key_type label lag language large last last_value lateral lead leading leakproof least left length level library like like_regex limit link listen ln load local localtime localtimestamp location locator lock locked log logged loop lower m map mapping match matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text method min minute minvalue mod mode modifies module month more move multiset mumps name names namespace national natural nchar nclob nesting new next nfc nfd nfkc nfkd nil no none normalize normalized not nothing notice notify notnull nowait nth_value ntile null nullable nullif nulls number numeric object occurrences_regex octet_length octets of off offset oids old on only open operator option options or order ordering ordinality others out outer output over overlaps overlay overriding owned owner p pad parallel parameter parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partial partition pascal passing passthrough password path percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding precision prepare prepared preserve primary print_strict_params prior privileges procedural procedure procedures program public publication query quote raise range rank read reads real reassign recheck recovery recursive ref references referencing refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex relative release rename repeatable replace replica requiring reset respect restart restore restrict result result_oid return returned_cardinality returned_length returned_octet_length returned_sqlstate returning returns reverse revoke right role rollback rollup routine routine_catalog routine_name routine_schema routines row row_count row_number rows rowtype rule savepoint scale schema schema_name schemas scope scope_catalog scope_name scope_schema scroll search second section security select selective self sensitive sequence sequences serializable server server_name session session_user set setof sets share show similar simple size skip slice smallint snapshot some source space specific specific_name specifictype sql sqlcode sqlerror sqlexception sqlstate sqlwarning sqrt stable stacked standalone start state statement static statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time system_user t table table_name tables tablesample tablespace temp template temporary text then ties time timestamp timezone_hour timezone_minute to token top_level_count trailing transaction transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex translation treat trigger trigger_catalog trigger_name trigger_schema trim trim_array true truncate trusted type types uescape unbounded uncommitted under unencrypted union unique unknown unlink unlisten unlogged unnamed unnest until untyped update upper uri usage use_column use_variable user user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema using vacuum valid validate validator value value_of values var_pop var_samp varbinary varchar variable_conflict variadic varying verbose version versioning view views volatile warning when whenever where while whitespace width_bucket window with within without work wrapper write xml xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate year yes zone"),builtin:o("bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml"),atoms:o("false true null unknown"),operatorChars:/^[*\/+\-%<>!=&|^\/#@?~]/,backslashStringEscapes:!1,dateSQL:o("date time timestamp"),support:o("ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast escapeConstant")}),e.defineMIME("text/x-gql",{name:"sql",keywords:o("ancestor and asc by contains desc descendant distinct from group has in is limit offset on order select superset where"),atoms:o("false true"),builtin:o("blob datetime first key __key__ string integer double boolean null"),operatorChars:/^[*+\-%<>!=]/}),e.defineMIME("text/x-gpsql",{name:"sql",client:o("source"),keywords:o("abort absolute access action active add admin after aggregate all also alter always analyse analyze and any array as asc assertion assignment asymmetric at authorization backward before begin between bigint binary bit boolean both by cache called cascade cascaded case cast chain char character characteristics check checkpoint class close cluster coalesce codegen collate column comment commit committed concurrency concurrently configuration connection constraint constraints contains content continue conversion copy cost cpu_rate_limit create createdb createexttable createrole createuser cross csv cube current current_catalog current_date current_role current_schema current_time current_timestamp current_user cursor cycle data database day deallocate dec decimal declare decode default defaults deferrable deferred definer delete delimiter delimiters deny desc dictionary disable discard distinct distributed do document domain double drop dxl each else enable encoding encrypted end enum errors escape every except exchange exclude excluding exclusive execute exists explain extension external extract false family fetch fields filespace fill filter first float following for force foreign format forward freeze from full function global grant granted greatest group group_id grouping handler hash having header hold host hour identity if ignore ilike immediate immutable implicit in including inclusive increment index indexes inherit inherits initially inline inner inout input insensitive insert instead int integer intersect interval into invoker is isnull isolation join key language large last leading least left level like limit list listen load local localtime localtimestamp location lock log login mapping master match maxvalue median merge minute minvalue missing mode modifies modify month move name names national natural nchar new newline next no nocreatedb nocreateexttable nocreaterole nocreateuser noinherit nologin none noovercommit nosuperuser not nothing notify notnull nowait null nullif nulls numeric object of off offset oids old on only operator option options or order ordered others out outer over overcommit overlaps overlay owned owner parser partial partition partitions passing password percent percentile_cont percentile_disc placing plans position preceding precision prepare prepared preserve primary prior privileges procedural procedure protocol queue quote randomly range read readable reads real reassign recheck recursive ref references reindex reject relative release rename repeatable replace replica reset resource restart restrict returning returns revoke right role rollback rollup rootpartition row rows rule savepoint scatter schema scroll search second security segment select sequence serializable session session_user set setof sets share show similar simple smallint some split sql stable standalone start statement statistics stdin stdout storage strict strip subpartition subpartitions substring superuser symmetric sysid system table tablespace temp template temporary text then threshold ties time timestamp to trailing transaction treat trigger trim true truncate trusted type unbounded uncommitted unencrypted union unique unknown unlisten until update user using vacuum valid validation validator value values varchar variadic varying verbose version view volatile web when where whitespace window with within without work writable write xml xmlattributes xmlconcat xmlelement xmlexists xmlforest xmlparse xmlpi xmlroot xmlserialize year yes zone"),builtin:o("bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml"),atoms:o("false true null unknown"),operatorChars:/^[*+\-%<>!=&|^\/#@?~]/,dateSQL:o("date time timestamp"),support:o("ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast")}),e.defineMIME("text/x-sparksql",{name:"sql",keywords:o("add after all alter analyze and anti archive array as asc at between bucket buckets by cache cascade case cast change clear cluster clustered codegen collection column columns comment commit compact compactions compute concatenate cost create cross cube current current_date current_timestamp database databases data dbproperties defined delete delimited deny desc describe dfs directories distinct distribute drop else end escaped except exchange exists explain export extended external false fields fileformat first following for format formatted from full function functions global grant group grouping having if ignore import in index indexes inner inpath inputformat insert intersect interval into is items join keys last lateral lazy left like limit lines list load local location lock locks logical macro map minus msck natural no not null nulls of on optimize option options or order out outer outputformat over overwrite partition partitioned partitions percent preceding principals purge range recordreader recordwriter recover reduce refresh regexp rename repair replace reset restrict revoke right rlike role roles rollback rollup row rows schema schemas select semi separated serde serdeproperties set sets show skewed sort sorted start statistics stored stratify struct table tables tablesample tblproperties temp temporary terminated then to touch transaction transactions transform true truncate unarchive unbounded uncache union unlock unset use using values view when where window with"),builtin:o("abs acos acosh add_months aggregate and any approx_count_distinct approx_percentile array array_contains array_distinct array_except array_intersect array_join array_max array_min array_position array_remove array_repeat array_sort array_union arrays_overlap arrays_zip ascii asin asinh assert_true atan atan2 atanh avg base64 between bigint bin binary bit_and bit_count bit_get bit_length bit_or bit_xor bool_and bool_or boolean bround btrim cardinality case cast cbrt ceil ceiling char char_length character_length chr coalesce collect_list collect_set concat concat_ws conv corr cos cosh cot count count_if count_min_sketch covar_pop covar_samp crc32 cume_dist current_catalog current_database current_date current_timestamp current_timezone current_user date date_add date_format date_from_unix_date date_part date_sub date_trunc datediff day dayofmonth dayofweek dayofyear decimal decode degrees delimited dense_rank div double e element_at elt encode every exists exp explode explode_outer expm1 extract factorial filter find_in_set first first_value flatten float floor forall format_number format_string from_csv from_json from_unixtime from_utc_timestamp get_json_object getbit greatest grouping grouping_id hash hex hour hypot if ifnull in initcap inline inline_outer input_file_block_length input_file_block_start input_file_name inputformat instr int isnan isnotnull isnull java_method json_array_length json_object_keys json_tuple kurtosis lag last last_day last_value lcase lead least left length levenshtein like ln locate log log10 log1p log2 lower lpad ltrim make_date make_dt_interval make_interval make_timestamp make_ym_interval map map_concat map_entries map_filter map_from_arrays map_from_entries map_keys map_values map_zip_with max max_by md5 mean min min_by minute mod monotonically_increasing_id month months_between named_struct nanvl negative next_day not now nth_value ntile nullif nvl nvl2 octet_length or outputformat overlay parse_url percent_rank percentile percentile_approx pi pmod posexplode posexplode_outer position positive pow power printf quarter radians raise_error rand randn random rank rcfile reflect regexp regexp_extract regexp_extract_all regexp_like regexp_replace repeat replace reverse right rint rlike round row_number rpad rtrim schema_of_csv schema_of_json second sentences sequence sequencefile serde session_window sha sha1 sha2 shiftleft shiftright shiftrightunsigned shuffle sign signum sin sinh size skewness slice smallint some sort_array soundex space spark_partition_id split sqrt stack std stddev stddev_pop stddev_samp str_to_map string struct substr substring substring_index sum tan tanh textfile timestamp timestamp_micros timestamp_millis timestamp_seconds tinyint to_csv to_date to_json to_timestamp to_unix_timestamp to_utc_timestamp transform transform_keys transform_values translate trim trunc try_add try_divide typeof ucase unbase64 unhex uniontype unix_date unix_micros unix_millis unix_seconds unix_timestamp upper uuid var_pop var_samp variance version weekday weekofyear when width_bucket window xpath xpath_boolean xpath_double xpath_float xpath_int xpath_long xpath_number xpath_short xpath_string xxhash64 year zip_with"),atoms:o("false true null"),operatorChars:/^[*\/+\-%<>!=~&|^]/,dateSQL:o("date time timestamp"),support:o("ODBCdotTable doubleQuote zerolessFloat")}),e.defineMIME("text/x-esper",{name:"sql",client:o("source"),keywords:o("alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit after all and as at asc avedev avg between by case cast coalesce count create current_timestamp day days delete define desc distinct else end escape events every exists false first from full group having hour hours in inner insert instanceof into irstream is istream join last lastweekday left limit like max match_recognize matches median measures metadatasql min minute minutes msec millisecond milliseconds not null offset on or order outer output partition pattern prev prior regexp retain-union retain-intersection right rstream sec second seconds select set some snapshot sql stddev sum then true unidirectional until update variable weekday when where window"),builtin:{},atoms:o("false true null"),operatorChars:/^[*+\-%<>!=&|^\/#@?~]/,dateSQL:o("time"),support:o("decimallessFloat zerolessFloat binaryNumber hexNumber")})}))}}]);