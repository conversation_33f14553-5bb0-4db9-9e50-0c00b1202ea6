(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4a25b5e8"],{"0dd0":function(n,e,t){},"3f23":function(n,e){n.exports='/* Jison generated parser */\nvar jsonlint = (function(){\nvar parser = {trace: function trace() { },\nyy: {},\nsymbols_: {"error":2,"JSONString":3,"STRING":4,"JSONNumber":5,"NUMBER":6,"JSONNullLiteral":7,"NULL":8,"JSONBooleanLiteral":9,"TRUE":10,"FALSE":11,"JSONText":12,"JSONValue":13,"EOF":14,"JSONObject":15,"JSONArray":16,"{":17,"}":18,"JSONMemberList":19,"JSONMember":20,":":21,",":22,"[":23,"]":24,"JSONElementList":25,"$accept":0,"$end":1},\nterminals_: {2:"error",4:"STRING",6:"NUMBER",8:"NULL",10:"TRUE",11:"FALSE",14:"EOF",17:"{",18:"}",21:":",22:",",23:"[",24:"]"},\nproductions_: [0,[3,1],[5,1],[7,1],[9,1],[9,1],[12,2],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[15,2],[15,3],[20,3],[19,1],[19,3],[16,2],[16,3],[25,1],[25,3]],\nperformAction: function anonymous(yytext,yyleng,yylineno,yy,yystate,$$,_$) {\n\nvar $0 = $$.length - 1;\nswitch (yystate) {\ncase 1: // replace escaped characters with actual character\n          this.$ = yytext.replace(/\\\\(\\\\|")/g, "$"+"1")\n                     .replace(/\\\\n/g,\'\\n\')\n                     .replace(/\\\\r/g,\'\\r\')\n                     .replace(/\\\\t/g,\'\\t\')\n                     .replace(/\\\\v/g,\'\\v\')\n                     .replace(/\\\\f/g,\'\\f\')\n                     .replace(/\\\\b/g,\'\\b\');\n        \nbreak;\ncase 2:this.$ = Number(yytext);\nbreak;\ncase 3:this.$ = null;\nbreak;\ncase 4:this.$ = true;\nbreak;\ncase 5:this.$ = false;\nbreak;\ncase 6:return this.$ = $$[$0-1];\nbreak;\ncase 13:this.$ = {};\nbreak;\ncase 14:this.$ = $$[$0-1];\nbreak;\ncase 15:this.$ = [$$[$0-2], $$[$0]];\nbreak;\ncase 16:this.$ = {}; this.$[$$[$0][0]] = $$[$0][1];\nbreak;\ncase 17:this.$ = $$[$0-2]; $$[$0-2][$$[$0][0]] = $$[$0][1];\nbreak;\ncase 18:this.$ = [];\nbreak;\ncase 19:this.$ = $$[$0-1];\nbreak;\ncase 20:this.$ = [$$[$0]];\nbreak;\ncase 21:this.$ = $$[$0-2]; $$[$0-2].push($$[$0]);\nbreak;\n}\n},\ntable: [{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],12:1,13:2,15:7,16:8,17:[1,14],23:[1,15]},{1:[3]},{14:[1,16]},{14:[2,7],18:[2,7],22:[2,7],24:[2,7]},{14:[2,8],18:[2,8],22:[2,8],24:[2,8]},{14:[2,9],18:[2,9],22:[2,9],24:[2,9]},{14:[2,10],18:[2,10],22:[2,10],24:[2,10]},{14:[2,11],18:[2,11],22:[2,11],24:[2,11]},{14:[2,12],18:[2,12],22:[2,12],24:[2,12]},{14:[2,3],18:[2,3],22:[2,3],24:[2,3]},{14:[2,4],18:[2,4],22:[2,4],24:[2,4]},{14:[2,5],18:[2,5],22:[2,5],24:[2,5]},{14:[2,1],18:[2,1],21:[2,1],22:[2,1],24:[2,1]},{14:[2,2],18:[2,2],22:[2,2],24:[2,2]},{3:20,4:[1,12],18:[1,17],19:18,20:19},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:23,15:7,16:8,17:[1,14],23:[1,15],24:[1,21],25:22},{1:[2,6]},{14:[2,13],18:[2,13],22:[2,13],24:[2,13]},{18:[1,24],22:[1,25]},{18:[2,16],22:[2,16]},{21:[1,26]},{14:[2,18],18:[2,18],22:[2,18],24:[2,18]},{22:[1,28],24:[1,27]},{22:[2,20],24:[2,20]},{14:[2,14],18:[2,14],22:[2,14],24:[2,14]},{3:20,4:[1,12],20:29},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:30,15:7,16:8,17:[1,14],23:[1,15]},{14:[2,19],18:[2,19],22:[2,19],24:[2,19]},{3:5,4:[1,12],5:6,6:[1,13],7:3,8:[1,9],9:4,10:[1,10],11:[1,11],13:31,15:7,16:8,17:[1,14],23:[1,15]},{18:[2,17],22:[2,17]},{18:[2,15],22:[2,15]},{22:[2,21],24:[2,21]}],\ndefaultActions: {16:[2,6]},\nparseError: function parseError(str, hash) {\n    throw new Error(str);\n},\nparse: function parse(input) {\n    var self = this,\n        stack = [0],\n        vstack = [null], // semantic value stack\n        lstack = [], // location stack\n        table = this.table,\n        yytext = \'\',\n        yylineno = 0,\n        yyleng = 0,\n        recovering = 0,\n        TERROR = 2,\n        EOF = 1;\n\n    //this.reductionCount = this.shiftCount = 0;\n\n    this.lexer.setInput(input);\n    this.lexer.yy = this.yy;\n    this.yy.lexer = this.lexer;\n    if (typeof this.lexer.yylloc == \'undefined\')\n        this.lexer.yylloc = {};\n    var yyloc = this.lexer.yylloc;\n    lstack.push(yyloc);\n\n    if (typeof this.yy.parseError === \'function\')\n        this.parseError = this.yy.parseError;\n\n    function popStack (n) {\n        stack.length = stack.length - 2*n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n    }\n\n    function lex() {\n        var token;\n        token = self.lexer.lex() || 1; // $end = 1\n        // if token isn\'t its numeric value, convert\n        if (typeof token !== \'number\') {\n            token = self.symbols_[token] || token;\n        }\n        return token;\n    }\n\n    var symbol, preErrorSymbol, state, action, a, r, yyval={},p,len,newState, expected;\n    while (true) {\n        // retreive state number from top of stack\n        state = stack[stack.length-1];\n\n        // use default actions if available\n        if (this.defaultActions[state]) {\n            action = this.defaultActions[state];\n        } else {\n            if (symbol == null)\n                symbol = lex();\n            // read action for current state and first input\n            action = table[state] && table[state][symbol];\n        }\n\n        // handle parse error\n        _handle_error:\n        if (typeof action === \'undefined\' || !action.length || !action[0]) {\n\n            if (!recovering) {\n                // Report error\n                expected = [];\n                for (p in table[state]) if (this.terminals_[p] && p > 2) {\n                    expected.push("\'"+this.terminals_[p]+"\'");\n                }\n                var errStr = \'\';\n                if (this.lexer.showPosition) {\n                    errStr = \'Parse error on line \'+(yylineno+1)+":\\n"+this.lexer.showPosition()+"\\nExpecting "+expected.join(\', \') + ", got \'" + this.terminals_[symbol]+ "\'";\n                } else {\n                    errStr = \'Parse error on line \'+(yylineno+1)+": Unexpected " +\n                                  (symbol == 1 /*EOF*/ ? "end of input" :\n                                              ("\'"+(this.terminals_[symbol] || symbol)+"\'"));\n                }\n                this.parseError(errStr,\n                    {text: this.lexer.match, token: this.terminals_[symbol] || symbol, line: this.lexer.yylineno, loc: yyloc, expected: expected});\n            }\n\n            // just recovered from another error\n            if (recovering == 3) {\n                if (symbol == EOF) {\n                    throw new Error(errStr || \'Parsing halted.\');\n                }\n\n                // discard current lookahead and grab another\n                yyleng = this.lexer.yyleng;\n                yytext = this.lexer.yytext;\n                yylineno = this.lexer.yylineno;\n                yyloc = this.lexer.yylloc;\n                symbol = lex();\n            }\n\n            // try to recover from error\n            while (1) {\n                // check for error recovery rule in this state\n                if ((TERROR.toString()) in table[state]) {\n                    break;\n                }\n                if (state == 0) {\n                    throw new Error(errStr || \'Parsing halted.\');\n                }\n                popStack(1);\n                state = stack[stack.length-1];\n            }\n\n            preErrorSymbol = symbol; // save the lookahead token\n            symbol = TERROR;         // insert generic error symbol as new lookahead\n            state = stack[stack.length-1];\n            action = table[state] && table[state][TERROR];\n            recovering = 3; // allow 3 real symbols to be shifted before reporting a new error\n        }\n\n        // this shouldn\'t happen, unless resolve defaults are off\n        if (action[0] instanceof Array && action.length > 1) {\n            throw new Error(\'Parse Error: multiple actions possible at state: \'+state+\', token: \'+symbol);\n        }\n\n        switch (action[0]) {\n\n            case 1: // shift\n                //this.shiftCount++;\n\n                stack.push(symbol);\n                vstack.push(this.lexer.yytext);\n                lstack.push(this.lexer.yylloc);\n                stack.push(action[1]); // push state\n                symbol = null;\n                if (!preErrorSymbol) { // normal execution/no error\n                    yyleng = this.lexer.yyleng;\n                    yytext = this.lexer.yytext;\n                    yylineno = this.lexer.yylineno;\n                    yyloc = this.lexer.yylloc;\n                    if (recovering > 0)\n                        recovering--;\n                } else { // error just occurred, resume old lookahead f/ before error\n                    symbol = preErrorSymbol;\n                    preErrorSymbol = null;\n                }\n                break;\n\n            case 2: // reduce\n                //this.reductionCount++;\n\n                len = this.productions_[action[1]][1];\n\n                // perform semantic action\n                yyval.$ = vstack[vstack.length-len]; // default to $$ = $1\n                // default location, uses first token for firsts, last for lasts\n                yyval._$ = {\n                    first_line: lstack[lstack.length-(len||1)].first_line,\n                    last_line: lstack[lstack.length-1].last_line,\n                    first_column: lstack[lstack.length-(len||1)].first_column,\n                    last_column: lstack[lstack.length-1].last_column\n                };\n                r = this.performAction.call(yyval, yytext, yyleng, yylineno, this.yy, action[1], vstack, lstack);\n\n                if (typeof r !== \'undefined\') {\n                    return r;\n                }\n\n                // pop off stack\n                if (len) {\n                    stack = stack.slice(0,-1*len*2);\n                    vstack = vstack.slice(0, -1*len);\n                    lstack = lstack.slice(0, -1*len);\n                }\n\n                stack.push(this.productions_[action[1]][0]);    // push nonterminal (reduce)\n                vstack.push(yyval.$);\n                lstack.push(yyval._$);\n                // goto new state = table[STATE][NONTERMINAL]\n                newState = table[stack[stack.length-2]][stack[stack.length-1]];\n                stack.push(newState);\n                break;\n\n            case 3: // accept\n                return true;\n        }\n\n    }\n\n    return true;\n}};\n/* Jison generated lexer */\nvar lexer = (function(){\nvar lexer = ({EOF:1,\nparseError:function parseError(str, hash) {\n        if (this.yy.parseError) {\n            this.yy.parseError(str, hash);\n        } else {\n            throw new Error(str);\n        }\n    },\nsetInput:function (input) {\n        this._input = input;\n        this._more = this._less = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \'\';\n        this.conditionStack = [\'INITIAL\'];\n        this.yylloc = {first_line:1,first_column:0,last_line:1,last_column:0};\n        return this;\n    },\ninput:function () {\n        var ch = this._input[0];\n        this.yytext+=ch;\n        this.yyleng++;\n        this.match+=ch;\n        this.matched+=ch;\n        var lines = ch.match(/\\n/);\n        if (lines) this.yylineno++;\n        this._input = this._input.slice(1);\n        return ch;\n    },\nunput:function (ch) {\n        this._input = ch + this._input;\n        return this;\n    },\nmore:function () {\n        this._more = true;\n        return this;\n    },\nless:function (n) {\n        this._input = this.match.slice(n) + this._input;\n    },\npastInput:function () {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \'...\':\'\') + past.substr(-20).replace(/\\n/g, "");\n    },\nupcomingInput:function () {\n        var next = this.match;\n        if (next.length < 20) {\n            next += this._input.substr(0, 20-next.length);\n        }\n        return (next.substr(0,20)+(next.length > 20 ? \'...\':\'\')).replace(/\\n/g, "");\n    },\nshowPosition:function () {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join("-");\n        return pre + this.upcomingInput() + "\\n" + c+"^";\n    },\nnext:function () {\n        if (this.done) {\n            return this.EOF;\n        }\n        if (!this._input) this.done = true;\n\n        var token,\n            match,\n            tempMatch,\n            index,\n            col,\n            lines;\n        if (!this._more) {\n            this.yytext = \'\';\n            this.match = \'\';\n        }\n        var rules = this._currentRules();\n        for (var i=0;i < rules.length; i++) {\n            tempMatch = this._input.match(this.rules[rules[i]]);\n            if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n                match = tempMatch;\n                index = i;\n                if (!this.options.flex) break;\n            }\n        }\n        if (match) {\n            lines = match[0].match(/\\n.*/g);\n            if (lines) this.yylineno += lines.length;\n            this.yylloc = {first_line: this.yylloc.last_line,\n                           last_line: this.yylineno+1,\n                           first_column: this.yylloc.last_column,\n                           last_column: lines ? lines[lines.length-1].length-1 : this.yylloc.last_column + match[0].length}\n            this.yytext += match[0];\n            this.match += match[0];\n            this.yyleng = this.yytext.length;\n            this._more = false;\n            this._input = this._input.slice(match[0].length);\n            this.matched += match[0];\n            token = this.performAction.call(this, this.yy, this, rules[index],this.conditionStack[this.conditionStack.length-1]);\n            if (this.done && this._input) this.done = false;\n            if (token) return token;\n            else return;\n        }\n        if (this._input === "") {\n            return this.EOF;\n        } else {\n            this.parseError(\'Lexical error on line \'+(this.yylineno+1)+\'. Unrecognized text.\\n\'+this.showPosition(), \n                    {text: "", token: null, line: this.yylineno});\n        }\n    },\nlex:function lex() {\n        var r = this.next();\n        if (typeof r !== \'undefined\') {\n            return r;\n        } else {\n            return this.lex();\n        }\n    },\nbegin:function begin(condition) {\n        this.conditionStack.push(condition);\n    },\npopState:function popState() {\n        return this.conditionStack.pop();\n    },\n_currentRules:function _currentRules() {\n        return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules;\n    },\ntopState:function () {\n        return this.conditionStack[this.conditionStack.length-2];\n    },\npushState:function begin(condition) {\n        this.begin(condition);\n    }});\nlexer.options = {};\nlexer.performAction = function anonymous(yy,yy_,$avoiding_name_collisions,YY_START) {\n\nvar YYSTATE=YY_START\nswitch($avoiding_name_collisions) {\ncase 0:/* skip whitespace */\nbreak;\ncase 1:return 6\nbreak;\ncase 2:yy_.yytext = yy_.yytext.substr(1,yy_.yyleng-2); return 4\nbreak;\ncase 3:return 17\nbreak;\ncase 4:return 18\nbreak;\ncase 5:return 23\nbreak;\ncase 6:return 24\nbreak;\ncase 7:return 22\nbreak;\ncase 8:return 21\nbreak;\ncase 9:return 10\nbreak;\ncase 10:return 11\nbreak;\ncase 11:return 8\nbreak;\ncase 12:return 14\nbreak;\ncase 13:return \'INVALID\'\nbreak;\n}\n};\nlexer.rules = [/^(?:\\s+)/,/^(?:(-?([0-9]|[1-9][0-9]+))(\\.[0-9]+)?([eE][-+]?[0-9]+)?\\b)/,/^(?:"(?:\\\\[\\\\"bfnrt/]|\\\\u[a-fA-F0-9]{4}|[^\\\\\\0-\\x09\\x0a-\\x1f"])*")/,/^(?:\\{)/,/^(?:\\})/,/^(?:\\[)/,/^(?:\\])/,/^(?:,)/,/^(?::)/,/^(?:true\\b)/,/^(?:false\\b)/,/^(?:null\\b)/,/^(?:$)/,/^(?:.)/];\nlexer.conditions = {"INITIAL":{"rules":[0,1,2,3,4,5,6,7,8,9,10,11,12,13],"inclusive":true}};\n\n\n;\nreturn lexer;})()\nparser.lexer = lexer;\nreturn parser;\n})();\nif (typeof require !== \'undefined\' && typeof exports !== \'undefined\') {\nexports.parser = jsonlint;\nexports.parse = function () { return jsonlint.parse.apply(jsonlint, arguments); }\nexports.main = function commonjsMain(args) {\n    if (!args[1])\n        throw new Error(\'Usage: \'+args[0]+\' FILE\');\n    if (typeof process !== \'undefined\') {\n        var source = require(\'fs\').readFileSync(require(\'path\').join(process.cwd(), args[1]), "utf8");\n    } else {\n        var cwd = require("file").path(require("file").cwd());\n        var source = cwd.join(args[1]).read({charset: "utf-8"});\n    }\n    return exports.parser.parse(source);\n}\nif (typeof module !== \'undefined\' && require.main === module) {\n  exports.main(typeof process !== \'undefined\' ? process.argv.slice(1) : require("system").args);\n}\n}'},8822:function(n,e,t){(function(n){n(t("56b3"))})((function(n){"use strict";var e="CodeMirror-lint-markers",t="CodeMirror-lint-line-";function r(e,t,r){var i=document.createElement("div");function a(e){if(!i.parentNode)return n.off(document,"mousemove",a);i.style.top=Math.max(0,e.clientY-i.offsetHeight-5)+"px",i.style.left=e.clientX+5+"px"}return i.className="CodeMirror-lint-tooltip cm-s-"+e.options.theme,i.appendChild(r.cloneNode(!0)),e.state.lint.options.selfContain?e.getWrapperElement().appendChild(i):document.body.appendChild(i),n.on(document,"mousemove",a),a(t),null!=i.style.opacity&&(i.style.opacity=1),i}function i(n){n.parentNode&&n.parentNode.removeChild(n)}function a(n){n.parentNode&&(null==n.style.opacity&&i(n),n.style.opacity=0,setTimeout((function(){i(n)}),600))}function o(e,t,i,o){var s=r(e,t,i);function l(){n.off(o,"mouseout",l),s&&(a(s),s=null)}var c=setInterval((function(){if(s)for(var n=o;;n=n.parentNode){if(n&&11==n.nodeType&&(n=n.host),n==document.body)return;if(!n){l();break}}if(!s)return clearInterval(c)}),400);n.on(o,"mouseout",l)}function s(n,e,t){for(var r in this.marked=[],e instanceof Function&&(e={getAnnotations:e}),e&&!0!==e||(e={}),this.options={},this.linterOptions=e.options||{},l)this.options[r]=l[r];for(var r in e)l.hasOwnProperty(r)?null!=e[r]&&(this.options[r]=e[r]):e.options||(this.linterOptions[r]=e[r]);this.timeout=null,this.hasGutter=t,this.onMouseOver=function(e){b(n,e)},this.waitingFor=0}var l={highlightLines:!1,tooltips:!0,delay:500,lintOnChange:!0,getAnnotations:null,async:!1,selfContain:null,formatAnnotation:null,onUpdateLinting:null};function c(n){var t=n.state.lint;t.hasGutter&&n.clearGutter(e),t.options.highlightLines&&u(n);for(var r=0;r<t.marked.length;++r)t.marked[r].clear();t.marked.length=0}function u(n){n.eachLine((function(e){var t=e.wrapClass&&/\bCodeMirror-lint-line-\w+\b/.exec(e.wrapClass);t&&n.removeLineClass(e,"wrap",t[0])}))}function f(e,t,r,i,a){var s=document.createElement("div"),l=s;return s.className="CodeMirror-lint-marker CodeMirror-lint-marker-"+r,i&&(l=s.appendChild(document.createElement("div")),l.className="CodeMirror-lint-marker CodeMirror-lint-marker-multiple"),0!=a&&n.on(l,"mouseover",(function(n){o(e,n,t,l)})),s}function p(n,e){return"error"==n?n:e}function h(n){for(var e=[],t=0;t<n.length;++t){var r=n[t],i=r.from.line;(e[i]||(e[i]=[])).push(r)}return e}function d(n){var e=n.severity;e||(e="error");var t=document.createElement("div");return t.className="CodeMirror-lint-message CodeMirror-lint-message-"+e,"undefined"!=typeof n.messageHTML?t.innerHTML=n.messageHTML:t.appendChild(document.createTextNode(n.message)),t}function m(e,t){var r=e.state.lint,i=++r.waitingFor;function a(){i=-1,e.off("change",a)}e.on("change",a),t(e.getValue(),(function(t,o){e.off("change",a),r.waitingFor==i&&(o&&t instanceof n&&(t=o),e.operation((function(){k(e,t)})))}),r.linterOptions,e)}function y(e){var t=e.state.lint;if(t){var r=t.options,i=r.getAnnotations||e.getHelper(n.Pos(0,0),"lint");if(i)if(r.async||i.async)m(e,i);else{var a=i(e.getValue(),t.linterOptions,e);if(!a)return;a.then?a.then((function(n){e.operation((function(){k(e,n)}))})):e.operation((function(){k(e,a)}))}}}function k(n,r){var i=n.state.lint;if(i){var a=i.options;c(n);for(var o=h(r),s=0;s<o.length;++s){var l=o[s];if(l){var u=[];l=l.filter((function(n){return!(u.indexOf(n.message)>-1)&&u.push(n.message)}));for(var m=null,y=i.hasGutter&&document.createDocumentFragment(),k=0;k<l.length;++k){var v=l[k],g=v.severity;g||(g="error"),m=p(m,g),a.formatAnnotation&&(v=a.formatAnnotation(v)),i.hasGutter&&y.appendChild(d(v)),v.to&&i.marked.push(n.markText(v.from,v.to,{className:"CodeMirror-lint-mark CodeMirror-lint-mark-"+g,__annotation:v}))}i.hasGutter&&n.setGutterMarker(s,e,f(n,y,m,o[s].length>1,a.tooltips)),a.highlightLines&&n.addLineClass(s,"wrap",t+m)}}a.onUpdateLinting&&a.onUpdateLinting(r,o,n)}}function v(n){var e=n.state.lint;e&&(clearTimeout(e.timeout),e.timeout=setTimeout((function(){y(n)}),e.options.delay))}function g(n,e,t){for(var r=t.target||t.srcElement,i=document.createDocumentFragment(),a=0;a<e.length;a++){var s=e[a];i.appendChild(d(s))}o(n,t,i,r)}function b(n,e){var t=e.target||e.srcElement;if(/\bCodeMirror-lint-mark-/.test(t.className)){for(var r=t.getBoundingClientRect(),i=(r.left+r.right)/2,a=(r.top+r.bottom)/2,o=n.findMarksAt(n.coordsChar({left:i,top:a},"client")),s=[],l=0;l<o.length;++l){var c=o[l].__annotation;c&&s.push(c)}s.length&&g(n,s,e)}}n.defineOption("lint",!1,(function(t,r,i){if(i&&i!=n.Init&&(c(t),!1!==t.state.lint.options.lintOnChange&&t.off("change",v),n.off(t.getWrapperElement(),"mouseover",t.state.lint.onMouseOver),clearTimeout(t.state.lint.timeout),delete t.state.lint),r){for(var a=t.getOption("gutters"),o=!1,l=0;l<a.length;++l)a[l]==e&&(o=!0);var u=t.state.lint=new s(t,r,o);u.options.lintOnChange&&t.on("change",v),0!=u.options.tooltips&&"gutter"!=u.options.tooltips&&n.on(t.getWrapperElement(),"mouseover",u.onMouseOver),y(t)}})),n.defineExtension("performLint",(function(){y(this)}))}))},a7be:function(n,e,t){},acdf:function(n,e,t){},ae67:function(n,e,t){t("f2b5")(t("3f23"))},d2de:function(n,e,t){(function(n){n(t("56b3"))})((function(n){"use strict";n.registerHelper("lint","json",(function(e){var t=[];if(!window.jsonlint)return window.console&&window.console.error("Error: window.jsonlint not defined, CodeMirror JSON linting cannot run."),t;var r=window.jsonlint.parser||window.jsonlint;r.parseError=function(e,r){var i=r.loc;t.push({from:n.Pos(i.first_line-1,i.first_column),to:n.Pos(i.last_line-1,i.last_column),message:e})};try{r.parse(e)}catch(i){}return t}))}))},f2b5:function(n,e){n.exports=function(n){function e(n){"undefined"!==typeof console&&(console.error||console.log)("[Script Loader]",n)}function t(){return"undefined"!==typeof attachEvent&&"undefined"===typeof addEventListener}try{"undefined"!==typeof execScript&&t()?execScript(n):"undefined"!==typeof eval?eval.call(null,n):e("EvalError: No eval function available")}catch(r){e(r)}}},f9d4:function(n,e,t){(function(n){n(t("56b3"))})((function(n){"use strict";n.defineMode("javascript",(function(e,t){var r,i,a=e.indentUnit,o=t.statementIndent,s=t.jsonld,l=t.json||s,c=!1!==t.trackScope,u=t.typescript,f=t.wordCharacters||/[\w$\xa1-\uffff]/,p=function(){function n(n){return{type:n,style:"keyword"}}var e=n("keyword a"),t=n("keyword b"),r=n("keyword c"),i=n("keyword d"),a=n("operator"),o={type:"atom",style:"atom"};return{if:n("if"),while:e,with:e,else:t,do:t,try:t,finally:t,return:i,break:i,continue:i,new:n("new"),delete:r,void:r,throw:r,debugger:n("debugger"),var:n("var"),const:n("var"),let:n("var"),function:n("function"),catch:n("catch"),for:n("for"),switch:n("switch"),case:n("case"),default:n("default"),in:a,typeof:a,instanceof:a,true:o,false:o,null:o,undefined:o,NaN:o,Infinity:o,this:n("this"),class:n("class"),super:n("atom"),yield:r,export:n("export"),import:n("import"),extends:r,await:r}}(),h=/[+\-*&%=<>!?|~^@]/,d=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function m(n){var e,t=!1,r=!1;while(null!=(e=n.next())){if(!t){if("/"==e&&!r)return;"["==e?r=!0:r&&"]"==e&&(r=!1)}t=!t&&"\\"==e}}function y(n,e,t){return r=n,i=t,e}function k(n,e){var t=n.next();if('"'==t||"'"==t)return e.tokenize=v(t),e.tokenize(n,e);if("."==t&&n.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return y("number","number");if("."==t&&n.match(".."))return y("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(t))return y(t);if("="==t&&n.eat(">"))return y("=>","operator");if("0"==t&&n.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return y("number","number");if(/\d/.test(t))return n.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),y("number","number");if("/"==t)return n.eat("*")?(e.tokenize=g,g(n,e)):n.eat("/")?(n.skipToEnd(),y("comment","comment")):ae(n,e,1)?(m(n),n.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),y("regexp","string-2")):(n.eat("="),y("operator","operator",n.current()));if("`"==t)return e.tokenize=b,b(n,e);if("#"==t&&"!"==n.peek())return n.skipToEnd(),y("meta","meta");if("#"==t&&n.eatWhile(f))return y("variable","property");if("<"==t&&n.match("!--")||"-"==t&&n.match("->")&&!/\S/.test(n.string.slice(0,n.start)))return n.skipToEnd(),y("comment","comment");if(h.test(t))return">"==t&&e.lexical&&">"==e.lexical.type||(n.eat("=")?"!"!=t&&"="!=t||n.eat("="):/[<>*+\-|&?]/.test(t)&&(n.eat(t),">"==t&&n.eat(t))),"?"==t&&n.eat(".")?y("."):y("operator","operator",n.current());if(f.test(t)){n.eatWhile(f);var r=n.current();if("."!=e.lastType){if(p.propertyIsEnumerable(r)){var i=p[r];return y(i.type,i.style,r)}if("async"==r&&n.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return y("async","keyword",r)}return y("variable","variable",r)}}function v(n){return function(e,t){var r,i=!1;if(s&&"@"==e.peek()&&e.match(d))return t.tokenize=k,y("jsonld-keyword","meta");while(null!=(r=e.next())){if(r==n&&!i)break;i=!i&&"\\"==r}return i||(t.tokenize=k),y("string","string")}}function g(n,e){var t,r=!1;while(t=n.next()){if("/"==t&&r){e.tokenize=k;break}r="*"==t}return y("comment","comment")}function b(n,e){var t,r=!1;while(null!=(t=n.next())){if(!r&&("`"==t||"$"==t&&n.eat("{"))){e.tokenize=k;break}r=!r&&"\\"==t}return y("quasi","string-2",n.current())}var x="([{}])";function w(n,e){e.fatArrowAt&&(e.fatArrowAt=null);var t=n.string.indexOf("=>",n.start);if(!(t<0)){if(u){var r=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(n.string.slice(n.start,t));r&&(t=r.index)}for(var i=0,a=!1,o=t-1;o>=0;--o){var s=n.string.charAt(o),l=x.indexOf(s);if(l>=0&&l<3){if(!i){++o;break}if(0==--i){"("==s&&(a=!0);break}}else if(l>=3&&l<6)++i;else if(f.test(s))a=!0;else if(/["'\/`]/.test(s))for(;;--o){if(0==o)return;var c=n.string.charAt(o-1);if(c==s&&"\\"!=n.string.charAt(o-2)){o--;break}}else if(a&&!i){++o;break}}a&&!i&&(e.fatArrowAt=o)}}var $={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function _(n,e,t,r,i,a){this.indented=n,this.column=e,this.type=t,this.prev=i,this.info=a,null!=r&&(this.align=r)}function E(n,e){if(!c)return!1;for(var t=n.localVars;t;t=t.next)if(t.name==e)return!0;for(var r=n.context;r;r=r.prev)for(t=r.vars;t;t=t.next)if(t.name==e)return!0}function S(n,e,t,r,i){var a=n.cc;M.state=n,M.stream=i,M.marked=null,M.cc=a,M.style=e,n.lexical.hasOwnProperty("align")||(n.lexical.align=!0);while(1){var o=a.length?a.pop():l?H:U;if(o(t,r)){while(a.length&&a[a.length-1].lex)a.pop()();return M.marked?M.marked:"variable"==t&&E(n,r)?"variable-2":e}}}var M={state:null,column:null,marked:null,cc:null};function j(){for(var n=arguments.length-1;n>=0;n--)M.cc.push(arguments[n])}function A(){return j.apply(null,arguments),!0}function O(n,e){for(var t=e;t;t=t.next)if(t.name==n)return!0;return!1}function C(n){var e=M.state;if(M.marked="def",c){if(e.context)if("var"==e.lexical.info&&e.context&&e.context.block){var r=T(n,e.context);if(null!=r)return void(e.context=r)}else if(!O(n,e.localVars))return void(e.localVars=new L(n,e.localVars));t.globalVars&&!O(n,e.globalVars)&&(e.globalVars=new L(n,e.globalVars))}}function T(n,e){if(e){if(e.block){var t=T(n,e.prev);return t?t==e.prev?e:new I(t,e.vars,!0):null}return O(n,e.vars)?e:new I(e.prev,new L(n,e.vars),!1)}return null}function N(n){return"public"==n||"private"==n||"protected"==n||"abstract"==n||"readonly"==n}function I(n,e,t){this.prev=n,this.vars=e,this.block=t}function L(n,e){this.name=n,this.next=e}var R=new L("this",new L("arguments",null));function V(){M.state.context=new I(M.state.context,M.state.localVars,!1),M.state.localVars=R}function F(){M.state.context=new I(M.state.context,M.state.localVars,!0),M.state.localVars=null}function z(){M.state.localVars=M.state.context.vars,M.state.context=M.state.context.prev}function P(n,e){var t=function(){var t=M.state,r=t.indented;if("stat"==t.lexical.type)r=t.lexical.indented;else for(var i=t.lexical;i&&")"==i.type&&i.align;i=i.prev)r=i.indented;t.lexical=new _(r,M.stream.column(),n,null,t.lexical,e)};return t.lex=!0,t}function J(){var n=M.state;n.lexical.prev&&(")"==n.lexical.type&&(n.indented=n.lexical.indented),n.lexical=n.lexical.prev)}function q(n){function e(t){return t==n?A():";"==n||"}"==t||")"==t||"]"==t?j():A(e)}return e}function U(n,e){return"var"==n?A(P("vardef",e),An,q(";"),J):"keyword a"==n?A(P("form"),B,U,J):"keyword b"==n?A(P("form"),U,J):"keyword d"==n?M.stream.match(/^\s*$/,!1)?A():A(P("stat"),D,q(";"),J):"debugger"==n?A(q(";")):"{"==n?A(P("}"),F,hn,J,z):";"==n?A():"if"==n?("else"==M.state.lexical.info&&M.state.cc[M.state.cc.length-1]==J&&M.state.cc.pop()(),A(P("form"),B,U,J,Ln)):"function"==n?A(zn):"for"==n?A(P("form"),F,Rn,U,z,J):"class"==n||u&&"interface"==e?(M.marked="keyword",A(P("form","class"==n?n:e),Gn,J)):"variable"==n?u&&"declare"==e?(M.marked="keyword",A(U)):u&&("module"==e||"enum"==e||"type"==e)&&M.stream.match(/^\s*\w/,!1)?(M.marked="keyword","enum"==e?A(te):"type"==e?A(Jn,q("operator"),vn,q(";")):A(P("form"),On,q("{"),P("}"),hn,J,J)):u&&"namespace"==e?(M.marked="keyword",A(P("form"),H,U,J)):u&&"abstract"==e?(M.marked="keyword",A(U)):A(P("stat"),on):"switch"==n?A(P("form"),B,q("{"),P("}","switch"),F,hn,J,J,z):"case"==n?A(H,q(":")):"default"==n?A(q(":")):"catch"==n?A(P("form"),V,G,U,J,z):"export"==n?A(P("stat"),Wn,J):"import"==n?A(P("stat"),Xn,J):"async"==n?A(U):"@"==e?A(H,U):j(P("stat"),H,q(";"),J)}function G(n){if("("==n)return A(qn,q(")"))}function H(n,e){return W(n,e,!1)}function Y(n,e){return W(n,e,!0)}function B(n){return"("!=n?j():A(P(")"),D,q(")"),J)}function W(n,e,t){if(M.state.fatArrowAt==M.stream.start){var r=t?en:nn;if("("==n)return A(V,P(")"),fn(qn,")"),J,q("=>"),r,z);if("variable"==n)return j(V,On,q("=>"),r,z)}var i=t?K:X;return $.hasOwnProperty(n)?A(i):"function"==n?A(zn,i):"class"==n||u&&"interface"==e?(M.marked="keyword",A(P("form"),Un,J)):"keyword c"==n||"async"==n?A(t?Y:H):"("==n?A(P(")"),D,q(")"),J,i):"operator"==n||"spread"==n?A(t?Y:H):"["==n?A(P("]"),ee,J,i):"{"==n?pn(ln,"}",null,i):"quasi"==n?j(Q,i):"new"==n?A(tn(t)):A()}function D(n){return n.match(/[;\}\)\],]/)?j():j(H)}function X(n,e){return","==n?A(D):K(n,e,!1)}function K(n,e,t){var r=0==t?X:K,i=0==t?H:Y;return"=>"==n?A(V,t?en:nn,z):"operator"==n?/\+\+|--/.test(e)||u&&"!"==e?A(r):u&&"<"==e&&M.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?A(P(">"),fn(vn,">"),J,r):"?"==e?A(H,q(":"),i):A(i):"quasi"==n?j(Q,r):";"!=n?"("==n?pn(Y,")","call",r):"."==n?A(sn,r):"["==n?A(P("]"),D,q("]"),J,r):u&&"as"==e?(M.marked="keyword",A(vn,r)):"regexp"==n?(M.state.lastType=M.marked="operator",M.stream.backUp(M.stream.pos-M.stream.start-1),A(i)):void 0:void 0}function Q(n,e){return"quasi"!=n?j():"${"!=e.slice(e.length-2)?A(Q):A(D,Z)}function Z(n){if("}"==n)return M.marked="string-2",M.state.tokenize=b,A(Q)}function nn(n){return w(M.stream,M.state),j("{"==n?U:H)}function en(n){return w(M.stream,M.state),j("{"==n?U:Y)}function tn(n){return function(e){return"."==e?A(n?an:rn):"variable"==e&&u?A(Sn,n?K:X):j(n?Y:H)}}function rn(n,e){if("target"==e)return M.marked="keyword",A(X)}function an(n,e){if("target"==e)return M.marked="keyword",A(K)}function on(n){return":"==n?A(J,U):j(X,q(";"),J)}function sn(n){if("variable"==n)return M.marked="property",A()}function ln(n,e){return"async"==n?(M.marked="property",A(ln)):"variable"==n||"keyword"==M.style?(M.marked="property","get"==e||"set"==e?A(cn):(u&&M.state.fatArrowAt==M.stream.start&&(t=M.stream.match(/^\s*:\s*/,!1))&&(M.state.fatArrowAt=M.stream.pos+t[0].length),A(un))):"number"==n||"string"==n?(M.marked=s?"property":M.style+" property",A(un)):"jsonld-keyword"==n?A(un):u&&N(e)?(M.marked="keyword",A(ln)):"["==n?A(H,dn,q("]"),un):"spread"==n?A(Y,un):"*"==e?(M.marked="keyword",A(ln)):":"==n?j(un):void 0;var t}function cn(n){return"variable"!=n?j(un):(M.marked="property",A(zn))}function un(n){return":"==n?A(Y):"("==n?j(zn):void 0}function fn(n,e,t){function r(i,a){if(t?t.indexOf(i)>-1:","==i){var o=M.state.lexical;return"call"==o.info&&(o.pos=(o.pos||0)+1),A((function(t,r){return t==e||r==e?j():j(n)}),r)}return i==e||a==e?A():t&&t.indexOf(";")>-1?j(n):A(q(e))}return function(t,i){return t==e||i==e?A():j(n,r)}}function pn(n,e,t){for(var r=3;r<arguments.length;r++)M.cc.push(arguments[r]);return A(P(e,t),fn(n,e),J)}function hn(n){return"}"==n?A():j(U,hn)}function dn(n,e){if(u){if(":"==n)return A(vn);if("?"==e)return A(dn)}}function mn(n,e){if(u&&(":"==n||"in"==e))return A(vn)}function yn(n){if(u&&":"==n)return M.stream.match(/^\s*\w+\s+is\b/,!1)?A(H,kn,vn):A(vn)}function kn(n,e){if("is"==e)return M.marked="keyword",A()}function vn(n,e){return"keyof"==e||"typeof"==e||"infer"==e||"readonly"==e?(M.marked="keyword",A("typeof"==e?Y:vn)):"variable"==n||"void"==e?(M.marked="type",A(En)):"|"==e||"&"==e?A(vn):"string"==n||"number"==n||"atom"==n?A(En):"["==n?A(P("]"),fn(vn,"]",","),J,En):"{"==n?A(P("}"),bn,J,En):"("==n?A(fn(_n,")"),gn,En):"<"==n?A(fn(vn,">"),vn):"quasi"==n?j(wn,En):void 0}function gn(n){if("=>"==n)return A(vn)}function bn(n){return n.match(/[\}\)\]]/)?A():","==n||";"==n?A(bn):j(xn,bn)}function xn(n,e){return"variable"==n||"keyword"==M.style?(M.marked="property",A(xn)):"?"==e||"number"==n||"string"==n?A(xn):":"==n?A(vn):"["==n?A(q("variable"),mn,q("]"),xn):"("==n?j(Pn,xn):n.match(/[;\}\)\],]/)?void 0:A()}function wn(n,e){return"quasi"!=n?j():"${"!=e.slice(e.length-2)?A(wn):A(vn,$n)}function $n(n){if("}"==n)return M.marked="string-2",M.state.tokenize=b,A(wn)}function _n(n,e){return"variable"==n&&M.stream.match(/^\s*[?:]/,!1)||"?"==e?A(_n):":"==n?A(vn):"spread"==n?A(_n):j(vn)}function En(n,e){return"<"==e?A(P(">"),fn(vn,">"),J,En):"|"==e||"."==n||"&"==e?A(vn):"["==n?A(vn,q("]"),En):"extends"==e||"implements"==e?(M.marked="keyword",A(vn)):"?"==e?A(vn,q(":"),vn):void 0}function Sn(n,e){if("<"==e)return A(P(">"),fn(vn,">"),J,En)}function Mn(){return j(vn,jn)}function jn(n,e){if("="==e)return A(vn)}function An(n,e){return"enum"==e?(M.marked="keyword",A(te)):j(On,dn,Nn,In)}function On(n,e){return u&&N(e)?(M.marked="keyword",A(On)):"variable"==n?(C(e),A()):"spread"==n?A(On):"["==n?pn(Tn,"]"):"{"==n?pn(Cn,"}"):void 0}function Cn(n,e){return"variable"!=n||M.stream.match(/^\s*:/,!1)?("variable"==n&&(M.marked="property"),"spread"==n?A(On):"}"==n?j():"["==n?A(H,q("]"),q(":"),Cn):A(q(":"),On,Nn)):(C(e),A(Nn))}function Tn(){return j(On,Nn)}function Nn(n,e){if("="==e)return A(Y)}function In(n){if(","==n)return A(An)}function Ln(n,e){if("keyword b"==n&&"else"==e)return A(P("form","else"),U,J)}function Rn(n,e){return"await"==e?A(Rn):"("==n?A(P(")"),Vn,J):void 0}function Vn(n){return"var"==n?A(An,Fn):"variable"==n?A(Fn):j(Fn)}function Fn(n,e){return")"==n?A():";"==n?A(Fn):"in"==e||"of"==e?(M.marked="keyword",A(H,Fn)):j(H,Fn)}function zn(n,e){return"*"==e?(M.marked="keyword",A(zn)):"variable"==n?(C(e),A(zn)):"("==n?A(V,P(")"),fn(qn,")"),J,yn,U,z):u&&"<"==e?A(P(">"),fn(Mn,">"),J,zn):void 0}function Pn(n,e){return"*"==e?(M.marked="keyword",A(Pn)):"variable"==n?(C(e),A(Pn)):"("==n?A(V,P(")"),fn(qn,")"),J,yn,z):u&&"<"==e?A(P(">"),fn(Mn,">"),J,Pn):void 0}function Jn(n,e){return"keyword"==n||"variable"==n?(M.marked="type",A(Jn)):"<"==e?A(P(">"),fn(Mn,">"),J):void 0}function qn(n,e){return"@"==e&&A(H,qn),"spread"==n?A(qn):u&&N(e)?(M.marked="keyword",A(qn)):u&&"this"==n?A(dn,Nn):j(On,dn,Nn)}function Un(n,e){return"variable"==n?Gn(n,e):Hn(n,e)}function Gn(n,e){if("variable"==n)return C(e),A(Hn)}function Hn(n,e){return"<"==e?A(P(">"),fn(Mn,">"),J,Hn):"extends"==e||"implements"==e||u&&","==n?("implements"==e&&(M.marked="keyword"),A(u?vn:H,Hn)):"{"==n?A(P("}"),Yn,J):void 0}function Yn(n,e){return"async"==n||"variable"==n&&("static"==e||"get"==e||"set"==e||u&&N(e))&&M.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(M.marked="keyword",A(Yn)):"variable"==n||"keyword"==M.style?(M.marked="property",A(Bn,Yn)):"number"==n||"string"==n?A(Bn,Yn):"["==n?A(H,dn,q("]"),Bn,Yn):"*"==e?(M.marked="keyword",A(Yn)):u&&"("==n?j(Pn,Yn):";"==n||","==n?A(Yn):"}"==n?A():"@"==e?A(H,Yn):void 0}function Bn(n,e){if("!"==e)return A(Bn);if("?"==e)return A(Bn);if(":"==n)return A(vn,Nn);if("="==e)return A(Y);var t=M.state.lexical.prev,r=t&&"interface"==t.info;return j(r?Pn:zn)}function Wn(n,e){return"*"==e?(M.marked="keyword",A(ne,q(";"))):"default"==e?(M.marked="keyword",A(H,q(";"))):"{"==n?A(fn(Dn,"}"),ne,q(";")):j(U)}function Dn(n,e){return"as"==e?(M.marked="keyword",A(q("variable"))):"variable"==n?j(Y,Dn):void 0}function Xn(n){return"string"==n?A():"("==n?j(H):"."==n?j(X):j(Kn,Qn,ne)}function Kn(n,e){return"{"==n?pn(Kn,"}"):("variable"==n&&C(e),"*"==e&&(M.marked="keyword"),A(Zn))}function Qn(n){if(","==n)return A(Kn,Qn)}function Zn(n,e){if("as"==e)return M.marked="keyword",A(Kn)}function ne(n,e){if("from"==e)return M.marked="keyword",A(H)}function ee(n){return"]"==n?A():j(fn(Y,"]"))}function te(){return j(P("form"),On,q("{"),P("}"),fn(re,"}"),J,J)}function re(){return j(On,Nn)}function ie(n,e){return"operator"==n.lastType||","==n.lastType||h.test(e.charAt(0))||/[,.]/.test(e.charAt(0))}function ae(n,e,t){return e.tokenize==k&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(e.lastType)||"quasi"==e.lastType&&/\{\s*$/.test(n.string.slice(0,n.pos-(t||0)))}return V.lex=F.lex=!0,z.lex=!0,J.lex=!0,{startState:function(n){var e={tokenize:k,lastType:"sof",cc:[],lexical:new _((n||0)-a,0,"block",!1),localVars:t.localVars,context:t.localVars&&new I(null,null,!1),indented:n||0};return t.globalVars&&"object"==typeof t.globalVars&&(e.globalVars=t.globalVars),e},token:function(n,e){if(n.sol()&&(e.lexical.hasOwnProperty("align")||(e.lexical.align=!1),e.indented=n.indentation(),w(n,e)),e.tokenize!=g&&n.eatSpace())return null;var t=e.tokenize(n,e);return"comment"==r?t:(e.lastType="operator"!=r||"++"!=i&&"--"!=i?r:"incdec",S(e,t,r,i,n))},indent:function(e,r){if(e.tokenize==g||e.tokenize==b)return n.Pass;if(e.tokenize!=k)return 0;var i,s=r&&r.charAt(0),l=e.lexical;if(!/^\s*else\b/.test(r))for(var c=e.cc.length-1;c>=0;--c){var u=e.cc[c];if(u==J)l=l.prev;else if(u!=Ln&&u!=z)break}while(("stat"==l.type||"form"==l.type)&&("}"==s||(i=e.cc[e.cc.length-1])&&(i==X||i==K)&&!/^[,\.=+\-*:?[\(]/.test(r)))l=l.prev;o&&")"==l.type&&"stat"==l.prev.type&&(l=l.prev);var f=l.type,p=s==f;return"vardef"==f?l.indented+("operator"==e.lastType||","==e.lastType?l.info.length+1:0):"form"==f&&"{"==s?l.indented:"form"==f?l.indented+a:"stat"==f?l.indented+(ie(e,r)?o||a:0):"switch"!=l.info||p||0==t.doubleIndentSwitch?l.align?l.column+(p?0:1):l.indented+(p?0:a):l.indented+(/^(?:case|default)\b/.test(r)?a:2*a)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:l?null:"/*",blockCommentEnd:l?null:"*/",blockCommentContinue:l?null:" * ",lineComment:l?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:l?"json":"javascript",jsonldMode:s,jsonMode:l,expressionAllowed:ae,skipExpression:function(e){S(e,"atom","atom","true",new n.StringStream("",2,null))}}})),n.registerHelper("wordChars","javascript",/[\w$]/),n.defineMIME("text/javascript","javascript"),n.defineMIME("text/ecmascript","javascript"),n.defineMIME("application/javascript","javascript"),n.defineMIME("application/x-javascript","javascript"),n.defineMIME("application/ecmascript","javascript"),n.defineMIME("application/json",{name:"javascript",json:!0}),n.defineMIME("application/x-json",{name:"javascript",json:!0}),n.defineMIME("application/manifest+json",{name:"javascript",json:!0}),n.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),n.defineMIME("text/typescript",{name:"javascript",typescript:!0}),n.defineMIME("application/typescript",{name:"javascript",typescript:!0})}))}}]);