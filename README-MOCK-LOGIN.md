# 数据中台完整Mock服务说明

## 概述

为了方便开发和测试，我们为这个数据中台前端项目添加了**完整的Mock服务套件**，提供：

- **🚀 自动跳过登录**: 直接以管理员身份进入系统
- **🗄️ 完整数据模拟**: 用户、报表、图表等所有数据
- **🌐 API接口拦截**: 模拟所有后端接口响应
- **📡 实时数据推送**: WebSocket实时数据模拟
- **💾 本地数据存储**: 用户偏好和缓存数据持久化
- **🎛️ 服务管理面板**: 实时监控和调试工具

## Mock服务架构

### 1. 核心服务文件
- **`auto-skip-login.js`** - 自动跳过登录服务
- **`mock-data-service.js`** - 数据生成服务
- **`mock-api-service.js`** - API拦截服务
- **`mock-websocket-service.js`** - WebSocket模拟服务
- **`mock-storage-service.js`** - 本地存储服务
- **`mock-service-manager.js`** - 服务管理器

### 2. 服务功能

#### 🚀 自动登录服务
- 页面加载时立即设置登录状态
- 拦截所有登录相关的API和路由
- 自动重定向登录页面到主页

#### 🗄️ 数据生成服务
- 生成用户、仪表板、表格、图表等完整数据
- 支持分页、搜索、筛选等功能
- 提供随机数据生成器

#### 🌐 API拦截服务
- 拦截所有API请求并返回模拟数据
- 支持GET、POST、PUT、DELETE等方法
- 模拟网络延迟和真实响应格式

#### 📡 WebSocket服务
- 模拟实时数据推送
- 支持多种数据类型（监控、通知、实时指标）
- 自动生成实时数据流

#### 💾 存储服务
- 用户偏好设置持久化
- API响应缓存
- 最近搜索和查看记录
- 支持数据导入导出

#### 🎛️ 服务管理器
- 实时监控所有服务状态
- 调试面板和日志记录
- 统计信息和性能监控
- 数据导出和测试工具

## 使用方法

### 1. 启动项目
```bash
# 使用Python启动本地服务器
python -m http.server 8080

# 或者直接打开index.html文件
```

### 2. 自动功能
- **🚀 自动登录**: 页面加载后自动以管理员身份登录
- **🗄️ 数据就绪**: 所有模拟数据自动生成并可用
- **🌐 API可用**: 所有接口请求自动被拦截并返回模拟数据
- **📡 实时推送**: WebSocket连接自动建立并推送实时数据

### 3. 调试面板
- **快捷键**: 按 `Ctrl+Shift+M` 打开/关闭调试面板
- **服务状态**: 查看所有服务的运行状态
- **统计信息**: API调用次数、WebSocket消息数等
- **实时日志**: 查看所有服务的操作日志
- **数据导出**: 一键导出所有模拟数据

### 4. 控制台命令
```javascript
// 测试所有服务
mockServiceManager.testAllServices();

// 查看服务状态
mockServiceManager.getStats();

// 保存用户偏好
mockStorage.setUserPref('theme', 'dark');

// 获取用户偏好
mockStorage.getUserPref('theme');

// 导出所有数据
mockServiceManager.exportAllData();
```

## 自动跳过登录功能

### 自动设置的登录信息
1. **Token信息**
   - `token`: `mock-token-admin-123456`
   - `access_token`: `mock-token-admin-123456`
   - `authToken`: `mock-token-admin-123456`

2. **用户信息**
   - `username`: `admin`
   - `name`: `管理员`
   - `roles`: `['admin']`
   - `avatar`: `/user.png`

3. **登录状态**
   - `isLoggedIn`: `true`
   - `loginStatus`: `true`

### 拦截功能
1. **API请求拦截**
   - 自动拦截所有包含 `/login` 或 `/auth` 的请求
   - 返回模拟的成功登录响应

2. **路由拦截**
   - 检测登录页面URL并自动重定向到主页
   - 支持hash路由和Vue Router

3. **存储设置**
   - 同时设置localStorage和sessionStorage
   - 覆盖多种可能的token存储方式

## 注意事项

1. **仅用于开发测试**: 这个自动跳过登录功能仅用于前端开发和测试，不应在生产环境中使用

2. **完全跳过验证**: 系统会完全跳过所有登录验证，直接以管理员身份进入

3. **立即生效**: 脚本在页面加载时立即执行，无延迟设置登录状态

4. **全面拦截**: 拦截所有可能的登录检查，包括API请求、路由跳转等

5. **持久化存储**: 登录状态会保存在localStorage中，刷新页面后仍然有效

## 扩展说明

如果需要添加更多用户或修改登录逻辑，可以编辑 `mock-login.js` 文件中的 `users` 对象：

```javascript
this.users = {
    admin: {
        username: 'admin',
        password: '123456',
        token: 'mock-token-admin-123456',
        roles: ['admin'],
        name: '管理员',
        avatar: '/user.png'
    },
    // 可以添加更多用户
    user1: {
        username: 'user1',
        password: 'password1',
        token: 'mock-token-user1',
        roles: ['user'],
        name: '普通用户',
        avatar: '/user.png'
    }
};
```

## 模拟数据详情

### 📊 仪表板数据
- **概览统计**: 用户数1250、活跃用户980、总收入258万、月增长12.5%
- **活动记录**: 最近用户操作和系统活动（登录、查看、导出等）
- **通知消息**: 系统维护、数据更新、新功能上线等通知

### 👥 用户数据
- **管理员**: admin（系统管理员）
- **普通用户**: 张三（业务部）、李四（数据部）
- **完整信息**: 姓名、邮箱、电话、部门、角色、状态等

### 📈 图表数据
- **折线图**: 12个月的用户访问量和数据处理量趋势
- **柱状图**: 5种数据源分布（数据库45、API32、文件28等）
- **饼图**: 系统状态分布（正常65%、警告20%、错误10%、离线5%）
- **实时数据**: 30天的动态监控指标

### 📋 表格数据
- **100条记录**: 数据项目1-100，包含名称、类型、状态、大小等
- **多种类型**: 数据库、文件、API、流数据
- **多种状态**: 运行中、已停止、维护中、错误
- **完整信息**: 更新时间、负责人、描述、标签等

## API接口列表

### 用户管理
- `GET /api/user/info` - 获取当前用户信息
- `GET /api/user/list` - 获取用户列表（支持分页、搜索）
- `POST /api/user/create` - 创建新用户
- `PUT /api/user/update` - 更新用户信息
- `DELETE /api/user/delete` - 删除用户

### 仪表板
- `GET /api/dashboard/overview` - 获取概览数据
- `GET /api/dashboard/activities` - 获取活动数据
- `GET /api/dashboard/notifications` - 获取通知数据

### 数据管理
- `GET /api/data/list` - 获取数据列表（支持筛选）
- `GET /api/data/detail` - 获取数据详情
- `POST /api/data/create` - 创建数据
- `PUT /api/data/update` - 更新数据
- `DELETE /api/data/delete` - 删除数据

### 图表数据
- `GET /api/chart/line` - 获取折线图数据
- `GET /api/chart/bar` - 获取柱状图数据
- `GET /api/chart/pie` - 获取饼图数据
- `GET /api/chart/realtime` - 获取实时数据

### 系统功能
- `GET /api/menu/list` - 获取菜单列表
- `GET /api/system/config` - 获取系统配置
- `POST /api/system/config` - 更新系统配置
- `GET /api/search` - 通用搜索
- `POST /api/upload` - 文件上传

### 报表功能
- `GET /api/report/daily` - 获取日报数据
- `GET /api/report/weekly` - 获取周报数据
- `GET /api/report/monthly` - 获取月报数据
- `POST /api/report/export` - 导出报表

## WebSocket实时数据

### 连接类型
- `ws://localhost/realtime` - 实时监控数据（CPU、内存、网络等）
- `ws://localhost/notification` - 系统通知推送
- `ws://localhost/monitor` - 服务监控数据

### 数据格式示例
```json
{
  "type": "realtime_data",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "cpu": 45.2,
    "memory": 68.5,
    "activeUsers": 1250,
    "requests": 85
  }
}
```

## 技术特性

### 🔄 自动拦截
- **Fetch API**: 自动拦截所有fetch请求
- **XMLHttpRequest**: 拦截传统AJAX请求
- **WebSocket**: 模拟WebSocket连接和消息推送

### ⚡ 性能优化
- **响应缓存**: API响应自动缓存5分钟
- **延迟模拟**: 模拟真实网络延迟（100-500ms）
- **数据分页**: 大数据集自动分页处理

### 🛠️ 开发工具
- **调试面板**: 按Ctrl+Shift+M打开，实时监控所有服务
- **日志记录**: 详细的操作日志和错误追踪
- **数据导出**: 一键导出所有模拟数据为JSON文件
- **服务测试**: 自动测试所有Mock服务的可用性

### 💾 数据持久化
- **用户偏好**: 主题、语言、页面大小等设置
- **搜索历史**: 最近10次搜索记录
- **浏览历史**: 最近20个查看的页面和数据
- **收藏夹**: 常用报表和功能收藏

## 移除Mock服务

如果要移除所有Mock服务，只需要：
1. 从 `index.html` 中删除所有Mock服务的script标签
2. 删除对应的JavaScript文件
3. 清除浏览器的localStorage和sessionStorage（可选）

现在您拥有了一个功能完整的数据中台Mock环境！🎉
