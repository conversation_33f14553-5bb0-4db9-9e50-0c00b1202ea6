# Mock登录服务说明

## 概述

为了方便开发和测试，我们为这个数据中台前端项目添加了一个Mock登录服务，将登录的账号密码硬编码为：

- **账号**: `admin`
- **密码**: `123456`

## 实现方式

### 1. Mock登录服务文件
- 文件位置: `mock-login.js`
- 功能: 提供模拟的登录API，拦截原有的登录请求

### 2. 集成方式
- 在 `index.html` 中引入了 `mock-login.js`
- 通过XMLHttpRequest拦截技术，自动拦截登录API请求
- 当检测到 `/api/auth/login` 的POST请求时，使用mock数据响应

## 使用方法

1. **启动项目**
   - 直接打开 `index.html` 文件
   - 或者使用本地服务器运行项目

2. **登录**
   - 在登录页面输入账号: `admin`
   - 输入密码: `123456`
   - 点击登录按钮

3. **验证**
   - 登录成功后会获得mock的token和用户信息
   - 控制台会显示 "Mock登录服务已启用 - 账号: admin, 密码: 123456"

## Mock服务功能

### 支持的API
1. **登录接口** (`/api/auth/login`)
   - 验证用户名和密码
   - 返回token和用户信息

2. **获取用户信息接口**
   - 根据token返回用户详细信息

3. **登出接口**
   - 模拟登出操作

### 返回数据格式
```javascript
// 登录成功响应
{
    code: 200,
    message: '登录成功',
    content: {
        data: 'mock-token-admin-123456',
        roles: ['admin'],
        user: {
            username: 'admin',
            name: '管理员',
            avatar: '/user.png'
        }
    }
}
```

## 注意事项

1. **仅用于开发测试**: 这个mock服务仅用于前端开发和测试，不应在生产环境中使用

2. **硬编码凭据**: 账号密码是硬编码的，任何人都可以使用 `admin/123456` 登录

3. **网络延迟模拟**: mock服务模拟了500ms的网络延迟，使体验更接近真实环境

4. **自动拦截**: 服务会自动拦截登录相关的API请求，无需修改原有的前端代码

## 扩展说明

如果需要添加更多用户或修改登录逻辑，可以编辑 `mock-login.js` 文件中的 `users` 对象：

```javascript
this.users = {
    admin: {
        username: 'admin',
        password: '123456',
        token: 'mock-token-admin-123456',
        roles: ['admin'],
        name: '管理员',
        avatar: '/user.png'
    },
    // 可以添加更多用户
    user1: {
        username: 'user1',
        password: 'password1',
        token: 'mock-token-user1',
        roles: ['user'],
        name: '普通用户',
        avatar: '/user.png'
    }
};
```

## 移除Mock服务

如果要移除mock服务，只需要：
1. 从 `index.html` 中删除 `<script src="/mock-login.js"></script>` 这一行
2. 删除 `mock-login.js` 文件
