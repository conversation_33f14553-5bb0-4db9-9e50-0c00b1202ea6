# 自动跳过登录服务说明

## 概述

为了方便开发和测试，我们为这个数据中台前端项目添加了一个**自动跳过登录**功能，直接以管理员身份进入系统：

- **自动登录账号**: `admin`
- **无需输入密码**: 自动跳过登录页面
- **直接进入系统**: 页面加载后立即以管理员身份登录

## 实现方式

### 1. 自动跳过登录脚本
- 文件位置: `auto-skip-login.js`
- 功能: 页面加载时立即设置登录状态，跳过所有登录验证

### 2. 集成方式
- 在 `index.html` 中引入了 `auto-skip-login.js`
- 脚本在页面加载时立即执行，设置登录状态到localStorage和sessionStorage
- 自动拦截所有登录相关的API请求和路由跳转
- 如果检测到登录页面，立即重定向到主页

## 使用方法

1. **启动项目**
   - 直接打开 `index.html` 文件
   - 或者使用本地服务器运行项目

2. **自动登录**
   - **无需任何操作**: 页面加载后自动以管理员身份登录
   - **跳过登录页面**: 如果系统尝试跳转到登录页面，会自动重定向到主页
   - **即时生效**: 登录状态立即设置，无需等待

3. **验证**
   - 控制台会显示 "🚀 启动自动跳过登录模式" 和 "🎉 自动跳过登录完成，已直接进入系统"
   - 可以在浏览器开发者工具的Application/Storage中查看设置的登录信息

## 自动跳过登录功能

### 自动设置的登录信息
1. **Token信息**
   - `token`: `mock-token-admin-123456`
   - `access_token`: `mock-token-admin-123456`
   - `authToken`: `mock-token-admin-123456`

2. **用户信息**
   - `username`: `admin`
   - `name`: `管理员`
   - `roles`: `['admin']`
   - `avatar`: `/user.png`

3. **登录状态**
   - `isLoggedIn`: `true`
   - `loginStatus`: `true`

### 拦截功能
1. **API请求拦截**
   - 自动拦截所有包含 `/login` 或 `/auth` 的请求
   - 返回模拟的成功登录响应

2. **路由拦截**
   - 检测登录页面URL并自动重定向到主页
   - 支持hash路由和Vue Router

3. **存储设置**
   - 同时设置localStorage和sessionStorage
   - 覆盖多种可能的token存储方式

## 注意事项

1. **仅用于开发测试**: 这个自动跳过登录功能仅用于前端开发和测试，不应在生产环境中使用

2. **完全跳过验证**: 系统会完全跳过所有登录验证，直接以管理员身份进入

3. **立即生效**: 脚本在页面加载时立即执行，无延迟设置登录状态

4. **全面拦截**: 拦截所有可能的登录检查，包括API请求、路由跳转等

5. **持久化存储**: 登录状态会保存在localStorage中，刷新页面后仍然有效

## 扩展说明

如果需要添加更多用户或修改登录逻辑，可以编辑 `mock-login.js` 文件中的 `users` 对象：

```javascript
this.users = {
    admin: {
        username: 'admin',
        password: '123456',
        token: 'mock-token-admin-123456',
        roles: ['admin'],
        name: '管理员',
        avatar: '/user.png'
    },
    // 可以添加更多用户
    user1: {
        username: 'user1',
        password: 'password1',
        token: 'mock-token-user1',
        roles: ['user'],
        name: '普通用户',
        avatar: '/user.png'
    }
};
```

## 移除自动跳过登录

如果要移除自动跳过登录功能，只需要：
1. 从 `index.html` 中删除 `<script src="/auto-skip-login.js"></script>` 这一行
2. 删除 `auto-skip-login.js` 文件
3. 清除浏览器的localStorage和sessionStorage（可选）
