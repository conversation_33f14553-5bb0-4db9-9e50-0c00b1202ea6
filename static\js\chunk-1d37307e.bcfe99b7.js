(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1d37307e"],{"09f4":function(e,a,t){"use strict";t.d(a,"a",(function(){return s})),Math.easeInOutQuad=function(e,a,t,n){return e/=n/2,e<1?t/2*e*e+a:(e--,-t/2*(e*(e-2)-1)+a)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(e,a,t){var s=l(),o=e-s,r=20,c=0;a="undefined"===typeof a?500:a;var d=function e(){c+=r;var l=Math.easeInOutQuad(c,s,o,a);i(l),c<a?n(e):t&&"function"===typeof t&&t()};d()}},1694:function(e,a,t){"use strict";t.r(a);var n=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"app-container"},[t("div",{staticClass:"filter-container"},[t("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"数据源类型",filterable:""},on:{change:e.dataSourceTypeChange},model:{value:e.params.dbtype,callback:function(a){e.$set(e.params,"dbtype",a)},expression:"params.dbtype"}},e._l(e.dataSourceTypeList,(function(e,a){return t("el-option",{key:"db-type-"+a,attrs:{label:e.label,value:e.value}})})),1),t("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{disabled:!e.params.dbtype,placeholder:"数据源名称",filterable:""},on:{change:e.dataSourceChange},model:{value:e.params.datasourceId,callback:function(a){e.$set(e.params,"datasourceId",a)},expression:"params.datasourceId"}},e._l(e.dataSourceList,(function(e,a){return t("el-option",{key:"data-source-id-"+a,attrs:{label:e.label,value:e.value}})})),1),"postgresql"===e.params.dbtype?t("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{disabled:!e.params.datasourceId,placeholder:"schema",filterable:""},on:{change:e.tableSchemaChange},model:{value:e.params.tableSchema,callback:function(a){e.$set(e.params,"tableSchema",a)},expression:"params.tableSchema"}},e._l(e.tableSchemaList,(function(e,a){return t("el-option",{key:"data-source-id-"+a,attrs:{label:e.label,value:e.value}})})),1):e._e(),t("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{disabled:!e.params.datasourceId,filterable:"",placeholder:"表名称"},on:{change:e.tableNameChange},model:{value:e.params.tablename,callback:function(a){e.$set(e.params,"tablename",a)},expression:"params.tablename"}},e._l(e.tableList,(function(e,a){return t("el-option",{key:"table-name-"+a,attrs:{label:e,value:e}})})),1),t("el-select",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{disabled:!e.params.tablename,filterable:"",multiple:"","collapse-tags":"",placeholder:"字段名称"},on:{change:e.fieldNameChange},model:{value:e.params.fieldname,callback:function(a){e.$set(e.params,"fieldname",a)},expression:"params.fieldname"}},e._l(e.fieldList,(function(e,a){return t("el-option",{key:"field-item-"+a,attrs:{label:e,value:e}})})),1),t("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:e.getList}},[e._v(" 搜索 ")]),t("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-files"},on:{click:e.getExportExcel}},[e._v(" 导出 Excel ")])],1),t("div",{staticClass:"table-box"},[e.params.fieldname.length?t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.dataList,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[t("el-table-column",{attrs:{align:"left",label:"序号",width:"95"},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(e._s(a.$index+1))]}}],null,!1,811300409)}),e._l(e.params.fieldname,(function(a,n){return t("el-table-column",{key:"field-name-key-"+n,attrs:{label:a,"min-width":"90",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row[a]))]}}],null,!0)})}))],2):e._e()],1),e.params.fieldname.length?t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.pageData.total>e.pageData.pageSize,expression:"pageData.total > pageData.pageSize"}],attrs:{total:e.pageData.total,page:e.pageData.pageNo,limit:e.pageData.pageSize},on:{"update:page":function(a){return e.$set(e.pageData,"pageNo",a)},"update:limit":function(a){return e.$set(e.pageData,"pageSize",a)},pagination:e.getList}}):e._e()],1)},i=[],l=(t("a15b"),t("d81d"),t("d3b7"),t("3ca3"),t("ddb0"),t("2b3d"),t("bf19"),t("9861"),t("b775"));function s(e){return Object(l["a"])({url:"/api/excel/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/api/metadata/getTables",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/api/metadata/getDBSchema",method:"get",params:e})}function c(e){return Object(l["a"])({url:"/api/metadata/getColumns",method:"get",params:e})}function d(e){return Object(l["a"])({url:"/api/metadataManager/findDBByType",method:"get",params:e})}function u(e){var a={url:"/api/excel/exportExcel",method:"get",params:e,responseType:"blob",transformResponse:function(e){return e instanceof Blob&&e.size>0?e:void 0}};return Object(l["a"])(a)}var p=t("67248"),m=t("333d"),f={name:"JdbcDatasource",components:{Pagination:m["a"]},directives:{waves:p["a"]},data:function(){return{dataList:null,listLoading:!1,pageData:{pageNo:1,pageSize:10,total:0},params:{dbtype:"",tableSchema:"",datasourceId:"",tablename:"",fieldname:[]},dataSourceTypeList:[{value:"mysql",label:"mysql"},{value:"oracle",label:"oracle"},{value:"postgresql",label:"postgresql"},{value:"doris",label:"doris"}],dataSourceList:[],tableSchemaList:[],tableList:[],fieldList:[]}},methods:{getList:function(){var e,a=this;if(!(this.params.dbtype&&this.params.datasourceId&&this.params.tablename&&null!==(e=this.params.fieldname)&&void 0!==e&&e.length))return this.initPageData(),void this.$message.error("存在未选择条件");var t={pageNo:this.pageData.pageNo,pageSize:this.pageData.pageSize,dbtype:this.params.dbtype,datasourceId:this.params.datasourceId,tablename:this.params.tablename,fieldname:this.params.fieldname.join(",")};this.listLoading=!0,s(t).then((function(e){a.listLoading=!1,a.pageData.total=e.content.recordsTotal,a.dataList=e.content.data}))},getExportExcel:function(){var e,a=this;if(!(this.params.dbtype&&this.params.datasourceId&&this.params.tablename&&null!==(e=this.params.fieldname)&&void 0!==e&&e.length))return this.initPageData(),void this.$message.error("存在未选择条件");var t={datasourceId:this.params.datasourceId,tablename:this.params.tablename,fieldname:this.params.fieldname.join(",")};return this.listLoading=!0,u(t,{headers:{Accept:"application/octet-stream;charset=utf-8"}}).then((function(e){a.$notify({title:"导出Excel",message:"导出Excel成功",type:"success",duration:2e3});var t=window.URL.createObjectURL(e),n=document.createElement("a");n.style.display="none",n.href=t,n.setAttribute("download",e.filename||"未命名文件.xlsx"),"undefined"===typeof n.download&&n.setAttribute("target","_blank"),document.body.appendChild(n),n.click(),setTimeout((function(){document.body.removeChild(n),window.URL.revokeObjectURL(e)}),1e3)})).finally((function(){a.listLoading=!1}))},initPageData:function(){this.pageData.pageNo=1,this.pageData.pageSize=10,this.dataList=[]},dataSourceTypeChange:function(e){var a=this;return this.dataSourceList=[],d({type:e}).then((function(e){for(var t=e.content.data,n=0;n<t.length;n++)t[n].label=t[n].dbname,t[n].value=t[n].id;a.dataSourceList=t}))},dataSourceChange:function(){var e=this;return this.params.tablename="",this.params.fieldname=[],"postgresql"===this.params.dbtype?r({datasourceId:this.params.datasourceId}).then((function(a){e.tableSchemaList=a.map((function(e){return{label:e,value:e}}))})):o({datasourceId:this.params.datasourceId}).then((function(a){console.log("dataReport.getTables res : ",a),e.tableList=a}))},tableSchemaChange:function(){var e=this;return o({datasourceId:this.params.datasourceId,tableSchema:this.params.tableSchema}).then((function(a){e.tableList=a}))},tableNameChange:function(){var e=this;this.params.fieldname=[],c({datasourceId:this.params.datasourceId,tableName:this.params.tablename}).then((function(a){e.fieldList=a}))},fieldNameChange:function(){this.initPageData()}}},h=f,b=t("2877"),g=Object(b["a"])(h,n,i,!1,null,null,null);a["default"]=g.exports},67248:function(e,a,t){"use strict";t("8d41");var n="@@wavesContext";function i(e,a){function t(t){var n=Object.assign({},a.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},n),l=i.ele;if(l){l.style.position="relative",l.style.overflow="hidden";var s=l.getBoundingClientRect(),o=l.querySelector(".waves-ripple");switch(o?o.className="waves-ripple":(o=document.createElement("span"),o.className="waves-ripple",o.style.height=o.style.width=Math.max(s.width,s.height)+"px",l.appendChild(o)),i.type){case"center":o.style.top=s.height/2-o.offsetHeight/2+"px",o.style.left=s.width/2-o.offsetWidth/2+"px";break;default:o.style.top=(t.pageY-s.top-o.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",o.style.left=(t.pageX-s.left-o.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return o.style.backgroundColor=i.color,o.className="waves-ripple z-active",!1}}return e[n]?e[n].removeHandle=t:e[n]={removeHandle:t},t}var l={bind:function(e,a){e.addEventListener("click",i(e,a),!1)},update:function(e,a){e.removeEventListener("click",e[n].removeHandle,!1),e.addEventListener("click",i(e,a),!1)},unbind:function(e){e.removeEventListener("click",e[n].removeHandle,!1),e[n]=null,delete e[n]}},s=function(e){e.directive("waves",l)};window.Vue&&(window.waves=l,Vue.use(s)),l.install=s;a["a"]=l},"8d41":function(e,a,t){}}]);