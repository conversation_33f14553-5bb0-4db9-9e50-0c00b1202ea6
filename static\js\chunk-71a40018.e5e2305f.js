(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-71a40018"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(e,t,a){var l=s(),r=e-l,o=20,u=0;t="undefined"===typeof t?500:t;var c=function e(){u+=o;var s=Math.easeInOutQuad(u,l,r,t);n(s),u<t?i(e):a&&"function"===typeof a&&a()};c()}},67248:function(e,t,a){"use strict";a("8d41");var i="@@wavesContext";function n(e,t){function a(a){var i=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),s=n.ele;if(s){s.style.position="relative",s.style.overflow="hidden";var l=s.getBoundingClientRect(),r=s.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(l.width,l.height)+"px",s.appendChild(r)),n.type){case"center":r.style.top=l.height/2-r.offsetHeight/2+"px",r.style.left=l.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(a.pageY-l.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(a.pageX-l.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}return e[i]?e[i].removeHandle=a:e[i]={removeHandle:a},a}var s={bind:function(e,t){e.addEventListener("click",n(e,t),!1)},update:function(e,t){e.removeEventListener("click",e[i].removeHandle,!1),e.addEventListener("click",n(e,t),!1)},unbind:function(e){e.removeEventListener("click",e[i].removeHandle,!1),e[i]=null,delete e[i]}},l=function(e){e.directive("waves",s)};window.Vue&&(window.waves=s,Vue.use(l)),s.install=l;t["a"]=s},"8d41":function(e,t,a){},"911f":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("div",{staticClass:"search-header"},[a("el-form",{attrs:{"label-suffix":"：","label-width":"138px",inline:""}},[a("el-form-item",{attrs:{label:"任务类型","label-width":"auto"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"任务类型"},model:{value:e.listQuery.tasktype,callback:function(t){e.$set(e.listQuery,"tasktype",t)},expression:"listQuery.tasktype"}})],1),a("el-form-item",{attrs:{label:"任务名称"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"266px"},attrs:{placeholder:"任务名称"},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}})],1)],1),a("div",{staticClass:"search-opt"},[a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item search",attrs:{type:"primary round"},on:{click:e.fetchData}},[e._v(" 搜索 ")]),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item reset-btn",attrs:{type:"primary round"},on:{click:e.handleReset}},[e._v(" 重置 ")])],1)],1),a("el-divider")],1),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:e.list,"element-loading-text":"Loading",border:"","highlight-current-row":""}},[a("el-table-column",{attrs:{label:"任务类型",align:"left",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.tasktype))]}}])}),a("el-table-column",{attrs:{label:"开发方式",align:"left",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.type))]}}])}),a("el-table-column",{attrs:{label:"任务名称",align:"left",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.name))]}}])}),a("el-table-column",{attrs:{label:"运行方式",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.runtype))]}}])}),a("el-table-column",{attrs:{label:"任务调度内存(MB)",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.jobManagerMemory))]}}])}),a("el-table-column",{attrs:{label:"任务执行内存(MB)",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.taskManagerMemory))]}}])}),a("el-table-column",{attrs:{label:"任务槽数量",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.slot))]}}])}),a("el-table-column",{attrs:{label:"任务描述",align:"left","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.task_describe))]}}])}),a("el-table-column",{attrs:{label:"更新时间",align:"left",width:"180px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.update_time))]}}])}),a("el-table-column",{attrs:{label:"操作",align:"left",width:"150px","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.row;return[a("span",{staticClass:"table-btn",on:{click:function(t){return e.handleUpdate(i)}}},[e._v("任务上线")]),"deleted"!==i.status?a("span",{staticClass:"table-btn",on:{click:function(t){return e.handleDelete(i)}}},[e._v(" 删除 ")]):e._e()]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.current,limit:e.listQuery.size},on:{"update:page":function(t){return e.$set(e.listQuery,"current",t)},"update:limit":function(t){return e.$set(e.listQuery,"size",t)},pagination:e.fetchData}}),a("el-dialog",{attrs:{title:e.textMap[e.dialogStatus],visible:e.dialogFormVisible},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"right","label-width":"100px"}},[a("el-form-item",{attrs:{label:"资源名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"资源名称"},model:{value:e.temp.name,callback:function(t){e.$set(e.temp,"name",t)},expression:"temp.name"}})],1),a("el-form-item",{attrs:{label:"资源地址",prop:"resourcePath"}},[a("el-input",{attrs:{placeholder:"资源地址"},model:{value:e.temp.resource_address,callback:function(t){e.$set(e.temp,"resource_address",t)},expression:"temp.resource_address"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v("确定")])],1)],1)],1)},n=[],s=(a("b0c0"),a("b775"));function l(e){return Object(s["a"])({url:"/api/base/resource/add",method:"post",data:e})}function r(e){return Object(s["a"])({url:"/api/devTask/execute",method:"post",data:e})}function o(e){return Object(s["a"])({url:"/api/devJar/remove?id="+e,method:"post"})}function u(e){return Object(s["a"])({url:"/api/devJar/list",method:"get",params:e})}var c=a("67248"),d=a("333d"),f={name:"User",components:{Pagination:d["a"]},directives:{waves:c["a"]},filters:{statusFilter:function(e){var t={published:"success",draft:"gray",deleted:"danger"};return t[e]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{current:1,size:10,tasktype:"",name:""},roles:["ROLE_USER","ROLE_ADMIN"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"Edit",create:"Create"},rules:{role:[{required:!0,message:"role is required",trigger:"change"}],name:[{required:!0,message:"name is required",trigger:"blur"}],password:[{required:!1,message:"password is required",trigger:"blur"}]},temp:{id:void 0,role:"",name:"",password:"",permission:"",resource_address:""},resetTemp:function(){this.temp=this.$options.data().temp}}},created:function(){this.fetchData()},methods:{handleReset:function(){this.listQuery.name="",this.listQuery.tasktype="",this.fetchData()},fetchData:function(){var e=this;this.listLoading=!0,u(this.listQuery).then((function(t){var a=t.content;e.total=a.recordsTotal,e.list=a.data,e.listLoading=!1}))},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={name:e.temp.name,resource_address:e.temp.resource_address};l(a).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}}))},handleVisit:function(e){window.open(e.resource_address)},handleUpdate:function(e){var t=this;r(e).then((function(e){t.fetchData(),t.$notify({title:"状态",message:"任务执行成功",type:"success",duration:2e3})}))},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var a={id:e.temp.id,name:e.temp.name,resource_address:e.temp.resource_address},i=Object.assign({},a);r(i).then((function(){e.fetchData(),e.dialogFormVisible=!1,e.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(e){var t=this;o(e.id).then((function(e){t.fetchData(),t.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))}}},p=f,m=a("2877"),h=Object(m["a"])(p,i,n,!1,null,null,null);t["default"]=h.exports}}]);