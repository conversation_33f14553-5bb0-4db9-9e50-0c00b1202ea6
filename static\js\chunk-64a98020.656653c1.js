(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-64a98020"],{"0d2a":function(t,e,n){},"133a":function(t,e,n){"use strict";n("0d2a")},"5c1a":function(t,e,n){"use strict";n("e046")},"7ba3":function(t,e,n){},"8c06":function(t,e,n){},"8f94":function(t,e,n){!function(e,i){t.exports=i(n("56b3"))}(0,(function(t){return function(t){function e(i){if(n[i])return n[i].exports;var a=n[i]={i:i,l:!1,exports:{}};return t[i].call(a.exports,a,a.exports,e),a.l=!0,a.exports}var n={};return e.m=t,e.c=n,e.i=function(t){return t},e.d=function(t,n,i){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:i})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="/",e(e.s=3)}([function(e,n){e.exports=t},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n(0),a=function(t){return t&&t.__esModule?t:{default:t}}(i),s=window.CodeMirror||a.default;"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),i=1;i<arguments.length;i++){var a=arguments[i];if(null!=a)for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(n[s]=a[s])}return n},writable:!0,configurable:!0}),e.default={name:"codemirror",data:function(){return{content:"",codemirror:null,cminstance:null}},props:{code:String,value:String,marker:Function,unseenLines:Array,name:{type:String,default:"codemirror"},placeholder:{type:String,default:""},merge:{type:Boolean,default:!1},options:{type:Object,default:function(){return{}}},events:{type:Array,default:function(){return[]}},globalOptions:{type:Object,default:function(){return{}}},globalEvents:{type:Array,default:function(){return[]}}},watch:{options:{deep:!0,handler:function(t){for(var e in t)this.cminstance.setOption(e,t[e])}},merge:function(){this.$nextTick(this.switchMerge)},code:function(t){this.handerCodeChange(t)},value:function(t){this.handerCodeChange(t)}},methods:{initialize:function(){var t=this,e=Object.assign({},this.globalOptions,this.options);this.merge?(this.codemirror=s.MergeView(this.$refs.mergeview,e),this.cminstance=this.codemirror.edit):(this.codemirror=s.fromTextArea(this.$refs.textarea,e),this.cminstance=this.codemirror,this.cminstance.setValue(this.code||this.value||this.content)),this.cminstance.on("change",(function(e){t.content=e.getValue(),t.$emit&&t.$emit("input",t.content)}));var n={};["scroll","changes","beforeChange","cursorActivity","keyHandled","inputRead","electricInput","beforeSelectionChange","viewportChange","swapDoc","gutterClick","gutterContextMenu","focus","blur","refresh","optionChange","scrollCursorIntoView","update"].concat(this.events).concat(this.globalEvents).filter((function(t){return!n[t]&&(n[t]=!0)})).forEach((function(e){t.cminstance.on(e,(function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];t.$emit.apply(t,[e].concat(i));var s=e.replace(/([A-Z])/g,"-$1").toLowerCase();s!==e&&t.$emit.apply(t,[s].concat(i))}))})),this.$emit("ready",this.codemirror),this.unseenLineMarkers(),this.refresh()},refresh:function(){var t=this;this.$nextTick((function(){t.cminstance.refresh()}))},destroy:function(){var t=this.cminstance.doc.cm.getWrapperElement();t&&t.remove&&t.remove()},handerCodeChange:function(t){if(t!==this.cminstance.getValue()){var e=this.cminstance.getScrollInfo();this.cminstance.setValue(t),this.content=t,this.cminstance.scrollTo(e.left,e.top)}this.unseenLineMarkers()},unseenLineMarkers:function(){var t=this;void 0!==this.unseenLines&&void 0!==this.marker&&this.unseenLines.forEach((function(e){var n=t.cminstance.lineInfo(e);t.cminstance.setGutterMarker(e,"breakpoints",n.gutterMarkers?null:t.marker())}))},switchMerge:function(){var t=this.cminstance.doc.history,e=this.cminstance.doc.cleanGeneration;this.options.value=this.cminstance.getValue(),this.destroy(),this.initialize(),this.cminstance.doc.history=t,this.cminstance.doc.cleanGeneration=e}},mounted:function(){this.initialize()},beforeDestroy:function(){this.destroy()}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n(1),a=n.n(i);for(var s in i)["default","default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);var l=n(5),r=n(4),o=r(a.a,l.a,!1,null,null,null);e.default=o.exports},function(t,e,n){"use strict";function i(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0}),e.install=e.codemirror=e.CodeMirror=void 0;var a=n(0),s=i(a),l=n(2),r=i(l),o=window.CodeMirror||s.default,c=function(t,e){e&&(e.options&&(r.default.props.globalOptions.default=function(){return e.options}),e.events&&(r.default.props.globalEvents.default=function(){return e.events})),t.component(r.default.name,r.default)},u={CodeMirror:o,codemirror:r.default,install:c};e.default=u,e.CodeMirror=o,e.codemirror=r.default,e.install=c},function(t,e){t.exports=function(t,e,n,i,a,s){var l,r=t=t||{},o=typeof t.default;"object"!==o&&"function"!==o||(l=t,r=t.default);var c,u="function"==typeof r?r.options:r;if(e&&(u.render=e.render,u.staticRenderFns=e.staticRenderFns,u._compiled=!0),n&&(u.functional=!0),a&&(u._scopeId=a),s?(c=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(s)},u._ssrRegister=c):i&&(c=i),c){var d=u.functional,f=d?u.render:u.beforeCreate;d?(u._injectStyles=c,u.render=function(t,e){return c.call(e),f(t,e)}):u.beforeCreate=f?[].concat(f,c):[c]}return{esModule:l,exports:r,options:u}}},function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"vue-codemirror",class:{merge:t.merge}},[t.merge?n("div",{ref:"mergeview"}):n("textarea",{ref:"textarea",attrs:{name:t.name,placeholder:t.placeholder}})])},a=[],s={render:i,staticRenderFns:a};e.a=s}])}))},b866:function(t,e,n){},cfa5:function(t,e,n){},da6a:function(t,e,n){"use strict";n("cfa5")},e046:function(t,e,n){},eb18:function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-tabs",{attrs:{"tab-position":"top",type:"border-card"}},[n("el-tab-pane",{attrs:{label:"基础配置"}},[n("el-form",{attrs:{"label-width":"100px"}},[n("el-form-item",{attrs:{label:"基本信息"}},[n("my-input",{attrs:{label:"名称",nullable:!1},model:{value:t.detail.name,callback:function(e){t.$set(t.detail,"name",e)},expression:"detail.name"}}),n("my-input",{attrs:{label:"路径",preffix:"http://"+t.address+"/",nullable:!1,width:"400px"},model:{value:t.detail.path,callback:function(e){t.$set(t.detail,"path",e)},expression:"detail.path"}}),n("my-select",{attrs:{options:t.groups,label:"返回值类型",option_label:"name",option_value:"id",nullable:!1},model:{value:t.detail.groupId,callback:function(e){t.$set(t.detail,"groupId",e)},expression:"detail.groupId"}}),n("my-input",{attrs:{label:"描述",width:"500px"},model:{value:t.detail.note,callback:function(e){t.$set(t.detail,"note",e)},expression:"detail.note"}})],1),n("el-form-item",{attrs:{label:"sql"}},[n("div",[n("sql-code",{ref:"sqlCode",attrs:{apiSql:t.detail.sqlList}})],1)]),n("el-form-item",{attrs:{label:"参数"}},[t._l(t.detail.params,(function(e,i){return n("div",{staticStyle:{"margin-bottom":"5px",display:"flex","align-items":"center"}},[n("el-autocomplete",{staticStyle:{width:"200px","margin-right":"5px"},attrs:{"fetch-suggestions":t.parseParams,placeholder:"*参数名称"},model:{value:e.name,callback:function(n){t.$set(e,"name",n)},expression:"item.name"}}),n("el-select",{staticStyle:{"margin-right":"5px"},attrs:{options:t.options,placeholder:"*数据类型"},model:{value:e.type,callback:function(n){t.$set(e,"type",n)},expression:"item.type"}},t._l(t.options,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),n("el-input",{staticStyle:{width:"400px","margin-right":"5px"},attrs:{placeholder:"参数说明"},model:{value:e.note,callback:function(n){t.$set(e,"note",n)},expression:"item.note"}}),"/api/detail"!=t.$route.path?n("el-button",{attrs:{circle:"",type:"danger",icon:"el-icon-delete",size:"mini"},on:{click:function(e){return t.deleteRow(i)}}}):t._e()],1)})),"/api/detail"!=t.$route.path?n("el-button",{attrs:{icon:"el-icon-plus",type:"primary",circle:"",size:"mini"},on:{click:t.addRow}}):t._e()],2),n("el-form-item",{attrs:{label:"Access"}},[n("el-radio-group",{model:{value:t.detail.previlege,callback:function(e){t.$set(t.detail,"previlege",e)},expression:"detail.previlege"}},[n("el-radio",{attrs:{label:0}},[t._v("私有API")]),n("el-radio",{attrs:{label:1}},[t._v("开放API")])],1),n("el-tooltip",{attrs:{placement:"top-start",effect:"dark"}},[n("div",{attrs:{slot:"content"},slot:"content"},[t._v("开放接口可以直接访问。私有接口在访问时必须在请求头中携带token，且该token值对此接口有访问权限，具体请到权限菜单查看")]),n("i",{staticClass:"el-icon-info tip"})])],1)],1)],1),n("el-tab-pane",{attrs:{label:"高级配置"}},[n("el-form",{attrs:{"label-width":"100px"}},[n("el-form-item",{attrs:{label:"数据转换"}},[t._l(this.$store.state.sqls,(function(e,i){return n("div",[n("span",[t._v("sql-"+t._s(e.id)+" :")]),n("my-input",{attrs:{label:"插件类名",placeholder:"填写数据转换插件java类名",width:"400px"},model:{value:e.transformPlugin,callback:function(n){t.$set(e,"transformPlugin",n)},expression:"item.transformPlugin"}}),n("my-input",{attrs:{label:"插件参数",width:"300px"},model:{value:e.transformPluginParams,callback:function(n){t.$set(e,"transformPluginParams",n)},expression:"item.transformPluginParams"}})],1)})),n("el-alert",{attrs:{type:"warning","show-icon":""}},[t._v(" 填写“插件类名”表示对sql执行结果开启数据转换功能，不填写表示不转换。 如果有多条sql，每个sql对应一个数据转换插件 ")])],2),n("el-form-item",{attrs:{label:"缓存"}},[n("el-tooltip",{attrs:{placement:"top-start",effect:"dark"}}),n("my-input",{attrs:{label:"插件类名",placeholder:"填写缓存插件java类名",width:"400px"},model:{value:t.detail.cachePlugin,callback:function(e){t.$set(t.detail,"cachePlugin",e)},expression:"detail.cachePlugin"}}),n("my-input",{attrs:{label:"插件参数",width:"300px"},model:{value:t.detail.cachePluginParams,callback:function(e){t.$set(t.detail,"cachePluginParams",e)},expression:"detail.cachePluginParams"}}),n("el-alert",{attrs:{type:"warning","show-icon":""}},[t._v("填写“插件类名”表示对结果数据开启缓存，不填写表示不开启缓存")]),n("div",[n("a",{staticClass:"el-icon-question",attrs:{target:"_blank",href:"https://gitee.com/freakchicken/db-api/blob/master/dbapi-assembly/docs/instruction.md#%E6%8F%92%E4%BB%B6"}},[t._v("什么是插件")]),n("a",{staticClass:"el-icon-question",attrs:{target:"_blank",href:"https://gitee.com/freakchicken/db-api/blob/master/dbapi-assembly/docs/plugin%20development.md#252-%E5%B1%80%E9%83%A8%E5%8F%82%E6%95%B0"}},[t._v("什么是插件参数")])])],1)],1)],1)],1)],1)},a=[],s=(n("14d9"),n("a434"),function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{class:["root",t.isFullScreen?"full":""]},[n("div",{staticClass:"left"},[n("div",[n("my-select",{attrs:{options:t.datasources,nullable:!1,label:"数据源",size:"mini",width:"176px",option_label:"name",option_value:"id"},on:{onchange:t.getTables},model:{value:t.datasourceId,callback:function(e){t.datasourceId=e},expression:"datasourceId"}})],1),n("div",{staticClass:"bottom"},t._l(t.tables,(function(e,i){return n("div",[n("div",[n("div",{staticClass:"table",on:{click:function(n){return n.preventDefault(),t.getColumns(e.label,i)},contextmenu:function(e){return e.preventDefault(),t.showMenu()}}},[n("i",{staticClass:"iconfont icon-table"}),t._v(" "+t._s(e.label)+" ")]),n("div",{directives:[{name:"show",rawName:"v-show",value:e.showColumns,expression:"item.showColumns"}]},t._l(e.columns,(function(e){return n("div",{staticClass:"column"},[n("span",{staticClass:"columnName"},[t._v(t._s(e.label))]),n("span",{staticClass:"columnType"},[t._v(t._s(e.TypeName))])])})),0)])])})),0)]),n("div",{staticClass:"right"},[n("div",{staticClass:"top"},[n("div",{staticClass:"tool"},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.isFullScreen,expression:"isFullScreen"}]},[n("div",{staticClass:"item",on:{click:function(e){return t.run(!1)}}},[n("i",{staticClass:"iconfont icon-play"}),n("span",[t._v("运行SQL")])]),n("div",{staticClass:"item",on:{click:function(e){return t.run(!0)}}},[n("i",{staticClass:"iconfont icon-play"}),n("span",[t._v("运行选中SQL")])]),n("div",{staticClass:"item",on:{click:t.parseSql}},[n("i",{staticClass:"iconfont icon-play"}),n("span",[t._v("解析动态SQL")])]),n("div",{staticClass:"item",on:{click:t.formatSql}},[n("i",{staticClass:"iconfont icon-play"}),n("span",[t._v("格式化SQL")])])])]),n("div",{staticClass:"quick"},[n("div",{staticClass:"tag",on:{click:function(e){return t.tag("foreach")}}},[t._v("foreach")]),n("div",{staticClass:"tag",on:{click:function(e){return t.tag("if")}}},[t._v("if")]),n("div",{staticClass:"tag",on:{click:function(e){return t.tag("where")}}},[t._v("where")]),n("div",{staticClass:"tag",on:{click:function(e){return t.tag("trim")}}},[t._v("trim")]),"mini"==t.mode?n("i",{staticClass:"iconfont icon-full",on:{click:t.fullWindow}}):t._e(),"large"==t.mode?n("i",{staticClass:"iconfont icon-mini2",on:{click:t.miniWindow}}):t._e()])]),n("div",{staticClass:"code"},[n("div",{staticClass:"multi-sql"},[t._l(this.$store.state.sqls,(function(e,i){return n("code-ui",{directives:[{name:"show",rawName:"v-show",value:t.currentIndex===i,expression:"currentIndex === index"}],key:e.id,ref:"codeui-"+i,refInFor:!0,attrs:{sql:e.sqlText,mode:t.mode,"table-hints":t.tableHints},on:{appendCm:t.appendCm}})})),n("div",{staticClass:"tabs"},[t._l(this.$store.state.sqls,(function(e,i){return n("div",{class:{tab:!0,"tab-active":t.currentIndex===i}},[n("div",{staticClass:"text",on:{click:function(e){return t.focusCM(i)}}},[t._v("SQL-"+t._s(e.id))]),i>0?n("span",{staticClass:"el-icon-circle-close close",on:{click:function(e){return t.removeTab(i)}}}):t._e()])})),n("div",{staticClass:"tab",on:{click:t.addTab}},[t._m(0)])],2)],2),n("div",{staticClass:"params"},[n("div",{staticStyle:{display:"inline-block"}},[t._v("参数设置：")]),n("el-tooltip",{attrs:{placement:"top-start",effect:"dark"}},[n("div",{attrs:{slot:"content"},slot:"content"},[t._v("填写sql运行需要的参数值，拼接成json格式")]),n("i",{staticClass:"el-icon-info tip",staticStyle:{color:"#ccc"}})]),n("el-input",{attrs:{type:"textarea",rows:"24"},on:{input:function(e){return t.input(e)}},model:{value:t.params,callback:function(e){t.params=e},expression:"params"}}),n("el-button",{attrs:{size:"mini"},on:{click:t.formatJson}},[t._v("json格式化")])],1)]),n("div",{staticClass:"result"},[null!=t.error?n("div",{staticClass:"error"},[n("i",{staticClass:"el-icon-error"}),t._v(" "+t._s(t.error)+" ")]):t._e(),null!=t.updateMsg?n("div",{staticClass:"updateMsg"},[n("i",{staticClass:"el-icon-success"}),t._v(" "+t._s(t.updateMsg)+" ")]):t._e(),null!=t.sqlMeta?n("div",{staticClass:"sqlMeta"},[n("div",{staticClass:"sql"},[t._v(t._s(t.sqlMeta.sql))]),n("div",{staticClass:"sql"},[t._v(t._s(t.sqlMeta.jdbcParamValues))])]):t._e(),n("div",{staticClass:"table table-box"},[null!=t.resultList&&t.resultList.length>0?n("el-table",{staticStyle:{width:"100%"},attrs:{data:t.resultList,border:"",stripe:"",size:"mini",height:"100%"}},t._l(Object.keys(t.resultList[0]),(function(t,e){return n("el-table-column",{key:"table-col-"+e,attrs:{prop:t,label:t}})})),1):t._e(),null!=t.resultList&&0==t.resultList.length?n("div",[t._v("查询结果为空")]):t._e()],1)])])])}),l=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"text"},[n("i",{staticClass:"el-icon-circle-plus"}),t._v(" 新增 ")])}],r=(n("e9c4"),n("b64b"),n("ac1f"),n("5319"),n("498a"),function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("codemirror",{staticClass:"myMirror",attrs:{options:t.cmOptions},on:{ready:t.onCmReady,focus:t.onCmFocus,inputRead:t.onCmCodeChange}})],1)}),o=[],c=(n("00b4"),n("8f94"));n("8c06"),n("b866"),n("7ba3"),n("f6b6"),n("ffda");n("56b3"),n("ffda"),n("9b74"),n("991c");var u={name:"codeUI",components:{codemirror:c["codemirror"]},data:function(){return{cmInstance:null,cmOptions:{value:"",styleActiveLine:!0,lineNumbers:!0,mode:"text/x-mysql",theme:"idea",lint:!0,matchBrackets:!0,extraKeys:{Tab:"autocomplete"},hintOptions:{completeSingle:!1,tables:{}}}}},props:{sql:{type:String,default:""},mode:{type:String,default:"mini"},tableHints:{type:Object}},methods:{onCmReady:function(t){t.setValue(this.sql),this.cmInstance=t,this.$emit("appendCm",t),"mini"===this.mode?t.setSize("100%","400px"):t.setSize("100%","calc(100vh - 350px)")},onCmFocus:function(t){},onCmCodeChange:function(t,e){/^[a-zA-Z.]/.test(e.text[0])&&t.showHint()}},watch:{mode:function(t,e){"mini"===t?this.cmInstance.setSize("100%","400px"):this.cmInstance.setSize("100%","calc(100vh - 350px)")},tableHints:function(t,e){this.cmOptions.hintOptions.tables=t}},created:function(){this.cmOptions.hintOptions.tables=this.tableHints}},d=u,f=(n("da6a"),n("2877")),m=Object(f["a"])(d,r,o,!1,null,"4a30beae",null),p=m.exports,h=n("db05"),v={components:{codeUi:p},props:{apiSql:{type:Array,default:function(){return[{sqlText:"",transformPlugin:null,transformPluginParams:null}]}}},data:function(){return{resultList:null,error:null,updateMsg:null,isFullScreen:!1,mode:"mini",params:"{}",datasourceId:null,datasources:[],tables:[],table:null,currentIndex:0,sqlMeta:null,tableHints:{}}},watch:{apiSql:function(t,e){this.$store.commit("initSqls",this.apiSql)}},created:function(){this.getAllSource()},methods:{appendCm:function(t){this.$store.commit("addCm",t)},addTab:function(){this.$store.commit("addSql"),this.currentIndex=this.$store.state.sqls.length-1},formatJson:function(){var t=JSON.parse(this.params);this.params=JSON.stringify(t,null,4)},input:function(t){console.log(t)},parseSql:function(){this.resultList=null,this.updateMsg=null,this.error=null,this.sqlMeta=null},formatSql:function(){var t=Object(h["format"])(this.$store.getters.currentCm(this.currentIndex).getValue()).replace(/# /g,"#").replace(/{ /g,"{").replace(/ }/g,"}").replace(/< foreach/g,"\n<foreach\n").replace(/< \/ foreach >/g,"\n</foreach>\n").replace(/< if/g,"\n<if").replace(/< \/ if >/g,"\n</if>\n").replace(/<\nwhere\n {2}>/g,"\n<where>\n").replace(/< \/\nwhere\n {2}>/g,"\n</where>\n").replace(/< trim/g,"\n<trim").replace(/< \/ trim >/g,"\n</trim>\n");this.$store.getters.currentCm(this.currentIndex).setValue(t)},run:function(t){var e;null!=this.datasourceId?(e=t?this.$store.getters.currentCm(this.currentIndex).getSelection():this.$store.getters.currentCm(this.currentIndex).getValue(),null!=e&&""!=e.trim()?(this.resultList=null,this.updateMsg=null,this.error=null,this.sqlMeta=null):this.$message.error("请先输入sql")):this.$message.error("请先选择数据源")},fullWindow:function(){this.mode="large",this.isFullScreen=!0},miniWindow:function(){this.mode="mini",this.isFullScreen=!1},getColumns:function(t,e){0==this.tables[e].columns.length||(this.tables[e].showColumns=!this.tables[e].showColumns)},getAllSource:function(){},getTables:function(t){},removeTab:function(t){this.$store.commit("removeSql",t),t<=this.currentIndex&&(this.currentIndex-=1)},focusCM:function(t){this.currentIndex=t},tag:function(t){var e="";"foreach"==t?e='\n<foreach open="(" close=")" collection="" separator="," item="item" index="index">#{item}</foreach>':"if"==t?e='\n<if test="" ></if>':"where"==t?e="\n<where></where>":"trim"==t&&(e='\n<trim prefix="" suffix="" suffixesToOverride="" prefixesToOverride=""></trim>'),this.$store.getters.currentCm(this.currentIndex).setValue(this.$store.getters.currentCm(this.currentIndex).getValue()+e)}}},b=v,g=(n("5c1a"),Object(f["a"])(b,s,l,!1,null,"031dfe08",null)),C=g.exports,_={data:function(){return{datasources:[],address:null,show:!1,groups:[{label:"姓名",value:"name"}],dialogVisible:!1,detail:{name:null,note:null,path:null,params:[],groupId:null,previlege:0,cachePlugin:null,cachePluginParams:null,sqlList:[{sqlText:"",transformPlugin:null,transformPluginParams:null}]},options:[{label:"string",value:"string"},{label:"bigint",value:"bigint"},{label:"double",value:"double"},{label:"date",value:"date"},{label:"string 数组",value:"Array<string>"},{label:"bigint 数组",value:"Array<bigint>"},{label:"double 数组",value:"Array<double>"},{label:"date 数组",value:"Array<date>"}],table:null,tables:[],columns:[],column:null,isFullScreen:!1,mode:"mini"}},props:["id"],methods:{addRow:function(){this.detail.params.push({name:null,type:null})},deleteRow:function(t){this.detail.params.splice(t,1)},parseParams:function(t,e){},getAddress:function(){},getDetail:function(t){this.id=t},getAllGroups:function(){}},mounted:function(){console.log("mount----+++"),this.getAddress(),void 0!=this.id?(console.log("edit api page"),this.getDetail(this.id)):(console.log("add api page"),this.$store.commit("initSqls",[{sqlText:"-- 请输入sql",transformPlugin:null,transformPluginParams:null}])),this.getAllGroups()},components:{sqlCode:C}},x=_,y=(n("133a"),Object(f["a"])(x,i,a,!1,null,"18a4ef93",null));e["a"]=y.exports}}]);