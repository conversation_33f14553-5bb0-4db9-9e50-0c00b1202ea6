<!DOCTYPE html>
<html>
<head>
    <meta charset=utf-8>
    <meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1">
    <meta name=renderer content=webkit>
    <meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel=icon href=/logo.ico>
    <title>数据中台</title>
    <link href=/static/css/chunk-libs.b4f89059.css rel=stylesheet>
    <link href=/static/css/app.49ddc19a.css rel=stylesheet>
</head>
<body>
    <div id=app></div>

    <!-- 内联自动登录脚本 - 立即执行 -->
    <script>
    (function() {
        console.log('🚀 启动内联自动登录');

        // 立即设置登录状态
        const mockUser = {
            username: 'admin',
            password: '123456',
            token: 'mock-token-admin-123456',
            roles: ['admin'],
            name: '管理员'
        };

        // 设置所有可能的存储
        const storageData = {
            'token': mockUser.token,
            'access_token': mockUser.token,
            'authToken': mockUser.token,
            'user': JSON.stringify(mockUser),
            'userInfo': JSON.stringify(mockUser),
            'isLoggedIn': 'true',
            'loginStatus': 'true',
            'authenticated': 'true'
        };

        Object.keys(storageData).forEach(key => {
            localStorage.setItem(key, storageData[key]);
            sessionStorage.setItem(key, storageData[key]);
        });

        console.log('✅ 已设置登录状态');

        // 拦截登录API和路由跳转
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                if (typeof url === 'string' && (url.includes('/login') || url.includes('/auth'))) {
                    console.log('🔄 拦截登录API:', url);
                    return Promise.resolve(new Response(JSON.stringify({
                        code: 200,
                        message: '登录成功',
                        data: mockUser
                    }), { status: 200, headers: { 'Content-Type': 'application/json' } }));
                }
                return originalFetch.apply(this, arguments);
            };
        }

        // 拦截页面跳转
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;

        history.pushState = function(state, title, url) {
            if (url && url.includes('/login')) {
                console.log('🔄 拦截路由跳转到登录页:', url);
                // 尝试多种可能的主页路由
                const possibleRoutes = ['#/dashboard', '#/home', '#/main', '#/', '/'];
                for (const route of possibleRoutes) {
                    try {
                        return originalPushState.call(this, state, title, route);
                    } catch (e) {
                        console.log('尝试路由失败:', route);
                    }
                }
                return originalPushState.call(this, state, title, '#/');
            }
            return originalPushState.apply(this, arguments);
        };

        history.replaceState = function(state, title, url) {
            if (url && url.includes('/login')) {
                console.log('🔄 拦截路由替换到登录页:', url);
                return originalReplaceState.call(this, state, title, '#/');
            }
            return originalReplaceState.apply(this, arguments);
        };

        // 拦截window.location变化
        let currentLocation = window.location.href;
        const checkLocationChange = () => {
            if (window.location.href !== currentLocation) {
                currentLocation = window.location.href;
                if (currentLocation.includes('/login')) {
                    console.log('🔄 检测到跳转到登录页，重定向到主页');
                    // 尝试hash路由
                    if (window.location.hash) {
                        window.location.hash = '#/';
                    } else {
                        window.location.href = '/#/';
                    }
                }
            }
        };

        setInterval(checkLocationChange, 100);

        // 立即检查当前URL
        if (window.location.pathname.includes('/login') || window.location.search.includes('redirect=')) {
            console.log('🔄 当前在登录相关页面，立即重定向到主页');
            setTimeout(() => {
                window.location.href = '/#/';
            }, 100);
        }

        // 处理当前URL如果已经在登录页面
        if (window.location.pathname.includes('/login') || window.location.search.includes('redirect=')) {
            console.log('🔄 当前在登录相关页面，立即重定向');
            setTimeout(() => {
                window.location.href = '/';
            }, 100);
        }

        // 自动填充登录表单
        function autoLogin() {
            setTimeout(() => {
                const usernameInput = document.querySelector('input[type="text"], input[name*="user"], input[placeholder*="用户"], input[placeholder*="账号"]');
                const passwordInput = document.querySelector('input[type="password"]');

                if (usernameInput && passwordInput) {
                    console.log('📝 找到登录表单，自动填充...');
                    usernameInput.value = 'admin';
                    passwordInput.value = '123456';

                    // 触发事件
                    [usernameInput, passwordInput].forEach(input => {
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                    });

                    // 查找并点击登录按钮
                    setTimeout(() => {
                        let submitBtn = null;

                        // 先尝试标准选择器
                        submitBtn = document.querySelector('button[type="submit"]') ||
                                   document.querySelector('.el-button--primary') ||
                                   document.querySelector('.login-btn') ||
                                   document.querySelector('.submit-btn');

                        // 如果没找到，遍历所有按钮查找包含"登录"文字的
                        if (!submitBtn) {
                            const allButtons = document.querySelectorAll('button');
                            for (const btn of allButtons) {
                                const text = (btn.textContent || btn.innerText || '').trim();
                                if (text.includes('登录') || text.includes('确定') || text.includes('提交')) {
                                    submitBtn = btn;
                                    break;
                                }
                            }
                        }

                        if (submitBtn) {
                            console.log('🚀 找到登录按钮，自动点击:', submitBtn.textContent || submitBtn.innerText);
                            submitBtn.click();
                        } else {
                            console.log('⚠️ 未找到登录按钮，尝试表单提交');
                            const form = document.querySelector('form');
                            if (form) {
                                form.submit();
                            }
                        }
                    }, 200);
                }
            }, 1000);
        }

        // 页面加载后执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', autoLogin);
        } else {
            autoLogin();
        }

        // 持续监控
        let checkCount = 0;
        const monitor = setInterval(() => {
            checkCount++;
            if (checkCount > 10) {
                clearInterval(monitor);
                return;
            }

            if (document.querySelector('input[type="password"]')) {
                autoLogin();
            }
        }, 2000);

    })();
    </script>

    <!-- Mock服务 - 完整的数据和API模拟 -->
    <script src="/auto-skip-login.js"></script>
    <script src="/mock-data-service.js"></script>
    <script src="/mock-api-service.js"></script>
    <script src="/mock-websocket-service.js"></script>
    <script src="/mock-storage-service.js"></script>
    <script src="/mock-service-manager.js"></script>

    <script>(function(c){function e(e){for(var u,a,k=e[0],d=e[1],b=e[2],t=0,r=[];t<k.length;t++)a=k[t],Object.prototype.hasOwnProperty.call(h,a)&&h[a]&&r.push(h[a][0]),h[a]=0;for(u in d)Object.prototype.hasOwnProperty.call(d,u)&&(c[u]=d[u]);o&&o(e);while(r.length)r.shift()();return f.push.apply(f,b||[]),n()}function n(){for(var c,e=0;e<f.length;e++){for(var n=f[e],u=!0,a=1;a<n.length;a++){var k=n[a];0!==h[k]&&(u=!1)}u&&(f.splice(e--,1),c=d(d.s=n[0]))}return c}var u={},a={runtime:0},h={runtime:0},f=[];function k(c){return d.p+"static/js/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-01b3d7e4":"5834f403","chunk-0b759c92":"2861136e","chunk-1ada36a9":"fece018c","chunk-49a31025":"05c0b32e","chunk-8ff765de":"eb21c1c6","chunk-164ce15a":"567df9e9","chunk-169fa71c":"a2a1e95e","chunk-1789dd1f":"191ea5f8","chunk-1f779376":"3ac6f99b","chunk-27f08700":"65e39a23","chunk-2d0c8f68":"ee7e675e","chunk-139a3e43":"cff426f2","chunk-89639478":"b53d4f07","chunk-64a98020":"656653c1","chunk-59901e18":"766db882","chunk-b9397cee":"4c2f4eeb","chunk-fd148aa2":"27794f79","chunk-47e6381d":"71591f64","chunk-5c46b6c7":"bcbfb30d","chunk-5d36bb00":"4d352d21","chunk-6f0bc29c":"9ec8f512","chunk-b0003992":"885facc2","chunk-cec6db74":"2ff15fb4","chunk-commons":"e5e7e347","chunk-00954410":"9e732038","chunk-00b2f898":"1011983d","chunk-0209b0d8":"6a778709","chunk-074fde65":"f8677f15","chunk-13114cfb":"65c0e2b2","chunk-1d37307e":"bcfe99b7","chunk-326b88ca":"b76dd82e","chunk-20deeeb0":"74fbf3d2","chunk-225fbb49":"b40f6b5a","chunk-250514a9":"2b00f76c","chunk-26886c31":"b0f08660","chunk-72091c20":"64c7e8ab","chunk-17a231b2":"582660cb","chunk-43155c8d":"23a96ce2","chunk-6f8bf045":"a20683c6","chunk-70c57a74":"c1c3edeb","chunk-8e53cc98":"f395fea3","chunk-c9b7e52e":"9d557855","chunk-4a25b5e8":"8fd59682","chunk-5c8098c9":"e1f4e481","chunk-5d8ef9eb":"138035a4","chunk-d049e36c":"d85c715c","chunk-5c0d7996":"40ed8ada","chunk-2eb6ad92":"0dbe8059","chunk-31859dd0":"189404a1","chunk-345868cf":"2fa99f1e","chunk-439f9198":"58f9fd49","chunk-44715fee":"a7dfc1d3","chunk-455241a8":"a48fbeef","chunk-5a44076f":"a508ac30","chunk-642a342e":"1490b90b","chunk-6549fb46":"b01963b6","chunk-71a40018":"e5e2305f","chunk-7589dfc4":"40954660","chunk-77cb55eb":"445db2b0","chunk-786ae63d":"7c9e077b","chunk-798611d4":"e0c774e7","chunk-01680444":"2db55b4f","chunk-7b3fbff2":"bafd39fc","chunk-7c47e8a0":"53919581","chunk-8e0d8a8e":"88194a9d","chunk-9a9b33d4":"455ff51f","chunk-d800437c":"b5e5faf7","chunk-9ff42110":"822fc46a","chunk-2d0db496":"3c77bc0d","chunk-d70bef62":"bbaf8363","chunk-d80be4c8":"8dab56eb","chunk-e084679a":"12ed84b5","chunk-df06e6b2":"145273b7","chunk-ffe7b3aa":"bed2c90d","chunk-a7c1aa34":"3b89a5ba"}[c]+".js"}function d(e){if(u[e])return u[e].exports;var n=u[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,d),n.l=!0,n.exports}d.e=function(c){var e=[],n={"chunk-01b3d7e4":1,"chunk-1ada36a9":1,"chunk-49a31025":1,"chunk-8ff765de":1,"chunk-169fa71c":1,"chunk-1789dd1f":1,"chunk-1f779376":1,"chunk-27f08700":1,"chunk-139a3e43":1,"chunk-64a98020":1,"chunk-59901e18":1,"chunk-b9397cee":1,"chunk-fd148aa2":1,"chunk-47e6381d":1,"chunk-5c46b6c7":1,"chunk-5d36bb00":1,"chunk-6f0bc29c":1,"chunk-b0003992":1,"chunk-cec6db74":1,"chunk-commons":1,"chunk-00954410":1,"chunk-00b2f898":1,"chunk-0209b0d8":1,"chunk-074fde65":1,"chunk-1d37307e":1,"chunk-326b88ca":1,"chunk-20deeeb0":1,"chunk-225fbb49":1,"chunk-250514a9":1,"chunk-26886c31":1,"chunk-72091c20":1,"chunk-17a231b2":1,"chunk-43155c8d":1,"chunk-6f8bf045":1,"chunk-70c57a74":1,"chunk-8e53cc98":1,"chunk-c9b7e52e":1,"chunk-4a25b5e8":1,"chunk-5d8ef9eb":1,"chunk-5c0d7996":1,"chunk-2eb6ad92":1,"chunk-31859dd0":1,"chunk-345868cf":1,"chunk-439f9198":1,"chunk-44715fee":1,"chunk-455241a8":1,"chunk-5a44076f":1,"chunk-642a342e":1,"chunk-6549fb46":1,"chunk-71a40018":1,"chunk-7589dfc4":1,"chunk-77cb55eb":1,"chunk-786ae63d":1,"chunk-01680444":1,"chunk-7b3fbff2":1,"chunk-7c47e8a0":1,"chunk-9a9b33d4":1,"chunk-9ff42110":1,"chunk-d70bef62":1,"chunk-d80be4c8":1,"chunk-e084679a":1,"chunk-df06e6b2":1,"chunk-a7c1aa34":1};a[c]?e.push(a[c]):0!==a[c]&&n[c]&&e.push(a[c]=new Promise((function(e,n){for(var u="static/css/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-01b3d7e4":"6859a90c","chunk-0b759c92":"31d6cfe0","chunk-1ada36a9":"55a106d0","chunk-49a31025":"adc5827d","chunk-8ff765de":"42dafb92","chunk-164ce15a":"31d6cfe0","chunk-169fa71c":"13a7e89e","chunk-1789dd1f":"25f41a06","chunk-1f779376":"a4ab123c","chunk-27f08700":"fec1cad6","chunk-2d0c8f68":"31d6cfe0","chunk-139a3e43":"829dcd80","chunk-89639478":"31d6cfe0","chunk-64a98020":"4826aae7","chunk-59901e18":"75c5e740","chunk-b9397cee":"074d5567","chunk-fd148aa2":"9e391cab","chunk-47e6381d":"225cb76f","chunk-5c46b6c7":"fc85ca32","chunk-5d36bb00":"0e690591","chunk-6f0bc29c":"bfe679ba","chunk-b0003992":"a6cd2477","chunk-cec6db74":"5f8941eb","chunk-commons":"47a31464","chunk-00954410":"53ac87fa","chunk-00b2f898":"53ac87fa","chunk-0209b0d8":"53ac87fa","chunk-074fde65":"6bb07c3a","chunk-13114cfb":"31d6cfe0","chunk-1d37307e":"53ac87fa","chunk-326b88ca":"53ac87fa","chunk-20deeeb0":"53ac87fa","chunk-225fbb49":"b2a03494","chunk-250514a9":"53ac87fa","chunk-26886c31":"335a1e16","chunk-72091c20":"f459dc8e","chunk-17a231b2":"b9ee1a47","chunk-43155c8d":"c072422d","chunk-6f8bf045":"b45d03f0","chunk-70c57a74":"115e28e3","chunk-8e53cc98":"161dc65f","chunk-c9b7e52e":"814f8777","chunk-4a25b5e8":"3894724d","chunk-5c8098c9":"31d6cfe0","chunk-5d8ef9eb":"ae8ad169","chunk-d049e36c":"31d6cfe0","chunk-5c0d7996":"8926175c","chunk-2eb6ad92":"53ac87fa","chunk-31859dd0":"a8ee6bf8","chunk-345868cf":"53ac87fa","chunk-439f9198":"738ea13c","chunk-44715fee":"0533d4d1","chunk-455241a8":"d59ef7b0","chunk-5a44076f":"53ac87fa","chunk-642a342e":"4fe25b0b","chunk-6549fb46":"53ac87fa","chunk-71a40018":"53ac87fa","chunk-7589dfc4":"d2c5078e","chunk-77cb55eb":"eb1bb76e","chunk-786ae63d":"53ac87fa","chunk-798611d4":"31d6cfe0","chunk-01680444":"e811b76e","chunk-7b3fbff2":"53ac87fa","chunk-7c47e8a0":"bcba7768","chunk-8e0d8a8e":"31d6cfe0","chunk-9a9b33d4":"53ac87fa","chunk-d800437c":"31d6cfe0","chunk-9ff42110":"76eeb16f","chunk-2d0db496":"31d6cfe0","chunk-d70bef62":"73250ab4","chunk-d80be4c8":"53ac87fa","chunk-e084679a":"934470b2","chunk-df06e6b2":"18841567","chunk-ffe7b3aa":"31d6cfe0","chunk-a7c1aa34":"3cbe29b9"}[c]+".css",h=d.p+u,f=document.getElementsByTagName("link"),k=0;k<f.length;k++){var b=f[k],t=b.getAttribute("data-href")||b.getAttribute("href");if("stylesheet"===b.rel&&(t===u||t===h))return e()}var r=document.getElementsByTagName("style");for(k=0;k<r.length;k++){b=r[k],t=b.getAttribute("data-href");if(t===u||t===h)return e()}var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",o.onload=e,o.onerror=function(e){var u=e&&e.target&&e.target.src||h,f=new Error("Loading CSS chunk "+c+" failed.\n("+u+")");f.code="CSS_CHUNK_LOAD_FAILED",f.request=u,delete a[c],o.parentNode.removeChild(o),n(f)},o.href=h;var i=document.getElementsByTagName("head")[0];i.appendChild(o)})).then((function(){a[c]=0})));var u=h[c];if(0!==u)if(u)e.push(u[2]);else{var f=new Promise((function(e,n){u=h[c]=[e,n]}));e.push(u[2]=f);var b,t=document.createElement("script");t.charset="utf-8",t.timeout=120,d.nc&&t.setAttribute("nonce",d.nc),t.src=k(c);var r=new Error;b=function(e){t.onerror=t.onload=null,clearTimeout(o);var n=h[c];if(0!==n){if(n){var u=e&&("load"===e.type?"missing":e.type),a=e&&e.target&&e.target.src;r.message="Loading chunk "+c+" failed.\n("+u+": "+a+")",r.name="ChunkLoadError",r.type=u,r.request=a,n[1](r)}h[c]=void 0}};var o=setTimeout((function(){b({type:"timeout",target:t})}),12e4);t.onerror=t.onload=b,document.head.appendChild(t)}return Promise.all(e)},d.m=c,d.c=u,d.d=function(c,e,n){d.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},d.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},d.t=function(c,e){if(1&e&&(c=d(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(d.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var u in c)d.d(n,u,function(e){return c[e]}.bind(null,u));return n},d.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return d.d(e,"a",e),e},d.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},d.p="/",d.oe=function(c){throw console.error(c),c};var b=window["webpackJsonp"]=window["webpackJsonp"]||[],t=b.push.bind(b);b.push=e,b=b.slice();for(var r=0;r<b.length;r++)e(b[r]);var o=t;n()})([]);</script>
    <script src=/static/js/chunk-elementUI.8b809683.js></script>
    <script src=/static/js/chunk-libs.2d635341.js></script>
    <script src=/static/js/app.4724f546.js></script>
</body>
</html>