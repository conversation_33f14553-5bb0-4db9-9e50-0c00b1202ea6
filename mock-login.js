// Mock登录服务 - 自动登录，跳过登录页面
// 硬编码账号: admin, 密码: 123456

class MockLoginService {
    constructor() {
        // 硬编码的用户信息
        this.users = {
            admin: {
                username: 'admin',
                password: '123456',
                token: 'mock-token-admin-123456',
                roles: ['admin'],
                name: '管理员',
                avatar: '/user.png'
            }
        };

        // 自动设置登录状态
        this.autoLogin();
    }

    // 自动登录方法
    autoLogin() {
        const user = this.users.admin;

        // 设置token到localStorage
        localStorage.setItem('token', user.token);
        localStorage.setItem('roles', JSON.stringify(user.roles));
        localStorage.setItem('username', user.username);
        localStorage.setItem('menuIsChecked', JSON.stringify([]));

        console.log('🚀 自动登录成功 - 账号: admin, 已跳过登录页面');
    }

    // 模拟登录API
    login(username, password) {
        return new Promise((resolve, reject) => {
            // 模拟网络延迟
            setTimeout(() => {
                const user = this.users[username];
                
                if (!user) {
                    reject({
                        code: 401,
                        message: '用户不存在'
                    });
                    return;
                }

                if (user.password !== password) {
                    reject({
                        code: 401,
                        message: '密码错误'
                    });
                    return;
                }

                // 登录成功，返回用户信息和token
                resolve({
                    code: 200,
                    message: '登录成功',
                    content: {
                        data: user.token,
                        roles: user.roles,
                        user: {
                            username: user.username,
                            name: user.name,
                            avatar: user.avatar
                        }
                    }
                });
            }, 500); // 模拟500ms网络延迟
        });
    }

    // 模拟获取用户信息API
    getUserInfo(token) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // 查找对应token的用户
                const user = Object.values(this.users).find(u => u.token === token);
                
                if (!user) {
                    reject({
                        code: 401,
                        message: 'Token无效'
                    });
                    return;
                }

                resolve({
                    code: 200,
                    message: '获取用户信息成功',
                    content: {
                        username: user.username,
                        name: user.name,
                        avatar: user.avatar,
                        roles: user.roles
                    }
                });
            }, 200);
        });
    }

    // 模拟登出API
    logout() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    code: 200,
                    message: '登出成功'
                });
            }, 200);
        });
    }
}

// 创建全局实例
window.mockLoginService = new MockLoginService();

// 拦截原有的登录请求，替换为mock服务
if (window.XMLHttpRequest) {
    const originalXHR = window.XMLHttpRequest;
    
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        
        let requestUrl = '';
        let requestMethod = '';
        let requestData = null;
        
        xhr.open = function(method, url, ...args) {
            requestMethod = method.toLowerCase();
            requestUrl = url;
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        xhr.send = function(data) {
            requestData = data;
            
            // 拦截登录请求
            if (requestUrl.includes('/api/auth/login') && requestMethod === 'post') {
                try {
                    const loginData = JSON.parse(data);
                    
                    window.mockLoginService.login(loginData.username, loginData.password)
                        .then(response => {
                            // 模拟成功响应
                            Object.defineProperty(xhr, 'status', { value: 200 });
                            Object.defineProperty(xhr, 'responseText', { value: JSON.stringify(response) });
                            Object.defineProperty(xhr, 'readyState', { value: 4 });
                            
                            if (xhr.onreadystatechange) {
                                xhr.onreadystatechange();
                            }
                        })
                        .catch(error => {
                            // 模拟错误响应
                            Object.defineProperty(xhr, 'status', { value: error.code || 401 });
                            Object.defineProperty(xhr, 'responseText', { value: JSON.stringify(error) });
                            Object.defineProperty(xhr, 'readyState', { value: 4 });
                            
                            if (xhr.onreadystatechange) {
                                xhr.onreadystatechange();
                            }
                        });
                    
                    return;
                } catch (e) {
                    console.error('解析登录数据失败:', e);
                }
            }
            
            // 其他请求正常处理
            return originalSend.apply(this, arguments);
        };
        
        return xhr;
    };
}

// 页面加载完成后检查并处理登录状态
document.addEventListener('DOMContentLoaded', function() {
    // 等待一下让Vue应用初始化
    setTimeout(() => {
        window.mockLoginService.handleAutoLogin();
    }, 1000);
});

// 处理自动登录和页面跳转
window.mockLoginService.handleAutoLogin = function() {
    const currentHash = window.location.hash;

    // 如果当前在登录页面，直接跳转到主页
    if (currentHash.includes('/login') || currentHash.includes('login')) {
        console.log('🔄 检测到登录页面，自动跳转到主页');
        // 尝试多种跳转方式
        if (window.location.hash) {
            window.location.hash = '#/';
        } else {
            window.location.href = '/#/';
        }
        return;
    }

    // 监听路由变化，拦截登录页面
    if (window.addEventListener) {
        window.addEventListener('hashchange', function() {
            const hash = window.location.hash;
            if (hash.includes('/login') || hash.includes('login')) {
                console.log('🔄 拦截登录页面跳转，重定向到主页');
                setTimeout(() => {
                    window.location.hash = '#/';
                }, 100);
            }
        });
    }
};

// 拦截Vue Router的登录路由（如果使用Vue Router）
if (window.Vue && window.VueRouter) {
    const originalPush = window.VueRouter.prototype.push;
    window.VueRouter.prototype.push = function(location) {
        if (typeof location === 'string' && location.includes('login')) {
            console.log('🔄 拦截Vue Router登录路由，重定向到主页');
            return originalPush.call(this, '/');
        }
        if (typeof location === 'object' && (location.path?.includes('login') || location.name?.includes('login'))) {
            console.log('🔄 拦截Vue Router登录路由，重定向到主页');
            return originalPush.call(this, '/');
        }
        return originalPush.call(this, location);
    };
}

console.log('🚀 Mock登录服务已启用 - 自动登录模式，已跳过登录页面');
