// Mock登录服务
// 硬编码账号: admin, 密码: 123456

class MockLoginService {
    constructor() {
        // 硬编码的用户信息
        this.users = {
            admin: {
                username: 'admin',
                password: '123456',
                token: 'mock-token-admin-123456',
                roles: ['admin'],
                name: '管理员',
                avatar: '/user.png'
            }
        };
    }

    // 模拟登录API
    login(username, password) {
        return new Promise((resolve, reject) => {
            // 模拟网络延迟
            setTimeout(() => {
                const user = this.users[username];
                
                if (!user) {
                    reject({
                        code: 401,
                        message: '用户不存在'
                    });
                    return;
                }

                if (user.password !== password) {
                    reject({
                        code: 401,
                        message: '密码错误'
                    });
                    return;
                }

                // 登录成功，返回用户信息和token
                resolve({
                    code: 200,
                    message: '登录成功',
                    content: {
                        data: user.token,
                        roles: user.roles,
                        user: {
                            username: user.username,
                            name: user.name,
                            avatar: user.avatar
                        }
                    }
                });
            }, 500); // 模拟500ms网络延迟
        });
    }

    // 模拟获取用户信息API
    getUserInfo(token) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // 查找对应token的用户
                const user = Object.values(this.users).find(u => u.token === token);
                
                if (!user) {
                    reject({
                        code: 401,
                        message: 'Token无效'
                    });
                    return;
                }

                resolve({
                    code: 200,
                    message: '获取用户信息成功',
                    content: {
                        username: user.username,
                        name: user.name,
                        avatar: user.avatar,
                        roles: user.roles
                    }
                });
            }, 200);
        });
    }

    // 模拟登出API
    logout() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    code: 200,
                    message: '登出成功'
                });
            }, 200);
        });
    }
}

// 创建全局实例
window.mockLoginService = new MockLoginService();

// 拦截原有的登录请求，替换为mock服务
if (window.XMLHttpRequest) {
    const originalXHR = window.XMLHttpRequest;
    
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        
        let requestUrl = '';
        let requestMethod = '';
        let requestData = null;
        
        xhr.open = function(method, url, ...args) {
            requestMethod = method.toLowerCase();
            requestUrl = url;
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        xhr.send = function(data) {
            requestData = data;
            
            // 拦截登录请求
            if (requestUrl.includes('/api/auth/login') && requestMethod === 'post') {
                try {
                    const loginData = JSON.parse(data);
                    
                    window.mockLoginService.login(loginData.username, loginData.password)
                        .then(response => {
                            // 模拟成功响应
                            Object.defineProperty(xhr, 'status', { value: 200 });
                            Object.defineProperty(xhr, 'responseText', { value: JSON.stringify(response) });
                            Object.defineProperty(xhr, 'readyState', { value: 4 });
                            
                            if (xhr.onreadystatechange) {
                                xhr.onreadystatechange();
                            }
                        })
                        .catch(error => {
                            // 模拟错误响应
                            Object.defineProperty(xhr, 'status', { value: error.code || 401 });
                            Object.defineProperty(xhr, 'responseText', { value: JSON.stringify(error) });
                            Object.defineProperty(xhr, 'readyState', { value: 4 });
                            
                            if (xhr.onreadystatechange) {
                                xhr.onreadystatechange();
                            }
                        });
                    
                    return;
                } catch (e) {
                    console.error('解析登录数据失败:', e);
                }
            }
            
            // 其他请求正常处理
            return originalSend.apply(this, arguments);
        };
        
        return xhr;
    };
}

console.log('Mock登录服务已启用 - 账号: admin, 密码: 123456');
