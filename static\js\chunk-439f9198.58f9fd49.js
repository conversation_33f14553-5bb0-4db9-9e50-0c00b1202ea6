(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-439f9198"],{"09f4":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),Math.easeInOutQuad=function(t,e,n,i){return t/=i/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function a(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(t,e,n){var r=a(),l=t-r,s=20,u=0;e="undefined"===typeof e?500:e;var c=function t(){u+=s;var a=Math.easeInOutQuad(u,r,l,e);o(a),u<e?i(t):n&&"function"===typeof n&&n()};c()}},"0da5":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"220px","margin-right":"10px"},attrs:{clearable:"",placeholder:"调度类名"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}}),n("el-input",{staticClass:"filter-item",staticStyle:{width:"220px","margin-right":"10px"},attrs:{clearable:"",placeholder:"任务描述"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.describe,callback:function(e){t.$set(t.listQuery,"describe",e)},expression:"listQuery.describe"}}),n("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:t.fetchData}},[t._v(" 搜索 ")]),n("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v(" 新增 ")])],1),n("div",{staticClass:"table-box"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{height:"100%",data:t.list,"element-loading-text":"Loading",width:"640",border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"left",label:"序号",width:"65"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.$index+1))]}}])}),n("el-table-column",{attrs:{label:"调度类名",width:"160",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.name)+" ")]}}])}),n("el-table-column",{attrs:{label:"调度表达式",width:"160",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.rule)+" ")]}}])}),n("el-table-column",{attrs:{label:"任务描述",align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.describe))]}}])}),n("el-table-column",{attrs:{label:"任务状态",align:"left",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":"启用","inactive-value":"停用","active-text":"启用","inactive-text":"停用"},on:{change:function(n){return t.statusChange(e.row)}},model:{value:e.row.status,callback:function(n){t.$set(e.row,"status",n)},expression:"scope.row.status"}})]}}])}),n("el-table-column",{attrs:{label:"触发时间",align:"left",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-popover",{attrs:{placement:"bottom",width:"160"},on:{show:function(n){return t.nextTriggerTime(e.row)}}},[n("h5",{domProps:{innerHTML:t._s(t.triggerNextTimes)}}),n("el-button",{attrs:{slot:"reference",size:"small"},slot:"reference"},[t._v("查看")])],1)]}}])}),n("el-table-column",{attrs:{label:"负责人",align:"left",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.warnperson))]}}])}),n("el-table-column",{attrs:{label:"操作",align:"left","class-name":"small-padding fixed-width",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.row;return[n("el-dropdown",{attrs:{trigger:"click"}},[n("span",{staticClass:"el-dropdown-link"},[t._v(" 操作"),n("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{nativeOn:{click:function(e){return t.handlerExecute(i)}}},[t._v("执行一次")]),n("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(e){return t.handleUpdate(i)}}},[t._v("编辑")]),n("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(e){return t.handleDelete(i)}}},[t._v("删除")])],1)],1)]}}])})],1)],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.pageNo,limit:t.listQuery.pageSize},on:{"update:page":function(e){return t.$set(t.listQuery,"pageNo",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.fetchData}}),n("el-dialog",{attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,width:"600px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[n("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[n("el-form-item",{attrs:{label:"调度类名",prop:"name"}},[n("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"调度类名"},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}})],1),n("el-dialog",{attrs:{title:"提示",visible:t.showCronBox,width:"60%","append-to-body":""},on:{"update:visible":function(e){t.showCronBox=e}}},[n("cron",{model:{value:t.temp.rule,callback:function(e){t.$set(t.temp,"rule",e)},expression:"temp.rule"}}),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.showCronBox=!1}}},[t._v("关闭")]),n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.showCronBox=!1}}},[t._v("确 定")])],1)],1),n("el-form-item",{attrs:{label:"调度表达式",prop:"rule"}},[n("el-input",{attrs:{"auto-complete":"off",placeholder:"请输入Cron表达式"},model:{value:t.temp.rule,callback:function(e){t.$set(t.temp,"rule",e)},expression:"temp.rule"}},[t.showCronBox?n("el-button",{attrs:{slot:"append",icon:"el-icon-open",title:"关闭图形配置"},on:{click:function(e){t.showCronBox=!1}},slot:"append"}):n("el-button",{attrs:{slot:"append",icon:"el-icon-turn-off",title:"打开图形配置"},on:{click:function(e){t.showCronBox=!0}},slot:"append"})],1)],1),n("el-form-item",{attrs:{label:"联系方式",prop:"warntype"}},[n("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"联系方式"},model:{value:t.temp.warntype,callback:function(e){t.$set(t.temp,"warntype",e)},expression:"temp.warntype"}},[n("el-option",{attrs:{label:"电子邮件",value:"电子邮件"}})],1)],1),n("el-form-item",{attrs:{label:"负责人",prop:"warnperson"}},[n("el-select",{attrs:{placeholder:"负责人"},model:{value:t.temp.warnperson,callback:function(e){t.$set(t.temp,"warnperson",e)},expression:"temp.warnperson"}},t._l(t.warnpersonList,(function(t,e){return n("el-option",{key:"person-"+e,attrs:{label:t.username,value:t.username}})})),1)],1),n("el-form-item",{attrs:{label:"任务状态",prop:"status"}},[n("el-select",{staticClass:"filter-item",attrs:{filterable:"",placeholder:"任务状态"},model:{value:t.temp.status,callback:function(e){t.$set(t.temp,"status",e)},expression:"temp.status"}},[n("el-option",{attrs:{label:"启用",value:"启用"}}),n("el-option",{attrs:{label:"停用",value:"停用"}})],1)],1),n("el-form-item",{attrs:{label:"任务描述",prop:"describe"}},[n("el-input",{staticStyle:{width:"80%"},attrs:{type:"textarea",rows:"2",placeholder:"任务描述"},model:{value:t.temp.describe,callback:function(e){t.$set(t.temp,"describe",e)},expression:"temp.describe"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v(" 取消 ")]),n("el-button",{attrs:{type:"primary"},on:{click:function(e){"create"===t.dialogStatus?t.createData():t.updateData()}}},[t._v(" 确认 ")])],1)],1),n("el-dialog",{attrs:{visible:t.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(e){t.dialogPluginVisible=e}}},[n("el-table",{staticStyle:{width:"100%"},attrs:{data:t.pluginData,border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{prop:"key",label:"Channel"}}),n("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogPvVisible=!1}}},[t._v("Confirm")])],1)],1),n("el-dialog",{attrs:{title:"日志查看",visible:t.dialogVisible,width:"65%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[n("div",{staticClass:"log-container"},[n("pre",{attrs:{loading:t.logLoading},domProps:{textContent:t._s(t.logContent)}})]),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v(" 关闭 ")]),n("el-button",{attrs:{type:"primary"},on:{click:t.loadLog}},[t._v(" 刷新日志 ")])],1)])],1)},o=[],a=(n("a15b"),n("14d9"),n("b0c0"),n("32e8")),r=n("f26b"),l=n("5ec8"),s=n("67248"),u=n("333d"),c=n("3a8d"),d={name:"DevEnvSetting",components:{Pagination:u["a"],Cron:l["a"]},directives:{waves:s["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return{list:null,listLoading:!0,dialogVisible:!1,total:0,listQuery:{pageNo:1,pageSize:10,name:"",describe:""},showCronBox:!1,pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,dialogStatus:"",textMap:{update:"编辑系统任务",create:"新增系统任务"},rules:{name:[{required:!0,message:"调度类名不能为空",trigger:"blur"}],rule:[{required:!0,message:"调度表达式不能为空",trigger:"blur"}],warntype:[{required:!0,message:"联系方式不能为空",trigger:"blur"}],warnperson:[{required:!0,message:"负责人不能为空",trigger:"blur"}],describe:[{required:!0,message:"任务描述不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}]},warnpersonList:[],temp:{id:void 0,name:"",description:""},visible:!0,triggerNextTimes:"",jobLogQuery:{executorAddress:""},logContent:"",logShow:!1,logLoading:!1}},created:function(){this.fetchData(),this.listWarnPerson()},methods:{statusChange:function(t){var e=this;r["updateStatus"]({id:t.id,status:t.status}).then((function(){e.$notify({title:"切换状态",message:"切换成功",type:"success",duration:2e3})}))},listWarnPerson:function(){var t=this;r["listWarnPerson"]({}).then((function(e){t.warnpersonList=e.content.data}))},fetchData:function(){var t=this;console.log(1),this.listLoading=!0,console.log(r),r["list"](this.listQuery).then((function(e){var n=e.content;t.total=n.recordsTotal,t.list=n.data,t.listLoading=!1}))},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&r["created"](t.temp).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"新增 操作",message:"新增 成功",type:"success",duration:2e3})}))}))},handleUpdate:function(t){var e=this;this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var n=Object.assign({},t.temp);r["updated"](n).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;console.log("删除");var n=[];n.push(t.id),r["deleted"](t.id).then((function(t){e.fetchData(),e.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},nextTriggerTime:function(t){var e=this;c["d"](t.rule).then((function(t){var n=t.content;e.triggerNextTimes=n.join("<br>")}))},handlerExecute:function(t){var e=this;this.$confirm("确定执行吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n={};n.id=t.id,n.name=t.name,r["triggerJob"](n).then((function(t){e.$notify({title:"执行操作",message:"执行成功",type:"success",duration:2e3})}))}))},handleViewJobLog:function(t){this.dialogVisible=!0,this.jobLogQuery.executorAddress=t.logpath||"",!1===this.logShow&&(this.logShow=!0),this.loadLog()},loadLog:function(){var t=this;this.logLoading=!0,a["e"](this.jobLogQuery.executorAddress).then((function(e){"\n"===e.content.logContent||(t.logContent=e.content.logContent),t.logLoading=!1}))}}},p=d,f=(n("6351"),n("2877")),m=Object(f["a"])(p,i,o,!1,null,"4df735f6",null);e["default"]=m.exports},"32e8":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"e",(function(){return l})),n.d(e,"d",(function(){return s}));var i=n("b775");function o(t){return Object(i["a"])({url:"api/log/pageList",method:"get",params:t})}function a(t,e,n){return Object(i["a"])({url:"/api/log/clearLog?jobGroup="+t+"&jobId="+e+"&type="+n,method:"post"})}function r(t){return Object(i["a"])({url:"/api/log/killJob",method:"post",data:t})}function l(t,e,n,o){return Object(i["a"])({url:"/api/log/logDetailCat?executorAddress="+t+"&triggerTime="+e+"&logId="+n+"&fromLineNum="+o,method:"get"})}function s(t,e,n,o,a){return Object(i["a"])({url:"/api/schedulerlog/logDetailCat?executorAddress="+t+"&tasktype="+e+"&triggerTime="+n+"&logId="+o+"&fromLineNum="+a,method:"get"})}},"3a8d":function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"b",(function(){return a})),n.d(e,"f",(function(){return r})),n.d(e,"a",(function(){return l})),n.d(e,"e",(function(){return s})),n.d(e,"d",(function(){return u}));var i=n("b775");function o(t){return Object(i["a"])({url:"/api/jobTemplate/pageList",method:"get",params:t})}function a(){return Object(i["a"])({url:"/api/jobGroup/list",method:"get"})}function r(t){return Object(i["a"])({url:"/api/jobTemplate/update",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/api/jobTemplate/add/",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/api/jobTemplate/remove/"+t,method:"post"})}function u(t){return Object(i["a"])({url:"/api/jobTemplate/nextTriggerTime?cron="+t,method:"get"})}},6351:function(t,e,n){"use strict";n("8354")},67248:function(t,e,n){"use strict";n("8d41");var i="@@wavesContext";function o(t,e){function n(n){var i=Object.assign({},e.value),o=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),a=o.ele;if(a){a.style.position="relative",a.style.overflow="hidden";var r=a.getBoundingClientRect(),l=a.querySelector(".waves-ripple");switch(l?l.className="waves-ripple":(l=document.createElement("span"),l.className="waves-ripple",l.style.height=l.style.width=Math.max(r.width,r.height)+"px",a.appendChild(l)),o.type){case"center":l.style.top=r.height/2-l.offsetHeight/2+"px",l.style.left=r.width/2-l.offsetWidth/2+"px";break;default:l.style.top=(n.pageY-r.top-l.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",l.style.left=(n.pageX-r.left-l.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return l.style.backgroundColor=o.color,l.className="waves-ripple z-active",!1}}return t[i]?t[i].removeHandle=n:t[i]={removeHandle:n},n}var a={bind:function(t,e){t.addEventListener("click",o(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[i].removeHandle,!1),t.addEventListener("click",o(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[i].removeHandle,!1),t[i]=null,delete t[i]}},r=function(t){t.directive("waves",a)};window.Vue&&(window.waves=a,Vue.use(r)),a.install=r;e["a"]=a},8354:function(t,e,n){},"8d41":function(t,e,n){},f26b:function(t,e,n){"use strict";n.r(e),n.d(e,"list",(function(){return o})),n.d(e,"listWarnPerson",(function(){return a})),n.d(e,"updated",(function(){return r})),n.d(e,"created",(function(){return l})),n.d(e,"deleted",(function(){return s})),n.d(e,"updateStatus",(function(){return u})),n.d(e,"triggerJob",(function(){return c}));var i=n("b775");function o(t){return Object(i["a"])({url:"/api/warnrule/list",method:"get",params:t})}function a(){return Object(i["a"])({url:"/api/warnrule/listWarnPerson",method:"get"})}function r(t){return Object(i["a"])({url:"/api/warnrule/update",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/api/warnrule/add",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/api/warnrule/remove?id="+t,method:"post"})}function u(t){return Object(i["a"])({url:"/api/warnrule/updateStatus",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/api/warnrule/trigger",method:"post",data:t})}}}]);