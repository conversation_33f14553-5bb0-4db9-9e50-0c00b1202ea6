(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-77cb55eb"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=s(),r=t-o,l=20,c=0;e="undefined"===typeof e?500:e;var d=function t(){c+=l;var s=Math.easeInOutQuad(c,o,r,e);n(s),c<e?i(t):a&&"function"===typeof a&&a()};d()}},"1acd":function(t,e,a){},67248:function(t,e,a){"use strict";a("8d41");var i="@@wavesContext";function n(t,e){function a(a){var i=Object.assign({},e.value),n=Object.assign({ele:t,type:"hit",color:"rgba(0, 0, 0, 0.15)"},i),s=n.ele;if(s){s.style.position="relative",s.style.overflow="hidden";var o=s.getBoundingClientRect(),r=s.querySelector(".waves-ripple");switch(r?r.className="waves-ripple":(r=document.createElement("span"),r.className="waves-ripple",r.style.height=r.style.width=Math.max(o.width,o.height)+"px",s.appendChild(r)),n.type){case"center":r.style.top=o.height/2-r.offsetHeight/2+"px",r.style.left=o.width/2-r.offsetWidth/2+"px";break;default:r.style.top=(a.pageY-o.top-r.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",r.style.left=(a.pageX-o.left-r.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return r.style.backgroundColor=n.color,r.className="waves-ripple z-active",!1}}return t[i]?t[i].removeHandle=a:t[i]={removeHandle:a},a}var s={bind:function(t,e){t.addEventListener("click",n(t,e),!1)},update:function(t,e){t.removeEventListener("click",t[i].removeHandle,!1),t.addEventListener("click",n(t,e),!1)},unbind:function(t){t.removeEventListener("click",t[i].removeHandle,!1),t[i]=null,delete t[i]}},o=function(t){t.directive("waves",s)};window.Vue&&(window.waves=s,Vue.use(o)),s.install=o;e["a"]=s},"7a5a":function(t,e,a){},8705:function(t,e,a){"use strict";a("7a5a")},"8d41":function(t,e,a){},"990b":function(t,e,a){"use strict";a.d(e,"g",(function(){return n})),a.d(e,"e",(function(){return s})),a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"f",(function(){return l})),a.d(e,"i",(function(){return c})),a.d(e,"h",(function(){return d})),a.d(e,"a",(function(){return u})),a.d(e,"b",(function(){return h}));var i=a("b775");function n(t){return Object(i["a"])({url:"/api/dashboardConfig/listDashBoardAPI",method:"get",params:t})}function s(){return Object(i["a"])({url:"/api/dashboardConfig/getDashboardNameList",method:"get"})}function o(t){return Object(i["a"])({url:"/api/dashboradData/findINameField",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/api/dashboradData/findFieldsByInterface",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/api/dashboardConfig/list",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/api/dashboardConfig/update",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/api/dashboardConfig/getValueById?id="+t,method:"get"})}function u(t){return Object(i["a"])({url:"/api/dashboardConfig/add",method:"post",data:t})}function h(t){return Object(i["a"])({url:"/api/dashboardConfig/remove",method:"post",params:t})}},"9cb7":function(t,e,a){"use strict";a("1acd")},ad17:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.showIfr,expression:"showIfr"}],staticClass:"filter-container"},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"240px","margin-right":"10px"},attrs:{placeholder:"看板名称"},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}},t._l(t.getDashboardNameList,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})})),1),a("el-select",{staticClass:"filter-item",staticStyle:{width:"150px"},attrs:{placeholder:"是否已分享"},model:{value:t.listQuery.isshare,callback:function(e){t.$set(t.listQuery,"isshare",e)},expression:"listQuery.isshare"}},t._l(t.shareTypes,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),a("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary round",icon:"el-icon-search"},on:{click:t.fetchData}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v(" 新增 ")])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showIfr,expression:"showIfr"}],staticStyle:{display:"flex","flex-wrap":"wrap"}},t._l(t.list,(function(e){return a("div",{key:e.id},[a("Dashboard",{attrs:{item:e},on:{handleView:t.handleWatch,handleEdit:t.handleUpdate,handleCopy:t.handleCopy,changeSwitch:t.changeSwitch}})],1)})),0),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.showIfr,expression:"!showIfr"}],staticClass:"iframe-container"},[a("el-button",{staticStyle:{float:"right"},attrs:{type:"danger"},on:{click:t.handleClose}},[t._v("关闭")]),a("iframe",{staticClass:"iframe",attrs:{src:t.copyData,frameborder:"0"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{height:"5px"},attrs:{total:t.total,page:t.listQuery.pageNo,limit:t.listQuery.pageSize,pageSizes:[8,16,32]},on:{"update:page":function(e){return t.$set(t.listQuery,"pageNo",e)},"update:limit":function(e){return t.$set(t.listQuery,"pageSize",e)},pagination:t.fetchData}}),a("el-dialog",{attrs:{title:t.textMap[t.dialogStatus],visible:t.dialogFormVisible,width:"800px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"dataForm",attrs:{rules:t.rules,model:t.temp,"label-position":"left","label-width":"100px"}},[a("el-form-item",{attrs:{label:"看板类型",prop:"type"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.temp.type,callback:function(e){t.$set(t.temp,"type",e)},expression:"temp.type"}},[a("el-option",{attrs:{label:"外部",value:"外部"}}),a("el-option",{attrs:{label:"模板",value:"模板"}})],1)],1),a("el-form-item",{attrs:{label:"看板名称",prop:"tname"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"看板名称"},model:{value:t.temp.name,callback:function(e){t.$set(t.temp,"name",e)},expression:"temp.name"}})],1),a("el-form-item",{attrs:{label:"字段路径",prop:"urlpath"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{placeholder:"字段路径"},model:{value:t.temp.urlpath,callback:function(e){t.$set(t.temp,"urlpath",e)},expression:"temp.urlpath"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogFormVisible=!1}}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){"create"===t.dialogStatus?t.createData():t.updateData()}}},[t._v(" 确认 ")])],1)],1),a("el-dialog",{attrs:{visible:t.dialogPluginVisible,title:"Reading statistics"},on:{"update:visible":function(e){t.dialogPluginVisible=e}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.pluginData,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"key",label:"Channel"}}),a("el-table-column",{attrs:{prop:"pv",label:"Pv"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogPvVisible=!1}}},[t._v("Confirm")])],1)],1)],1)},n=[],s=(a("14d9"),a("b0c0"),a("990b")),o=a("b775");function r(t){return Object(o["a"])({url:"/api/dashboardManager/list",method:"get",params:t})}function l(t){return Object(o["a"])({url:"/api/dashboardManager/update",method:"post",data:t})}function c(t){return Object(o["a"])({url:"/api/dashboardManager/add",method:"post",data:t})}function d(t){return Object(o["a"])({url:"/api/dashboardManager/remove",method:"post",params:t})}function u(t){return Object(o["a"])({url:"/api/dashboardManager/stop?id="+t,method:"post"})}function h(t){return Object(o["a"])({url:"/api/dashboardManager/start?id="+t,method:"post"})}var p=a("67248"),m=a("333d"),f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dashboard-manager-c"},[a("h1",{staticClass:"dashboard-manager-c__title"},[t._v(t._s(t.item.name))]),a("div",{staticClass:"dashboard-manager-c__time"},[a("span",[t._v(t._s(t.item.ctime))]),a("div",{staticClass:"dashboard-manager-c__time__btn"},["1"==t.item.isshare?a("i",{staticClass:"el-icon-share el-icon--right",on:{click:t.handleCopy}}):t._e(),"0"==t.item.isshare?a("i",{staticClass:"el-icon-lock",on:{click:function(e){return t.changeSwitch("1")}}}):t._e(),"1"==t.item.isshare?a("i",{staticClass:"el-icon-unlock",on:{click:function(e){return t.changeSwitch("0")}}}):t._e(),a("i",{staticClass:"el-icon-view el-icon--right",on:{click:t.handleView}}),a("i",{staticClass:"el-icon-edit",on:{click:t.handleEdit}})])])])},b=[],g={props:{item:{type:Object,default:""}},created:function(){},methods:{handleView:function(){this.$emit("handleView",this.item)},handleEdit:function(){this.$emit("handleEdit",this.item)},handleCopy:function(){this.$emit("handleCopy",this.item)},changeSwitch:function(t){this.item.isshare=t,this.$emit("changeSwitch",this.item)}}},v=g,y=(a("9cb7"),a("2877")),w=Object(y["a"])(v,f,b,!1,null,null,null),C=w.exports,k={name:"DevEnvSetting",components:{Pagination:m["a"],Dashboard:C},directives:{waves:p["a"]},filters:{statusFilter:function(t){var e={published:"success",draft:"gray",deleted:"danger"};return e[t]}},data:function(){return{list:null,listLoading:!0,total:0,listQuery:{pageNo:1,pageSize:8,name:"",isshare:""},shareTypes:[{value:"",label:""},{value:"1",label:"是"},{value:"0",label:"否"}],pluginTypeOptions:["reader","writer"],dialogPluginVisible:!1,pluginData:[],dialogFormVisible:!1,showIfr:!0,getDashboardNameList:"",dialogStatus:"",copyData:"",textMap:{update:"看板管理修改",create:"看板管理新增"},rules:{name:[{required:!0,message:"看板名称必填",trigger:"blur"}],urlpath:[{required:!0,message:"看板路径必填",trigger:"blur"}]},temp:{id:void 0,name:"",urlpath:""},visible:!0}},created:function(){this.fetchData(),this.getDashboardName()},methods:{fetchData:function(){var t=this;this.listLoading=!0,r(this.listQuery).then((function(e){var a=e.content;t.total=a.recordsTotal,t.list=a.data,t.listLoading=!1}))},handlerStart:function(t){var e=this;h(t.id).then((function(t){e.$notify({title:"分享成功",message:"分享成功",type:"success",duration:2e3})}))},handlerStop:function(t){var e=this;u(t.id).then((function(t){e.$notify({title:"停止分享",message:"停止分享成功",type:"success",duration:2e3})}))},changeSwitch:function(t){"1"===t.isshare?this.handlerStart(t):this.handlerStop(t)},resetTemp:function(){this.temp={id:void 0,name:"",description:""}},handleCreate:function(){var t=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},createData:function(){var t=this;this.$refs["dataForm"].validate((function(e){e&&c(t.temp).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"添加操作",message:"添加成功",type:"success",duration:2e3})}))}))},handleUpdate:function(t){var e=this;console.log(123),this.temp=Object.assign({},t),this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},handleWatch:function(t){"外部"==t.type?(this.copyData=t.urlpath+"?"+t.accessToken,this.showIfr=!1,this.total=0):this.$router.push({path:"/dashboard/screen-display",query:{name:t.name}})},handleCopy:function(t){this.copyData=t.urlpath+"?"+t.accessToken,this.copy(this.copyData)},copy:function(t){var e=t,a=document.createElement("input");a.value=e,document.body.appendChild(a),a.select(),console.log(a.value),document.execCommand("Copy"),this.$message({message:"复制链接成功",type:"success"}),a.remove()},getDashboardName:function(){var t=this;s["e"]().then((function(e){t.getDashboardNameList=e.content}))},updateData:function(){var t=this;this.$refs["dataForm"].validate((function(e){if(e){var a=Object.assign({},t.temp);l(a).then((function(){t.fetchData(),t.dialogFormVisible=!1,t.$notify({title:"更新操作",message:"更新成功",type:"success",duration:2e3})}))}}))},handleDelete:function(t){var e=this;d({id:t.id}).then((function(t){e.fetchData(),e.$notify({title:"删除操作",message:"删除成功",type:"success",duration:2e3})}))},handleClose:function(){this.showIfr=!0,this.fetchData()}}},x=k,D=(a("8705"),Object(y["a"])(x,i,n,!1,null,"029cdbec",null));e["default"]=D.exports}}]);